﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Net


Public Class user_reg
    Private sqlconn2 As New SqlConnection
    Dim web As New WebClient

    Sub new_cus()
        name.Text = ""
        user_adress.Text = ""
        user_phone.Text = ""
        user_email.Text = ""
        user_name.Text = ""
        user_pass.Text = ""
        user_pass2.Text = ""
    End Sub
    Private Sub openconnection2()

        Try
            If My.Computer.Network.Ping("www.google.com") Then

                If sqlconn2.State = 1 Then sqlconn2.Close()
                Try
                    sqlconn2.ConnectionString = web.DownloadString("https://dl.dropbox.com/s/r5yhhzes9vi1qzk/connection.txt?dl=0")
                    sqlconn2.Open()
                Catch ex As Exception
                    MsgBox("فشل في الاتصال بالسيرفير حاول مرة أخري")
                    sqlconn2.Close()
                End Try
            Else
                MsgBox("تاكد من الاتصال بالانترنت", MsgBoxStyle.OkCancel, "")

            End If
        Catch ex As Exception

        End Try


        
    End Sub



    Private Sub exit_btn_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub Label16_Click(sender As Object, e As EventArgs) Handles Label16.Click
        If user_pass.Text <> user_pass2.Text Then
            XtraMessageBox.Show("كلمة السر غير متطابقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub

        End If
        If name.Text = "" Or user_email.Text = "" Or user_phone.Text = "" Or user_adress.Text = "" Or user_name.Text = "" Or user_pass.Text = "" Then
            XtraMessageBox.Show("املأ حميع البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim sql = "select * from user_spaxet where user_name=N'" & (user_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn2)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            XtraMessageBox.Show("يوجد اسم مستخدم مشابه لهذا الاسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============

            dr!name = name.Text
            dr!user_email = user_email.Text
            dr!user_phone = user_phone.Text
            dr!user_adress = user_adress.Text
            dr!user_name = user_name.Text
            dr!user_pass = user_pass.Text
            dr!active_num = ""
            dr!MACAddress = ""
            dr!Win32_Processor = ""
            dr!SerialNumber = ""
            dr!date = Now
            dr!last_seen = Now
            dr!date_active = Now
            dr!active = False
            dr!active_device = False
            '============================
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)

        End If

        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
        XtraMessageBox.Show("تم تسجيل الحساب", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)

        new_cus()
    End Sub


    Private Sub Label21_Click(sender As Object, e As EventArgs) Handles Label21.Click
        exit_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub user_name_TextChanged(sender As Object, e As EventArgs) Handles user_name.TextChanged
        If user_name.Text <> Nothing Then
            Dim sql = "select user_name from user_spaxet where user_name=N'" & (user_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn2)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                PictureBox1.Image = My.Resources.wrong
            Else
                PictureBox1.Image = My.Resources.check_true
            End If
        Else
            PictureBox1.Image = Nothing
        End If
    End Sub

    Private Sub user_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        openconnection2()
    End Sub

End Class