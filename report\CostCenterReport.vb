Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Linq

Public Class CostCenterReport
    Inherits DevExpress.XtraEditors.XtraForm

    Private GridControl1 As DevExpress.XtraGrid.GridControl
    Private GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Private DateFrom As DevExpress.XtraEditors.DateEdit
    Private DateTo As DevExpress.XtraEditors.DateEdit
    Private CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Private ShowReportBtn As DevExpress.XtraEditors.SimpleButton
    Private PrintBtn As DevExpress.XtraEditors.SimpleButton
    Private CloseBtn As DevExpress.XtraEditors.SimpleButton
    Private SummaryBtn As DevExpress.XtraEditors.SimpleButton
    Private ExportBtn As DevExpress.XtraEditors.SimpleButton
    Private RefreshBtn As DevExpress.XtraEditors.SimpleButton

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.DateFrom = New DevExpress.XtraEditors.DateEdit()
        Me.DateTo = New DevExpress.XtraEditors.DateEdit()
        Me.CostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        Me.ShowReportBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.PrintBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.CloseBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.SummaryBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.ExportBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.RefreshBtn = New DevExpress.XtraEditors.SimpleButton()

        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()

        ' Form
        Me.Text = "تقرير مراكز التكلفة"
        Me.Size = New System.Drawing.Size(1000, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes

        ' DateFrom
        Me.DateFrom.Location = New System.Drawing.Point(750, 20)
        Me.DateFrom.Size = New System.Drawing.Size(120, 20)
        Me.DateFrom.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateFrom.EditValue = DateTime.Now.AddMonths(-1)

        ' DateTo
        Me.DateTo.Location = New System.Drawing.Point(600, 20)
        Me.DateTo.Size = New System.Drawing.Size(120, 20)
        Me.DateTo.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateTo.EditValue = DateTime.Now

        ' CostCenterCombo
        Me.CostCenterCombo.Location = New System.Drawing.Point(450, 20)
        Me.CostCenterCombo.Size = New System.Drawing.Size(120, 20)
        Me.CostCenterCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CostCenterCombo.Properties.DisplayMember = "display_name"
        Me.CostCenterCombo.Properties.ValueMember = "cost_center_id"

        ' ShowReportBtn
        Me.ShowReportBtn.Location = New System.Drawing.Point(350, 18)
        Me.ShowReportBtn.Size = New System.Drawing.Size(80, 25)
        Me.ShowReportBtn.Text = "عرض التقرير"
        AddHandler Me.ShowReportBtn.Click, AddressOf ShowReportBtn_Click

        ' PrintBtn
        Me.PrintBtn.Location = New System.Drawing.Point(260, 18)
        Me.PrintBtn.Size = New System.Drawing.Size(80, 25)
        Me.PrintBtn.Text = "طباعة"
        AddHandler Me.PrintBtn.Click, AddressOf PrintBtn_Click

        ' CloseBtn
        Me.CloseBtn.Location = New System.Drawing.Point(20, 18)
        Me.CloseBtn.Size = New System.Drawing.Size(80, 25)
        Me.CloseBtn.Text = "إغلاق"
        AddHandler Me.CloseBtn.Click, AddressOf CloseBtn_Click

        ' SummaryBtn
        Me.SummaryBtn.Location = New System.Drawing.Point(110, 18)
        Me.SummaryBtn.Size = New System.Drawing.Size(80, 25)
        Me.SummaryBtn.Text = "ملخص"
        AddHandler Me.SummaryBtn.Click, AddressOf SummaryBtn_Click

        ' ExportBtn
        Me.ExportBtn.Location = New System.Drawing.Point(200, 18)
        Me.ExportBtn.Size = New System.Drawing.Size(80, 25)
        Me.ExportBtn.Text = "تصدير"
        AddHandler Me.ExportBtn.Click, AddressOf ExportBtn_Click

        ' RefreshBtn
        Me.RefreshBtn.Location = New System.Drawing.Point(290, 18)
        Me.RefreshBtn.Size = New System.Drawing.Size(80, 25)
        Me.RefreshBtn.Text = "تحديث"
        AddHandler Me.RefreshBtn.Click, AddressOf RefreshBtn_Click

        ' GridControl1
        Me.GridControl1.Location = New System.Drawing.Point(12, 60)
        Me.GridControl1.Size = New System.Drawing.Size(976, 500)
        Me.GridControl1.MainView = Me.GridView1

        ' GridView1
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowFooter = True

        ' Add controls to form
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.DateFrom)
        Me.Controls.Add(Me.DateTo)
        Me.Controls.Add(Me.CostCenterCombo)
        Me.Controls.Add(Me.ShowReportBtn)
        Me.Controls.Add(Me.PrintBtn)
        Me.Controls.Add(Me.CloseBtn)
        Me.Controls.Add(Me.SummaryBtn)
        Me.Controls.Add(Me.ExportBtn)
        Me.Controls.Add(Me.RefreshBtn)

        ' Add labels
        Dim lblFrom As New Label()
        lblFrom.Text = "من تاريخ:"
        lblFrom.Location = New System.Drawing.Point(880, 23)
        lblFrom.Size = New System.Drawing.Size(50, 20)
        Me.Controls.Add(lblFrom)

        Dim lblTo As New Label()
        lblTo.Text = "إلى تاريخ:"
        lblTo.Location = New System.Drawing.Point(730, 23)
        lblTo.Size = New System.Drawing.Size(50, 20)
        Me.Controls.Add(lblTo)

        Dim lblCostCenter As New Label()
        lblCostCenter.Text = "مركز التكلفة:"
        lblCostCenter.Location = New System.Drawing.Point(580, 23)
        lblCostCenter.Size = New System.Drawing.Size(70, 20)
        Me.Controls.Add(lblCostCenter)

        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
    End Sub

    Private Sub CostCenterReport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تحميل مراكز التكلفة
            LoadCostCenters()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowReportBtn_Click(sender As Object, e As EventArgs)
        Try
            LoadCostCenterReport()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCostCenterReport()
        Dim sql As String = BuildReportQuery()
        
        Using adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet()
            adp.Fill(ds, "CostCenterReport")
            
            GridControl1.DataSource = ds.Tables("CostCenterReport")
            
            ' تكوين الأعمدة
            ConfigureGridColumns()
        End Using
    End Sub

    Private Function BuildReportQuery() As String
        Dim sql As String = "
            SELECT 
                cc.cost_center_name AS 'مركز التكلفة',
                'فاتورة مبيعات' AS 'نوع العملية',
                CAST(ia.invoice_number AS VARCHAR(50)) AS 'رقم الفاتورة',
                ia.invoice_date AS 'التاريخ',
                ia.Accounts_name AS 'العميل/المورد',
                ia.total_invoice AS 'المبلغ',
                ia.invoice_count AS 'عدد الأصناف',
                ia.earn_invoice AS 'الربح',
                ia.user_invoice AS 'المستخدم',
                CASE 
                    WHEN ia.type_money = 'نقدي' THEN 'نقدي'
                    WHEN ia.type_money = 'أجل' THEN 'آجل'
                    ELSE 'غير محدد'
                END AS 'نوع الدفع',
                ia.invoice_note AS 'ملاحظات'
            FROM invoice_add ia
            INNER JOIN cost_centers cc ON ia.cost_center_id = cc.cost_center_id
            WHERE ia.invoice_date BETWEEN @DateFrom AND @DateTo"
        
        If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
            sql += " AND ia.cost_center_id = " & CostCenterCombo.EditValue.ToString()
        End If
        
        sql += "
            UNION ALL
            SELECT 
                cc.cost_center_name AS 'مركز التكلفة',
                'مصروفات' AS 'نوع العملية',
                CAST(ea.Expenses_number AS VARCHAR(50)) AS 'رقم الفاتورة',
                ea.Expenses_date AS 'التاريخ',
                'مصروفات عامة' AS 'العميل/المورد',
                ea.total_Expenses AS 'المبلغ',
                ea.Expenses_count AS 'عدد الأصناف',
                0 AS 'الربح',
                ea.user_Expenses AS 'المستخدم',
                'نقدي' AS 'نوع الدفع',
                ea.Expenses_note AS 'ملاحظات'
            FROM Expenses_add ea
            INNER JOIN cost_centers cc ON ea.cost_center_id = cc.cost_center_id
            WHERE ea.Expenses_date BETWEEN @DateFrom AND @DateTo"
        
        If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
            sql += " AND ea.cost_center_id = " & CostCenterCombo.EditValue.ToString()
        End If
        
        sql += " ORDER BY 'التاريخ' DESC, 'نوع العملية'"
        
        ' استبدال المعاملات
        sql = sql.Replace("@DateFrom", "'" & DateFrom.DateTime.ToString("yyyy-MM-dd") & "'")
        sql = sql.Replace("@DateTo", "'" & DateTo.DateTime.ToString("yyyy-MM-dd") & "'")
        
        Return sql
    End Function

    Private Sub ConfigureGridColumns()
        With GridView1
            ' تكوين عرض الأعمدة
            .Columns("مركز التكلفة").Width = 120
            .Columns("نوع العملية").Width = 80
            .Columns("رقم الفاتورة").Width = 80
            .Columns("التاريخ").Width = 90
            .Columns("العميل/المورد").Width = 150
            .Columns("المبلغ").Width = 100
            .Columns("عدد الأصناف").Width = 80
            .Columns("الربح").Width = 80
            .Columns("المستخدم").Width = 100
            .Columns("نوع الدفع").Width = 70
            .Columns("ملاحظات").Width = 200

            ' تنسيق الأعمدة الرقمية
            .Columns("المبلغ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            .Columns("المبلغ").DisplayFormat.FormatString = "N2"
            .Columns("الربح").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            .Columns("الربح").DisplayFormat.FormatString = "N2"
            .Columns("عدد الأصناف").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            .Columns("عدد الأصناف").DisplayFormat.FormatString = "N0"

            ' تنسيق التاريخ
            .Columns("التاريخ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            .Columns("التاريخ").DisplayFormat.FormatString = "dd/MM/yyyy"

            ' إضافة مجاميع
            .Columns("المبلغ").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            .Columns("المبلغ").SummaryItem.DisplayFormat = "إجمالي المبلغ: {0:N2}"
            .Columns("الربح").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            .Columns("الربح").SummaryItem.DisplayFormat = "إجمالي الربح: {0:N2}"
            .Columns("عدد الأصناف").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            .Columns("عدد الأصناف").SummaryItem.DisplayFormat = "إجمالي الأصناف: {0:N0}"

            ' تجميد الأعمدة الأساسية
            .Columns("مركز التكلفة").Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left
            .Columns("نوع العملية").Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left

            ' ترتيب الأعمدة
            .Columns("مركز التكلفة").VisibleIndex = 0
            .Columns("نوع العملية").VisibleIndex = 1
            .Columns("رقم الفاتورة").VisibleIndex = 2
            .Columns("التاريخ").VisibleIndex = 3
            .Columns("العميل/المورد").VisibleIndex = 4
            .Columns("المبلغ").VisibleIndex = 5
            .Columns("عدد الأصناف").VisibleIndex = 6
            .Columns("الربح").VisibleIndex = 7
            .Columns("المستخدم").VisibleIndex = 8
            .Columns("نوع الدفع").VisibleIndex = 9
            .Columns("ملاحظات").VisibleIndex = 10

            ' تمكين التجميع والفلترة
            .OptionsView.ShowGroupPanel = True
            .OptionsView.ShowAutoFilterRow = True
            .OptionsCustomization.AllowGroup = True
            .OptionsCustomization.AllowSort = True
            .OptionsCustomization.AllowFilter = True
        End With
    End Sub

    Private Sub PrintBtn_Click(sender As Object, e As EventArgs)
        Try
            GridView1.ShowPrintPreview()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في الطباعة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CloseBtn_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub

    Private Sub SummaryBtn_Click(sender As Object, e As EventArgs)
        Try
            ShowSummaryReport()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض الملخص: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ExportBtn_Click(sender As Object, e As EventArgs)
        Try
            GridView1.ExportToXlsx("تقرير_مراكز_التكلفة_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".xlsx")
            XtraMessageBox.Show("تم تصدير التقرير بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub RefreshBtn_Click(sender As Object, e As EventArgs)
        Try
            LoadCostCenterReport()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحديث التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowSummaryReport()
        Dim sql As String = BuildSummaryQuery()

        Using adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet()
            adp.Fill(ds, "SummaryReport")

            ' إنشاء نافذة جديدة لعرض الملخص
            Dim summaryForm As New XtraForm()
            summaryForm.Text = "ملخص تقرير مراكز التكلفة"
            summaryForm.Size = New System.Drawing.Size(800, 500)
            summaryForm.StartPosition = FormStartPosition.CenterParent
            summaryForm.RightToLeft = RightToLeft.Yes

            Dim summaryGrid As New DevExpress.XtraGrid.GridControl()
            Dim summaryView As New DevExpress.XtraGrid.Views.Grid.GridView()

            summaryGrid.Dock = DockStyle.Fill
            summaryGrid.MainView = summaryView
            summaryGrid.DataSource = ds.Tables("SummaryReport")

            ' تكوين عرض الملخص
            With summaryView
                .OptionsView.ShowFooter = True
                .OptionsView.ShowGroupPanel = False
                .Columns("مركز التكلفة").Width = 200
                .Columns("عدد فواتير المبيعات").Width = 120
                .Columns("إجمالي المبيعات").Width = 120
                .Columns("عدد المصروفات").Width = 120
                .Columns("إجمالي المصروفات").Width = 120
                .Columns("صافي الربح").Width = 120

                ' تنسيق الأعمدة الرقمية
                .Columns("إجمالي المبيعات").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("إجمالي المبيعات").DisplayFormat.FormatString = "N2"
                .Columns("إجمالي المصروفات").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("إجمالي المصروفات").DisplayFormat.FormatString = "N2"
                .Columns("صافي الربح").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("صافي الربح").DisplayFormat.FormatString = "N2"

                ' إضافة مجاميع
                .Columns("إجمالي المبيعات").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
                .Columns("إجمالي المبيعات").SummaryItem.DisplayFormat = "الإجمالي: {0:N2}"
                .Columns("إجمالي المصروفات").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
                .Columns("إجمالي المصروفات").SummaryItem.DisplayFormat = "الإجمالي: {0:N2}"
                .Columns("صافي الربح").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
                .Columns("صافي الربح").SummaryItem.DisplayFormat = "الإجمالي: {0:N2}"
            End With

            summaryForm.Controls.Add(summaryGrid)
            summaryForm.ShowDialog()
        End Using
    End Sub

    Private Function BuildSummaryQuery() As String
        Dim sql As String = "
            SELECT
                cc.cost_center_name AS 'مركز التكلفة',
                ISNULL(sales.sales_count, 0) AS 'عدد فواتير المبيعات',
                ISNULL(sales.sales_total, 0) AS 'إجمالي المبيعات',
                ISNULL(expenses.expenses_count, 0) AS 'عدد المصروفات',
                ISNULL(expenses.expenses_total, 0) AS 'إجمالي المصروفات',
                ISNULL(sales.sales_total, 0) - ISNULL(expenses.expenses_total, 0) AS 'صافي الربح'
            FROM cost_centers cc
            LEFT JOIN (
                SELECT
                    cost_center_id,
                    COUNT(*) AS sales_count,
                    SUM(total_invoice) AS sales_total
                FROM invoice_add
                WHERE invoice_date BETWEEN @DateFrom AND @DateTo
                GROUP BY cost_center_id
            ) sales ON cc.cost_center_id = sales.cost_center_id
            LEFT JOIN (
                SELECT
                    cost_center_id,
                    COUNT(*) AS expenses_count,
                    SUM(total_Expenses) AS expenses_total
                FROM Expenses_add
                WHERE Expenses_date BETWEEN @DateFrom AND @DateTo
                GROUP BY cost_center_id
            ) expenses ON cc.cost_center_id = expenses.cost_center_id
            WHERE cc.is_active = 1"

        If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
            sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
        End If

        sql += " ORDER BY cc.cost_center_name"

        ' استبدال المعاملات
        sql = sql.Replace("@DateFrom", "'" & DateFrom.DateTime.ToString("yyyy-MM-dd") & "'")
        sql = sql.Replace("@DateTo", "'" & DateTo.DateTime.ToString("yyyy-MM-dd") & "'")

        Return sql
    End Function

    Private Sub LoadCostCenters()
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            CreateCostCenterTableIfNotExists()

            ' تحميل مراكز التكلفة
            Dim sql As String = "SELECT cost_center_id, cost_center_name + ' - ' + cost_center_code AS display_name FROM cost_centers WHERE is_active = 1 ORDER BY cost_center_name"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostCenters")

                ' إضافة خيار "الكل"
                Dim allRow As DataRow = ds.Tables("CostCenters").NewRow()
                allRow("cost_center_id") = DBNull.Value
                allRow("display_name") = "جميع مراكز التكلفة"
                ds.Tables("CostCenters").Rows.InsertAt(allRow, 0)

                CostCenterCombo.Properties.DataSource = ds.Tables("CostCenters")
                CostCenterCombo.Properties.DisplayMember = "display_name"
                CostCenterCombo.Properties.ValueMember = "cost_center_id"
                CostCenterCombo.EditValue = DBNull.Value
            End Using
        Catch ex As Exception
            ' في حالة عدم وجود الجدول، نخفي العناصر
            CostCenterCombo.Visible = False
            Dim lblCostCenter As Label = Me.Controls.OfType(Of Label)().FirstOrDefault(Function(l) l.Text.Contains("مركز التكلفة"))
            If lblCostCenter IsNot Nothing Then
                lblCostCenter.Visible = False
            End If
        End Try
    End Sub

    Private Sub CreateCostCenterTableIfNotExists()
        Try
            Dim checkTableSql As String = "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
                CREATE TABLE cost_centers (
                    cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                    cost_center_code NVARCHAR(50) NOT NULL UNIQUE,
                    cost_center_name NVARCHAR(255) NOT NULL,
                    description NVARCHAR(500),
                    is_active BIT DEFAULT 1,
                    created_date DATETIME DEFAULT GETDATE()
                )"

            Using cmd As New SqlCommand(checkTableSql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using

            ' إضافة أعمدة مراكز التكلفة للجداول الموجودة إذا لم تكن موجودة
            Dim addColumnSql As String = "
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('invoice_add') AND name = 'cost_center_id')
                    ALTER TABLE invoice_add ADD cost_center_id INT;

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Expenses_add') AND name = 'cost_center_id')
                    ALTER TABLE Expenses_add ADD cost_center_id INT;"

            Using cmd As New SqlCommand(addColumnSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            ' تجاهل الأخطاء في إنشاء الجدول
        End Try
    End Sub
End Class
