﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class cashing_emp

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Sub fill()
        Net_salary.Text = Val(Basic_salary.Text) + Val(value_extra_hours.Text) - Val(value_absence.Text) - Val(Delay_value.Text) + Val(meal_allowance.Text) + Val(Transfer_allowance.Text) + Val(Other_rewards.Text) - Val(ancestor.Text) - Val(Insurances.Text) - Val(Other_discounts.Text)
    End Sub

    Private Sub cashing_emp_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim dateFrom = New DateTime(DateTime.Now.Year, DateTime.Now.Month, 1)
        Dim dateTo = dateFrom.AddMonths(1).AddDays(-1)
        fromdate.Value = dateFrom
        todate.Value = dateTo
        fillemp()
        fill_treasury_name()
        type_pay.SelectedIndex = 0
        month_name.Text = Now.Month
        month_add()
        year_cash.Text = Date.Now.ToString("yyyy")
    
    End Sub
    Sub new_ec()
        Basic_salary.Text = 0
        Extra_hours.Text = 0
        value_extra_hours.Text = 0
        number_of_days.Text = 0
        value_absence.Text = 0
        Number_absence.Text = 0
        Delay_value.Text = 0
        meal_allowance.Text = 0
        Transfer_allowance.Text = 0
        Other_rewards.Text = 0
        ancestor.Text = 0
        Insurances.Text = 0
        Other_discounts.Text = 0
        Net_salary.Text = 0
    End Sub
    Function GetDays(month As Integer, year As Integer) As List(Of DaysInfo)
        Dim daysInfoList As New List(Of DaysInfo)
        Dim currentDate As DateTime
        For day As Integer = 1 To Date.DaysInMonth(year, month)
            currentDate = New DateTime(year, month, day)
            daysInfoList.Add(New DaysInfo() With {.NameOfDay = currentDate.DayOfWeek.ToString(), .DateOfDay = currentDate})
        Next
        Return daysInfoList
    End Function
    Function gettreasury(subname) As String

        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function
    Sub fillemp()
        emp_name.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from employees order by emp_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            emp_name.Properties.Items.Add(dt.Rows(i).Item("emp_name"))
        Next
    End Sub
    Sub fill_treasury_name()
        Try
            type_pay.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from treasury_name where Treasury_active='true'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                type_pay.Items.Add(dt.Rows(i).Item("treasury_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try

    End Sub

    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged
        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)

        Dim dr = dt.Rows(0)
        treasury_balace.Text = dr!balace
    End Sub

    Private Sub emp_name_SelectedIndexChanged(sender As Object, e As EventArgs) Handles emp_name.SelectedIndexChanged
        new_ec()
        Dim sql = "select * from employees where emp_name=N'" & (emp_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            Basic_salary.Text = dr!Salary
            account_type.Text = dr!account_type
            Day_absence.Text = dr!Day_absence
            clock_absence.Text = dr!clock_absence
            extra_hour.Text = dr!extra_hour
            Day_absence2.Text = dr!Day_absence2
            clock_absence2.Text = dr!clock_absence2
            extra_hour2.Text = dr!extra_hour2
            meal_allowance.Text = dr!meal_allowance
            Transfer_allowance.Text = dr!Transfer_allowance
            Insurances.Text = dr!Insurances
            roseca.Text = dr!Rosacea
        End If
        month_fill()
    End Sub
    Sub ansector()
        Dim sql = "select * from ancestor where emp_name=N'" & (emp_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            ancestor.Text = dr!amount
        End If
    End Sub

    Sub discound_salary()
        Dim Str = "select sum(amount) from Discounts where emp_name=N'" & (emp_name.Text) & "' and date_ancestor>='" & Format(fromdate.Value, "yyyy/MM/dd") & "'  and date_ancestor<='" & Format(todate.Value, "yyyy/MM/dd") & "'"
        Dim cmd As New SqlCommand(Str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("Discounts")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        Other_discounts.Text = Val(sumdebit)
    End Sub
    Sub Rewards_salary()
        Dim Str = "select sum(amount) from Rewards where emp_name=N'" & (emp_name.Text) & "' and date_ancestor>='" & Format(fromdate.Value, "yyyy/MM/dd") & "'  and date_ancestor<='" & Format(todate.Value, "yyyy/MM/dd") & "'"
        Dim cmd As New SqlCommand(Str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("Rewards")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        Other_rewards.Text = Val(sumdebit)
    End Sub

    Private Sub month_fill()

        If emp_name.Text = "" Then
            Exit Sub
        End If
        dgv.DataSource = GetDays(emp_type.SelectedIndex + 1, Convert.ToInt32(year_cash.Text))

        For i = 0 To dgv.Rows.Count - 1

            Dim sql = "select * from Attending_leaving where emp_name=N'" & (emp_name.Text) & "' and date_ancestor='" & dgv.Rows(i).Cells(4).Value & "'"

            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                dgv.Rows(i).Cells(0).Value = dr!time_attendance
                dgv.Rows(i).Cells(1).Value = dr!time_leave
                dgv.Rows(i).Cells(2).Value = dr!betwen_attendance
            Else
                dgv.Rows(i).Cells(0).Value = "غياب"
                dgv.Rows(i).Cells(1).Value = "غياب"
                dgv.Rows(i).Cells(2).Value = "غياب"
                dgv.Rows(i).DefaultCellStyle.BackColor = Color.Red
            End If
        Next
        For a = 0 To dgv.Rows.Count - 1
            If dgv.Rows(a).Cells(3).Value = Holiday1.Text Then
                dgv.Rows(a).Cells(0).Value = "اجازة"
                dgv.Rows(a).Cells(1).Value = "اجازة"
                dgv.Rows(a).Cells(2).Value = "اجازة"
                dgv.Rows(a).DefaultCellStyle.BackColor = Color.Yellow
            End If
            If dgv.Rows(a).Cells(3).Value = Holiday2.Text Then
                dgv.Rows(a).Cells(0).Value = "اجازة"
                dgv.Rows(a).Cells(1).Value = "اجازة"
                dgv.Rows(a).Cells(2).Value = "اجازة"
                dgv.Rows(a).DefaultCellStyle.BackColor = Color.Yellow
            End If
        Next
        For t = 0 To dgv.Rows.Count - 1
            If dgv.Rows(t).Cells(0).Value.ToString <> "غياب" And dgv.Rows(t).Cells(0).Value.ToString <> "اجازة" And dgv.Rows(t).Cells(0).Value.ToString > time_attendance.Text Then
                Dim ts2 As TimeSpan = TimeSpan.Parse(time_attendance.Text)
                Dim ts4 As TimeSpan = TimeSpan.Parse(dgv.Rows(t).Cells(0).Value.ToString)
                Dim x = ts4 - ts2
                Number_absence.Text = CType(x.TotalHours, String)
            End If
        Next
        For g = 0 To dgv.Rows.Count - 1
            If dgv.Rows(g).Cells(0).Value.ToString <> "غياب" And dgv.Rows(g).Cells(0).Value.ToString <> "اجازة" And dgv.Rows(g).Cells(1).Value.ToString > Time_leave.Text Then
                Dim ts2 As TimeSpan = TimeSpan.Parse(Time_leave.Text)
                Dim ts4 As TimeSpan = TimeSpan.Parse(dgv.Rows(g).Cells(1).Value.ToString)
                Dim x = ts4 - ts2
                Extra_hours.Text = CType(x.TotalHours, String)
            End If
        Next
        work_day.Text = dgv.Rows.Count
        For c = 0 To dgv.Rows.Count - 1
            If dgv.Rows(c).Cells(0).Value.ToString = "اجازة" Then
                work_day.Text = Val(work_day.Text) - 1
            End If
        Next
        For q = 0 To dgv.Rows.Count - 1
            If dgv.Rows(q).Cells(0).Value.ToString = "غياب" Then
                number_of_days.Text = Val(number_of_days.Text) + 1
            End If
        Next
        fill_salary()
        discound_salary()
        Rewards_salary()
        ansector()
    End Sub
    Sub fill_salary()
        If account_type.SelectedIndex = 0 Then
            Delay_value.Text = Val(Number_absence.Text) * (Val(clock_absence.Text) * Val(work_clock.Text))
            value_absence.Text = Val(number_of_days.Text) * (Val(Day_absence.Text) * (Val(Basic_salary.Text) / Val(work_day.Text)))
            value_absence.Text = Math.Round(Val(value_absence.Text), 2)
            value_extra_hours.Text = Val(Extra_hours.Text) * (Val(extra_hour.Text) * Val(work_clock.Text))
        ElseIf account_type.SelectedIndex = 1 Then
            Delay_value.Text = Val(Number_absence.Text) * Val(clock_absence2.Text)
            value_absence.Text = Val(number_of_days.Text) * Val(Day_absence2.Text)
            value_extra_hours.Text = Val(Extra_hours.Text) * Val(extra_hour2.Text)
        End If
    End Sub
    Private Sub roseca_TextChanged(sender As Object, e As EventArgs) Handles roseca.TextChanged
        Dim sql = "select * from Rosacea where Rosacea_name=N'" & (roseca.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            time_attendance.EditValue = dr!time_attendance
            Time_leave.EditValue = dr!Time_leave
            Holiday1.Text = dr!Holiday1
            Holiday2.Text = dr!Holiday2
            clock_work.Text = dr!clock_work
        End If
    End Sub
    Public Class DaysInfo
        Public Property NameOfDay As String
        Public Property DateOfDay As Date
    End Class
    Private Sub month_add()
        If month_name.Text = 1 Then
            emp_type.Text = "يناير"
        End If
        If month_name.Text = 2 Then
            emp_type.Text = "فبراير"
        End If
        If month_name.Text = 3 Then
            emp_type.Text = "مارس"
        End If
        If month_name.Text = 4 Then
            emp_type.Text = "أبريل"
        End If
        If month_name.Text = 5 Then
            emp_type.Text = "مايو"
        End If
        If month_name.Text = 6 Then
            emp_type.Text = "يونيو"
        End If
        If month_name.Text = 7 Then
            emp_type.Text = "يوليو"
        End If
        If month_name.Text = 8 Then
            emp_type.Text = "أغسطس"
        End If
        If month_name.Text = 9 Then
            emp_type.Text = "سبتمبر"
        End If
        If month_name.Text = 10 Then
            emp_type.Text = "أكتوبر"
        End If
        If month_name.Text = 11 Then
            emp_type.Text = "نوفمبر"
        End If
        If month_name.Text = 12 Then
            emp_type.Text = "ديسمبر"
        End If

    End Sub


    Private Sub emp_type_SelectedIndexChanged(sender As Object, e As EventArgs) Handles emp_type.SelectedIndexChanged
        month_fill()
    End Sub

    Private Sub year_cash_SelectedIndexChanged(sender As Object, e As EventArgs) Handles year_cash.SelectedIndexChanged
        month_fill()
    End Sub
    Private Sub Other_discounts_TextChanged(sender As Object, e As EventArgs) Handles Other_discounts.TextChanged
        fill()
    End Sub

    Private Sub Insurances_TextChanged(sender As Object, e As EventArgs) Handles Insurances.TextChanged
        fill()
    End Sub

    Private Sub ancestor_TextChanged(sender As Object, e As EventArgs) Handles ancestor.TextChanged
        fill()
    End Sub

    Private Sub Other_rewards_TextChanged(sender As Object, e As EventArgs) Handles Other_rewards.TextChanged
        fill()
    End Sub

    Private Sub Transfer_allowance_TextChanged(sender As Object, e As EventArgs) Handles Transfer_allowance.TextChanged
        fill()
    End Sub

    Private Sub meal_allowance_TextChanged(sender As Object, e As EventArgs) Handles meal_allowance.TextChanged
        fill()
    End Sub

    Private Sub Delay_value_TextChanged(sender As Object, e As EventArgs) Handles Delay_value.TextChanged
        fill()
    End Sub

    Private Sub value_absence_TextChanged(sender As Object, e As EventArgs) Handles value_absence.TextChanged
        fill()
    End Sub

    Private Sub value_extra_hours_TextChanged(sender As Object, e As EventArgs) Handles value_extra_hours.TextChanged
        fill()
    End Sub
    Private Sub work_day_TextChanged(sender As Object, e As EventArgs) Handles work_day.TextChanged
        work_clock.Text = (Val(Basic_salary.Text) / Val(work_day.Text)) / Val(clock_work.Text)
        work_clock.Text = Math.Round(Val(work_clock.Text), 2)
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        Dim sql = "select * from cashing_emp where emp_name=N'" & (emp_name.Text) & "' and date_ancestor>='" & Format(fromdate.Value, "yyyy/MM/dd") & "'  and date_ancestor<='" & Format(todate.Value, "yyyy/MM/dd") & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
          
            MsgBox("تم سداد مرتب هذا الموظف")
        Else
            Dim dr = dt.NewRow
            dr!emp_name = emp_name.Text
            dr!type_pay = type_pay.Text
            dr!date_ancestor = Now.Date
            dr!Basic_salary = Val(Basic_salary.Text)
            dr!Extra_hours = Val(Extra_hours.Text)
            dr!value_extra_hours = Val(value_extra_hours.Text)
            dr!number_of_days = Val(number_of_days.Text)
            dr!value_absence = Val(value_absence.Text)
            dr!Number_absence = Val(Number_absence.Text)
            dr!Delay_value = Val(Delay_value.Text)
            dr!meal_allowance = Val(meal_allowance.Text)
            dr!Transfer_allowance = Val(Transfer_allowance.Text)
            dr!Other_rewards = Val(Other_rewards.Text)
            dr!ancestor = Val(ancestor.Text)
            dr!Insurances = Val(Insurances.Text)
            dr!Other_discounts = Val(Other_discounts.Text)
            dr!Net_salary = Val(Net_salary.Text)
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            aa()
            MsgBox("تم سداد المرتب")
        End If
    End Sub
    Sub aa()
        Dim adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'", sqlconn)
        Dim ds = New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr6 = dt.Rows(0)
        dr6!balace = Val(dr6!balace) - Val(Net_salary.Text)
        Dim cmd6 As New SqlCommandBuilder(adp)
        adp.Update(dt)
        treasury_pay(type_pay.Text, "ايداع", Now.Date, Now.ToString("hh:mm:ss:tt"), "سداد مرتب", XtraForm1.user_name.Text, 0, Val(Net_salary.Text), 0, 0)
    End Sub
End Class