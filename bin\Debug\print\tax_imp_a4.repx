﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.tax_imp_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="tax_imp_a4" Margins="5, 8, 6, 3" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="tax_imp_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="191.4583" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="XrLabel5" RightToLeft="Yes" Text="تـــاريخ الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="235.9999, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1">
          <StylePriority Ref="4" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="5" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="وذلك عن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.6042, 152.5" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="6" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="7" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="مـبلغ وقدره :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.6042, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="8" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="9" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="أســـــــم العميل :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.6042, 57.8125" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="10" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="11" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="كود الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.6042, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="12" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="13" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:#,#}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="187.2916,31.25" LocationFloat="477.3127, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="14" Expression="[Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="15" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="16" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="460.2084,31.25" LocationFloat="17.10419, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="17" Expression="[amount_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="18" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="19" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="647.5,31.25" LocationFloat="17.10419, 57.8125" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="20" Expression="[customername]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="21" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="22" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleCenter" SizeF="296.3126,31.25" LocationFloat="368.2916, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="23" Expression="[desccode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="24" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="25" ControlType="XRLabel" Name="XrLabel12" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel12" TextAlignment="MiddleCenter" SizeF="218.8958,31.25" LocationFloat="17.10419, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="26" Expression="[descdate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="27" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="28" ControlType="XRLabel" Name="XrLabel13" Text="XrLabel13" TextAlignment="MiddleRight" SizeF="649.052,31.25" LocationFloat="15.55211, 152.5" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="29" Expression="[cash_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="30" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item1>
    <Item2 Ref="31" ControlType="TopMarginBand" Name="TopMargin" HeightF="6" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="32" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="2.916654" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="33" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="99.375">
      <Controls>
        <Item1 Ref="34" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="119.2499,99.37499" LocationFloat="9.99999, 0">
          <ExpressionBindings>
            <Item1 Ref="35" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="36" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="533.6459, 41.33333" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="37" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="38" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="39" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="533.6459, 10" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="40" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="41" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="42" ControlType="PageHeaderBand" Name="PageHeader" HeightF="130.208328">
      <Controls>
        <Item1 Ref="43" ControlType="XRBarCode" Name="barCode1" SizeF="136.458313,23.0000038" LocationFloat="136.666687, 41.3333244" Padding="10,10,0,0,96">
          <Symbology Ref="44" Name="Code128" />
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[desccode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="46" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="23.6145649, 83.74999" Padding="2,2,0,0,100">
          <StylePriority Ref="47" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="48" ControlType="XRLabel" Name="XrLabel19" Text="إشعار بالاضافة" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="23.61456, 10.00001" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="49" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item5>
    <Item6 Ref="50" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="127.25">
      <Controls>
        <Item1 Ref="51" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="0.5, 66.66665" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="52" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="53" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="0.5, 102.1667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="54" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="55" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="503.0001, 25.08332" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="56" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="57" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="503.0001, 0" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="58" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="59" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="84.2501, 27.16665" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="60" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="62" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="84.2501, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="63" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="65" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="485.3751, 102.1667" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="66" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="67" UseFont="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="68" ControlType="PageFooterBand" Name="PageFooter" HeightF="29.54166">
      <Controls>
        <Item1 Ref="69" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="17.10419, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="71" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52081,25.08331" LocationFloat="731.375, 2.083333" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="72" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="73" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="682.4166, 6.541659" Padding="2,2,0,0,100">
          <StylePriority Ref="74" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="75" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="76" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="77" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="78" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="79" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="80" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>