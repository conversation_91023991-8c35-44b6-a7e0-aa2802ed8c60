﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.damage_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="damage_a4" Margins="0, 0, 14, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="damag_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="14" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="128.5417">
      <Controls>
        <Item1 Ref="6" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="128.7499,108.5417" LocationFloat="6.364489, 10.00001">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="8" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="546.6459, 61.4583" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="9" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="10" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="11" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="546.6459, 30.12497" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="12" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="14" ControlType="PageHeaderBand" Name="PageHeader" HeightF="241.0417">
      <Controls>
        <Item1 Ref="15" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="277.0832,31.25" LocationFloat="387.5106, 94.47918" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="16" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="17" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="18" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="المخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.5938, 94.47918" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="19" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="20" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="827,33.33334" LocationFloat="0, 201.4584" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="21" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="22" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.6964836904534818" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="23" UsePadding="false" />
                </Item1>
                <Item2 Ref="24" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.62367750488082152" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="25" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.58136839300753063" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="26" UsePadding="false" />
                </Item3>
                <Item4 Ref="27" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.77315790733206891" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="28" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5590196630162829" Text="اسم الصنف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="30" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="277.0833,31.24999" LocationFloat="387.5104, 148.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="31" Expression="[damage_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="32" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="33" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="225.1043,31.25004" LocationFloat="30.11456, 148.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="34" Expression="[damage_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="35" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="36" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.5938, 148.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="37" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="38" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="255.2188, 148.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="39" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="40" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="30.11456, 76.79166" Padding="2,2,0,0,100">
          <StylePriority Ref="41" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="42" ControlType="XRLabel" Name="XrLabel19" Text="إذن هالك" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="30.11456, 0.2083397" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="43" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="44" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="680.2186, 0.2083461">
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item10>
        <Item11 Ref="46" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="90.11458, 31.54165">
          <ExpressionBindings>
            <Item1 Ref="47" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item11>
      </Controls>
    </Item5>
    <Item6 Ref="48" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="205.375">
      <Controls>
        <Item1 Ref="49" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="526.5315,25" LocationFloat="290.4686, 9.999974" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="50" Expression="[damage_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="51" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="52" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="526.5314,25" LocationFloat="290.4686, 34.99997" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="53" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="54" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="55" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="16, 9.999974" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="56" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="57" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="58" Expression="[damage_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="59" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" Padding="2,2,0,0,100" />
              </Cells>
            </Item1>
            <Item2 Ref="60" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="61" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell11" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="62" Expression="[total_damage]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="63" UsePadding="false" />
                </Item1>
                <Item2 Ref="64" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="65" UsePadding="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="66" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="67" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="6.364489, 143.75" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="68" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="69" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="6.364489, 179.25" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="71" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="508.8646, 102.1666" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="72" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="73" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="508.8646, 77.08334" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="74" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="75" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="90.11458, 104.25" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="76" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="77" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="78" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="90.11458, 77.08334" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="79" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="81" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="491.2396, 179.25" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="82" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="83" UseFont="false" />
        </Item10>
      </Controls>
    </Item6>
    <Item7 Ref="84" ControlType="PageFooterBand" Name="PageFooter" HeightF="40.625">
      <Controls>
        <Item1 Ref="85" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="16, 7.77084" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="86" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="87" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="745.4792, 7.77084" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="88" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="89" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="696.5208, 7.77084" Padding="2,2,0,0,100">
          <StylePriority Ref="90" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="91" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="92" ControlType="DetailBand" Name="Detail1" HeightF="29.16667">
          <Controls>
            <Item1 Ref="93" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="827.0001,25" LocationFloat="0, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="94" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="95" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.1005432128906252" TextFormatString="{0:#,#}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="96" Expression="[damag_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="97" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="98" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.98549850463867172" TextFormatString="{0:#,#}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="99" Expression="[damag_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="100" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="101" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.918644561767578" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="102" Expression="[damag_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="103" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.2216995239257815" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="104" Expression="[damag_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="105" ControlType="XRTableCell" Name="XrTableCell8" Weight="4.0436148071289058" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="106" Expression="[damag_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="107" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="108" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="109" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="110" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="111" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="112" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="113" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="114" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="PFNxbERhdGFTb3VyY2U+PE5hbWU+U3FsRGF0YVNvdXJjZTE8L05hbWU+PENvbm5lY3Rpb24gTmFtZT0iZGF0YSIgRnJvbUFwcENvbmZpZz0idHJ1ZSIgLz48UXVlcnkgVHlwZT0iU2VsZWN0UXVlcnkiIE5hbWU9ImRhbWFnX3ByaW50Ij48VGFibGVzPjxUYWJsZSBOYW1lPSJkYW1hZ19wcmludCIgLz48L1RhYmxlcz48Q29sdW1ucz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iZGFtYWdlX251bWJlciIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0ic3RvcmUiIC8+PENvbHVtbiBUYWJsZT0iZGFtYWdfcHJpbnQiIE5hbWU9ImRhbWFnZV9ub3RlIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJkYW1hZ2VfZGF0ZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0idG90YWxfZGFtYWdlIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJkYW1hZ2VfY291bnQiIC8+PENvbHVtbiBUYWJsZT0iZGFtYWdfcHJpbnQiIE5hbWU9Iml0ZW1fZGF0ZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0icmVhc29uX2RhbWFnZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iaXRlbV9wcmljZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iaXRlbV9jb3VudCIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iaXRlbV90b3RhbCIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iaXRlbV91bml0IiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJpdGVtX25hbWUiIC8+PENvbHVtbiBUYWJsZT0iZGFtYWdfcHJpbnQiIE5hbWU9Iml0ZW1fY29kZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iRXhwcjEiIC8+PENvbHVtbiBUYWJsZT0iZGFtYWdfcHJpbnQiIE5hbWU9IkV4cHIyIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJzZWNfbmFtZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0ic2VjX3Bob25lIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJzZWNfYWRkcmVzcyIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0ic2VjX2VtYWlsIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJzZWNfd2ViIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJzZWNfbnVtYmVyIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJzZWNfczEiIC8+PENvbHVtbiBUYWJsZT0iZGFtYWdfcHJpbnQiIE5hbWU9InNlY19zMiIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0ic2VjX3BpYyIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iY29kZSIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0iY29kZV9wcmludCIgLz48Q29sdW1uIFRhYmxlPSJkYW1hZ19wcmludCIgTmFtZT0idG90YWxfc3RyaW5nIiAvPjxDb2x1bW4gVGFibGU9ImRhbWFnX3ByaW50IiBOYW1lPSJwaWNfcXIiIC8+PENvbHVtbiBUYWJsZT0iZGFtYWdfcHJpbnQiIE5hbWU9InBpY19iYXJhY29kZSIgLz48L0NvbHVtbnM+PC9RdWVyeT48UmVzdWx0U2NoZW1hPjxEYXRhU2V0IE5hbWU9IlNxbERhdGFTb3VyY2UxIj48VmlldyBOYW1lPSJkYW1hZ19wcmludCI+PEZpZWxkIE5hbWU9ImRhbWFnZV9udW1iZXIiIFR5cGU9IkludDMyIiAvPjxGaWVsZCBOYW1lPSJzdG9yZSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJkYW1hZ2Vfbm90ZSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJkYW1hZ2VfZGF0ZSIgVHlwZT0iRGF0ZVRpbWUiIC8+PEZpZWxkIE5hbWU9InRvdGFsX2RhbWFnZSIgVHlwZT0iRGVjaW1hbCIgLz48RmllbGQgTmFtZT0iZGFtYWdlX2NvdW50IiBUeXBlPSJEZWNpbWFsIiAvPjxGaWVsZCBOYW1lPSJpdGVtX2RhdGUiIFR5cGU9IkRhdGVUaW1lIiAvPjxGaWVsZCBOYW1lPSJyZWFzb25fZGFtYWdlIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9Iml0ZW1fcHJpY2UiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9Iml0ZW1fY291bnQiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9Iml0ZW1fdG90YWwiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9Iml0ZW1fdW5pdCIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJpdGVtX25hbWUiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0iaXRlbV9jb2RlIiBUeXBlPSJJbnQzMiIgLz48RmllbGQgTmFtZT0iRXhwcjEiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0iRXhwcjIiIFR5cGU9IkludDMyIiAvPjxGaWVsZCBOYW1lPSJzZWNfbmFtZSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJzZWNfcGhvbmUiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX2FkZHJlc3MiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX2VtYWlsIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY193ZWIiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX251bWJlciIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJzZWNfczEiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX3MyIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19waWMiIFR5cGU9IkJ5dGVBcnJheSIgLz48RmllbGQgTmFtZT0iY29kZSIgVHlwZT0iSW50MzIiIC8+PEZpZWxkIE5hbWU9ImNvZGVfcHJpbnQiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0idG90YWxfc3RyaW5nIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InBpY19xciIgVHlwZT0iQnl0ZUFycmF5IiAvPjxGaWVsZCBOYW1lPSJwaWNfYmFyYWNvZGUiIFR5cGU9IkJ5dGVBcnJheSIgLz48L1ZpZXc+PC9EYXRhU2V0PjwvUmVzdWx0U2NoZW1hPjxDb25uZWN0aW9uT3B0aW9ucyBDbG9zZUNvbm5lY3Rpb249InRydWUiIC8+PC9TcWxEYXRhU291cmNlPg==" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>