﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.tax_cus_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="tax_cus_a4" Margins="4, 0, 2, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="tax_cus_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="198.9584" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLine" Name="XrLine3" SizeF="813.0001,4.791673" LocationFloat="9.999871, 194.1667" />
        <Item2 Ref="4" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="وذلك عن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="662.5939, 150.9375" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="5" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="مـبلغ وقدره :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="662.5939, 103.0208" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="7" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="أســـــــم العميل :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="662.5939, 56.25" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="9" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="XrLabel5" RightToLeft="Yes" Text="تـــاريخ الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="233.9896, 8.437507" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1">
          <StylePriority Ref="11" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="12" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="كود الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="662.5939, 8.437507" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="13" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="14" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:#,#}" RightToLeft="Yes" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="172.7187,31.25" LocationFloat="489.8751, 103.0208" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="15" Expression="[Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="16" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="17" ControlType="XRLabel" Name="XrLabel6" RightToLeft="Yes" Text="XrLabel6" SizeF="634.4792,31.25" LocationFloat="28.11456, 150.9375" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="18" Expression="[cash_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="19" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item8>
        <Item9 Ref="20" ControlType="XRLabel" Name="XrLabel9" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="296.3126,31.25" LocationFloat="366.2812, 8.437507" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="21" Expression="[desccode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="22" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="23" ControlType="XRLabel" Name="XrLabel11" RightToLeft="Yes" Text="XrLabel11" SizeF="634.4791,31.25" LocationFloat="28.11459, 56.25" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="24" Expression="[customername]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="25" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item10>
        <Item11 Ref="26" ControlType="XRLabel" Name="XrLabel12" RightToLeft="Yes" Text="XrLabel12" SizeF="461.7605,31.25" LocationFloat="28.11459, 103.0208" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="27" Expression="[state_Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="28" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item11>
        <Item12 Ref="29" ControlType="XRLabel" Name="XrLabel13" TextFormatString="{0:yyyy-MM-dd}" RightToLeft="Yes" Text="XrLabel13" SizeF="205.875,31.25" LocationFloat="28.11456, 8.437507" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="30" Expression="[descdate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="31" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item12>
      </Controls>
    </Item1>
    <Item2 Ref="32" ControlType="TopMarginBand" Name="TopMargin" HeightF="2.083333" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="33" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="34" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="107.7083">
      <Controls>
        <Item1 Ref="35" ControlType="XRLine" Name="XrLine4" SizeF="813.0001,4.791673" LocationFloat="0, 0" />
        <Item2 Ref="36" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="541.7291, 25.33332" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="37" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="38" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="39" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="541.7291, 56.66665" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="40" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="41" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="42" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="127.0834,97.99998" LocationFloat="24.61452, 8.041668">
          <ExpressionBindings>
            <Item1 Ref="43" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="44" ControlType="PageHeaderBand" Name="PageHeader" HeightF="132.3334">
      <Controls>
        <Item1 Ref="45" ControlType="XRLabel" Name="XrLabel19" Text="إشعار بإستلام اضافة" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="24.61452, 10.00001" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="46" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="47" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="85.25003, 41.33332">
          <ExpressionBindings>
            <Item1 Ref="48" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item2>
        <Item3 Ref="49" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="24.61452, 87.91666" Padding="2,2,0,0,100">
          <StylePriority Ref="50" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="51" ControlType="XRLine" Name="XrLine2" SizeF="813.0001,4.791673" LocationFloat="4.99997, 127.5417" />
        <Item5 Ref="52" ControlType="XRLine" Name="XrLine1" SizeF="813.0001,4.791673" LocationFloat="4.999939, 0" />
      </Controls>
    </Item5>
    <Item6 Ref="53" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="127.25">
      <Controls>
        <Item1 Ref="54" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="489.8751, 102.1667" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="55" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="56" UseFont="false" />
        </Item1>
        <Item2 Ref="57" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="88.75009, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="58" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="60" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="88.75009, 27.16665" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="61" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="63" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="507.5001, 0" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="65" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="507.5001, 25.08332" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="66" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="67" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="5, 102.1667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="68" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="69" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="5, 66.66665" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="71" ControlType="PageFooterBand" Name="PageFooter" HeightF="32.37494">
      <Controls>
        <Item1 Ref="72" ControlType="XRLine" Name="XrLine5" SizeF="813.0001,4.791673" LocationFloat="5.000003, 0" />
        <Item2 Ref="73" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.9585,23" LocationFloat="697.6041, 9.374941" Padding="2,2,0,0,100">
          <StylePriority Ref="74" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="75" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="746.5624, 7.291633" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="76" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="77" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="32.29167, 5.2083" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="78" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="79" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="80" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="81" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="82" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="83" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="84" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>