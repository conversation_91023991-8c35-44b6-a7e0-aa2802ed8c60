﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI
Public Class invoice_back_show

    Private Sub searsh_btn_Click(sender As Object, e As EventArgs) Handles searsh_btn.Click
        Dim adp As New SqlDataAdapter("select * from invoice_back  where store=N'" & (store.Text) & "' and invoice_date>='" & Format(Datefrom.Value, "yyy/MM/dd") & "'  and invoice_date<='" & Format(Dateto.Value, "yyy/MM/dd") & "' order by invoice_date", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt
        Dim value As Integer = GridView2.RowCount
        GridView2.TopRowIndex = value
        GridView2.FocusedRowHandle = value
       
    End Sub

    Private Sub cashing_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        fillstore()
        store.Text = XtraForm1.store.Text
        day_option.SelectedIndex = 2
        searsh_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub fillstore()
        store.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from store where store_active=N'true'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            store.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub

    Private Sub day_option_SelectedIndexChanged(sender As Object, e As EventArgs) Handles day_option.SelectedIndexChanged
        If day_option.SelectedIndex = 0 Then
            Datefrom.Value = Now.Date
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 1 Then
            Datefrom.Value = DateAdd("d", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 2 Then
            Datefrom.Value = DateAdd("ww", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 3 Then
            Datefrom.Value = DateAdd("ww", -2, Now.Date)
            Dateto.Value = DateAdd("ww", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 4 Then
            Datefrom.Value = DateAdd("m", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 5 Then
            Datefrom.Value = DateAdd("m", -2, Now.Date)
            Dateto.Value = DateAdd("m", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 6 Then
            Datefrom.Value = DateAdd("m", -3, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 7 Then
            Datefrom.Value = DateAdd("m", -6, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 8 Then
            Datefrom.Value = DateAdd("yyyy", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 9 Then
            Datefrom.Value = DateAdd("yyyy", -2, Now.Date)
            Dateto.Value = DateAdd("yyyy", -1, Now.Date)
        End If
    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Dim f As invoice_back = New invoice_back()
        f.Text = "  مرتجع مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
        f.show_data(GridView2.GetFocusedRowCellValue("invoice_number"))
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click

        Dim adp As New SqlDataAdapter("select * from print_bac_invo where invoice_number=N'" & GridView2.GetFocusedRowCellValue("invoice_number") & "'", sqlconn)
        Dim ds As New DataSet

        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_back_a4.repx"), True)
        rep.DataSource = dt

        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_defult.Text)
            End Using
        End If

      
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub

   

End Class