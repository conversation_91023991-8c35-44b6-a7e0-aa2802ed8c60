﻿Imports System.Data.SqlClient

Public Class Discounts
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fillemp()

      
    End Sub
    Sub new_fill()
        emp_name.Text = ""
        date_ancestor.Value = Now.Date
        amount.Text = 0
        reason.Text = ""
    End Sub

    Sub fillemp()
        emp_name.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from employees order by emp_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            emp_name.Properties.Items.Add(dt.Rows(i).Item("emp_name"))
        Next
    End Sub
    Private Sub save_btn_Click_1(sender As Object, e As EventArgs) Handles save_btn.Click
        If emp_name.Text = "" Then
            MsgBox("أدخل اسم الموظف")
            emp_name.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from Discounts where emp_name=N'" & (emp_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.NewRow
            dr!emp_name = emp_name.Text
            dr!date_ancestor = date_ancestor.Text
            dr!amount = amount.Text
            dr!reason = reason.Text
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            MsgBox("تم أضافة خصم")
            new_fill()
        Catch ex As Exception
            MsgBox("فشل حفظ خصم اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub
End Class