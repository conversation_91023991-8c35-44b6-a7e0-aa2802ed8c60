-- سكريبت تصفير الجداول الأساسية فقط
-- للاستخدام اليومي وإعادة البدء

USE data
GO

PRINT '============================================'
PRINT 'تصفير الجداول الأساسية'
PRINT '============================================'

-- تعطيل فحص المفاتيح الخارجية
EXEC sp_MSforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all"

-- 1. تصفير جداول الفواتير والمبيعات
PRINT 'تصفير جداول الفواتير والمبيعات...'

DELETE FROM invoice_add
DBCC CHECKIDENT ('invoice_add', RESEED, 0)
PRINT '✓ تم تصفير invoice_add'

IF EXISTS (SELECT * FROM sysobjects WHERE name='invoice_details' AND xtype='U')
BEGIN
    DELETE FROM invoice_details
    DBCC CHECKIDENT ('invoice_details', RESEED, 0)
    PRINT '✓ تم تصفير invoice_details'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='invoice_list_back' AND xtype='U')
BEGIN
    DELETE FROM invoice_list
    DBCC CHECKIDENT ('invoice_list_back', RESEED, 0)
    PRINT '✓ تم تصفير invoice_list_back'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='invoice_list' AND xtype='U')
BEGIN
    DELETE FROM invoice_list
    DBCC CHECKIDENT ('invoice_list', RESEED, 0)
    PRINT '✓ تم تصفير invoice_list'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add' AND xtype='U')
BEGIN
    DELETE FROM pos_add
    DBCC CHECKIDENT ('pos_add', RESEED, 0)
    PRINT '✓ تم تصفير pos_add'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add2' AND xtype='U')
BEGIN
    DELETE FROM pos_add2
    DBCC CHECKIDENT ('pos_add2', RESEED, 0)
    PRINT '✓ تم تصفير pos_add2'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='invoice_back' AND xtype='U')
BEGIN
    DELETE FROM invoice_back
    DBCC CHECKIDENT ('invoice_back', RESEED, 0)
    PRINT '✓ تم تصفير invoice_back'
END

-- 2. تصفير جداول الشيفتات
PRINT 'تصفير جداول الشيفتات...'

IF EXISTS (SELECT * FROM sysobjects WHERE name='shifts' AND xtype='U')
BEGIN
    DELETE FROM shifts
    DBCC CHECKIDENT ('shifts', RESEED, 0)
    PRINT '✓ تم تصفير shifts'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='shift_details' AND xtype='U')
BEGIN
    DELETE FROM shift_details
    DBCC CHECKIDENT ('shift_details', RESEED, 0)
    PRINT '✓ تم تصفير shift_details'
END

-- 3. تصفير جداول المدفوعات والإيصالات
PRINT 'تصفير جداول المدفوعات...'

IF EXISTS (SELECT * FROM sysobjects WHERE name='payments' AND xtype='U')
BEGIN
    DELETE FROM payments
    DBCC CHECKIDENT ('payments', RESEED, 0)
    PRINT '✓ تم تصفير payments'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='receipts' AND xtype='U')
BEGIN
    DELETE FROM receipts
    DBCC CHECKIDENT ('receipts', RESEED, 0)
    PRINT '✓ تم تصفير receipts'
END

-- 4. تصفير جداول المرتجعات
IF EXISTS (SELECT * FROM sysobjects WHERE name='returns' AND xtype='U')
BEGIN
    DELETE FROM returns
    DBCC CHECKIDENT ('returns', RESEED, 0)
    PRINT '✓ تم تصفير returns'
END

-- 5. تصفير جداول السندات
IF EXISTS (SELECT * FROM sysobjects WHERE name='vouchers' AND xtype='U')
BEGIN
    DELETE FROM vouchers
    DBCC CHECKIDENT ('vouchers', RESEED, 0)
    PRINT '✓ تم تصفير vouchers'
END

-- 6. تصفير جداول المصروفات والإيرادات
IF EXISTS (SELECT * FROM sysobjects WHERE name='expenses' AND xtype='U')
BEGIN
    DELETE FROM expenses
    DBCC CHECKIDENT ('expenses', RESEED, 0)
    PRINT '✓ تم تصفير expenses'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='income' AND xtype='U')
BEGIN
    DELETE FROM income
    DBCC CHECKIDENT ('income', RESEED, 0)
    PRINT '✓ تم تصفير income'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='tax_customer' AND xtype='U')
BEGIN
    DELETE FROM tax_customer
    DBCC CHECKIDENT ('tax_customer', RESEED, 0)
    PRINT '✓ تم تصفير tax_customer'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='tax_importer' AND xtype='U')
BEGIN
    DELETE FROM tax_importer
    DBCC CHECKIDENT ('tax_importer', RESEED, 0)
    PRINT '✓ تم تصفير tax_importer'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Expenses_list' AND xtype='U')
BEGIN
    DELETE FROM Expenses_list
    DBCC CHECKIDENT ('Expenses_list', RESEED, 0)
    PRINT '✓ تم تصفير Expenses_list'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Expenses_list2' AND xtype='U')
BEGIN
    DELETE FROM Expenses_list2
    DBCC CHECKIDENT ('Expenses_list2', RESEED, 0)
    PRINT '✓ تم تصفير Expenses_list2'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases_add' AND xtype='U')
BEGIN
    DELETE FROM Purchases_add
    DBCC CHECKIDENT ('Purchases_add', RESEED, 0)
    PRINT '✓ تم تصفير Purchases_add'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases_add' AND xtype='U')
BEGIN
    DELETE FROM Purchases_add
    DBCC CHECKIDENT ('Purchases_add', RESEED, 0)
    PRINT '✓ تم تصفير Purchases_add'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases_back' AND xtype='U')
BEGIN
    DELETE FROM Purchases_back
    DBCC CHECKIDENT ('Purchases_back', RESEED, 0)
    PRINT '✓ تم تصفير Purchases_back'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases_back_collection' AND xtype='U')
BEGIN
    DELETE FROM Purchases_back_collection
    DBCC CHECKIDENT ('Purchases_back_collection', RESEED, 0)
    PRINT '✓ تم تصفير Purchases_back_collection'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases_collection' AND xtype='U')
BEGIN
    DELETE FROM Purchases_collection
    DBCC CHECKIDENT ('Purchases_collection', RESEED, 0)
    PRINT '✓ تم تصفير Purchases_collection'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='purchases_list' AND xtype='U')
BEGIN
    DELETE FROM purchases_list
    DBCC CHECKIDENT ('purchases_list', RESEED, 0)
    PRINT '✓ تم تصفير purchases_list'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='purchases_list_back' AND xtype='U')
BEGIN
    DELETE FROM purchases_list_back
    DBCC CHECKIDENT ('purchases_list_back', RESEED, 0)
    PRINT '✓ تم تصفير purchases_list_back'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases_collection' AND xtype='U')
BEGIN
    DELETE FROM Purchases_collection
    DBCC CHECKIDENT ('Purchases_collection', RESEED, 0)
    PRINT '✓ تم تصفير Purchases_collection'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='importer' AND xtype='U')
BEGIN
    DELETE FROM importer
    DBCC CHECKIDENT ('importer', RESEED, 0)
    PRINT '✓ تم تصفير importer'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='importer_cashing' AND xtype='U')
BEGIN
    DELETE FROM importer_cashing
    DBCC CHECKIDENT ('importer_cashing', RESEED, 0)
    PRINT '✓ تم تصفير importer_cashing'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='importer_trans' AND xtype='U')
BEGIN
    DELETE FROM importer_trans
    DBCC CHECKIDENT ('importer_trans', RESEED, 0)
    PRINT '✓ تم تصفير importer_trans'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='treasury_pay' AND xtype='U')
BEGIN
    DELETE FROM treasury_pay
    DBCC CHECKIDENT ('treasury_pay', RESEED, 0)
    PRINT '✓ تم تصفير treasury_pay'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='Expenses_add' AND xtype='U')
BEGIN
    DELETE FROM Expenses_add
    DBCC CHECKIDENT ('Expenses_add', RESEED, 0)
    PRINT '✓ تم تصفير Expenses_add'
END
IF EXISTS (SELECT * FROM sysobjects WHERE name='Expenses_add2' AND xtype='U')
BEGIN
    DELETE FROM Expenses_add2
    DBCC CHECKIDENT ('Expenses_add2', RESEED, 0)
    PRINT '✓ تم تصفير Expenses_add2'
END
-- إعادة تفعيل فحص المفاتيح الخارجية
EXEC sp_MSforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all"

PRINT ''
PRINT '============================================'
PRINT 'تم تصفير الجداول الأساسية بنجاح'
PRINT 'ملاحظة: لم يتم تصفير جداول:'
PRINT '- العملاء (customer)'
PRINT '- الموردين (supplier)' 
PRINT '- الأصناف (items)'
PRINT '- الحسابات (accounts)'
PRINT '- المستخدمين (users)'
PRINT '============================================'

-- عرض عدد الصفوف في الجداول المهمة
SELECT 
    'invoice_add' AS الجدول,
    COUNT(*) AS عدد_الصفوف
FROM invoice_add
UNION ALL
SELECT 'shifts', COUNT(*) FROM shifts
UNION ALL
SELECT 'customer', COUNT(*) FROM customer
UNION ALL
SELECT 'items', COUNT(*) FROM items
