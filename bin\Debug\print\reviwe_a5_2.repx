﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.invoice_a5_2, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="invoice_a5_2" Margins="0, 0, 0, 0" PaperKind="A5" PageWidth="583" PageHeight="827" Version="17.2" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="100">
      <Controls>
        <Item1 Ref="6" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="261.3542,31.33333" LocationFloat="289.4792, 27.625" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="261.3542,41.04168" LocationFloat="289.4792, 58.95832" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="12" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="115.2917,83.99998" LocationFloat="43.22531, 6.000026">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="14" ControlType="PageHeaderBand" Name="PageHeader" HeightF="105.9166">
      <Controls>
        <Item1 Ref="15" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="71.32465,31.24984" LocationFloat="158.517, 39.54166" Font="Droid Arabic Kufi, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="16" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="17" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="516.0834,33.33334" LocationFloat="32.66663, 72.58327" Font="Droid Arabic Kufi, 8pt" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="18" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="19" ControlType="XRTableCell" Name="XrTableCell17" Weight="0.64785404692130832" Text="مستهلك" Padding="2,2,0,0,100">
                  <StylePriority Ref="20" UsePadding="false" />
                </Item1>
                <Item2 Ref="21" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.64785404692130832" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="22" UsePadding="false" />
                </Item2>
                <Item3 Ref="23" ControlType="XRTableCell" Name="XrTableCell15" Weight="0.46868698935474512" Text="الخصم" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100">
                  <StylePriority Ref="24" UseFont="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="25" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.57038110529438424" Text="السعر" Padding="2,2,0,0,100" />
                <Item5 Ref="26" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.58124792843161965" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="27" UsePadding="false" />
                </Item5>
                <Item6 Ref="28" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.68075613084416631" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item7 Ref="29" ControlType="XRTableCell" Name="XrTableCell3" Weight="1.7166200372424629" Text="اسم الصنف" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="White">
                  <StylePriority Ref="30" UseFont="false" />
                </Item7>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="32" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="125.8504,31.24998" LocationFloat="32.66663, 8.291698" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="33" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="34" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="35" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="71.32462,31.25" LocationFloat="158.517, 8.29169" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="36" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="37" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="125.8504,31.24998" LocationFloat="32.66662, 39.54168" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="38" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="40" ControlType="XRLabel" Name="XrLabel19" Text="بيان اسعار" TextAlignment="MiddleCenter" SizeF="74.74249,31.33332" LocationFloat="241.1537, 8.124828" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="41" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="42" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="74.74249,31.25" LocationFloat="241.1537, 39.45815" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="43" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="44" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="45" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleRight" SizeF="136.8855,31.25004" LocationFloat="334.1828, 8.291626" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="46" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="47" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="48" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="77.68185,31.25" LocationFloat="471.0683, 8.208148" Font="Droid Arabic Kufi, 9pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="49" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="50" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="77.68167,31.25" LocationFloat="471.0683, 39.5415" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="51" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="52" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="136.8854,31.24998" LocationFloat="334.1829, 39.54169" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="53" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="54" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item5>
    <Item6 Ref="55" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="177.127">
      <Controls>
        <Item1 Ref="56" ControlType="XRLabel" Name="XrLabel27" Text="XrLabel27" TextAlignment="TopRight" SizeF="250.6404,23" LocationFloat="210.2347, 152.0437" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="57" Expression="[user_invoice]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="58" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="59" ControlType="XRLabel" Name="XrLabel18" RightToLeft="Yes" Text="المستخدم :" TextAlignment="MiddleRight" SizeF="77.48438,25.08331" LocationFloat="460.8752, 152.0437" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="60" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="61" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="363.1516, 93.21054" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="63" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="363.1516, 68.12731" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="65" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="272.5635,23" LocationFloat="90.58803, 95.29406" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="66" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="68" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="272.5635,23.00001" LocationFloat="90.58803, 68.12744" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="69" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="71" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="222.7085, 126.5439" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="72" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="73" UseFont="false" />
        </Item7>
        <Item8 Ref="74" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="51.33321, 126.5439" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="76" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="51.33321, 152.0437" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="77" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="78" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="516.0834,62.5" LocationFloat="32.66662, 0" Font="Droid Arabic Kufi, 8pt" Borders="All">
          <Rows>
            <Item1 Ref="79" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="80" ControlType="XRTableCell" Name="XrTableCell22" Weight="1.1721783522022122" Text="الباقي" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="81" UseBackColor="false" UsePadding="false" />
                </Item1>
                <Item2 Ref="82" ControlType="XRTableCell" Name="XrTableCell21" Weight="1.1480901595333732" Text="المدفوع" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="83" UseBackColor="false" UsePadding="false" />
                </Item2>
                <Item3 Ref="84" ControlType="XRTableCell" Name="XrTableCell14" Weight="1.1925784949628904" Text="المطلوب" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="85" UseBackColor="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="86" ControlType="XRTableCell" Name="XrTableCell11" Weight="1.0687436412920297" Text="السابق" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="87" UseBackColor="false" />
                </Item4>
                <Item5 Ref="88" ControlType="XRTableCell" Name="XrTableCell19" Weight="1.5727722906829018" Text="صافي الفاتورة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="89" UseBackColor="false" UsePadding="false" />
                </Item5>
                <Item6 Ref="90" ControlType="XRTableCell" Name="XrTableCell20" Weight="0.89178838796157678" Text="الضريبة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="91" UseBackColor="false" UsePadding="false" />
                </Item6>
                <Item7 Ref="92" ControlType="XRTableCell" Name="XrTableCell12" Weight="0.96671601439174848" Text="الخصم" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="93" UseBackColor="false" />
                </Item7>
                <Item8 Ref="94" ControlType="XRTableCell" Name="XrTableCell18" Weight="1.0696848087378743" Text="الاجمالي" BackColor="WhiteSmoke" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <StylePriority Ref="95" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="96" ControlType="XRTableCell" Name="XrTableCell13" Weight="1.45142626977306" Text="عدد الاصناف" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="97" UseBackColor="false" />
                </Item9>
              </Cells>
            </Item1>
            <Item2 Ref="98" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="99" ControlType="XRTableCell" Name="XrTableCell23" Weight="1.1721785833909919" TextFormatString="{0:n2}" Text="XrTableCell23" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="100" Expression="[invoice_print].[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="101" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="102" ControlType="XRTableCell" Name="XrTableCell24" Weight="1.1480894386640024" TextFormatString="{0:n2}" Text="XrTableCell24" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="103" Expression="[invoice_print].[pay]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="104" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="105" ControlType="XRTableCell" Name="XrTableCell25" Weight="1.1925780613556163" TextFormatString="{0:n2}" Text="XrTableCell25" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="106" Expression="[invoice_print].[money_plus]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="107" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="108" ControlType="XRTableCell" Name="XrTableCell26" Weight="1.0687439553948983" TextFormatString="{0:n2}" Text="XrTableCell26" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="109" Expression="[invoice_print].[past_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="110" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="111" ControlType="XRTableCell" Name="XrTableCell27" Weight="1.5727746184351217" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="112" Expression="[invoice_print].[total_invoice]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="113" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="114" ControlType="XRTableCell" Name="XrTableCell28" Weight="0.891786111768086" Text="XrTableCell28" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="115" Expression="[invoice_print].[invoice_tax]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="116" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="117" ControlType="XRTableCell" Name="XrTableCell29" Weight="0.96671585866522836" TextFormatString="{0:#,#}" Text="XrTableCell29" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="118" Expression="[invoice_print].[invoice_descound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="119" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="120" ControlType="XRTableCell" Name="XrTableCell31" Weight="1.06968607094668" TextFormatString="{0:n2}" Text="XrTableCell31" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="121" Expression="[invoice_print].[invoice_pound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="122" UseFont="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="123" ControlType="XRTableCell" Name="XrTableCell32" Weight="1.451425720917042" Text="XrTableCell12" TextAlignment="MiddleCenter" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="124" Expression="[invoice_print].[invoice_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="125" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item9>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="126" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
      </Controls>
    </Item6>
    <Item7 Ref="127" ControlType="PageFooterBand" Name="PageFooter" HeightF="15.70835">
      <Controls>
        <Item1 Ref="128" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,15.08344" LocationFloat="51.33321, 0" Font="Times New Roman, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="129" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="130" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,15.08335" LocationFloat="478.8999, 6.357829E-05" Font="Times New Roman, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="131" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="132" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,13.00004" LocationFloat="429.9416, 2.083333" Font="Times New Roman, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="133" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="134" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="135" ControlType="DetailBand" Name="Detail1" HeightF="25">
          <Controls>
            <Item1 Ref="136" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="516.0834,25" LocationFloat="32.66663, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="137" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="138" ControlType="XRTableCell" Name="XrTableCell30" Weight="1.1483655486893258" TextFormatString="{0:n2}" Text="XrTableCell30" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="139" Expression="[invoice_print].[cus_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="140" UseFont="false" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="141" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.1483655486893258" TextFormatString="{0:n2}" Text="XrTableCell9" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="142" Expression="[invoice_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="143" UseFont="false" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="144" ControlType="XRTableCell" Name="XrTableCell16" Weight="0.8307796527335698" Text="XrTableCell16" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="145" Expression="[invoice_print].[item_descound]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="146" UseFont="false" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="147" ControlType="XRTableCell" Name="XrTableCell10" Weight="1.0110397088115195" TextFormatString="{0:n2}" Text="XrTableCell10" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="148" Expression="[invoice_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="149" UseFont="false" UsePadding="false" />
                    </Item4>
                    <Item5 Ref="150" ControlType="XRTableCell" Name="XrTableCell6" Weight="1.0303016515208385" TextFormatString="{0:#,#}" Text="XrTableCell6" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="151" Expression="[invoice_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="152" UseFont="false" />
                    </Item5>
                    <Item6 Ref="153" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.2066867670530213" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="154" Expression="[invoice_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item6>
                    <Item7 Ref="155" ControlType="XRTableCell" Name="XrTableCell8" Weight="3.04282615407342" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="156" Expression="[invoice_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="157" UseFont="false" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="158" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>