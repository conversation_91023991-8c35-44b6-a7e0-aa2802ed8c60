Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Drawing

Public Class CostCenterReportsSimple
    Inherits XtraForm

    Private TabControl As DevExpress.XtraTab.XtraTabControl
    Private CostAnalysisTab As DevExpress.XtraTab.XtraTabPage
    Private BudgetComparisonTab As DevExpress.XtraTab.XtraTabPage
    Private ProfitLossTab As DevExpress.XtraTab.XtraTabPage
    Private ExpenseDistributionTab As DevExpress.XtraTab.XtraTabPage

    ' الجداول
    Private CostAnalysisGrid As DevExpress.XtraGrid.GridControl
    Private CostAnalysisView As DevExpress.XtraGrid.Views.Grid.GridView
    Private BudgetGrid As DevExpress.XtraGrid.GridControl
    Private BudgetView As DevExpress.XtraGrid.Views.Grid.GridView
    Private ProfitLossGrid As DevExpress.XtraGrid.GridControl
    Private ProfitLossView As DevExpress.XtraGrid.Views.Grid.GridView
    Private DistributionGrid As DevExpress.XtraGrid.GridControl
    Private DistributionView As DevExpress.XtraGrid.Views.Grid.GridView

    ' عناصر التحكم
    Private DateFromEdit As DevExpress.XtraEditors.DateEdit
    Private DateToEdit As DevExpress.XtraEditors.DateEdit
    Private CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Private RefreshBtn As DevExpress.XtraEditors.SimpleButton
    Private ExportBtn As DevExpress.XtraEditors.SimpleButton
    Private PrintBtn As DevExpress.XtraEditors.SimpleButton
    Private CloseBtn As DevExpress.XtraEditors.SimpleButton

    Public Sub New()
        InitializeComponent()
        LoadCostCenters()
        LoadAllReports()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "تقارير مراكز التكلفة المتقدمة"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes
        Me.WindowState = FormWindowState.Maximized

        CreateControlPanel()
        CreateTabControl()
        CreateReportTabs()
    End Sub

    Private Sub CreateControlPanel()
        Dim panel As New Panel()
        panel.Height = 60
        panel.Dock = DockStyle.Top
        panel.BackColor = Color.LightGray

        ' من تاريخ
        Dim lblFrom As New Label()
        lblFrom.Text = "من تاريخ:"
        lblFrom.Location = New Point(1000, 20)
        lblFrom.Size = New Size(60, 20)
        panel.Controls.Add(lblFrom)

        DateFromEdit = New DevExpress.XtraEditors.DateEdit()
        DateFromEdit.Location = New Point(900, 18)
        DateFromEdit.Size = New Size(90, 20)
        DateFromEdit.EditValue = DateTime.Now.AddMonths(-1)
        panel.Controls.Add(DateFromEdit)

        ' إلى تاريخ
        Dim lblTo As New Label()
        lblTo.Text = "إلى تاريخ:"
        lblTo.Location = New Point(800, 20)
        lblTo.Size = New Size(60, 20)
        panel.Controls.Add(lblTo)

        DateToEdit = New DevExpress.XtraEditors.DateEdit()
        DateToEdit.Location = New Point(700, 18)
        DateToEdit.Size = New Size(90, 20)
        DateToEdit.EditValue = DateTime.Now
        panel.Controls.Add(DateToEdit)

        ' مركز التكلفة
        Dim lblCostCenter As New Label()
        lblCostCenter.Text = "مركز التكلفة:"
        lblCostCenter.Location = New Point(600, 20)
        lblCostCenter.Size = New Size(80, 20)
        panel.Controls.Add(lblCostCenter)

        CostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        CostCenterCombo.Location = New Point(480, 18)
        CostCenterCombo.Size = New Size(110, 20)
        panel.Controls.Add(CostCenterCombo)

        ' الأزرار
        RefreshBtn = New DevExpress.XtraEditors.SimpleButton()
        RefreshBtn.Text = "تحديث"
        RefreshBtn.Location = New Point(380, 15)
        RefreshBtn.Size = New Size(80, 25)
        AddHandler RefreshBtn.Click, AddressOf RefreshBtn_Click
        panel.Controls.Add(RefreshBtn)

        ExportBtn = New DevExpress.XtraEditors.SimpleButton()
        ExportBtn.Text = "تصدير"
        ExportBtn.Location = New Point(290, 15)
        ExportBtn.Size = New Size(80, 25)
        AddHandler ExportBtn.Click, AddressOf ExportBtn_Click
        panel.Controls.Add(ExportBtn)

        PrintBtn = New DevExpress.XtraEditors.SimpleButton()
        PrintBtn.Text = "طباعة"
        PrintBtn.Location = New Point(200, 15)
        PrintBtn.Size = New Size(80, 25)
        AddHandler PrintBtn.Click, AddressOf PrintBtn_Click
        panel.Controls.Add(PrintBtn)

        CloseBtn = New DevExpress.XtraEditors.SimpleButton()
        CloseBtn.Text = "إغلاق"
        CloseBtn.Location = New Point(110, 15)
        CloseBtn.Size = New Size(80, 25)
        AddHandler CloseBtn.Click, AddressOf CloseBtn_Click
        panel.Controls.Add(CloseBtn)

        Me.Controls.Add(panel)
    End Sub

    Private Sub CreateTabControl()
        TabControl = New DevExpress.XtraTab.XtraTabControl()
        TabControl.Dock = DockStyle.Fill

        ' تبويب تحليل التكاليف
        CostAnalysisTab = New DevExpress.XtraTab.XtraTabPage()
        CostAnalysisTab.Text = "📊 تحليل التكاليف"

        ' تبويب مقارنة الموازنة
        BudgetComparisonTab = New DevExpress.XtraTab.XtraTabPage()
        BudgetComparisonTab.Text = "📈 مقارنة الموازنة"

        ' تبويب الأرباح والخسائر
        ProfitLossTab = New DevExpress.XtraTab.XtraTabPage()
        ProfitLossTab.Text = "📉 الأرباح والخسائر"

        ' تبويب توزيع المصروفات
        ExpenseDistributionTab = New DevExpress.XtraTab.XtraTabPage()
        ExpenseDistributionTab.Text = "📋 توزيع المصروفات"

        TabControl.TabPages.Add(CostAnalysisTab)
        TabControl.TabPages.Add(BudgetComparisonTab)
        TabControl.TabPages.Add(ProfitLossTab)
        TabControl.TabPages.Add(ExpenseDistributionTab)

        Me.Controls.Add(TabControl)
    End Sub

    Private Sub CreateReportTabs()
        ' تبويب تحليل التكاليف
        CostAnalysisGrid = New DevExpress.XtraGrid.GridControl()
        CostAnalysisView = New DevExpress.XtraGrid.Views.Grid.GridView()
        CostAnalysisGrid.MainView = CostAnalysisView
        CostAnalysisGrid.Dock = DockStyle.Fill
        CostAnalysisTab.Controls.Add(CostAnalysisGrid)

        ' تبويب مقارنة الموازنة
        BudgetGrid = New DevExpress.XtraGrid.GridControl()
        BudgetView = New DevExpress.XtraGrid.Views.Grid.GridView()
        BudgetGrid.MainView = BudgetView
        BudgetGrid.Dock = DockStyle.Fill
        BudgetComparisonTab.Controls.Add(BudgetGrid)

        ' تبويب الأرباح والخسائر
        ProfitLossGrid = New DevExpress.XtraGrid.GridControl()
        ProfitLossView = New DevExpress.XtraGrid.Views.Grid.GridView()
        ProfitLossGrid.MainView = ProfitLossView
        ProfitLossGrid.Dock = DockStyle.Fill
        ProfitLossTab.Controls.Add(ProfitLossGrid)

        ' تبويب توزيع المصروفات
        DistributionGrid = New DevExpress.XtraGrid.GridControl()
        DistributionView = New DevExpress.XtraGrid.Views.Grid.GridView()
        DistributionGrid.MainView = DistributionView
        DistributionGrid.Dock = DockStyle.Fill
        ExpenseDistributionTab.Controls.Add(DistributionGrid)
    End Sub

    Private Sub LoadCostCenters()
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            CreateCostCentersTableIfNotExists()
            
            Dim sql As String = "SELECT cost_center_id, cost_center_name + ' - ' + cost_center_code AS display_name FROM cost_centers WHERE is_active = 1 ORDER BY cost_center_name"
            
            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostCenters")
                
                ' إضافة خيار "الكل"
                Dim allRow As DataRow = ds.Tables("CostCenters").NewRow()
                allRow("cost_center_id") = DBNull.Value
                allRow("display_name") = "جميع مراكز التكلفة"
                ds.Tables("CostCenters").Rows.InsertAt(allRow, 0)
                
                CostCenterCombo.Properties.DataSource = ds.Tables("CostCenters")
                CostCenterCombo.Properties.DisplayMember = "display_name"
                CostCenterCombo.Properties.ValueMember = "cost_center_id"
                CostCenterCombo.EditValue = DBNull.Value
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CreateCostCentersTableIfNotExists()
        Try
            Dim sql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_centers (
                        cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_code NVARCHAR(20) NOT NULL,
                        cost_center_name NVARCHAR(100) NOT NULL,
                        description NVARCHAR(255),
                        is_active BIT DEFAULT 1,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                    
                    -- إدراج بيانات تجريبية
                    INSERT INTO cost_centers (cost_center_code, cost_center_name, description) VALUES
                    ('CC001', 'فرع الرئيسي', 'المكتب الرئيسي للشركة'),
                    ('CC002', 'فرع الشمال', 'فرع المنطقة الشمالية'),
                    ('CC003', 'فرع الجنوب', 'فرع المنطقة الجنوبية'),
                    ('CC004', 'قسم التسويق', 'قسم التسويق والمبيعات'),
                    ('CC005', 'قسم الإنتاج', 'قسم الإنتاج والتصنيع');
                END"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    Private Sub LoadAllReports()
        LoadCostAnalysisReport()
        LoadBudgetComparisonReport()
        LoadProfitLossReport()
        LoadExpenseDistributionReport()
    End Sub

    ' 📊 تقرير تحليل التكاليف حسب مركز التكلفة
    Private Sub LoadCostAnalysisReport()
        Try
            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    COUNT(DISTINCT ea.Expenses_id) AS 'عدد المصروفات',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'إجمالي المصروفات',
                    CASE
                        WHEN COUNT(DISTINCT ea.Expenses_id) = 0 THEN 0
                        ELSE ISNULL(SUM(ea.total_Expenses), 0) / COUNT(DISTINCT ea.Expenses_id)
                    END AS 'متوسط المصروف',
                    cc.description AS 'الوصف'
                FROM cost_centers cc
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                WHERE cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code, cc.description ORDER BY 'إجمالي المصروفات' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostAnalysis")

                CostAnalysisGrid.DataSource = ds.Tables("CostAnalysis")
                ConfigureGrid(CostAnalysisView)
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير تحليل التكاليف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 📈 تقرير مقارنة موازنة مركز تكلفة مقابل الفعلي
    Private Sub LoadBudgetComparisonReport()
        Try
            ' إنشاء جدول الموازنة إذا لم يكن موجوداً
            CreateBudgetTableIfNotExists()

            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    ISNULL(b.budget_amount, 0) AS 'الموازنة المخططة',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'المصروفات الفعلية',
                    ISNULL(b.budget_amount, 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'الفرق',
                    CASE
                        WHEN ISNULL(b.budget_amount, 0) = 0 THEN 0
                        ELSE (ISNULL(SUM(ea.total_Expenses), 0) / ISNULL(b.budget_amount, 0)) * 100
                    END AS 'نسبة الاستخدام %',
                    CASE
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) > ISNULL(b.budget_amount, 0) THEN 'تجاوز الموازنة'
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) = ISNULL(b.budget_amount, 0) THEN 'مطابق للموازنة'
                        ELSE 'أقل من الموازنة'
                    END AS 'الحالة'
                FROM cost_centers cc
                LEFT JOIN cost_center_budget b ON cc.cost_center_id = b.cost_center_id
                    AND b.budget_year = YEAR(GETDATE())
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                WHERE cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code, b.budget_amount ORDER BY 'نسبة الاستخدام %' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "BudgetComparison")

                BudgetGrid.DataSource = ds.Tables("BudgetComparison")
                ConfigureGrid(BudgetView)
                ConfigureBudgetColors()
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير مقارنة الموازنة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 📉 تقرير أرباح وخسائر على مستوى مركز تكلفة
    Private Sub LoadProfitLossReport()
        Try
            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    ISNULL(SUM(ia.total_invoice), 0) AS 'إجمالي الإيرادات',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'إجمالي المصروفات',
                    ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'صافي النتيجة',
                    CASE
                        WHEN ISNULL(SUM(ia.total_invoice), 0) = 0 THEN 0
                        ELSE ((ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0)) / ISNULL(SUM(ia.total_invoice), 0)) * 100
                    END AS 'هامش الربح %',
                    CASE
                        WHEN ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) > 0 THEN 'ربح'
                        WHEN ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) = 0 THEN 'تعادل'
                        ELSE 'خسارة'
                    END AS 'النتيجة'
                FROM cost_centers cc
                LEFT JOIN invoice_add ia ON cc.cost_center_id = ia.cost_center_id
                    AND ia.invoice_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                WHERE cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code ORDER BY 'صافي النتيجة' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "ProfitLoss")

                ProfitLossGrid.DataSource = ds.Tables("ProfitLoss")
                ConfigureGrid(ProfitLossView)
                ConfigureProfitLossColors()
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير الأرباح والخسائر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 📋 تقرير توزيع المصروفات حسب مراكز التكلفة
    Private Sub LoadExpenseDistributionReport()
        Try
            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    COUNT(*) AS 'عدد العمليات',
                    SUM(ea.total_Expenses) AS 'إجمالي المبلغ',
                    AVG(ea.total_Expenses) AS 'متوسط المبلغ',
                    (SUM(ea.total_Expenses) * 100.0 / (SELECT SUM(total_Expenses) FROM Expenses_add WHERE Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "')) AS 'النسبة من الإجمالي %'
                FROM Expenses_add ea
                INNER JOIN cost_centers cc ON ea.cost_center_id = cc.cost_center_id
                WHERE ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                AND cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code ORDER BY 'إجمالي المبلغ' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "ExpenseDistribution")

                DistributionGrid.DataSource = ds.Tables("ExpenseDistribution")
                ConfigureGrid(DistributionView)
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير توزيع المصروفات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CreateBudgetTableIfNotExists()
        Try
            Dim sql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_center_budget' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_center_budget (
                        budget_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_id INT NOT NULL,
                        budget_year INT NOT NULL,
                        budget_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
                        created_date DATETIME DEFAULT GETDATE()
                    );

                    -- إدراج بيانات تجريبية للموازنة
                    INSERT INTO cost_center_budget (cost_center_id, budget_year, budget_amount)
                    SELECT cost_center_id, YEAR(GETDATE()),
                        CASE cost_center_id
                            WHEN 1 THEN 100000
                            WHEN 2 THEN 75000
                            WHEN 3 THEN 80000
                            WHEN 4 THEN 50000
                            WHEN 5 THEN 120000
                            ELSE 60000
                        END
                    FROM cost_centers WHERE is_active = 1;
                END"

            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    Private Sub ConfigureGrid(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        With gridView
            .OptionsView.ShowFooter = True
            .OptionsView.ShowGroupPanel = False
            .BestFitColumns()

            ' تنسيق الأعمدة الرقمية
            For Each column As DevExpress.XtraGrid.Columns.GridColumn In .Columns
                If column.FieldName.Contains("إجمالي") OrElse column.FieldName.Contains("متوسط") OrElse column.FieldName.Contains("الموازنة") OrElse column.FieldName.Contains("المبلغ") Then
                    column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    column.DisplayFormat.FormatString = "N2"
                    column.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
                ElseIf column.FieldName.Contains("نسبة") OrElse column.FieldName.Contains("%") Then
                    column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    column.DisplayFormat.FormatString = "N1"
                End If
            Next
        End With
    End Sub

    Private Sub ConfigureBudgetColors()
        AddHandler BudgetView.RowCellStyle, AddressOf BudgetView_RowCellStyle
    End Sub

    Private Sub ConfigureProfitLossColors()
        AddHandler ProfitLossView.RowCellStyle, AddressOf ProfitLossView_RowCellStyle
    End Sub

    Private Sub BudgetView_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs)
        If e.Column.FieldName = "الحالة" Then
            Dim cellValue As String = e.CellValue?.ToString()
            Select Case cellValue
                Case "تجاوز الموازنة"
                    e.Appearance.BackColor = Color.LightCoral
                Case "مطابق للموازنة"
                    e.Appearance.BackColor = Color.LightYellow
                Case "أقل من الموازنة"
                    e.Appearance.BackColor = Color.LightGreen
            End Select
        End If
    End Sub

    Private Sub ProfitLossView_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs)
        If e.Column.FieldName = "النتيجة" Then
            Dim cellValue As String = e.CellValue?.ToString()
            Select Case cellValue
                Case "ربح"
                    e.Appearance.BackColor = Color.LightGreen
                Case "تعادل"
                    e.Appearance.BackColor = Color.LightYellow
                Case "خسارة"
                    e.Appearance.BackColor = Color.LightCoral
            End Select
        End If
    End Sub

    ' أحداث الأزرار
    Private Sub RefreshBtn_Click(sender As Object, e As EventArgs)
        LoadAllReports()
        XtraMessageBox.Show("تم تحديث التقارير بنجاح!", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ExportBtn_Click(sender As Object, e As EventArgs)
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx|PDF Files|*.pdf"
            saveDialog.Title = "تصدير التقرير"
            saveDialog.FileName = "تقارير_مراكز_التكلفة_" & DateTime.Now.ToString("yyyyMMdd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                Select Case TabControl.SelectedTabPageIndex
                    Case 0 ' تحليل التكاليف
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            CostAnalysisView.ExportToXlsx(saveDialog.FileName)
                        Else
                            CostAnalysisView.ExportToPdf(saveDialog.FileName)
                        End If
                    Case 1 ' مقارنة الموازنة
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            BudgetView.ExportToXlsx(saveDialog.FileName)
                        Else
                            BudgetView.ExportToPdf(saveDialog.FileName)
                        End If
                    Case 2 ' الأرباح والخسائر
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            ProfitLossView.ExportToXlsx(saveDialog.FileName)
                        Else
                            ProfitLossView.ExportToPdf(saveDialog.FileName)
                        End If
                    Case 3 ' توزيع المصروفات
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            DistributionView.ExportToXlsx(saveDialog.FileName)
                        Else
                            DistributionView.ExportToPdf(saveDialog.FileName)
                        End If
                End Select

                XtraMessageBox.Show("تم تصدير التقرير بنجاح!", "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub PrintBtn_Click(sender As Object, e As EventArgs)
        Try
            Select Case TabControl.SelectedTabPageIndex
                Case 0 ' تحليل التكاليف
                    CostAnalysisGrid.ShowPrintPreview()
                Case 1 ' مقارنة الموازنة
                    BudgetGrid.ShowPrintPreview()
                Case 2 ' الأرباح والخسائر
                    ProfitLossGrid.ShowPrintPreview()
                Case 3 ' توزيع المصروفات
                    DistributionGrid.ShowPrintPreview()
            End Select
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في طباعة التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CloseBtn_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub
End Class
