﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B508C2A8-A507-48B1-B0D9-24BA5A6F5DFF}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>spaxet.My.MyApplication</StartupObject>
    <RootNamespace>spaxet</RootNamespace>
    <AssemblyName>code store</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>code store.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>code store.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>unicentaopos.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>BBF57F83F67F9A7B9773DE50EC74C13D51E09B25</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>spaxet store_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DocumentationFile>code store.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x64\Release\</OutputPath>
    <DocumentationFile>code store.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Charts.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Images.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v22.1.Export, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v22.1.UI, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v22.1.UI, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v22.1.Wizard, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v22.1.Core, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraNavBar.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v22.1.Extensions, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraVerticalGrid.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="MessagingToolkit.QRCode">
      <HintPath>C:\Users\<USER>\Desktop\MessagingToolkit.QRCode.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Office.Interop.Excel.15.0.4795.1001\lib\net20\Microsoft.Office.Interop.Excel.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ServiceBrokerEnum, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.ServiceBrokerEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SmoExtended, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.SmoExtended.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlEnum, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.SqlEnum.dll</HintPath>
    </Reference>
    <Reference Include="MicroVisionPCID">
      <HintPath>C:\Users\<USER>\Desktop\MicroVisionPCID.dll</HintPath>
    </Reference>
    <Reference Include="MicroVisionSerial">
      <HintPath>C:\Users\<USER>\Desktop\MicroVisionSerial.dll</HintPath>
    </Reference>
    <Reference Include="PictureBoxAbouAmmar">
      <HintPath>C:\Users\<USER>\Desktop\PictureBoxAbouAmmar.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Web.Services\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="DevExpress.XtraLayout" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="customer\customer_export.Designer.vb">
      <DependentUpon>customer_export.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\customer_export.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\customer_edit.Designer.vb">
      <DependentUpon>customer_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\customer_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\setting\cus_visitor.Designer.vb">
      <DependentUpon>cus_visitor.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\setting\cus_visitor.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\dec\discound_show .Designer.vb">
      <DependentUpon>discound_show .vb</DependentUpon>
    </Compile>
    <Compile Include="customer\dec\discound_show .vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\Statistic_customer.Designer.vb">
      <DependentUpon>Statistic_customer.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\Statistic_customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\tax\tax_customer.Designer.vb">
      <DependentUpon>tax_customer.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\tax\tax_customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\dec\descound_customer.Designer.vb">
      <DependentUpon>descound_customer.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\dec\descound_customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\customer_Detailed.Designer.vb">
      <DependentUpon>customer_Detailed.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\customer_Detailed.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\trans\cus_trans_show.Designer.vb">
      <DependentUpon>cus_trans_show.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\trans\cus_trans_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Expenses_add2.Designer.vb">
      <DependentUpon>Expenses_add2.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Expenses_add2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Expenses_show2.Designer.vb">
      <DependentUpon>Expenses_show2.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Expenses_show2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Expense_item2.Designer.vb">
      <DependentUpon>Expense_item2.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Expense_item2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Revenues_show.Designer.vb">
      <DependentUpon>Revenues_show.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Revenues_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Revenues_add.Designer.vb">
      <DependentUpon>Revenues_add.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Revenues_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Revenues_item.Designer.vb">
      <DependentUpon>Revenues_item.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Revenues_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Expense_item.Designer.vb">
      <DependentUpon>Expense_item.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Expense_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Expenses_add.Designer.vb">
      <DependentUpon>Expenses_add.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Expenses_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Expenses\Expenses_show.Designer.vb">
      <DependentUpon>Expenses_show.vb</DependentUpon>
    </Compile>
    <Compile Include="Expenses\Expenses_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmdatabase.designer.vb">
      <DependentUpon>frmdatabase.vb</DependentUpon>
    </Compile>
    <Compile Include="frmdatabase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\desc\discound_show_imp.Designer.vb">
      <DependentUpon>discound_show_imp.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\desc\discound_show_imp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\importer_edit.Designer.vb">
      <DependentUpon>importer_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\importer_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\imp_cash\cashing_imp_show.Designer.vb">
      <DependentUpon>cashing_imp_show.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\imp_cash\cashing_imp_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\importer_export.Designer.vb">
      <DependentUpon>importer_export.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\importer_export.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\imp_cash\cashing_importer.Designer.vb">
      <DependentUpon>cashing_importer.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\imp_cash\cashing_importer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\trans\imp_trans_show.Designer.vb">
      <DependentUpon>imp_trans_show.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\trans\imp_trans_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\invoice_bac.Designer.vb">
      <DependentUpon>invoice_bac.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\invoice_bac.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\invoice_back_show.Designer.vb">
      <DependentUpon>invoice_back_show.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\invoice_back_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\order_item.Designer.vb">
      <DependentUpon>order_item.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\order_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\pos\pos_add.Designer.vb">
      <DependentUpon>pos_add.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\pos\pos_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\pos\pos_add2.Designer.vb">
      <DependentUpon>pos_add2.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\pos\pos_add2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\عرض اسعار\suplier_show.Designer.vb">
      <DependentUpon>suplier_show.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\عرض اسعار\suplier_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\عرض اسعار\suplier_add.Designer.vb">
      <DependentUpon>suplier_add.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\عرض اسعار\suplier_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\فاتورة حجز\Reservation_show.Designer.vb">
      <DependentUpon>Reservation_show.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\فاتورة حجز\Reservation_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\فاتورة حجز\Reservation_add.Designer.vb">
      <DependentUpon>Reservation_add.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\فاتورة حجز\Reservation_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\itemReport1.designer.vb">
      <DependentUpon>itemReport1.vb</DependentUpon>
    </Compile>
    <Compile Include="item\itemReport1.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="item\item_export.Designer.vb">
      <DependentUpon>item_export.vb</DependentUpon>
    </Compile>
    <Compile Include="item\item_export.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\item_edit.Designer.vb">
      <DependentUpon>item_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="item\item_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\barcode_export.Designer.vb">
      <DependentUpon>barcode_export.vb</DependentUpon>
    </Compile>
    <Compile Include="item\barcode_export.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\item_unit.Designer.vb">
      <DependentUpon>item_unit.vb</DependentUpon>
    </Compile>
    <Compile Include="item\item_unit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\اعداد الصنف\serial_item.Designer.vb">
      <DependentUpon>serial_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\اعداد الصنف\serial_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\اعداد الصنف\expire_item.Designer.vb">
      <DependentUpon>expire_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\اعداد الصنف\expire_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\حركة صنف\item_trans_show.Designer.vb">
      <DependentUpon>item_trans_show.vb</DependentUpon>
    </Compile>
    <Compile Include="item\حركة صنف\item_trans_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\مسحوبات\Drawings_item.Designer.vb">
      <DependentUpon>Drawings_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\مسحوبات\Drawings_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\item_bac.Designer.vb">
      <DependentUpon>item_bac.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\item_bac.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Purchases\طلبية مشتريات\Purchases_order.Designer.vb">
      <DependentUpon>Purchases_order.vb</DependentUpon>
    </Compile>
    <Compile Include="Purchases\طلبية مشتريات\Purchases_order.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Purchases\طلبية مشتريات\order_show.Designer.vb">
      <DependentUpon>order_show.vb</DependentUpon>
    </Compile>
    <Compile Include="Purchases\طلبية مشتريات\order_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Purchases\مرتجع المشتريات\Purchases_back.Designer.vb">
      <DependentUpon>Purchases_back.vb</DependentUpon>
    </Compile>
    <Compile Include="Purchases\مرتجع المشتريات\Purchases_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Purchases\مرتجع المشتريات\Purchases_back_show.Designer.vb">
      <DependentUpon>Purchases_back_show.vb</DependentUpon>
    </Compile>
    <Compile Include="Purchases\مرتجع المشتريات\Purchases_back_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ready.Designer.vb">
      <DependentUpon>ready.vb</DependentUpon>
    </Compile>
    <Compile Include="ready.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_earn2.Designer.vb">
      <DependentUpon>item_earn2.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_earn2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_earn1.Designer.vb">
      <DependentUpon>item_earn1.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_earn1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_expire.Designer.vb">
      <DependentUpon>item_expire.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_expire.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\التقارير\cus_trnas_print_a4.Designer.vb">
      <DependentUpon>cus_trnas_print_a4.vb</DependentUpon>
    </Compile>
    <Compile Include="report\التقارير\cus_trnas_print_a4.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="report\الحسابات\Company_evaluation.Designer.vb">
      <DependentUpon>Company_evaluation.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\Company_evaluation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\express_all3.Designer.vb">
      <DependentUpon>express_all3.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\express_all3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\express_all2.Designer.vb">
      <DependentUpon>express_all2.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\express_all2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\General_budget.Designer.vb">
      <DependentUpon>General_budget.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\General_budget.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\income_list.Designer.vb">
      <DependentUpon>income_list.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\income_list.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\Balance_Review.Designer.vb">
      <DependentUpon>Balance_Review.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\Balance_Review.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\Asset_report.Designer.vb">
      <DependentUpon>Asset_report.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\Asset_report.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\asstet_all.Designer.vb">
      <DependentUpon>asstet_all.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\asstet_all.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\format_data.Designer.vb">
      <DependentUpon>format_data.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\format_data.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\Form2.Designer.vb">
      <DependentUpon>Form2.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\Form2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\importer_Detailed.Designer.vb">
      <DependentUpon>importer_Detailed.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\importer_Detailed.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\desc\descound_importer.Designer.vb">
      <DependentUpon>descound_importer.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\desc\descound_importer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\importer_show.Designer.vb">
      <DependentUpon>importer_show.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\importer_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\importer_add2.Designer.vb">
      <DependentUpon>importer_add2.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\importer_add2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\tax\tax_show.Designer.vb">
      <DependentUpon>tax_show.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\tax\tax_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\cashing\cashing_customer.Designer.vb">
      <DependentUpon>cashing_customer.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\cashing\cashing_customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\cashing\cashing_show.Designer.vb">
      <DependentUpon>cashing_show.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\cashing\cashing_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\customer_add.Designer.vb">
      <DependentUpon>customer_add.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\customer_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\customer_show.Designer.vb">
      <DependentUpon>customer_show.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\customer_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\TAX\tax_importer.Designer.vb">
      <DependentUpon>tax_importer.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\TAX\tax_importer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="importer\TAX\taximp_show.Designer.vb">
      <DependentUpon>taximp_show.vb</DependentUpon>
    </Compile>
    <Compile Include="importer\TAX\taximp_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\invoice_add.Designer.vb">
      <DependentUpon>invoice_add.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\invoice_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="invoice\invoice_show.Designer.vb">
      <DependentUpon>invoice_show.vb</DependentUpon>
    </Compile>
    <Compile Include="invoice\invoice_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\price_item.Designer.vb">
      <DependentUpon>price_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\price_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\Alternative_item.Designer.vb">
      <DependentUpon>Alternative_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\Alternative_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\item_Detailed.Designer.vb">
      <DependentUpon>item_Detailed.vb</DependentUpon>
    </Compile>
    <Compile Include="item\item_Detailed.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\item_add.Designer.vb">
      <DependentUpon>item_add.vb</DependentUpon>
    </Compile>
    <Compile Include="item\item_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\مسحوبات\Drawings_show.Designer.vb">
      <DependentUpon>Drawings_show.vb</DependentUpon>
    </Compile>
    <Compile Include="item\مسحوبات\Drawings_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\Statistic_item.Designer.vb">
      <DependentUpon>Statistic_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\Statistic_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\تحويلات\trans_show.Designer.vb">
      <DependentUpon>trans_show.vb</DependentUpon>
    </Compile>
    <Compile Include="item\تحويلات\trans_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\تحويلات\trans_item.Designer.vb">
      <DependentUpon>trans_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\تحويلات\trans_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\جرد\inventory_show.Designer.vb">
      <DependentUpon>inventory_show.vb</DependentUpon>
    </Compile>
    <Compile Include="item\جرد\inventory_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\توالف\damage_show.Designer.vb">
      <DependentUpon>damage_show.vb</DependentUpon>
    </Compile>
    <Compile Include="item\توالف\damage_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\توالف\damage_item.Designer.vb">
      <DependentUpon>damage_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\توالف\damage_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\جرد\inventory_item.Designer.vb">
      <DependentUpon>inventory_item.vb</DependentUpon>
    </Compile>
    <Compile Include="item\جرد\inventory_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\item_show.Designer.vb">
      <DependentUpon>item_show.vb</DependentUpon>
    </Compile>
    <Compile Include="item\item_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="main_page.Designer.vb">
      <DependentUpon>main_page.vb</DependentUpon>
    </Compile>
    <Compile Include="main_page.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\NewFolder1\Treasury_add.Designer.vb">
      <DependentUpon>Treasury_add.vb</DependentUpon>
    </Compile>
    <Compile Include="item\NewFolder1\Treasury_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\NewFolder1\set_store.Designer.vb">
      <DependentUpon>set_store.vb</DependentUpon>
    </Compile>
    <Compile Include="item\NewFolder1\set_store.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="preview.Designer.vb">
      <DependentUpon>preview.vb</DependentUpon>
    </Compile>
    <Compile Include="preview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Purchases\Purchases_add.Designer.vb">
      <DependentUpon>Purchases_add.vb</DependentUpon>
    </Compile>
    <Compile Include="Purchases\Purchases_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\الشيكات\check_chashing.Designer.vb">
      <DependentUpon>check_chashing.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\الشيكات\check_chashing.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\الشيكات\check_at_let.Designer.vb">
      <DependentUpon>check_at_let.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\الشيكات\check_at_let.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\الشيكات\check_late.Designer.vb">
      <DependentUpon>check_late.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\الشيكات\check_late.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\dele_litle_earn.Designer.vb">
      <DependentUpon>dele_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\dele_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\dele_best_earn.Designer.vb">
      <DependentUpon>dele_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\dele_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\dele_cus_earn.Designer.vb">
      <DependentUpon>dele_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\dele_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\gov_cus_earn.Designer.vb">
      <DependentUpon>gov_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\gov_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\gov_litle_earn.Designer.vb">
      <DependentUpon>gov_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\gov_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\gov_best_earn.Designer.vb">
      <DependentUpon>gov_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\gov_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\city_litle_earn.Designer.vb">
      <DependentUpon>city_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\city_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\cus_item.Designer.vb">
      <DependentUpon>cus_item.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\cus_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\city_best_earn.Designer.vb">
      <DependentUpon>city_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\city_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\group_litle_earn.Designer.vb">
      <DependentUpon>group_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\group_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\group_best_earn.Designer.vb">
      <DependentUpon>group_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\group_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\cus_litleearn.Designer.vb">
      <DependentUpon>cus_litleearn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\cus_litleearn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\cus_bestearn.Designer.vb">
      <DependentUpon>cus_bestearn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\cus_bestearn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\cus_minus.Designer.vb">
      <DependentUpon>cus_minus.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\cus_minus.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\cus_Balance.Designer.vb">
      <DependentUpon>cus_Balance.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\cus_Balance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\cus_balace.Designer.vb">
      <DependentUpon>cus_balace.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\cus_balace.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\group_cus_earn.Designer.vb">
      <DependentUpon>group_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\group_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\Statistic_cus_earn.Designer.vb">
      <DependentUpon>Statistic_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\Statistic_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\cus\city_cus_earn.Designer.vb">
      <DependentUpon>city_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\cus\city_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\imp_item.Designer.vb">
      <DependentUpon>imp_item.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\imp_item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\imp_minus.Designer.vb">
      <DependentUpon>imp_minus.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\imp_minus.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\imp_Balance.Designer.vb">
      <DependentUpon>imp_Balance.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\imp_Balance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\imp_balace.Designer.vb">
      <DependentUpon>imp_balace.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\imp_balace.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\شيكات\imp_check_late.Designer.vb">
      <DependentUpon>imp_check_late.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\شيكات\imp_check_late.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\شيكات\imp_check_chashing.Designer.vb">
      <DependentUpon>imp_check_chashing.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\شيكات\imp_check_chashing.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\imp\شيكات\imp_check_at_let.Designer.vb">
      <DependentUpon>imp_check_at_let.vb</DependentUpon>
    </Compile>
    <Compile Include="report\imp\شيكات\imp_check_at_let.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\all_back_inv.Designer.vb">
      <DependentUpon>all_back_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\all_back_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\day_back_inv.Designer.vb">
      <DependentUpon>day_back_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\day_back_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\dele_best_invoice_back.Designer.vb">
      <DependentUpon>dele_best_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\dele_best_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\dele_cus_invoice_back.Designer.vb">
      <DependentUpon>dele_cus_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\dele_cus_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\month_back_inv.Designer.vb">
      <DependentUpon>month_back_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\month_back_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\Statistic_invoice_back.Designer.vb">
      <DependentUpon>Statistic_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\Statistic_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\1\year_back_inv.Designer.vb">
      <DependentUpon>year_back_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\1\year_back_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\city_best_invoice_back.Designer.vb">
      <DependentUpon>city_best_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\city_best_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\city_cus_invoice_back.Designer.vb">
      <DependentUpon>city_cus_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\city_cus_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\gov_best_invoice_back.Designer.vb">
      <DependentUpon>gov_best_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\gov_best_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\gov_cus_invoice_back.Designer.vb">
      <DependentUpon>gov_cus_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\gov_cus_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\group_best_invoice_back.Designer.vb">
      <DependentUpon>group_best_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\group_best_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\group_cus_invoice_back.Designer.vb">
      <DependentUpon>group_cus_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\group_cus_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\stoer_best_invoice_back.Designer.vb">
      <DependentUpon>stoer_best_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\stoer_best_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\bac\2\store_cus_invoice_back.Designer.vb">
      <DependentUpon>store_cus_invoice_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\bac\2\store_cus_invoice_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\all_inv.Designer.vb">
      <DependentUpon>all_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\all_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\store_cus_earn.Designer.vb">
      <DependentUpon>store_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\store_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\Statistic_earn.Designer.vb">
      <DependentUpon>Statistic_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\Statistic_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\Statistic_invoice.Designer.vb">
      <DependentUpon>Statistic_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\Statistic_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\year_inv.Designer.vb">
      <DependentUpon>year_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\year_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\month_inv.Designer.vb">
      <DependentUpon>month_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\month_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\1\day_inv.Designer.vb">
      <DependentUpon>day_inv.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\1\day_inv.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\city_best_invoice.Designer.vb">
      <DependentUpon>city_best_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\city_best_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\city_cus_invoice.Designer.vb">
      <DependentUpon>city_cus_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\city_cus_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\dele_best_invoice.Designer.vb">
      <DependentUpon>dele_best_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\dele_best_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\dele_cus_invoice.Designer.vb">
      <DependentUpon>dele_cus_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\dele_cus_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\gov_best_invoice.Designer.vb">
      <DependentUpon>gov_best_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\gov_best_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\gov_cus_invoice.Designer.vb">
      <DependentUpon>gov_cus_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\gov_cus_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\group_best_invoice.Designer.vb">
      <DependentUpon>group_best_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\group_best_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\group_cus_invoice.Designer.vb">
      <DependentUpon>group_cus_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\group_cus_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\stoer_best_invoice.Designer.vb">
      <DependentUpon>stoer_best_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\stoer_best_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\invo\inv\2\store_cus_invoice.Designer.vb">
      <DependentUpon>store_cus_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\invo\inv\2\store_cus_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\cat\catoregy_litle_earn.Designer.vb">
      <DependentUpon>catoregy_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\cat\catoregy_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\cat\catoregy_best_earn.Designer.vb">
      <DependentUpon>catoregy_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\cat\catoregy_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\cat\catoregy_earn.Designer.vb">
      <DependentUpon>catoregy_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\cat\catoregy_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\comp\company_litle_earn.Designer.vb">
      <DependentUpon>company_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\comp\company_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\comp\company_best_earn.Designer.vb">
      <DependentUpon>company_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\comp\company_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\comp\company_earn.Designer.vb">
      <DependentUpon>company_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\comp\company_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\group\catoregy_earn.Designer.vb">
      <DependentUpon>catoregy_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\group\catoregy_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\group\grou_litle_earn.Designer.vb">
      <DependentUpon>grou_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\group\grou_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\group\grou_best_earn.Designer.vb">
      <DependentUpon>grou_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\group\grou_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_stagnet.Designer.vb">
      <DependentUpon>item_stagnet.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_stagnet.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_earn.Designer.vb">
      <DependentUpon>item_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\damage_list.Designer.vb">
      <DependentUpon>damage_list.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\damage_list.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\Drawings_list.Designer.vb">
      <DependentUpon>Drawings_list.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\Drawings_list.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_bad.Designer.vb">
      <DependentUpon>item_bad.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_bad.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\section\sec_litle_earn2.Designer.vb">
      <DependentUpon>sec_litle_earn2.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\section\sec_litle_earn2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\section\sec_best_earn.Designer.vb">
      <DependentUpon>sec_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\section\sec_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\section\sec_earn.Designer.vb">
      <DependentUpon>sec_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\section\sec_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\trans_list.Designer.vb">
      <DependentUpon>trans_list.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\trans_list.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_inventory.Designer.vb">
      <DependentUpon>item_inventory.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_inventory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_Purchases.Designer.vb">
      <DependentUpon>item_Purchases.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_Purchases.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_invoice.Designer.vb">
      <DependentUpon>item_invoice.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_invoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_minus.Designer.vb">
      <DependentUpon>item_minus.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_minus.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_zero.Designer.vb">
      <DependentUpon>item_zero.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_zero.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\item\item_Deficiencies.Designer.vb">
      <DependentUpon>item_Deficiencies.vb</DependentUpon>
    </Compile>
    <Compile Include="report\item\item_Deficiencies.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\all_pur_back.Designer.vb">
      <DependentUpon>all_pur_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\all_pur_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\day_pur_back.Designer.vb">
      <DependentUpon>day_pur_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\day_pur_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\month_pur_back.Designer.vb">
      <DependentUpon>month_pur_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\month_pur_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\Statistic_Purchases_back.Designer.vb">
      <DependentUpon>Statistic_Purchases_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\Statistic_Purchases_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\stoer_best_Purchases_back.Designer.vb">
      <DependentUpon>stoer_best_Purchases_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\stoer_best_Purchases_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\store_cus_invoice_Purchases_back.Designer.vb">
      <DependentUpon>store_cus_invoice_Purchases_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\store_cus_invoice_Purchases_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\back\year_pur_back.Designer.vb">
      <DependentUpon>year_pur_back.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\back\year_pur_back.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\all_pur.Designer.vb">
      <DependentUpon>all_pur.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\all_pur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\Statistic_Purchases.Designer.vb">
      <DependentUpon>Statistic_Purchases.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\Statistic_Purchases.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\stoer_best_Purchases.Designer.vb">
      <DependentUpon>stoer_best_Purchases.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\stoer_best_Purchases.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\store_cus_invoice_Purchases.Designer.vb">
      <DependentUpon>store_cus_invoice_Purchases.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\store_cus_invoice_Purchases.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\year_pur.Designer.vb">
      <DependentUpon>year_pur.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\year_pur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\month_pur.Designer.vb">
      <DependentUpon>month_pur.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\month_pur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\Pur\pur\day_pur.Designer.vb">
      <DependentUpon>day_pur.vb</DependentUpon>
    </Compile>
    <Compile Include="report\Pur\pur\day_pur.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\report_show.Designer.vb">
      <DependentUpon>report_show.vb</DependentUpon>
    </Compile>
    <Compile Include="report\report_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\balancereview.Designer.vb">
      <DependentUpon>balancereview.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\balancereview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\user_cus_earn.Designer.vb">
      <DependentUpon>user_cus_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\user_cus_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\user_litle_earn.Designer.vb">
      <DependentUpon>user_litle_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\user_litle_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\user_best_earn.Designer.vb">
      <DependentUpon>user_best_earn.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\user_best_earn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="report\الحسابات\express_all.Designer.vb">
      <DependentUpon>express_all.vb</DependentUpon>
    </Compile>
    <Compile Include="report\الحسابات\express_all.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\frmbarcode.designer.vb">
      <DependentUpon>frmbarcode.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\frmbarcode.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\help_system.Designer.vb">
      <DependentUpon>help_system.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\help_system.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\start.Designer.vb">
      <DependentUpon>start.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\start.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\user\active_pro.Designer.vb">
      <DependentUpon>active_pro.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\user\active_pro.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\user\user_reg.Designer.vb">
      <DependentUpon>user_reg.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\user\user_reg.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\user\Power_user.Designer.vb">
      <DependentUpon>Power_user.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\user\Power_user.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\recovey.Designer.vb">
      <DependentUpon>recovey.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\recovey.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\login.Designer.vb">
      <DependentUpon>login.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\login.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="main.Designer.vb">
      <DependentUpon>main.vb</DependentUpon>
    </Compile>
    <Compile Include="main.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\Destruction_ethol.Designer.vb">
      <DependentUpon>Destruction_ethol.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\Destruction_ethol.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\partners_show.Designer.vb">
      <DependentUpon>partners_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\partners_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\Partners_add.Designer.vb">
      <DependentUpon>Partners_add.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\Partners_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\Fixed_assets.Designer.vb">
      <DependentUpon>Fixed_assets.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\Fixed_assets.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\dele_money.Designer.vb">
      <DependentUpon>dele_money.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\dele_money.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\الضربية\detial_tax.Designer.vb">
      <DependentUpon>detial_tax.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\الضربية\detial_tax.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\الضربية\pay_tax_show.Designer.vb">
      <DependentUpon>pay_tax_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\الضربية\pay_tax_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\الضربية\pay_tax.Designer.vb">
      <DependentUpon>pay_tax.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\الضربية\pay_tax.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\حزينة\trans_money.Designer.vb">
      <DependentUpon>trans_money.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\حزينة\trans_money.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\حزينة\Withdrawal.Designer.vb">
      <DependentUpon>Withdrawal.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\حزينة\Withdrawal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\حزينة\Deposit.Designer.vb">
      <DependentUpon>Deposit.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\حزينة\Deposit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الحسابات\حزينة\Treasury.Designer.vb">
      <DependentUpon>Treasury.vb</DependentUpon>
    </Compile>
    <Compile Include="الحسابات\حزينة\Treasury.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\backup.Designer.vb">
      <DependentUpon>backup.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\backup.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CostCentersManagement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CostCentersManagement.Designer.vb">
      <DependentUpon>CostCentersManagement.vb</DependentUpon>
    </Compile>
    <Compile Include="CostCenterHelper.vb" />
    <Compile Include="Module1.vb" />
    <Compile Include="Module2.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="item\NewFolder1\company.Designer.vb">
      <DependentUpon>company.vb</DependentUpon>
    </Compile>
    <Compile Include="item\NewFolder1\company.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\NewFolder1\whada.Designer.vb">
      <DependentUpon>whada.vb</DependentUpon>
    </Compile>
    <Compile Include="item\NewFolder1\whada.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\NewFolder1\Category.Designer.vb">
      <DependentUpon>Category.vb</DependentUpon>
    </Compile>
    <Compile Include="item\NewFolder1\Category.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\setting\Governorate.Designer.vb">
      <DependentUpon>Governorate.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\setting\Governorate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\setting\city.Designer.vb">
      <DependentUpon>city.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\setting\city.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="item\NewFolder1\contact.Designer.vb">
      <DependentUpon>contact.vb</DependentUpon>
    </Compile>
    <Compile Include="item\NewFolder1\contact.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\setting\group.Designer.vb">
      <DependentUpon>group.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\setting\group.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="customer\setting\delegate.Designer.vb">
      <DependentUpon>delegate.vb</DependentUpon>
    </Compile>
    <Compile Include="customer\setting\delegate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\setting_fatora.Designer.vb">
      <DependentUpon>setting_fatora.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\setting_fatora.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setting\user\users.Designer.vb">
      <DependentUpon>users.vb</DependentUpon>
    </Compile>
    <Compile Include="setting\user\users.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Purchases\Purchases_show.Designer.vb">
      <DependentUpon>Purchases_show.vb</DependentUpon>
    </Compile>
    <Compile Include="Purchases\Purchases_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\Attending_leaving.Designer.vb">
      <DependentUpon>Attending_leaving.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\Attending_leaving.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\cashing_emp.Designer.vb">
      <DependentUpon>cashing_emp.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\cashing_emp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\Attending_show.Designer.vb">
      <DependentUpon>Attending_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\Attending_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\Employee_show.Designer.vb">
      <DependentUpon>Employee_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\Employee_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\Employee_add.Designer.vb">
      <DependentUpon>Employee_add.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\Employee_add.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\holiday_show.Designer.vb">
      <DependentUpon>holiday_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\holiday_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\holiday.Designer.vb">
      <DependentUpon>holiday.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\holiday.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Discounts_show.Designer.vb">
      <DependentUpon>Discounts_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Discounts_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Discounts.Designer.vb">
      <DependentUpon>Discounts.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Discounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Rewards_show.Designer.vb">
      <DependentUpon>Rewards_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Rewards_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Rewards.Designer.vb">
      <DependentUpon>Rewards.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Rewards.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\ancestor.Designer.vb">
      <DependentUpon>ancestor.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\ancestor.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\ancestor_show.Designer.vb">
      <DependentUpon>ancestor_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\ancestor_show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\emp_Functioned.Designer.vb">
      <DependentUpon>emp_Functioned.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\emp_Functioned.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Rosacea.Designer.vb">
      <DependentUpon>Rosacea.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\Rosacea.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\emp_Section.Designer.vb">
      <DependentUpon>emp_Section.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\emp_Section.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\emp_manged.Designer.vb">
      <DependentUpon>emp_manged.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\NewFolder1\emp_manged.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="الموظفين\cashing_emp_show.Designer.vb">
      <DependentUpon>cashing_emp_show.vb</DependentUpon>
    </Compile>
    <Compile Include="الموظفين\cashing_emp_show.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="customer\customer_export.resx">
      <DependentUpon>customer_export.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\customer_edit.resx">
      <DependentUpon>customer_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\setting\cus_visitor.resx">
      <DependentUpon>cus_visitor.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\dec\discound_show .resx">
      <DependentUpon>discound_show .vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\Statistic_customer.resx">
      <DependentUpon>Statistic_customer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\tax\tax_customer.resx">
      <DependentUpon>tax_customer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\dec\descound_customer.resx">
      <DependentUpon>descound_customer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\customer_Detailed.resx">
      <DependentUpon>customer_Detailed.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\trans\cus_trans_show.resx">
      <DependentUpon>cus_trans_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Expenses_add2.resx">
      <DependentUpon>Expenses_add2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Expenses_show2.resx">
      <DependentUpon>Expenses_show2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Expense_item2.resx">
      <DependentUpon>Expense_item2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Revenues_show.resx">
      <DependentUpon>Revenues_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Revenues_add.resx">
      <DependentUpon>Revenues_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Revenues_item.resx">
      <DependentUpon>Revenues_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Expense_item.resx">
      <DependentUpon>Expense_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Expenses_add.resx">
      <DependentUpon>Expenses_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Expenses\Expenses_show.resx">
      <DependentUpon>Expenses_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmdatabase.resx">
      <DependentUpon>frmdatabase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\desc\discound_show_imp.resx">
      <DependentUpon>discound_show_imp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\importer_edit.resx">
      <DependentUpon>importer_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\imp_cash\cashing_imp_show.resx">
      <DependentUpon>cashing_imp_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\importer_export.resx">
      <DependentUpon>importer_export.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\imp_cash\cashing_importer.resx">
      <DependentUpon>cashing_importer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\trans\imp_trans_show.resx">
      <DependentUpon>imp_trans_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\invoice_bac.resx">
      <DependentUpon>invoice_bac.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\invoice_back_show.resx">
      <DependentUpon>invoice_back_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\order_item.resx">
      <DependentUpon>order_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\pos\pos_add.resx">
      <DependentUpon>pos_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\pos\pos_add2.resx">
      <DependentUpon>pos_add2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\عرض اسعار\suplier_show.resx">
      <DependentUpon>suplier_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\عرض اسعار\suplier_add.resx">
      <DependentUpon>suplier_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\فاتورة حجز\Reservation_show.resx">
      <DependentUpon>Reservation_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\فاتورة حجز\Reservation_add.resx">
      <DependentUpon>Reservation_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\itemReport1.resx">
      <DependentUpon>itemReport1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\item_export.resx">
      <DependentUpon>item_export.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\item_edit.resx">
      <DependentUpon>item_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\barcode_export.resx">
      <DependentUpon>barcode_export.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\item_unit.resx">
      <DependentUpon>item_unit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\اعداد الصنف\serial_item.resx">
      <DependentUpon>serial_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\اعداد الصنف\expire_item.resx">
      <DependentUpon>expire_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\حركة صنف\item_trans_show.resx">
      <DependentUpon>item_trans_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\مسحوبات\Drawings_item.resx">
      <DependentUpon>Drawings_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\item_bac.resx">
      <DependentUpon>item_bac.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Purchases\طلبية مشتريات\Purchases_order.resx">
      <DependentUpon>Purchases_order.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Purchases\طلبية مشتريات\order_show.resx">
      <DependentUpon>order_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Purchases\مرتجع المشتريات\Purchases_back.resx">
      <DependentUpon>Purchases_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Purchases\مرتجع المشتريات\Purchases_back_show.resx">
      <DependentUpon>Purchases_back_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ready.resx">
      <DependentUpon>ready.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_earn2.resx">
      <DependentUpon>item_earn2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_earn1.resx">
      <DependentUpon>item_earn1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_expire.resx">
      <DependentUpon>item_expire.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\التقارير\cus_trnas_print_a4.resx">
      <DependentUpon>cus_trnas_print_a4.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\Company_evaluation.resx">
      <DependentUpon>Company_evaluation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\express_all3.resx">
      <DependentUpon>express_all3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\express_all2.resx">
      <DependentUpon>express_all2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\General_budget.resx">
      <DependentUpon>General_budget.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\income_list.resx">
      <DependentUpon>income_list.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\Balance_Review.resx">
      <DependentUpon>Balance_Review.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\Asset_report.resx">
      <DependentUpon>Asset_report.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\asstet_all.resx">
      <DependentUpon>asstet_all.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\format_data.resx">
      <DependentUpon>format_data.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\Form2.resx">
      <DependentUpon>Form2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\importer_Detailed.resx">
      <DependentUpon>importer_Detailed.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\desc\descound_importer.resx">
      <DependentUpon>descound_importer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\importer_show.resx">
      <DependentUpon>importer_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\importer_add2.resx">
      <DependentUpon>importer_add2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\tax\tax_show.resx">
      <DependentUpon>tax_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\cashing\cashing_customer.resx">
      <DependentUpon>cashing_customer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\cashing\cashing_show.resx">
      <DependentUpon>cashing_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\customer_add.resx">
      <DependentUpon>customer_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\customer_show.resx">
      <DependentUpon>customer_show.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\TAX\tax_importer.resx">
      <DependentUpon>tax_importer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="importer\TAX\taximp_show.resx">
      <DependentUpon>taximp_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\invoice_add.resx">
      <DependentUpon>invoice_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="invoice\invoice_show.resx">
      <DependentUpon>invoice_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\price_item.resx">
      <DependentUpon>price_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\Alternative_item.resx">
      <DependentUpon>Alternative_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\item_Detailed.resx">
      <DependentUpon>item_Detailed.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\item_add.resx">
      <DependentUpon>item_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\مسحوبات\Drawings_show.resx">
      <DependentUpon>Drawings_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\Statistic_item.resx">
      <DependentUpon>Statistic_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\تحويلات\trans_show.resx">
      <DependentUpon>trans_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\تحويلات\trans_item.resx">
      <DependentUpon>trans_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\جرد\inventory_show.resx">
      <DependentUpon>inventory_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\توالف\damage_show.resx">
      <DependentUpon>damage_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\توالف\damage_item.resx">
      <DependentUpon>damage_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\جرد\inventory_item.resx">
      <DependentUpon>inventory_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\item_show.resx">
      <DependentUpon>item_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="main_page.resx">
      <DependentUpon>main_page.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="item\NewFolder1\Treasury_add.resx">
      <DependentUpon>Treasury_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\NewFolder1\set_store.resx">
      <DependentUpon>set_store.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="preview.resx">
      <DependentUpon>preview.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Purchases\Purchases_add.resx">
      <DependentUpon>Purchases_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\الشيكات\check_chashing.resx">
      <DependentUpon>check_chashing.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\الشيكات\check_at_let.resx">
      <DependentUpon>check_at_let.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\الشيكات\check_late.resx">
      <DependentUpon>check_late.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\dele_litle_earn.resx">
      <DependentUpon>dele_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\dele_best_earn.resx">
      <DependentUpon>dele_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\dele_cus_earn.resx">
      <DependentUpon>dele_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\gov_cus_earn.resx">
      <DependentUpon>gov_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\gov_litle_earn.resx">
      <DependentUpon>gov_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\gov_best_earn.resx">
      <DependentUpon>gov_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\city_litle_earn.resx">
      <DependentUpon>city_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\cus_item.resx">
      <DependentUpon>cus_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\city_best_earn.resx">
      <DependentUpon>city_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\group_litle_earn.resx">
      <DependentUpon>group_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\group_best_earn.resx">
      <DependentUpon>group_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\cus_litleearn.resx">
      <DependentUpon>cus_litleearn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\cus_bestearn.resx">
      <DependentUpon>cus_bestearn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\cus_minus.resx">
      <DependentUpon>cus_minus.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\cus_Balance.resx">
      <DependentUpon>cus_Balance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\cus_balace.resx">
      <DependentUpon>cus_balace.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\group_cus_earn.resx">
      <DependentUpon>group_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\Statistic_cus_earn.resx">
      <DependentUpon>Statistic_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\cus\city_cus_earn.resx">
      <DependentUpon>city_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\imp_item.resx">
      <DependentUpon>imp_item.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\imp_minus.resx">
      <DependentUpon>imp_minus.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\imp_Balance.resx">
      <DependentUpon>imp_Balance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\imp_balace.resx">
      <DependentUpon>imp_balace.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\شيكات\imp_check_late.resx">
      <DependentUpon>imp_check_late.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\شيكات\imp_check_chashing.resx">
      <DependentUpon>imp_check_chashing.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\imp\شيكات\imp_check_at_let.resx">
      <DependentUpon>imp_check_at_let.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\all_back_inv.resx">
      <DependentUpon>all_back_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\day_back_inv.resx">
      <DependentUpon>day_back_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\dele_best_invoice_back.resx">
      <DependentUpon>dele_best_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\dele_cus_invoice_back.resx">
      <DependentUpon>dele_cus_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\month_back_inv.resx">
      <DependentUpon>month_back_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\Statistic_invoice_back.resx">
      <DependentUpon>Statistic_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\1\year_back_inv.resx">
      <DependentUpon>year_back_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\city_best_invoice_back.resx">
      <DependentUpon>city_best_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\city_cus_invoice_back.resx">
      <DependentUpon>city_cus_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\gov_best_invoice_back.resx">
      <DependentUpon>gov_best_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\gov_cus_invoice_back.resx">
      <DependentUpon>gov_cus_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\group_best_invoice_back.resx">
      <DependentUpon>group_best_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\group_cus_invoice_back.resx">
      <DependentUpon>group_cus_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\stoer_best_invoice_back.resx">
      <DependentUpon>stoer_best_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\bac\2\store_cus_invoice_back.resx">
      <DependentUpon>store_cus_invoice_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\all_inv.resx">
      <DependentUpon>all_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\store_cus_earn.resx">
      <DependentUpon>store_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\Statistic_earn.resx">
      <DependentUpon>Statistic_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\Statistic_invoice.resx">
      <DependentUpon>Statistic_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\year_inv.resx">
      <DependentUpon>year_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\month_inv.resx">
      <DependentUpon>month_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\1\day_inv.resx">
      <DependentUpon>day_inv.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\city_best_invoice.resx">
      <DependentUpon>city_best_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\city_cus_invoice.resx">
      <DependentUpon>city_cus_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\dele_best_invoice.resx">
      <DependentUpon>dele_best_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\dele_cus_invoice.resx">
      <DependentUpon>dele_cus_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\gov_best_invoice.resx">
      <DependentUpon>gov_best_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\gov_cus_invoice.resx">
      <DependentUpon>gov_cus_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\group_best_invoice.resx">
      <DependentUpon>group_best_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\group_cus_invoice.resx">
      <DependentUpon>group_cus_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\stoer_best_invoice.resx">
      <DependentUpon>stoer_best_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\invo\inv\2\store_cus_invoice.resx">
      <DependentUpon>store_cus_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\cat\catoregy_litle_earn.resx">
      <DependentUpon>catoregy_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\cat\catoregy_best_earn.resx">
      <DependentUpon>catoregy_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\cat\catoregy_earn.resx">
      <DependentUpon>catoregy_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\comp\company_litle_earn.resx">
      <DependentUpon>company_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\comp\company_best_earn.resx">
      <DependentUpon>company_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\comp\company_earn.resx">
      <DependentUpon>company_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\group\catoregy_earn.resx">
      <DependentUpon>catoregy_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\group\grou_litle_earn.resx">
      <DependentUpon>grou_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\group\grou_best_earn.resx">
      <DependentUpon>grou_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_stagnet.resx">
      <DependentUpon>item_stagnet.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_earn.resx">
      <DependentUpon>item_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\damage_list.resx">
      <DependentUpon>damage_list.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\Drawings_list.resx">
      <DependentUpon>Drawings_list.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_bad.resx">
      <DependentUpon>item_bad.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\section\sec_litle_earn2.resx">
      <DependentUpon>sec_litle_earn2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\section\sec_best_earn.resx">
      <DependentUpon>sec_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\section\sec_earn.resx">
      <DependentUpon>sec_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\trans_list.resx">
      <DependentUpon>trans_list.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_inventory.resx">
      <DependentUpon>item_inventory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_Purchases.resx">
      <DependentUpon>item_Purchases.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_invoice.resx">
      <DependentUpon>item_invoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_minus.resx">
      <DependentUpon>item_minus.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_zero.resx">
      <DependentUpon>item_zero.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\item\item_Deficiencies.resx">
      <DependentUpon>item_Deficiencies.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\all_pur_back.resx">
      <DependentUpon>all_pur_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\day_pur_back.resx">
      <DependentUpon>day_pur_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\month_pur_back.resx">
      <DependentUpon>month_pur_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\Statistic_Purchases_back.resx">
      <DependentUpon>Statistic_Purchases_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\stoer_best_Purchases_back.resx">
      <DependentUpon>stoer_best_Purchases_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\store_cus_invoice_Purchases_back.resx">
      <DependentUpon>store_cus_invoice_Purchases_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\back\year_pur_back.resx">
      <DependentUpon>year_pur_back.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\all_pur.resx">
      <DependentUpon>all_pur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\Statistic_Purchases.resx">
      <DependentUpon>Statistic_Purchases.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\stoer_best_Purchases.resx">
      <DependentUpon>stoer_best_Purchases.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\store_cus_invoice_Purchases.resx">
      <DependentUpon>store_cus_invoice_Purchases.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\year_pur.resx">
      <DependentUpon>year_pur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\month_pur.resx">
      <DependentUpon>month_pur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\Pur\pur\day_pur.resx">
      <DependentUpon>day_pur.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\report_show.resx">
      <DependentUpon>report_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\balancereview.resx">
      <DependentUpon>balancereview.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\user_cus_earn.resx">
      <DependentUpon>user_cus_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\user_litle_earn.resx">
      <DependentUpon>user_litle_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\user_best_earn.resx">
      <DependentUpon>user_best_earn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="report\الحسابات\express_all.resx">
      <DependentUpon>express_all.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\frmbarcode.resx">
      <DependentUpon>frmbarcode.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\help_system.resx">
      <DependentUpon>help_system.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\start.resx">
      <DependentUpon>start.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\user\active_pro.resx">
      <DependentUpon>active_pro.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\user\user_reg.resx">
      <DependentUpon>user_reg.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\user\Power_user.resx">
      <DependentUpon>Power_user.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\recovey.resx">
      <DependentUpon>recovey.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\login.resx">
      <DependentUpon>login.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="main.resx">
      <DependentUpon>main.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\Destruction_ethol.resx">
      <DependentUpon>Destruction_ethol.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\partners_show.resx">
      <DependentUpon>partners_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\Partners_add.resx">
      <DependentUpon>Partners_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\Fixed_assets.resx">
      <DependentUpon>Fixed_assets.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\dele_money.resx">
      <DependentUpon>dele_money.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\الضربية\detial_tax.resx">
      <DependentUpon>detial_tax.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\الضربية\pay_tax_show.resx">
      <DependentUpon>pay_tax_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\الضربية\pay_tax.resx">
      <DependentUpon>pay_tax.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\حزينة\trans_money.resx">
      <DependentUpon>trans_money.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\حزينة\Withdrawal.resx">
      <DependentUpon>Withdrawal.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\حزينة\Deposit.resx">
      <DependentUpon>Deposit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الحسابات\حزينة\Treasury.resx">
      <DependentUpon>Treasury.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\backup.resx">
      <DependentUpon>backup.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="item\NewFolder1\company.resx">
      <DependentUpon>company.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\NewFolder1\whada.resx">
      <DependentUpon>whada.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\NewFolder1\Category.resx">
      <DependentUpon>Category.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\setting\Governorate.resx">
      <DependentUpon>Governorate.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\setting\city.resx">
      <DependentUpon>city.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="item\NewFolder1\contact.resx">
      <DependentUpon>contact.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\setting\group.resx">
      <DependentUpon>group.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="customer\setting\delegate.resx">
      <DependentUpon>delegate.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\setting_fatora.resx">
      <DependentUpon>setting_fatora.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="setting\user\users.resx">
      <DependentUpon>users.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Purchases\Purchases_show.resx">
      <DependentUpon>Purchases_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\Attending_leaving.resx">
      <DependentUpon>Attending_leaving.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\cashing_emp.resx">
      <DependentUpon>cashing_emp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\Attending_show.resx">
      <DependentUpon>Attending_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\Employee_show.resx">
      <DependentUpon>Employee_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\Employee_add.resx">
      <DependentUpon>Employee_add.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\holiday_show.resx">
      <DependentUpon>holiday_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\holiday.resx">
      <DependentUpon>holiday.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\Discounts_show.resx">
      <DependentUpon>Discounts_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\Discounts.resx">
      <DependentUpon>Discounts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\Rewards_show.resx">
      <DependentUpon>Rewards_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\Rewards.resx">
      <DependentUpon>Rewards.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\ancestor.resx">
      <DependentUpon>ancestor.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\ancestor_show.resx">
      <DependentUpon>ancestor_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\emp_Functioned.resx">
      <DependentUpon>emp_Functioned.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\Rosacea.resx">
      <DependentUpon>Rosacea.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\emp_Section.resx">
      <DependentUpon>emp_Section.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\NewFolder1\emp_manged.resx">
      <DependentUpon>emp_manged.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="الموظفين\cashing_emp_show.resx">
      <DependentUpon>cashing_emp_show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CostCentersManagement.resx">
      <DependentUpon>CostCentersManagement.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="My Project\app.manifest">
      <SubType>Designer</SubType>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="Resources\logo.png" />
    <None Include="Resources\hhhjjjj.png" />
    <Content Include="unicentaopos.ico" />
    <None Include="Resources\hhhjjjj1.png" />
    <None Include="Resources\when.wav" />
    <None Include="Resources\when.mp3" />
    <None Include="Resources\suc.wav" />
    <None Include="Resources\ok-like-hand-sign.png" />
    <None Include="Resources\talibov180200087.jpg" />
    <None Include="Resources\true %282%29.png" />
    <None Include="Resources\true.png" />
    <None Include="Resources\Info.png" />
    <None Include="Resources\error.png" />
    <None Include="Resources\cross.png" />
    <None Include="Resources\croak.wav" />
    <None Include="Resources\close.png" />
    <None Include="Resources\men.JPG" />
    <None Include="Resources\SettingsBlue.png" />
    <None Include="Resources\shopping-bag.png" />
    <None Include="Resources\check.png" />
    <None Include="Resources\2.jpg" />
    <None Include="Resources\1.jpg" />
    <Content Include="60305224_162720084758606_8944647698934398976_o.ico" />
    <None Include="Resources\boy.png" />
    <None Include="Resources\attention.png" />
    <None Include="Resources\bell.png" />
    <Content Include="Resources\clear.png" />
    <None Include="Resources\wrong.png" />
    <None Include="Resources\clerk-with-tie.png" />
    <None Include="Resources\clear1.png" />
    <None Include="Resources\white.PNG" />
    <None Include="Resources\alert.wav" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>