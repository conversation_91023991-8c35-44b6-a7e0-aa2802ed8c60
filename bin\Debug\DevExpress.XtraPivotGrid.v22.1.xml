<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <assembly>
    <name>DevExpress.XtraPivotGrid.v22.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraPivotGrid">
      <summary>
        <para>Contains classes which implement the main functionality of the PivotGridControl.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingEditor"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cellViewInfo">A PivotCellViewInfo object used to initialize the created <see cref="T:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs"/> object.</param>
      <param name="repositoryItem">A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that specifies the in-place editor for the current cell.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether the event must be canceled.</para>
      </summary>
      <value>true if the event must be canceled; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs.RepositoryItem">
      <summary>
        <para>Gets the repository item which identifies the settings of the in-place editor for the cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that represents the in-place editor’s settings.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomCustomizationFormSortEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCustomizationFormSort"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.CustomCustomizationFormSortEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.CustomCustomizationFormSortEventArgs"/> class.</para>
      </summary>
      <param name="data"></param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomEditValueEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomEditValue"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomEditValueEventArgs.Value">
      <summary>
        <para>Gets or sets the edit value of the processed cell.</para>
      </summary>
      <value>The edit value of the processed cell.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomEditValueEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomEditValue"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.CustomEditValueEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomExportCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportCell"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Appearance">
      <summary>
        <para>Gets the appearance settings used to paint the cell currently being exported.</para>
      </summary>
      <value>An ExportAppearanceObject object that contains corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.ColumnField">
      <summary>
        <para>Gets the column field which corresponds to the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the column field. null (Nothing in Visual Basic) if a column grand total cell is being processed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.DataField">
      <summary>
        <para>Gets the data field which identifies the column where the processed cell resides.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the data field which identifies the column where the processed cell resides.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Focused">
      <summary>
        <para>Gets whether the processed cell is focused.</para>
      </summary>
      <value>true if the processed cell is focused; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.RowField">
      <summary>
        <para>Gets the row field which corresponds to the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the row field. null (Nothing in Visual Basic) if a row grand total cell is being processed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Selected">
      <summary>
        <para>Gets whether the processed cell is selected.</para>
      </summary>
      <value>true if the processed cell is selected; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportFieldValue"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.Appearance">
      <summary>
        <para>Gets or sets the appearance object used to paint the current field value.</para>
      </summary>
      <value>An ExportAppearanceObject object that contains the corresponding settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.CustomTotal">
      <summary>
        <para>Gets a custom total to which the processed field value corresponds.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object that represents the custom total to which the processed value corresponds. null (Nothing in Visual Basic) if the field value doesn’t correspond to a custom total.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportHeader"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs.#ctor(DevExpress.XtraPrinting.IVisualBrick,DevExpress.XtraPivotGrid.Data.PivotFieldItemBase,DevExpress.XtraPivotGrid.Printing.ExportAppearanceObject,DevExpress.XtraPivotGrid.PivotGridFieldBase,System.Drawing.Rectangle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs"/> class.</para>
      </summary>
      <param name="brick"></param>
      <param name="fieldItem"></param>
      <param name="appearance"></param>
      <param name="field"></param>
      <param name="rect">A System.Drawing.Rectangle object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs.Appearance">
      <summary>
        <para>Gets or sets the appearance object used to paint the current header.</para>
      </summary>
      <value>An ExportAppearanceObject object that contains the corresponding settings.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomFieldDataEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.#ctor(System.Windows.Forms.Form,System.Windows.Forms.Control)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs"/> class.</para>
      </summary>
      <param name="customizationForm">A <see cref="T:System.Windows.Forms.Form"/> descendant which represents the Customization Form. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.CustomizationForm"/> property.</param>
      <param name="parentControl">A <see cref="T:System.Windows.Forms.Control"/> descendant (pivot grid) which owns the Customization Form. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.ParentControl"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether the standard Customization Form should be canceled.</para>
      </summary>
      <value>true if the standard Customization Form should be canceled; false if the form should be displayed after the current event handler completes.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.CustomizationForm">
      <summary>
        <para>Provides access to the Customization Form.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Form"/> descendant which represents the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.Handled">
      <summary>
        <para>Gets or sets whether the standard Customization Form should be canceled.</para>
      </summary>
      <value>true if the standard Customization Form should be canceled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.ParentControl">
      <summary>
        <para>Gets or sets the control to which the Customization Form belongs.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Control"/> descendant (pivot grid) which owns the Customization Form.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised an event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotXlsExportOptions.CustomizeCell"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.CustomizeCell"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.CellItemInfo">
      <summary>
        <para>Gets additional information about a PivotGrid control’s cell whose value should be customized in the exported document.</para>
      </summary>
      <value>A DevExpress.XtraPivotGrid.PivotCellValueExportEventArgs object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.ColumnType">
      <summary>
        <para>Gets a value that identifies the column containing the cell in the exported document.</para>
      </summary>
      <value>The type of the column containing the cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.ExportArea">
      <summary>
        <para>Gets the PivotGrid field area to which the current cell corresponds.</para>
      </summary>
      <value>The PivotGrid field area of the current cell in the export document.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.Formatting">
      <summary>
        <para>Gets or sets the object that defines the cell’s formatting settings (font, alignment, background color, format string, etc.)</para>
      </summary>
      <value>The object that defines the cell’s formatting settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.Handled">
      <summary>
        <para>Gets or sets a value indicating whether the <see cref="E:DevExpress.XtraPivotGrid.PivotXlsExportOptions.CustomizeCell"/> or <see cref="E:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.CustomizeCell"/> events were handled.</para>
      </summary>
      <value>true if the event is handled; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.Hyperlink">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.RowType">
      <summary>
        <para>Gets a value that identifies the row containing the cell in the exported document.</para>
      </summary>
      <value>The type of the row containing the cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.Value">
      <summary>
        <para>Specifies the value of the cell currently being processed.</para>
      </summary>
      <value>An object which represents the processed cell’s value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs.ValueItemInfo">
      <summary>
        <para>Gets the information on the field value to which the current cell corresponds.</para>
      </summary>
      <value>A PivotFieldValueExportEventArgs object which contains information on the field value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomizePivotCellEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.CustomizeCell"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.CustomizeCell"/> events.</para>
      </summary>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.CustomizePivotCellEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.CustomServerModeSortEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomServerModeSort"/> event</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraPivotGrid.Data">
      <summary>
        <para>Contains classes that encapsulate data processing operations.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow">
      <summary>
        <para>A row in the datasource passed to the chart control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow.CellInfo">
      <summary>
        <para>Gets information about a pivot grid cell that corresponds to the current datasource row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs"/> object that contains information about a pivot grid cell that corresponds to the current datasource row.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow.ColumnValueInfo">
      <summary>
        <para>Gets information about the column field value that corresponds to the current datasource row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs"/> object that contains information about the column field value that corresponds to the current datasource row.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow.RowValueInfo">
      <summary>
        <para>Gets information about the row field value that corresponds to the current datasource row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs"/> object that contains information about the row field value that corresponds to the current datasource row.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.DataSourceColumnBinding">
      <summary>
        <para>Defines a data binding to a source data column.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DataSourceColumnBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DataSourceColumnBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DataSourceColumnBinding.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DataSourceColumnBinding"/> class with specified settings.</para>
      </summary>
      <param name="columnName">The data source column name.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DataSourceColumnBinding.#ctor(System.String,DevExpress.XtraPivotGrid.PivotGroupInterval)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DataSourceColumnBinding"/> class with specified settings.</para>
      </summary>
      <param name="columnName">The data source column name.</param>
      <param name="groupInterval">A <see cref="T:DevExpress.XtraPivotGrid.PivotGroupInterval"/> enumeration that specifies how the values are combined into groups.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DataSourceColumnBinding.#ctor(System.String,DevExpress.XtraPivotGrid.PivotGroupInterval,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DataSourceColumnBinding"/> class with specified settings.</para>
      </summary>
      <param name="columnName">The data source column name.</param>
      <param name="groupInterval">A <see cref="T:DevExpress.XtraPivotGrid.PivotGroupInterval"/> enumeration that specifies how the values are combined into groups.’</param>
      <param name="groupIntervalNumericRange">The length of group intervals.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.DateTimeGroupIntervals">
      <summary>
        <para>Lists possible DateTime group interval combinations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.DateTimeGroupIntervals.DefaultIntervals">
      <summary>
        <para>Basic DateTime group intervals:</para>
        <para></para>
        <para></para>
        <para></para>
        <para></para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.DateTimeGroupIntervals.ExtendedIntervals">
      <summary>
        <para>Displays basic and additional DateTime group intervals. Additional group intervals are:</para>
        <para></para>
        <para></para>
        <para></para>
        <para></para>
        <para></para>
        <para></para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.DateTimeGroupIntervals.None">
      <summary>
        <para>Hides the DateTime group intervals.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.DifferenceBinding">
      <summary>
        <para>Defines the difference calculation between values across a window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DifferenceBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DifferenceBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DifferenceBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.CalculationDirection,DevExpress.XtraPivotGrid.DifferenceTarget,DevExpress.XtraPivotGrid.DifferenceType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DifferenceBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="direction">A <see cref="T:DevExpress.XtraPivotGrid.CalculationDirection"/> enumeration member that specifies the direction in which the values are retrieved.</param>
      <param name="target">A <see cref="T:DevExpress.XtraPivotGrid.DifferenceTarget"/> enumeration member that specifies the location of a value in a window.</param>
      <param name="type">A <see cref="T:DevExpress.XtraPivotGrid.DifferenceType"/> enumeration member that specifies the type of difference to calculate.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.DifferenceBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.DifferenceTarget,DevExpress.XtraPivotGrid.DifferenceType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.DifferenceBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="target">A <see cref="T:DevExpress.XtraPivotGrid.DifferenceTarget"/> enumeration member that specifies the location of a value in a window.</param>
      <param name="type">A <see cref="T:DevExpress.XtraPivotGrid.DifferenceType"/> enumeration member that specifies the type of difference to calculate.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.EditValueChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.EditValueChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.EditValueChangedEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraEditors.BaseEdit)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.EditValueChangedEventArgs"/> class.</para>
      </summary>
      <param name="cellInfo">A DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo object.</param>
      <param name="editor">A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> object representing an editor that was used to change the processed cell value. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.EditValueChangedEventArgs.Editor"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.EditValueChangedEventArgs.Editor">
      <summary>
        <para>Gets the editor that was used to change the processed cell value.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> object, which is the editor that was used to change the processed cell value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.EditValueChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.EditValueChanged"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">An <see cref="T:DevExpress.XtraPivotGrid.EditValueChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.ExcelFilterDateTimeValuesTabFilterType">
      <summary>
        <para>Lists values that specify the type of filtering UI used to create filters in the Values tab in the Excel-style Filter popup (if the current field contains date-time values).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterDateTimeValuesTabFilterType.Default">
      <summary>
        <para>The ‘Values’ tab displays a default filtering UI (Tree).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterDateTimeValuesTabFilterType.List">
      <summary>
        <para>The ‘Values’ tab displays date-time values as a list.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterDateTimeValuesTabFilterType.Tree">
      <summary>
        <para>The ‘Values’ tab displays date-time values as a tree.</para>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.ExcelFilterDefaultTab">
      <summary>
        <para>Lists values that specify the default tab in the Excel-style filter popup.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterDefaultTab.Default">
      <summary>
        <para>A default tab is opened by default.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterDefaultTab.Filters">
      <summary>
        <para>The ‘Filters’ tab is opened by default.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterDefaultTab.Values">
      <summary>
        <para>The ‘Values’ tab is opened by default.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.ExcelFilterEnumFilters">
      <summary>
        <para>Lists values that specify a set of relational operators (equality and inequalities) used to create a filter condition in the Filters tab of the Excel-style filter popup (if the current field contains enumeration data).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterEnumFilters.AllFilters">
      <summary>
        <para>The ‘Filters’ tab includes all available operators, including the inequality operators (GreaterThan, GreaterThanOrEqualTo, LessThan, LessThanOrEqualTo, Between).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterEnumFilters.Default">
      <summary>
        <para>The ‘Filters’ tab contains a default set of operators.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterEnumFilters.EqualityFilters">
      <summary>
        <para>The ‘Filters’ tab includes only the equality relational operators (Equals, DoesNotEqual).</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.ExcelFilterNumericValuesTabFilterType">
      <summary>
        <para>Lists values that specify the type of filtering UI used to create filters in the Values tab in the Excel-style filter popup (if the current field contains numeric data).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterNumericValuesTabFilterType.Default">
      <summary>
        <para>The ‘Values’ tab displays a default filtering UI (Range).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterNumericValuesTabFilterType.List">
      <summary>
        <para>The ‘Values’ tab displays numeric values as a list.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterNumericValuesTabFilterType.Range">
      <summary>
        <para>The ‘Values’ tab displays a range track bar.</para>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.ExcelFilterTextFilters">
      <summary>
        <para>Lists values that specify a set of relational operators (equality and inequalities) used to create a filter condition in the Filters tab of the Excel-style filter popup (if the current field contains text data).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterTextFilters.AllFilters">
      <summary>
        <para>The ‘Filters’ tab includes all available operators, including the inequality operators (GreaterThan, GreaterThanOrEqualTo, LessThan, LessThanOrEqualTo, Between).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterTextFilters.Default">
      <summary>
        <para>The ‘Filters’ tab contains a default set of operators.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.ExcelFilterTextFilters.TextFilters">
      <summary>
        <para>The ‘Filters’ tab includes only the equality relational operators (Equals, DoesNotEqual).</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.ExpressionDataBinding">
      <summary>
        <para>Defines a calculation based on a string expression.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.ExpressionDataBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.ExpressionDataBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.ExpressionDataBinding.#ctor(DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.ExpressionDataBinding"/> class with specified settings.</para>
      </summary>
      <param name="criteria"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.ExpressionDataBinding.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.ExpressionDataBinding"/> class with specified settings.</para>
      </summary>
      <param name="expression">A string expression.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FieldFilterPopupMode">
      <summary>
        <para>Lists values that specify the field’s filter popup mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.FieldFilterPopupMode.Classic">
      <summary>
        <para>A Classic filter popup.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.FieldFilterPopupMode.Default">
      <summary>
        <para>A default filter popup type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.FieldFilterPopupMode.Excel">
      <summary>
        <para>An Excel-style filter popup.</para>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FieldValueCell">
      <summary>
        <para>Represents a field value cell.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FieldValueCell.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.FieldValueCell"/> class.</para>
      </summary>
      <param name="item">A DevExpress.XtraPivotGrid.Data.PivotFieldValueItem object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FieldValueCell.Parent">
      <summary>
        <para>Gets the parent of the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.FieldValueCell"/> object that specifies the parent cell.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelCustomizeTemplateEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelCustomizeTemplate"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FilterPopupExcelCustomizeTemplateEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,System.Windows.Forms.Control)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelCustomizeTemplateEventArgs"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is a Pivot Grid field.</param>
      <param name="template">A <see cref="T:System.Windows.Forms.Control"/> object that is a filter popup’s template.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelCustomizeTemplateEventArgs.Field">
      <summary>
        <para>Gets the Pivot Grid field for which the event is raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the Pivot Grid field.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelCustomizeTemplateEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Base.ColumnView.FilterPopupExcelCustomizeTemplate"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelCustomizeTemplateEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelDataEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelData"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FilterPopupExcelDataEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,System.Object[],System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelDataEventArgs"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is a Pivot Grid field.</param>
      <param name="values">An array of objects that are filter values.</param>
      <param name="displayTexts">An array of <see cref="T:System.String"/> values that are display texts.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelDataEventArgs.Field">
      <summary>
        <para>Gets a Pivot Grid field for which the event is raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is a Pivot Grid field.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelDataEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelData"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraGrid.Views.Grid.FilterPopupExcelDataEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowFilterPopupExcel"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.DefaultFilterType">
      <summary>
        <para>Gets or sets the filter operator selected by default in the Filters tab.</para>
      </summary>
      <value>A CustomUIFilterType enumeration value that specifies the filter operator used by default.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.Field">
      <summary>
        <para>Get the Pivot Grid field being processed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the Pivot Grid field being processed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.IsRadioMode">
      <summary>
        <para>Gets or sets whether single (the radio mode) or multiple filter values can be selected simultaneously.</para>
      </summary>
      <value>true, to enable the radio mode; otherwise, false. Default is false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowAdvancedDatePeriods">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowAggregates">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowBlanks">
      <summary>
        <para>Gets or sets whether to show the ‘Is Blank’ and ‘Is Not Blank’ filter conditions.</para>
      </summary>
      <value>true, to show the ‘Is Blank’ and ‘Is Not Blank’ filter conditions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowComparisons">
      <summary>
        <para>Gets or sets whether to show comparison filter conditions (Greater Than, Less Than, etc.).</para>
      </summary>
      <value>true, to show comparison filter conditions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowCustomFilters">
      <summary>
        <para>Gets or sets whether to show the ‘Custom Filter’.</para>
      </summary>
      <value>true, to show the ‘Custom Filter’; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowDatePeriods">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowFiltersTab">
      <summary>
        <para>Gets or sets whether to show the Filters tab.</para>
      </summary>
      <value>true, to show the Filters tab; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowLikeFilters">
      <summary>
        <para>Gets or sets whether to show the pattern-matching (Is Like, Is Not Like) operators for string values.</para>
      </summary>
      <value>true to show the pattern-matching operators; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowNulls">
      <summary>
        <para>Gets or sets whether to show the ‘Is Null’ and ‘Is Not Null’ filter conditions.</para>
      </summary>
      <value>true, to show the ‘Is Null’ and ‘Is Not Null’ filter conditions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowPredefinedFilters">
      <summary>
        <para>Gets or sets whether to show the ‘Predefined Filters’.</para>
      </summary>
      <value>true, to show the ‘Predefined Filters’; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs.ShowSequences">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowFilterPopupExcel"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelParseFilterCriteriaEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelParseFilterCriteria"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelParseFilterCriteriaEventArgs.Field">
      <summary>
        <para>Gets the processed field.</para>
      </summary>
      <value>An object that specifies the processed field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelParseFilterCriteriaEventArgs.PivotGrid">
      <summary>
        <para>Gets the pivot grid that raised the event.</para>
      </summary>
      <value>An object that specifies the pivot grid that raised the event.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelPrepareTemplateEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelPrepareTemplate"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FilterPopupExcelPrepareTemplateEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,System.Windows.Forms.Control)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelPrepareTemplateEventArgs"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is a Pivot Grid field.</param>
      <param name="template">A <see cref="T:System.Windows.Forms.Control"/> object that is a filter popup’s template.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelPrepareTemplateEventArgs.Field">
      <summary>
        <para>Gets the Pivot Grid field for which the event is raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the Pivot Grid field.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelPrepareTemplateEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelPrepareTemplate"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelPrepareTemplateEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelQueryFilterCriteriaEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelQueryFilterCriteria"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelQueryFilterCriteriaEventArgs.Field">
      <summary>
        <para>Gets the Pivot Grid field for which the event is raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the Pivot Grid field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FilterPopupExcelQueryFilterCriteriaEventArgs.PivotGrid">
      <summary>
        <para>Get the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> that raised the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FilterPopupExcelQueryFilterCriteriaEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelCustomizeTemplate"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.FilterPopupExcelQueryFilterCriteriaEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings">
      <summary>
        <para>Contains format rule settings used to apply conditional formatting to data cells placed at the intersection of specified column and row fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings.Column">
      <summary>
        <para>Gets or sets a column field to which values the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatRules"/> property is applied.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object from column header area to which the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatRules"/> property is applied.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings.ColumnName">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings.Row">
      <summary>
        <para>Gets or sets a row field to which values the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatRules"/> property is applied.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object from row header area to which the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatRules"/> property is applied.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings.RowName">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FormatRuleFieldIntersectionSettings.ToString">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FormatRuleSettings">
      <summary>
        <para>Serves as the base class for classes that provide format rule settings used to apply conditional formatting to data cells.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings">
      <summary>
        <para>Contains format rule settings used to apply conditional formatting to various types of data cells (data cells, custom total cells, grand total cells, and total cells).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings.ApplyToCell">
      <summary>
        <para>Specifies if data cells are involved in conditional formatting.</para>
      </summary>
      <value>true if data cells are involved in conditional formatting; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings.ApplyToCustomTotalCell">
      <summary>
        <para>Specifies if custom total cells are involved in conditional formatting.</para>
      </summary>
      <value>true if custom total cells are involved in conditional formatting; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings.ApplyToGrandTotalCell">
      <summary>
        <para>Specifies if grand total cells are involved in conditional formatting.</para>
      </summary>
      <value>true if grand total cells are involved in conditional formatting; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings.ApplyToTotalCell">
      <summary>
        <para>Specifies if total cells are involved in conditional formatting.</para>
      </summary>
      <value>true if total cells are involved in conditional formatting; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.FormatRuleTotalTypeSettings.ToString">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.MovingCalculationBinding">
      <summary>
        <para>Defines aggregations across a specified number of values before and/or after the current value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.MovingCalculationBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.MovingCalculationBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.MovingCalculationBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.Data.PivotGrid.PivotSummaryType,System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.MovingCalculationBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="summaryType">A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration member that specifies the summary function type.</param>
      <param name="prevCount">The number of values before the current value.</param>
      <param name="nextCount">The number of values after the current value.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.MovingCalculationBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.CalculationDirection,DevExpress.Data.PivotGrid.PivotSummaryType,System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.MovingCalculationBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="direction">A <see cref="T:DevExpress.XtraPivotGrid.CalculationDirection"/> enumeration member that specifies the direction in which the values are retrieved.</param>
      <param name="summaryType">A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration member that specifies the summary function type.</param>
      <param name="prevCount">The number of values before the current value.</param>
      <param name="nextCount">The number of values after the current value.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.OLAPExpressionBinding">
      <summary>
        <para>Gets or sets an expression that is used to evaluate values for a Pivot Grid’s field in OLAP mode.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.OLAPExpressionBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.OLAPExpressionBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.OLAPExpressionBinding.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.OLAPExpressionBinding"/> class with specified settings.</para>
      </summary>
      <param name="expression"></param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PercentOfTotalBinding">
      <summary>
        <para>Defines the calculation of a percentage of all values in the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PercentOfTotalBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PercentOfTotalBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PercentOfTotalBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PercentOfTotalBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose location is being changed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldEventArgsBase`1.Field"/> property.</param>
      <param name="newArea">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which indicates the current position of the field being dragged. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewArea"/> property.</param>
      <param name="newAreaIndex">A zero-based integer which specifies the field’s index amongst the other fields displayed within the same area. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewAreaIndex"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.Allow">
      <summary>
        <para>Gets or sets whether the dragged field header can be dropped on the area it’s currently located over.</para>
      </summary>
      <value>true to allow the operation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewArea">
      <summary>
        <para>Gets the current position of the field being dragged.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the current position of the field being dragged.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewAreaIndex">
      <summary>
        <para>Gets the index of the field which is being dragged for the area it’s currently located over among the other fields displayed within the area.</para>
      </summary>
      <value>A zero-based integer which specifies the index of the dragged field among the other fields displayed within the area over which it’s currently located.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs">
      <summary>
        <para>Provides data for the events which are invoked for particular data cells.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs.DisplayText">
      <summary>
        <para>Gets or sets the display text for the cell currently being processed.</para>
      </summary>
      <value>A string that represents the cell’s display text.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellEditEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.HiddenEditor"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShownEditor"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCellEditEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraEditors.BaseEdit)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCellEditEventArgs"/> class.</para>
      </summary>
      <param name="cellViewInfo">A DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo object.</param>
      <param name="edit">A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> object, which is the editor that has been invoked or closed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCellEditEventArgs.Edit"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellEditEventArgs.Edit">
      <summary>
        <para>Gets the editor that has been invoked or closed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> object that is  the editor that has been invoked or closed.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellClick"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.Bounds">
      <summary>
        <para>Gets the cell’s bounds.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> value that specifies the cell’s bounds.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCellEventArgs.CreateDrillDownDataSourceAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCellEventArgs.CreateDrillDownDataSourceAsync(System.Collections.Generic.List{System.String})">
      <summary />
      <param name="customColumns"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCellEventArgs.CreateDrillDownDataSourceAsync(System.Int32)">
      <summary />
      <param name="maxRowCount"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCellEventArgs.CreateDrillDownDataSourceAsync(System.Int32,System.Collections.Generic.List{System.String})">
      <summary />
      <param name="maxRowCount"></param>
      <param name="customColumns"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.DisplayText">
      <summary>
        <para>Gets the display text of the cell currently being processed.</para>
      </summary>
      <value>A string representing the cell’s display text.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.Focused">
      <summary>
        <para>Gets whether the processed cell is the focused cell.</para>
      </summary>
      <value>true if the processed cell is the focused cell; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.Selected">
      <summary>
        <para>Gets whether the processed cell is selected.</para>
      </summary>
      <value>true if the processed cell is selected; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCellValueEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellValue"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCellValueEventArgs.Value">
      <summary>
        <para>Gets or sets the processed cell’s value.</para>
      </summary>
      <value>An object which represents the processed cell’s value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotContextMenuIds">
      <summary>
        <para>Lists IDs that identify context menu items.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.BestFitFieldMenuID">
      <summary>
        <para>The Best Fit context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ChangeExpandedMenuID">
      <summary>
        <para>The Expand/Collapse context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ClearSortingID">
      <summary>
        <para>The Clear Sorting context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.CollapseAllMenuID">
      <summary>
        <para>The Collapse All context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ExpandAllMenuID">
      <summary>
        <para>The Fix Column context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.FixColumnMenuID">
      <summary>
        <para>The Fix Column context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.HideFieldMenuID">
      <summary>
        <para>The Hide Field Menu context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.HideFieldOnCustomizationFormMenuID">
      <summary>
        <para>The Hide Field on Customization Form context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.MoveLeftMenuID">
      <summary>
        <para>The Move Left context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.MoveRightMenuID">
      <summary>
        <para>The Move Right context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.MoveToBeginningMenuID">
      <summary>
        <para>The Move to End context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.MoveToEndMenuID">
      <summary>
        <para>The Move to End context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.OrderMenuID">
      <summary>
        <para>The Order Menu context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ReloadDataMenuID">
      <summary>
        <para>The Reload Data context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.RemoveSortBySummaryMenuID">
      <summary>
        <para>The Remove Sort by Summary context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ShowHeaderExpressionMenuID">
      <summary>
        <para>The Show Header Expression context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ShowHideFieldListMenuID">
      <summary>
        <para>The Show/Hide Field List context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ShowPrefilterMenuID">
      <summary>
        <para>The Show Prefilter context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.ShowValueExpressionMenuID">
      <summary>
        <para>The Show Value Expression context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.SortAscendingID">
      <summary>
        <para>The Sort Ascending context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.SortBySummaryMenuID">
      <summary>
        <para>The Sort by Summary context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotContextMenuIds.SortDescendingID">
      <summary>
        <para>The Sort Descending context menu item.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs.Appearance">
      <summary>
        <para>Gets or sets the appearance settings of the currently processed cell.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object containing appearance settings for the processed cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomAppearanceThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomAppearanceThreadSafeEventArgs.Appearance">
      <summary>
        <para>Gets or sets the appearance settings of the currently processed cell.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object containing appearance settings for the processed cell.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomCellEditEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellEdit"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomCellEditEventArgs.RepositoryItem">
      <summary>
        <para>Gets or sets the in-place editor for the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that specifies the in-place editor for the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomCellEditEventArgs.Value">
      <summary>
        <para>Gets the processed cell’s value.</para>
      </summary>
      <value>An object that is the processed cell’s value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.CellInfo">
      <summary>
        <para>Gets an object which contains information about a PivotGrid control’s cell, whose value should be displayed in a ChartControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellValueEventArgs"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.FieldValueInfo">
      <summary>
        <para>Gets an object which contains information about a field value to be displayed in a ChartControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemDataMember">
      <summary>
        <para>Gets the type of a chart data member that will represent the current Pivot Grid Control item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemDataMember"/> enumeration member that specifies the type of a chart data member that will represent the current Pivot Grid Control item.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemType">
      <summary>
        <para>Gets a value representing the type of a PivotGrid control’s item to be represented in a ChartControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.Value">
      <summary>
        <para>Gets or sets a value to be displayed in a ChartControl.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> class descendant representing the value to be displayed.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceRowsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceRows"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceRowsEventArgs.CreateRow(System.Object,System.Object,System.Object)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow"/> instance that specifies a chart’s series point with the specified argument, series and value.</para>
      </summary>
      <param name="series">The point’s series. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotChartDataSourceRowBase.Series"/> property of the created <see cref="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow"/> object.</param>
      <param name="argument">The point’s argument. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotChartDataSourceRowBase.Argument"/> property of the created <see cref="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow"/> object.</param>
      <param name="value">The point’s value. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotChartDataSourceRowBase.Value"/> property of the created <see cref="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow"/> object.</param>
      <returns>The created <see cref="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow"/> instance that specifies a chart’s series point.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceRowsEventArgs.Rows">
      <summary>
        <para>Gets the collection of chart datasource rows.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.XtraPivotGrid.Data.PivotChartDataSourceRow"/> objects that specify chart datasource rows.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceRowsEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceRows"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceRowsEventArgs"/> event that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomColumnWidth"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs.ColumnCount">
      <summary>
        <para>Gets the number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> columns in the data area.</para>
      </summary>
      <value>An integer value that specifies the number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> columns in the data area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs.ColumnIndex">
      <summary>
        <para>Gets the column zero-based index for fields located in the data area.</para>
      </summary>
      <value>An integer value that specifies the column index.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs.ColumnLineCount">
      <summary>
        <para>Gets the height of the column field values located in the column header area, in text lines.</para>
      </summary>
      <value>An integer value that specifies the height of the current column field values, in text lines.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs.ColumnWidth">
      <summary>
        <para>Gets or sets a width for the data area columns.</para>
      </summary>
      <value>An integer value that specifies the width of the data area columns.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs.GetColumnCellValue(System.Int32)">
      <summary>
        <para>Returns the specified column field value for the cell addressed by its zero-based index in the data area.</para>
      </summary>
      <param name="rowIndex">An integer value that specifies a zero-based index of a column field’s cell in the data area.</param>
      <returns>The value of the specified cell.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomColumnWidthEventArgs.RowCount">
      <summary>
        <para>Gets a number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> rows in the data area.</para>
      </summary>
      <value>An integer value that specifies the number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> rows in the data area.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to custom painting events data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.ColumnCount">
      <summary>
        <para>Gets the number of columns displayed in the pivot grid.</para>
      </summary>
      <value>An integer value that specifies the number of columns displayed in the pivot grid.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.Fields">
      <summary>
        <para>Provides thread safe access to a pivot grid field collection.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeFieldCollection"/> interface. Provides thread safe read-only access to the collection of fields.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.GetCellDisplayText(System.Int32,System.Int32)">
      <summary>
        <para>Returns the text displayed in the specified cell.</para>
      </summary>
      <param name="columnIndex">A zero-based integer value that specifies the index of the column where the cell resides.</param>
      <param name="rowIndex">A zero-based integer value that specifies the index of the row where the cell resides.</param>
      <returns>A <see cref="T:System.String"/> that specifies the text displayed in the specified cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.GetFieldByArea(DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
      <summary>
        <para>Returns a field located at the specified visual position in the specified area.</para>
      </summary>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area containing the required field.</param>
      <param name="index">An integer value that specifies the visible index of the field within the specified area.</param>
      <returns>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Provides thread safe read-only access to basic field settings.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.GetFieldsByArea(DevExpress.XtraPivotGrid.PivotArea)">
      <summary>
        <para>Returns a list of fields displayed in the specified area.</para>
      </summary>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration member that identifies a pivot grid area.</param>
      <returns>A list of visible fields displayed in the specified area.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.GetFieldValueDisplayText(DevExpress.XtraPivotGrid.IThreadSafeField,System.Int32)">
      <summary>
        <para>Returns the display representation of the specified field value.</para>
      </summary>
      <param name="field">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Identifies a field that owns the value whose display representation should be returned.</param>
      <param name="index">A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.</param>
      <returns>A <see cref="T:System.String"/> that specifies the display representation of the specified field value.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.Groups">
      <summary>
        <para>Provides thread safe access to a pivot grid group collection.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeGroupCollection"/> interface. Provides thread safe read-only access to the collection of field groups.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawBaseThreadSafeEventArgs.RowCount">
      <summary>
        <para>Gets the number of rows displayed in the pivot grid.</para>
      </summary>
      <value>An integer value that specifies the number of rows displayed in the pivot grid.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs">
      <summary>
        <para>Provides data for custom painting events invoked for particular data cells.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.Bounds">
      <summary>
        <para>Gets the cell bounds.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> value that specifies the cell bounds.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.ColumnCustomTotal">
      <summary>
        <para>Gets the column custom total that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object, which specifies the column custom total that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.ColumnField">
      <summary>
        <para>Gets the innermost column field that corresponds to the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the column field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.ColumnFieldIndex">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.ColumnIndex">
      <summary>
        <para>Gets the visual index of the column that contains the current cell.</para>
      </summary>
      <value>A zero-based integer value that specifies the visual index of the column that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.ColumnValueType">
      <summary>
        <para>Gets the type of column that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value that specifies the type of column in which the current cell resides.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateDrillDownDataSource">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records used to calculate a summary value for the current cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateDrillDownDataSource(System.Collections.Generic.List{System.String})">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell. Allows you to specify the columns to return.</para>
      </summary>
      <param name="customColumns">A list of columns to return.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateDrillDownDataSource(System.Int32)">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell. Allows you to limit the number of records to return.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to return. Set it to -1 to retrieve all rows.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateDrillDownDataSource(System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to return.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to return. Set it to -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to return.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateOLAPDrillDownDataSource(System.Collections.Generic.List{System.String})">
      <summary>
        <para>In OLAP mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns to be returned.</para>
      </summary>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateOLAPDrillDownDataSource(System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In OLAP mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to be returned.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateServerModeDrillDownDataSource(System.Collections.Generic.List{System.String})">
      <summary>
        <para>In server mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns to be returned.</para>
      </summary>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateServerModeDrillDownDataSource(System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In server mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to be returned.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.CreateSummaryDataSource">
      <summary>
        <para>Returns a summary data source.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that is the summary data source.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.DataField">
      <summary>
        <para>Gets the data field that identifies the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object, which specifies the data field that identifies the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.DisplayText">
      <summary>
        <para>Gets the display text of the current cell.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the cell’s display text.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.Focused">
      <summary>
        <para>Gets whether the current cell is focused.</para>
      </summary>
      <value>true if the processed cell is the focused cell; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a cell value calculated for the current column and row field values, against the specified data field.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required cell value is calculated.</param>
      <returns>Value displayed in the specified cell; null (Nothing in Visual Basic) if the cell has not been found.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetCellValue(System.Int32,System.Int32)">
      <summary>
        <para>Returns a cell value by the column and row indexes.</para>
      </summary>
      <param name="columnIndex">A zero-based integer value that specifies the index of a column where the cell resides.</param>
      <param name="rowIndex">A zero-based integer value that specifies the index of a row where the cell resides.</param>
      <returns>A value displayed in the specified cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetCellValue(System.Object[],System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a cell value calculated for the specified column and row field values, against the specified data field.</para>
      </summary>
      <param name="columnValues">An array of column values.</param>
      <param name="rowValues">An array of row values.</param>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required cell value is calculated.</param>
      <returns>A summary value calculated for the specified column and row field values, against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetColumnFields">
      <summary>
        <para>Returns an array of column fields that correspond to the current cell.</para>
      </summary>
      <returns>An array of column fields.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetColumnGrandTotal(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a Column Grand Total value calculated for the current row field values, against the specified data field.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Column Grand Total is calculated.</param>
      <returns>The Column Grand Total value calculated for the current row field values, against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetColumnGrandTotal(System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a Column Grand Total value calculated for the specified row field values, against the specified data field.</para>
      </summary>
      <param name="rowValues">An array of row field values for which the required Column Grand Total is calculated.</param>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Column Grand Total is calculated.</param>
      <returns>The Column Grand Total value calculated for the specified row field values, against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a field value that belongs to the specified field and corresponds to the current data cell.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies a column or row field, whose value should be obtained.</param>
      <returns>The field value that belongs to the specified field and corresponds to the current data cell; null (Nothing in Visual Basic) if the specified field is not a column or row field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Returns a field value that belongs to the specified field and corresponds to a data cell with the specified column or row index.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies a column or row field, whose value should be obtained.</param>
      <param name="cellIndex">A zero-based index of a cell that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.</param>
      <returns>The field value that belongs to the specified field and corresponds to a data cell with the specified column or row index.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetGrandTotal(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the Grand Total value calculated against the specified data field.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Grand Total value is calculated.</param>
      <returns>The Grand Total value calculated against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetNextColumnCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the value of the cell located in the next column of the current row.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field against which the required cell value is calculated.</param>
      <returns>The value of the cell located in the next column of the current row.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetNextRowCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the value of the cell located in the next row of the current column.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field against which the required cell value is calculated.</param>
      <returns>An object that is the value of the cell located in the next row of the current column.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetPrevColumnCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the value of the cell located in the previous column of the current row.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field against which the required cell value is calculated.</param>
      <returns>The value of the cell located in the previous column of the current row.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetPrevRowCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the value of the cell located in the previous row of the current column.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field against which the required cell value is calculated.</param>
      <returns>The value of the cell located in the previous row of the current column.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetRowFields">
      <summary>
        <para>Returns an array of row fields that correspond to the current cell.</para>
      </summary>
      <returns>An array of row fields.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetRowGrandTotal(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a Row Grand Total value calculated for the current column field values, against the specified data field.</para>
      </summary>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Row Grand Total is calculated.</param>
      <returns>The Row Grand Total value calculated for the current column field values, against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.GetRowGrandTotal(System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a Row Grand Total value calculated for the specified column field values, against the specified data field.</para>
      </summary>
      <param name="columnValues">An array of column field values for which the required Row Grand Total is calculated.</param>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Row Grand Total is calculated.</param>
      <returns>The Row Grand Total value calculated for the specified column field values, against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.IsFieldValueExpanded(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Indicates whether a field value that belongs to the specified field and corresponds to the current cell is expanded.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the field in a pivot grid.</param>
      <returns>true if the field value is expanded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.IsFieldValueRetrievable(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Gets whether the value of the specified column or row field can be retrieved for the current cell by the PivotCustomDrawCellBaseEventArgs.GetFieldValue method.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the column or row field.</param>
      <returns>true if the specified field’s value can be retrieved via the PivotCustomDrawCellBaseEventArgs.GetFieldValue method; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.IsOthersFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Indicates whether the current data cell resides within the “Others” row/column when the Top X Value feature is enabled.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the field.</param>
      <returns>true if the data cell resides within the “Others” row/column; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.RowCustomTotal">
      <summary>
        <para>Gets the row custom total that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object, which specifies the row custom total that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.RowField">
      <summary>
        <para>Gets the innermost row field that corresponds to the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the row field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.RowFieldIndex">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.RowIndex">
      <summary>
        <para>Gets the visual index of the row that contains the current cell.</para>
      </summary>
      <value>An integer that specifies the visual index of the row that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.RowValueType">
      <summary>
        <para>Gets the type of row that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value that specifies the type of row in which the current cell resides.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.Selected">
      <summary>
        <para>Gets whether the current cell is selected.</para>
      </summary>
      <value>true if the processed cell is selected; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.SummaryType">
      <summary>
        <para>Gets the type of summary calculated in the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration value that specifies the summary type for the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.SummaryValue">
      <summary>
        <para>Gets values of the predefined summaries calculated for the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryValue"/> object that contains values of the predefined summaries calculated for the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs.Value">
      <summary>
        <para>Gets the current cell’s value.</para>
      </summary>
      <value>An object which is the current cell’s value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to data for custom painting events invoked for particular data cells.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.Bounds">
      <summary>
        <para>Gets the cell bounds.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> value that specifies the cell bounds.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.ColumnCustomTotal">
      <summary>
        <para>Gets the column custom total that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object, which specifies the column custom total that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.ColumnField">
      <summary>
        <para>Gets the innermost column field that corresponds to the current cell.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Specifies the innermost column field that corresponds to the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.ColumnFieldIndex">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.ColumnIndex">
      <summary>
        <para>Gets the visual index of the column that contains the current cell.</para>
      </summary>
      <value>A zero-based integer value that specifies the visual index of the column that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.ColumnValueType">
      <summary>
        <para>Gets the type of column that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value that specifies the type of column in which the current cell resides.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.DataField">
      <summary>
        <para>Gets the data field that identifies the current cell.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Specifies the data field that identifies the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.DisplayText">
      <summary>
        <para>Gets the display text of the current cell.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the cell’s display text.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.Focused">
      <summary>
        <para>Gets whether the current cell is focused.</para>
      </summary>
      <value>true if the processed cell is the focused cell; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.RowCustomTotal">
      <summary>
        <para>Gets the row custom total that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object, which specifies the row custom total that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.RowField">
      <summary>
        <para>Gets the innermost row field that corresponds to the current cell.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Specifies the innermost row field that corresponds to the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.RowFieldIndex">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.RowIndex">
      <summary>
        <para>Gets the visual index of the row that contains the current cell.</para>
      </summary>
      <value>An integer that specifies the visual index of the row that contains the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.RowValueType">
      <summary>
        <para>Gets the type of row that contains the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value that specifies the type of row in which the current cell resides.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.Selected">
      <summary>
        <para>Gets whether the current cell is selected.</para>
      </summary>
      <value>true if the processed cell is selected; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.SummaryType">
      <summary>
        <para>Gets the type of summary calculated in the current cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration value that specifies the summary type for the current cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellBaseThreadSafeEventArgs.Value">
      <summary>
        <para>Gets the current cell’s value.</para>
      </summary>
      <value>An object that is the current cell’s value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.Appearance">
      <summary>
        <para>Gets the painted cell’s appearance settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the painted cell’s appearance settings.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.DefaultDraw">
      <summary>
        <para>Performs default painting of an element.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.Graphics">
      <summary>
        <para>Gets the object used to paint a cell.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.GraphicsCache">
      <summary>
        <para>Gets an object which specifies the storage for the most  used pens, fonts and brushes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.Handled">
      <summary>
        <para>Gets or sets whether an event was handled, if it was handled the default actions are not required.</para>
      </summary>
      <value>true if default painting isn’t required; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs.Appearance">
      <summary>
        <para>Gets or sets the painted element’s appearance settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the painted element’s appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs.Graphics">
      <summary>
        <para>Gets an object used to paint an element.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object used to paint an element.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs.GraphicsCache">
      <summary>
        <para>Gets an object that specifies the storage for the most frequently used pens, fonts and brushes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that stores the most frequently used pens, fonts and brushes.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellThreadSafeEventArgs.Handled">
      <summary>
        <para>Gets or sets whether the event has been handled.</para>
      </summary>
      <value>true if the event has been handled and default painting is not required; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.IPivotCustomDrawAppearanceOwner,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,System.Drawing.Rectangle,System.Windows.Forms.MethodInvoker)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface.</param>
      <param name="appearanceOwner">An IPivotCustomDrawAppearanceOwner object.</param>
      <param name="paintArgs">A ViewInfoPaintArgs object.</param>
      <param name="bounds">A System.Drawing.Rectangle structure that specifies the drawing area.</param>
      <param name="defaultDraw">A MethodInvoker object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Appearance">
      <summary>
        <para>Gets the painted element’s appearance settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the painted element’s appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Bounds">
      <summary>
        <para>Gets the bounding rectangle of the painted element.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the element’s boundaries.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.DefaultDraw">
      <summary>
        <para>Performs default painting of an element.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Graphics">
      <summary>
        <para>Gets an object used to paint an element.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.GraphicsCache">
      <summary>
        <para>Gets an object which specifies the storage for the most  used pens, fonts and brushes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Handled">
      <summary>
        <para>Gets or sets whether an event was handled, if it was handled the default actions are not required.</para>
      </summary>
      <value>true if default painting isn’t required; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderViewInfoBase,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,DevExpress.Utils.Drawing.HeaderObjectPainter,System.Windows.Forms.MethodInvoker)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface.</param>
      <param name="headerViewInfo">A PivotHeaderViewInfoBase object.</param>
      <param name="paintArgs">A ViewInfoPaintArgs object.</param>
      <param name="painter">A DevExpress.Utils.Drawing.ObjectPainter object that provides default painting facilities.</param>
      <param name="defaultDraw">A MethodInvoker object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.Field">
      <summary>
        <para>Gets the field whose header is to be painted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose header is to be painted.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.Info">
      <summary>
        <para>Gets an object which provides the information required to paint a field header.</para>
      </summary>
      <value>A DevExpress.Utils.Drawing.HeaderObjectInfoArgs object which provides information about the painted field header.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.Painter">
      <summary>
        <para>Gets the painter object that provides the default element painting mechanism.</para>
      </summary>
      <value>A HeaderObjectPainter object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs.Field">
      <summary>
        <para>Gets the field whose header is to be painted.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Specifies the field whose header is to be painted.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs.Info">
      <summary>
        <para>Provides information required to paint a field header.</para>
      </summary>
      <value>A DevExpress.Utils.Drawing.HeaderObjectInfoArgs object that provides information about the painted field header.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderThreadSafeEventArgs.Painter">
      <summary>
        <para>Gets the painter object that provides the default element painting mechanism.</para>
      </summary>
      <value>A HeaderObjectPainter object.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfo,DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectInfoArgs,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectPainter,System.Windows.Forms.MethodInvoker)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface.</param>
      <param name="fieldCellViewInfo">An object that contains information on the processed cell.</param>
      <param name="info">A DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectInfoArgs object which provides information about the painted field value.</param>
      <param name="paintArgs">A ViewInfoPaintArgs object.</param>
      <param name="painter">A DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectPainter object.</param>
      <param name="defaultDraw">A MethodInvoker object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Area">
      <summary>
        <para>Gets the header area where the field is displayed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the header area in which the field is displayed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.CustomTotal">
      <summary>
        <para>Gets the custom total which the currently processed column/row header corresponds to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the custom total which the processed header corresponds to. null (Nothing in Visual Basic) if the processed header doesn’t correspond to a custom total.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.DisplayText">
      <summary>
        <para>Gets the display text of the header currently being painted.</para>
      </summary>
      <value>A string value representing the header’s display text.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Field">
      <summary>
        <para>Gets the field whose value is to be painted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose value is to be painted.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.FieldIndex">
      <summary>
        <para>Gets the field’s position among the visible fields within the header area.</para>
      </summary>
      <value>An integer value that specifies the field’s position among the visible fields.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetCellValue(System.Int32,System.Int32)">
      <summary>
        <para>Returns a value displayed in the specified cell.</para>
      </summary>
      <param name="columnIndex">A zero-based integer which identifies the visible index of the column.</param>
      <param name="rowIndex">A zero-based integer which identifies the visible index of the row.</param>
      <returns>A value displayed in the specified cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Returns the specified column or row field’s value for the cell addressed by its zero-based index in the Data Area.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing a column or row field, whose value is to be obtained.</param>
      <param name="cellIndex">A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.</param>
      <returns>An object representing the field’s value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetHigherLevelFields">
      <summary>
        <para>Returns the parent field(s) for the field value being currently processed.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects that represent parent fields for the field value currently being processed.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetHigherLevelFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the value of a specific parent field corresponding to the field value currently being processed.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the parent field whose value is returned.</param>
      <returns>An object that represents the value of the specified parent (higher level) field.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Info">
      <summary>
        <para>Gets an object which provides the information required to paint a field value.</para>
      </summary>
      <value>A DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectInfoArgs object which provides information about the painted field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.IsOthersValue">
      <summary>
        <para>Gets whether the current header corresponds to the “Others” row/column.</para>
      </summary>
      <value>true if the current header corresponds to the “Others” row/column.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.MaxIndex">
      <summary>
        <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.MinIndex">
      <summary>
        <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Painter">
      <summary>
        <para>Gets the painter object that provides the default element painting mechanism.</para>
      </summary>
      <value>A DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectPainter object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Value">
      <summary>
        <para>Gets the value of the column field or row field which the currently processed column/row header corresponds to.</para>
      </summary>
      <value>An object which represents the value of the corresponding column field or row field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.ValueType">
      <summary>
        <para>Gets the type of the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.Area">
      <summary>
        <para>Gets the header area where the field is displayed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the header area in which the field is displayed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.CustomTotal">
      <summary>
        <para>Gets the custom total that corresponds to the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object that specifies the custom total that corresponds to the currently processed column/row header; null (Nothing in Visual Basic) if the processed header does not correspond to a custom total.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.DisplayText">
      <summary>
        <para>Gets the display text of the header currently being painted.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the header’s display text.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.Field">
      <summary>
        <para>Gets the field whose value is to be painted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object, which is the field whose value is to be painted.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.FieldIndex">
      <summary>
        <para>Gets the field’s position among the visible fields within the header area.</para>
      </summary>
      <value>An integer value that specifies the field’s position among the visible fields.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.GetHigherLevelFields">
      <summary>
        <para>Returns the parent field(s) for the field value being currently processed.</para>
      </summary>
      <returns>An array of objects that implement the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. These objects specify parent fields for the field value currently being processed.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.Info">
      <summary>
        <para>Gets an object which provides the information required to paint a field value.</para>
      </summary>
      <value>A DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectInfoArgs object which provides information about the painted field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.IsOthersValue">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.MaxIndex">
      <summary>
        <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.MinIndex">
      <summary>
        <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.Painter">
      <summary>
        <para>Gets the painter object that provides the default element painting mechanism.</para>
      </summary>
      <value>A DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderObjectPainter object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.Value">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueThreadSafeEventArgs.ValueType">
      <summary>
        <para>Gets the type of the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value that identifies the type of the currently processed column or row header.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,System.Drawing.Rectangle,System.Windows.Forms.MethodInvoker)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface.</param>
      <param name="headersViewInfo">A PivotHeaderViewInfoBase object.</param>
      <param name="paintArgs">A ViewInfoPaintArgs object.</param>
      <param name="bounds">A System.Drawing.Rectangle structure that specifies the drawing area.</param>
      <param name="defaultDraw">A MethodInvoker object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs.Area">
      <summary>
        <para>Gets a value which specifies the header area currently being painted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the header area currently being painted.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaThreadSafeEventArgs.Area">
      <summary>
        <para>Gets a value that specifies the header area currently being painted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the header area currently being painted.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs.Appearance">
      <summary>
        <para>Gets the painted element’s appearance settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the painted element’s appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs.Bounds">
      <summary>
        <para>Gets the bounding rectangle of the painted element.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the element’s boundaries.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs.Graphics">
      <summary>
        <para>Gets an object used to paint an element.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object used to paint an element.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs.GraphicsCache">
      <summary>
        <para>Gets an object that specifies the storage for the most frequently used pens, fonts and brushes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that stores the most frequently used pens, fonts and brushes.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawThreadSafeEventArgs.Handled">
      <summary>
        <para>Gets or sets whether the event has been handled.</para>
      </summary>
      <value>true if the event has been handled and default painting is not required; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomFieldValueCellsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldValueCells"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomFieldValueCellsEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldValueCells"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomFieldValueCellsEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFilterPopupItems"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridFilterItems)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs"/> class.</para>
      </summary>
      <param name="items">A DevExpress.XtraPivotGrid.Data.PivotGridFilterItems object representing a collection of filter drop-down items. This collection is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.Items"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.CheckAllItems(System.Boolean)">
      <summary>
        <para>Checks or unchecks all filter items in the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.Items"/> collection.</para>
      </summary>
      <param name="isChecked">true to check the filter items; false to uncheck them.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.Field">
      <summary>
        <para>Gets the field for which the event has been raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object, representing the field for which the event has been raised.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.Items">
      <summary>
        <para>Gets the collection of filter items.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridFilterItem"/> objects that represent the filter items.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs.ShowBlanksItem">
      <summary>
        <para>Gets the ‘Show Blanks’ filter item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridFilterItem"/> object representing the ‘Show Blanks’ filter item.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFilterPopupItems"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomFilterPopupItemsEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotCustomRowHeightEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomRowHeight"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomRowHeightEventArgs.ColumnCount">
      <summary>
        <para>Gets a number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> columns in the data area.</para>
      </summary>
      <value>An integer value that specifies a number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> columns in the data area.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotCustomRowHeightEventArgs.GetRowCellValue(System.Int32)">
      <summary>
        <para>Returns the specified row field value for the cell addressed by its zero-based index in the data area.</para>
      </summary>
      <param name="columnIndex">An integer value that specifies a zero-based index of a row field cell in the data area.</param>
      <returns>The value of specified cell.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomRowHeightEventArgs.RowCount">
      <summary>
        <para>Gets the number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> rows in the data area.</para>
      </summary>
      <value>An integer value that specifies the number of <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> rows in the data area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomRowHeightEventArgs.RowHeight">
      <summary>
        <para>Gets or sets a height for the data area rows.</para>
      </summary>
      <value>An integer value that specifies the height of the data area rows.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotCustomRowHeightEventArgs.RowIndex">
      <summary>
        <para>Gets the row zero-based index for fields located in the data area.</para>
      </summary>
      <value>An integer value that specifies the row index.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotEndUpdateMode">
      <summary>
        <para>Lists values that specify whether the Pivot Grid Control is immediately redrawn after calling the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate"/> method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotEndUpdateMode.Invalidate">
      <summary>
        <para>The Pivot Grid Control will not be redrawn until the owner window is redrawn.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotEndUpdateMode.Refresh">
      <summary>
        <para>The Pivot Grid Control will be immediately redrawn after calling the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate"/> method.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.#ctor(DevExpress.PivotGrid.Utils.IFieldFormattableValue,System.String)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="formattable"></param>
      <param name="defaultText">A string value that specifies the item’s display text.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.DisplayText">
      <summary>
        <para>Gets or sets the item’s display text.</para>
      </summary>
      <value>A string value that specifies the item’s display text.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.IsPopulatingFilterDropdown">
      <summary>
        <para>Gets whether the current event is called to populate the filter dropdown.</para>
      </summary>
      <value>true if the event is called to populate the filter dropdown; false if the event is called to customize the display text of column and row headers.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.Value">
      <summary>
        <para>Gets the processed item’s value.</para>
      </summary>
      <value>An object which represents the item’s value.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldEventArgs">
      <summary>
        <para>Provides data for all field handling events.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanged"/>, <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanged"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldWidthChanged"/> events.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised an event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotFilterType,System.Boolean,System.Collections.Generic.IList{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the field for which the event has been raised.</param>
      <param name="filterType">A <see cref="T:DevExpress.XtraPivotGrid.PivotFilterType"/> enumeration member that specifies the current filter type.</param>
      <param name="showBlanks">true if records that contain NULL values in the current field are processed by the control; false if these records are ignored.</param>
      <param name="values">A collection of filter values.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether to cancel changing the filter condition.</para>
      </summary>
      <value>true to cancel changing the filter condition; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs.FilterType">
      <summary>
        <para>Gets the current filter type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFilterType"/> enumeration member that specifies the filter type currently set for the field filter.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs.ShowBlanks">
      <summary>
        <para>Gets whether records that contain NULL values in the current field are processed by the control.</para>
      </summary>
      <value>true if records that contain NULL values in the current field are processed by the control; false if these records are ignored.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs.Values">
      <summary>
        <para>Gets the collection of filter values that is about to be assigned to the filter.</para>
      </summary>
      <value>A list of objects representing field filter values.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanging"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldFilterChangingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.ChangeExpandedState">
      <summary>
        <para>Changes the expanded state of the current field value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CreateDrillDownDataSource">
      <summary>
        <para>Returns data records associated with the processed field value.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records associated with the processed field value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CreateDrillDownDataSource(System.Collections.Generic.List{System.String})">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell. Allows you to specify the columns to return.</para>
      </summary>
      <param name="customColumns">A list of columns to return.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CreateDrillDownDataSource(System.Int32)">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell. Allows you to limit the number of records to return.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to return. Set it to -1 to retrieve all rows.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CreateDrillDownDataSource(System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>Returns data records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to return.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to return. Set it -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to return.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CreateOLAPDrillDownDataSource(System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In OLAP mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to be returned.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CreateServerModeDrillDownDataSource(System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In server mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to be returned.</para>
      </summary>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.CustomTotal">
      <summary>
        <para>Gets the custom total that corresponds to the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object that specifies the custom total that corresponds to the currently processed column/row header.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.DataField">
      <summary>
        <para>Gets the data field that identifies the processed value.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object, which is the data field that identifies the processed value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.Field">
      <summary>
        <para>Gets the field being processed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object, which is the field currently being processed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.FieldIndex">
      <summary>
        <para>Gets the field position among the visible fields within the header area.</para>
      </summary>
      <value>An integer value that specifies the field position among the visible fields.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.GetCellValue(System.Int32,System.Int32)">
      <summary>
        <para>Returns a value displayed in a cell with the specified column and row indexes.</para>
      </summary>
      <param name="columnIndex">A zero-based integer value that specifies the index of the column where the cell resides.</param>
      <param name="rowIndex">A zero-based integer value that specifies the index of the row where the cell resides.</param>
      <returns>A value displayed in the specified cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Returns a field value that belongs to the specified field and corresponds to a data cell with the specified column or row index.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies a column or row field, whose value should be obtained.</param>
      <param name="cellIndex">A zero-based index of a cell that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.</param>
      <returns>The field value that belongs to the specified field and corresponds to a data cell with the specified column or row index.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.GetHigherLevelFields">
      <summary>
        <para>Returns the parent field(s) for the field value being currently processed.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects that are parent fields for the field value currently being processed.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.GetHigherLevelFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the value of a specific parent field corresponding to the field value currently being processed.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the parent field whose value is returned.</param>
      <returns>An object that is the value of the specified parent (higher level) field.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.ImageIndex">
      <summary>
        <para>Gets or sets the index of the image to display within the currently processed column/row header.</para>
      </summary>
      <value>An integer value specifying the zero-based index of the image within the source collection.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.IsCollapsed">
      <summary>
        <para>Gets whether the processed field value is collapsed.</para>
      </summary>
      <value>true if the field value is collapsed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.IsColumn">
      <summary>
        <para>Gets whether the field is displayed within the Column Header Area.</para>
      </summary>
      <value>true if the field is displayed within the Column Header Area; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.IsOthersValue">
      <summary>
        <para>Gets whether the current header corresponds to the “Others” row/column.</para>
      </summary>
      <value>true if the current header corresponds to the “Others” row/column.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.MaxIndex">
      <summary>
        <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.MinIndex">
      <summary>
        <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.ThreadSafeArgs">
      <summary>
        <para>Gets an event parameter that provides thread-safe access to event data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs"/> object that provides thread-safe access to event data.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.Value">
      <summary>
        <para>Gets the column field or row field value that corresponds to the currently processed column/row header.</para>
      </summary>
      <value>The field value currently being processed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.ValueType">
      <summary>
        <para>Gets the type of the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the Pivot Grid Control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs">
      <summary>
        <para>Provides thread-safe access to the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex"/> event data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.#ctor(DevExpress.XtraPivotGrid.IThreadSafeAccessible,DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs"/> class.</para>
      </summary>
      <param name="threadSafeAccess">An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeAccessible"/> interface. Provides access to pivot grid data.</param>
      <param name="ownerArgs">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs"/> object that owns the current event parameter.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.CustomTotal">
      <summary>
        <para>Gets the custom total that corresponds to the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object that specifies the custom total that corresponds to the currently processed column/row header.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.DataField">
      <summary>
        <para>Gets the data field that identifies the processed value.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Specifies the data field that identifies the processed value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.DisplayText">
      <summary>
        <para>Gets the display text of the processed field value.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the display text of the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.Field">
      <summary>
        <para>Gets the field being processed.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. Specifies the field currently being processed.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.FieldIndex">
      <summary>
        <para>Gets the field position among the visible fields within the header area.</para>
      </summary>
      <value>An integer value that specifies the field position among the visible fields.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.GetHigherLevelFields">
      <summary>
        <para>Returns the parent field(s) for the field value being currently processed.</para>
      </summary>
      <returns>An array of objects that implement the <see cref="T:DevExpress.XtraPivotGrid.IThreadSafeField"/> interface. These objects specify parent fields for the field value currently being processed.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.ImageIndex">
      <summary>
        <para>Gets or sets the index of the image to display within the currently processed column/row header.</para>
      </summary>
      <value>An integer value specifying the zero-based index of the image within the source collection.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.Index">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.IsColumn">
      <summary>
        <para>Gets whether the field is displayed within the Column Header Area.</para>
      </summary>
      <value>true if the field is displayed within the Column Header Area; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.MaxIndex">
      <summary>
        <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.MinIndex">
      <summary>
        <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.</para>
      </summary>
      <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexThreadSafeEventArgs.ValueType">
      <summary>
        <para>Gets the type of the currently processed column/row header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldPropertyChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotFieldPropertyName)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventArgs"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the field whose property has been changed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldEventArgsBase`1.Field"/> property.</param>
      <param name="propertyName">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldPropertyName"/> enumeration member that specifies which property has been changed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventArgs.PropertyName"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventArgs.PropertyName">
      <summary>
        <para>Gets which property has been changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldPropertyName"/> enumeration member that identifies the property that has been changed.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldPropertyChanged"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldPropertyChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldTooltipShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo,System.Drawing.Point,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs"/> class.</para>
      </summary>
      <param name="viewInfo">A DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo object.</param>
      <param name="point">A <see cref="T:System.Drawing.Point"/> structure that represents the coordinates of the tooltip. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.Point"/> property.</param>
      <param name="text">A <see cref="T:System.String"/> that specifies the tooltip text. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.Text"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.HitInfo">
      <summary>
        <para>Gets information about the point where the tooltip should be invoked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> contains information about the point where the tooltip should be invoked.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.Point">
      <summary>
        <para>Gets the coordinates of the point where the tooltip should be invoked.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure representing the coordinates of the point where the tooltip should be invoked.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.ShowTooltip">
      <summary>
        <para>Gets or sets whether to show the tooltip.</para>
      </summary>
      <value>true to show the tooltip; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs.Text">
      <summary>
        <para>Gets or sets the tooltip text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that represents the tooltip text.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldTooltipShowing"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldTooltipShowingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether the field value can be expanded/collapsed.</para>
      </summary>
      <value>true to cancel the operation; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding"/> events.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs">
      <summary>
        <para>Serves as a base for the classes that provide data for the events which are used to customize column and row headers.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.CustomTotal">
      <summary>
        <para>Gets the custom total which the currently processed column/row header corresponds to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the custom total which the processed header corresponds to.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.Data">
      <summary>
        <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsed"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanded"/> events.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the Pivot Grid Control which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo">
      <summary>
        <para>Contains information on the field value located under the test point.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfo,DevExpress.XtraPivotGrid.PivotGridValueHitTest)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo"/> class.</para>
      </summary>
      <param name="fieldCellViewInfo">A DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfo object.</param>
      <param name="valueHitTest">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueHitTest"/> enumeration member that specifies which field value cell element is located under the test point.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo.CreateDrillDownDataSourceAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo.CreateDrillDownDataSourceAsync(System.Collections.Generic.List{System.String})">
      <summary />
      <param name="customColumns"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo.CreateDrillDownDataSourceAsync(System.Int32)">
      <summary />
      <param name="maxRowCount"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo.CreateDrillDownDataSourceAsync(System.Int32,System.Collections.Generic.List{System.String})">
      <summary />
      <param name="maxRowCount"></param>
      <param name="customColumns"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo.ValueHitTest">
      <summary>
        <para>Gets which field value cell element is located under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueHitTest"/> enumeration member that specifies which field value cell element is located under the test point.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotFilter">
      <summary>
        <para>A filter applied to data displayed in the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFilter.ChangeFilterEditorVisible">
      <summary>
        <para>Toggles the Filter Editor visibility.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFilter.Criteria">
      <summary>
        <para>Gets or sets a filter criteria applied to data displayed in the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant that specifies a filter criteria applied to data displayed in the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFilter.HideForm">
      <summary>
        <para>Closes the Filter Editor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFilter.IsFilterEditorShown">
      <summary>
        <para>Gets whether or not the Filter Editor is currently displayed.</para>
      </summary>
      <value>true if the Filter Editor is currently displayed; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFilter.Reset">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFilter.ShouldSerialize">
      <summary>
        <para>For internal use.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFilter.ShowForm">
      <summary>
        <para>Opens the Filter Editor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotFilter.ShowOperandTypeIcon">
      <summary>
        <para>Gets or sets whether the operand’s value can be swapped.</para>
      </summary>
      <value>true, to allow end-users to swap the operand’s value; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotFilter.ToString">
      <summary>
        <para>Returns the string that represents the current object.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> that represents the current <see cref="T:DevExpress.XtraPivotGrid.PivotFilter"/> object.</returns>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearances">
      <summary>
        <para>Provides the appearance settings used to paint a PivotGridControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearances"/> class. For internal use.</para>
      </summary>
      <param name="data"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.ColumnHeaderArea">
      <summary>
        <para>Gets the appearance settings used to paint the column header area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the column header area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.CustomizationFormHint">
      <summary>
        <para>Gets or sets the appearance of the hint text displayed in an empty Customization Form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains corresponding style settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.DataHeaderArea">
      <summary>
        <para>Gets the appearance settings used to paint the data header area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the data header area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.Empty">
      <summary>
        <para>Gets the appearance settings used to paint the Pivot Grid Control’s empty area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the Pivot Grid Control’s empty space.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.ExpandButton">
      <summary>
        <para>Gets the appearance settings used to paint expand buttons.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint expand buttons.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.FilterHeaderArea">
      <summary>
        <para>Gets the appearance settings used to paint the filter header area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the filter header area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.FilterPanel">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.FixedLine">
      <summary>
        <para>Gets the appearance settings used to paint fixed lines.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the appearance settings used to paint fixed lines.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.FocusedCell">
      <summary>
        <para>Gets the appearance settings used to paint the currently focused cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the currently focused cell.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetAppearanceDefaultInfo(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Gets the array of default appearances used to paint the Pivot Grid Control’s elements.</para>
      </summary>
      <param name="lf">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which provides the look and feel settings for a PivotGridControl.</param>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetAppearanceDefaultInfoPrint(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Gets the array of default appearances used to paint the elements of a Pivot Grid Control when it’s printed.</para>
      </summary>
      <param name="lf">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which provides the look and feel settings for a PivotGridControl.</param>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetEmptyAppearanceDefaultInfo">
      <summary>
        <para>Gets the empty appearance settings used to paint the elements of a PivotGridControl.</para>
      </summary>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetFlatAppearanceDefaultInfo">
      <summary>
        <para>Gets the default appearance settings used to paint the elements of a Pivot Grid Control when it’s painted in the Flat style.</para>
      </summary>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetOffice2003AppearanceDefaultInfo">
      <summary>
        <para>Gets the default appearance settings used to paint the elements of a Pivot Grid Control when it’s painted in the Office2003 style.</para>
      </summary>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetSkinAppearanceDefaultInfo(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Gets the default appearance settings used to paint the elements of a Pivot Grid Control when it’s painted in the Skin style.</para>
      </summary>
      <param name="lf">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which provides the look and feel settings for a PivotGridControl.</param>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetSkinAppearanceDefaultInfo(DevExpress.LookAndFeel.UserLookAndFeel,System.Boolean)">
      <summary>
        <para>Gets the default appearance settings used to paint the elements of a Pivot Grid Control when it’s painted in the Skin style.</para>
      </summary>
      <param name="lf">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which provides the look and feel settings for a PivotGridControl.</param>
      <param name="assignForeIfNoBack"></param>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetWindowsXPAppearanceDefaultInfo">
      <summary>
        <para>Gets the default appearance settings used to paint the elements of a Pivot Grid Control when it’s painted in the Windows XP style.</para>
      </summary>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.HeaderArea">
      <summary>
        <para>Gets the appearance settings used to paint the header area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the header area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.HeaderFilterButton">
      <summary>
        <para>Gets the appearance settings used to paint filter buttons.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint filter buttons.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.HeaderFilterButtonActive">
      <summary>
        <para>Gets the appearance settings used to paint the filter buttons displayed within the field headers that are involved in filtering.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the filter buttons that are displayed within the field headers involved in filtering.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.RowHeaderArea">
      <summary>
        <para>Gets the appearance settings used to paint the row header area.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the row header area.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.SelectedCell">
      <summary>
        <para>Gets the appearance settings used to paint the selected cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the selected cells.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.SortByColumnIndicatorImage">
      <summary>
        <para>Gets or sets a glyph that is used to indicate that values of column/row fields are sorted by a specific row/column.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Image"/> object that specifies a corresponding glyph.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearancesBase">
      <summary>
        <para>Represents the base class for classes that provide appearance settings for a PivotGridControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesBase"/> class. For internal use.</para>
      </summary>
      <param name="data"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.Cell">
      <summary>
        <para>Gets the appearance settings used to paint data cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint data cells.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.CustomTotalCell">
      <summary>
        <para>Gets the appearance settings used to paint custom total cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint custom total cells.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldHeader">
      <summary>
        <para>Gets the default appearance settings used to paint field headers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint field headers.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldValue">
      <summary>
        <para>Gets the appearance settings used to paint the values of fields and the default appearance settings used to paint grand totals and totals.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint field values.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldValueGrandTotal">
      <summary>
        <para>Gets the appearance settings used to paint grand total headers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the grand total headers.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldValueTotal">
      <summary>
        <para>Gets the appearance settings used to paint the headers of Totals.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the headers of Totals.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FilterSeparator">
      <summary>
        <para>Gets the appearance settings used to paint the filter header area separator.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the filter header area separator.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.GrandTotalCell">
      <summary>
        <para>Gets the appearance settings used to paint Grand Total cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint Grand Total cells.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.HeaderGroupLine">
      <summary>
        <para>Gets the appearance settings used to paint connector lines between field headers combined in a field group.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.IsLoading">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.Lines">
      <summary>
        <para>Gets the appearance settings used to paint the horizontal and vertical lines.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint grid lines.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.PivotCellItemFlags">
      <summary />
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.PivotCellItemFlags.CustomTotalAppearance">
      <summary />
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.PivotCellItemFlags.GrandTotalAppearance">
      <summary />
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.PivotCellItemFlags.TotalAppearance">
      <summary />
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.TotalCell">
      <summary>
        <para>Gets the appearance settings used to paint automatic total cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint automatic total cells.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint">
      <summary>
        <para>Provides the appearance settings used to paint the elements in a Pivot Grid Control when it’s printed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint"/> class.</para>
      </summary>
      <param name="data"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint.FilterSeparator">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint.GetAppearanceDefaultInfo">
      <summary>
        <para>Gets an array of the default appearances used to paint the elements of a Pivot Grid Control when it’s printed.</para>
      </summary>
      <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint.HeaderGroupLine">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCells">
      <summary>
        <para>Stores information on the cells displayed within the PivotGrid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridCells"/> class with default settings.</para>
      </summary>
      <param name="data">A PivotGridViewInfoData object which contains the information required to initialize the created <see cref="T:DevExpress.XtraPivotGrid.PivotGridCells"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.ColumnCount">
      <summary>
        <para>Gets the number of columns in the PivotGrid control.</para>
      </summary>
      <value>An integer which specifies the number of columns displayed within the PivotGrid control.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.CopySelectionToClipboard">
      <summary>
        <para>Copies the selected cells to the Clipboard.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.FocusedCell">
      <summary>
        <para>Gets or sets the coordinates of the focused cell.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the coordinates of the focused cell.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.GetCellInfo(System.Int32,System.Int32)">
      <summary>
        <para>Returns an object that contains information on the specified cell.</para>
      </summary>
      <param name="columnIndex">A zero-based integer that identifies the visible index of the column.</param>
      <param name="rowIndex">A zero-based integer that identifies the visible index of the row.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object that contains information on the specified cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.GetFocusedCellInfo">
      <summary>
        <para>Returns an object that contains information on the currently focused cell.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object that contains information on the currently focused cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.InvalidateCell(DevExpress.XtraPivotGrid.PivotCellEventArgs)">
      <summary>
        <para>Invalidates the specified cell.</para>
      </summary>
      <param name="cellInfo">A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object that identifies the cell.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.InvalidateCell(System.Int32,System.Int32)">
      <summary>
        <para>Invalidates the cell which is located at the specified position.</para>
      </summary>
      <param name="x">A zero-based integer which identifies the visible index of the row that contains the cell.</param>
      <param name="y">A zero-based integer which identifies the visible index of the column that contains the cell.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.LeftTopCell">
      <summary>
        <para>Gets or sets the coordinates of the left top visible cell.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the coordinates of the left top visible cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.MultiSelection">
      <summary>
        <para>Gets the selected cells.</para>
      </summary>
      <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.Selection.IMultipleSelection"/> interface and represents the Pivot Grid Control’s selection.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.RowCount">
      <summary>
        <para>Gets the number of rows in the PivotGrid control.</para>
      </summary>
      <value>An integer which is the number of rows displayed within the PivotGrid control.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.Selection">
      <summary>
        <para>Gets or sets the coordinates of the selected cells.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> object which contains the coordinates of the selected cells.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.SetSelectionByFieldValues(System.Boolean,System.Object[])">
      <summary>
        <para>Selects cells that reside within the specified column or row.</para>
      </summary>
      <param name="isColumn">true to select cells within a column; false to select cells within a row.</param>
      <param name="values">An array of field values that identifies the column or row that should be selected.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.SetSelectionByFieldValues(System.Boolean,System.Object[],DevExpress.XtraPivotGrid.PivotGridFieldBase)">
      <summary>
        <para>Selects cells that reside within the specified column or row, identified by the specified data field.</para>
      </summary>
      <param name="isColumn">true to select cells within a column; false to select cells within a row.</param>
      <param name="values">An array of field values that identifies the column or row that should be selected.</param>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> descendant. Specifies a data field that identifies the column or row that should be selected.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridControl">
      <summary>
        <para>Allows you to create a pivot table (cross-tabular format) for multi-dimensional analysis of large amounts of data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.About">
      <summary>
        <para>Activates the Pivot Grid Control’s About dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveEditor">
      <summary>
        <para>Gets the PivotGrid’s active editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> descendant that represents the currently active editor. null (Nothing in Visual Basic) if no cell is currently being edited.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilter">
      <summary>
        <para>Provides access to settings of the filter applied to the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>‘s data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFilter"/> object that is a filter applied to data displayed in the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilterCriteria">
      <summary>
        <para>Gets or sets the filter criteria applied to the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>‘s data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant that specifies a filter criteria applied to data displayed in the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilterCriteriaChanged">
      <summary>
        <para>Occurs when the filter criteria, currently applied to the PivotGridControl, is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilterEnabled">
      <summary>
        <para>Gets or sets whether to apply the filter criteria specified using the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilterCriteria"/> or <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilterString"/>.</para>
      </summary>
      <value>true, to apply a filter criteria; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveFilterString">
      <summary>
        <para>Gets or sets a string that specifies the filter criteria applied to the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>‘s data.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies a string that specifies a filter criteria applied to data displayed in the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Appearance">
      <summary>
        <para>Provides access to the properties that control the appearance of the PivotGrid control’s elements.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearances"/> object which provides the appearance settings for the PivotGrid control’s elements.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.AppearancePrint">
      <summary>
        <para>Provides access to the properties that specify the appearances of the PivotGrid control’s elements when it’s printed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint"/> object which provides the appearance settings used to paint the Pivot Grid Control’s elements when it’s printed.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.AsyncOperationCompleted">
      <summary>
        <para>Occurs after an asynchronous operation has been completed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.AsyncOperationStarting">
      <summary>
        <para>Occurs before an asynchronous operation is started.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.BackgroundImage">
      <summary>
        <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.BackgroundImageLayout">
      <summary>
        <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.BeforeLoadLayout">
      <summary>
        <para>Occurs before a layout is restored from storage (a stream, xml file or the system registry).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.BeginRefresh">
      <summary>
        <para>Occurs before the control’s data recalculation starts.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"></see> object by preventing visual updates of the object and its elements until the EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFit">
      <summary>
        <para>Resizes all columns to fit their contents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFit(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Resizes the column(s) related to the specified field to fit the longest text currently displayed in the column.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the field whose columns are resized.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFitColumnArea">
      <summary>
        <para>Resizes columns in the Data Area to the minimum width required to display their contents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFitDataHeaders(System.Boolean)">
      <summary>
        <para>Resizes the Data Header Area to the minimum width required to display data field headers.</para>
      </summary>
      <param name="considerRowArea">true, to prevent the Row Header Area width from being decreased; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFitRowArea">
      <summary>
        <para>Resizes columns that display row field values to the minimum width required to display their contents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.BorderStyle">
      <summary>
        <para>Gets or sets the border style for the PivotGridControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value which specifies the border style of a PivotGridControl.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CalcHitInfo(System.Drawing.Point)">
      <summary>
        <para>Returns information on the grid elements located at the specified point.</para>
      </summary>
      <param name="hitPoint">A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the Pivot Grid Control’s top-left corner.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> object which contains information about the grid elements located at the test point.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CanResizeField(System.Drawing.Point)">
      <summary>
        <para>Indicates whether the field can be resized.</para>
      </summary>
      <param name="pt">A <see cref="T:System.Drawing.Point"/> structure which represents the test point.</param>
      <returns>true if the field can be resized; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CellClick">
      <summary>
        <para>Occurs when a cell is clicked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick">
      <summary>
        <para>Occurs when a cell is double-clicked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Cells">
      <summary>
        <para>Gets the object which contains information on the cells that are displayed by the control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCells"/> object which contains information on the data cells.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CellSelectionChanged">
      <summary>
        <para>Occurs when the cell selection is modified.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ChangeFieldExpandedAsync(DevExpress.XtraPivotGrid.PivotGridFieldBase,System.Boolean)">
      <summary>
        <para>Expands or collapses all values of the specified field asynchronously.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> descendant that specifies the field whose values should be expanded/collapsed.</param>
      <param name="expand">true to expand field values; false to collapse them.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ChangeFieldSortOrderAsync(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Toggles the sort order of the specified field asynchronously.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies a field whose sort order should be toggled.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ClearFieldSortingAsync(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Clears field sorting asynchronously in OLAP mode.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the field whose sort order should be changed.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CloseEditor">
      <summary>
        <para>Hides the active editor, saving changes made.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CloseEditorAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAll">
      <summary>
        <para>Collapses all the columns and rows in the PivotGridControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllAsync">
      <summary>
        <para>Collapses all columns and rows in a Pivot Grid Control asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllColumns">
      <summary>
        <para>Collapses all columns.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllColumnsAsync">
      <summary>
        <para>Collapses all columns asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllRows">
      <summary>
        <para>Collapses all rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllRowsAsync">
      <summary>
        <para>Collapses all rows asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseValue(System.Boolean,System.Object[])">
      <summary>
        <para>Collapses a specific column or row that is identified by the specified values.</para>
      </summary>
      <param name="isColumn">true to collapse a column; false to collapse a row.</param>
      <param name="values">An array of values that identify the column/row to be collapsed.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseValueAsync(System.Boolean,System.Object[])">
      <summary>
        <para>Collapses the specified column or row asynchronously.</para>
      </summary>
      <param name="isColumn">true to collapse a column; false to collapse a row.</param>
      <param name="values">An array of field values that identify the column/row to be collapsed.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ConvertFormatConditionToFormatRules">
      <summary>
        <para>Converts the <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object to the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatRule"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource">
      <summary>
        <para>Returns data records used to calculate summary values for all cells.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object containing the underlying data records.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource(System.Int32,System.Int32)">
      <summary>
        <para>Returns a list of records used to calculate a summary value for the specified cell.</para>
      </summary>
      <param name="columnIndex">A zero-based integer which is the visible column index. Set it to -1 to obtain the column’s Grand Total.</param>
      <param name="rowIndex">A zero-based integer which identifies the visible index of the row. Pass -1 as a row index to obtain the row’s Grand Total.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object containing the underlying data records.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource(System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>Returns data records used to calculate a summary value for the specified cell. Allows you to specify the columns to return.</para>
      </summary>
      <param name="columnIndex">A zero-based visible column’s index. Set it to -1 to obtain the column’s Grand Total.</param>
      <param name="rowIndex">A zero-based visible row’s index. Set it to -1 to obtain the row’s Grand Total.</param>
      <param name="customColumns">A list of columns to return.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource(System.Int32,System.Int32,System.Int32)">
      <summary>
        <para>Returns data records used to calculate a summary value for the specified cell. Allows you to limit the number of returned records.</para>
      </summary>
      <param name="columnIndex">A zero-based integer that is the column’s visible index. Set it to -1 to obtain the column’s Grand Total.</param>
      <param name="rowIndex">A zero-based integer that is the row’s visible index. Set it to -1 to obtain the row’s Grand Total.</param>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to return. -1 to retrieve all rows.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object containing the underlying data records.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>Returns data records used to calculate a summary value for the specified cell. Allows you to specify the columns and limit the number of records to return.</para>
      </summary>
      <param name="columnIndex">A zero-based visible column’s index of the column. Set it to -1 to obtain the column’s Grand Total.</param>
      <param name="rowIndex">A zero-based visible row’s index. Set it to -1 to obtain the row’s Grand Total.</param>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSourceAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSourceAsync(System.Int32,System.Int32)">
      <summary />
      <param name="columnIndex"></param>
      <param name="rowIndex"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSourceAsync(System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary />
      <param name="columnIndex"></param>
      <param name="rowIndex"></param>
      <param name="customColumns"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSourceAsync(System.Int32,System.Int32,System.Int32)">
      <summary />
      <param name="columnIndex"></param>
      <param name="rowIndex"></param>
      <param name="maxRowCount"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSourceAsync(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary />
      <param name="columnIndex"></param>
      <param name="rowIndex"></param>
      <param name="maxRowCount"></param>
      <param name="customColumns"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateOLAPDataSourceClone">
      <summary>
        <para>Creates a copy of the current pivot grid’s OLAP data source instance.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.XtraPivotGrid.Data.OLAPDataSourceBase"/> descendant that represents an OLAP data source.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateOLAPDrillDownDataSource(System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In OLAP mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns to be returned.</para>
      </summary>
      <param name="columnIndex">A zero-based integer which identifies the visible index of the column.</param>
      <param name="rowIndex">A zero-based integer which identifies the visible index of the row.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateOLAPDrillDownDataSource(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In OLAP mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns and limit the number of records to be returned.</para>
      </summary>
      <param name="columnIndex">A zero-based integer which identifies the visible index of the column.</param>
      <param name="rowIndex">A zero-based integer which identifies the visible index of the row.</param>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateServerModeDrillDownDataSource(System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In server mode, returns a list of records used to calculate a summary value for the specified cell.</para>
      </summary>
      <param name="columnIndex">A zero-based integer which identifies the visible index of the column.</param>
      <param name="rowIndex">A zero-based integer which identifies the visible index of the row.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data for the processed cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateServerModeDrillDownDataSource(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
      <summary>
        <para>In server mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns and limit the number of records to be returned.</para>
      </summary>
      <param name="columnIndex">A zero-based integer which identifies the visible index of the column.</param>
      <param name="rowIndex">A zero-based integer which identifies the visible index of the row.</param>
      <param name="maxRowCount">An integer value that specifies the maximum number of data rows to be returned. -1 to retrieve all rows.</param>
      <param name="customColumns">A list of columns to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateSummaryDataSource">
      <summary>
        <para>Returns a summary data source.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that represents the summary data source.</returns>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance">
      <summary>
        <para>Allows the appearances of cells to be dynamically customized when the control is displayed on screen and in the print output.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText">
      <summary>
        <para>Enables custom display text to be provided for the cells displayed within the Data Area.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellEdit">
      <summary>
        <para>Allows you to assign editors for particular cells. To avoid performance issues and increased memory consumption, assign repository items that already exist in the PivotGridControl.RepositoryItems collection. Do not create new repository items in this handler.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellEditForEditing">
      <summary>
        <para>Allows you to assign a custom editor to a cell for in-place editing, and override the default cell editor, which is used in both display and edit modes, by default. To avoid performance issues and increased memory consumption, assign repository items that already exist in the PivotGridControl.RepositoryItems collection. Do not create new repository items in this handler.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellValue">
      <summary>
        <para>Allows you to replace cell values with values calculated in the event handler.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData">
      <summary>
        <para>Occurs when a <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> prepares data to be displayed in a <see cref="T:DevExpress.XtraCharts.ChartControl"/>.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceRows">
      <summary>
        <para>Allows you to customize pivot grid data before passing it to a chart control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomColumnWidth">
      <summary>
        <para>Enables the column width to be customized.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCustomizationFormSort">
      <summary>
        <para>Allows you to change fields and folders order in the Customization Form.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell">
      <summary>
        <para>Enables data cells to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea">
      <summary>
        <para>Enables a Pivot Grid Control’s background to be custom painted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader">
      <summary>
        <para>Enables field headers to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea">
      <summary>
        <para>Enables header areas to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue">
      <summary>
        <para>Enables column and row headers to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomEditValue">
      <summary>
        <para>Enables you to change cell values.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportCell">
      <summary>
        <para>Enables you to render different content for individual cells in a printed or exported document, if the control is exported in the WYSIWYG mode.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportFieldValue">
      <summary>
        <para>Enables you to render different content for individual field values in a printed or exported document, if the PivotGrid is exported in the WYSIWYG mode.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportHeader">
      <summary>
        <para>Enables you to render different content for individual field headers in a printed or exported document, if the control is exported in the WYSIWYG mode.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort">
      <summary>
        <para>Allows you to provide a custom sorting algorithm for the field values.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldValueCells">
      <summary>
        <para>Allows you to customize field value cells.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFilterDisplayText">
      <summary>
        <para>Enables custom text to be displayed in the Filter Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFilterPopupItems">
      <summary>
        <para>Allows you to customize items displayed in the Classic filter popup.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval">
      <summary>
        <para>Allows you to custom group values of column and row fields.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationForm">
      <summary>
        <para>Provides access to the Customization Form.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Form"/> descendant that represents the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationFormBounds">
      <summary>
        <para>Gets or sets the boundaries of the Customization Form.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the Customization Form’s boundaries.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationTreeNodeImages">
      <summary>
        <para>Specifies the collection of images identifying nodes in the Customization Form used when the control is in OLAP mode.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object that contains the corresponding images.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationTreeNodeSvgImages">
      <summary>
        <para>Gets or sets a vector image that identifies nodes in the Customization Form when the control is in OLAP mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.SvgImageCollection"/> object that contains images from a project assembly.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomRowHeight">
      <summary>
        <para>Enables the rows height to be customized.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomServerModeSort">
      <summary>
        <para>In OLAP and server mode, provides the capability to sort data using custom rules.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary">
      <summary>
        <para>Allows you to calculate a custom summary.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData">
      <summary>
        <para>Enables providing data to unbound fields.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.DataMember">
      <summary>
        <para>Gets or sets the data source member which supplies data to the control.</para>
      </summary>
      <value>A string value representing the data source member.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.DataSource">
      <summary>
        <para>Gets or sets the object used as the data source for the current control.</para>
      </summary>
      <value>The object used as the data source.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.DataSourceChanged">
      <summary>
        <para>Fires when the Pivot Grid Control’s data source changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.DefaultDataProcessingEngine">
      <summary>
        <para>Gets or sets a default engine used to perform data processing in the pivot grid.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotDataProcessingEngine"/> enumeration value that specifies an engine used to perform data processing in the pivot grid.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.DefaultFilterEditorView">
      <summary>
        <para>Gets or sets the how an end-user can edit criteria in the Filter Editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.FilterEditorViewMode"/> value that defines the Filter Editor view.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.EditValue">
      <summary>
        <para>Gets or sets the active editor’s value.</para>
      </summary>
      <value>An object that represents the active editor’s value.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.EditValueChanged">
      <summary>
        <para>Occurs when the cell value changes and allows you to save the changed value.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.EndRefresh">
      <summary>
        <para>Occurs after the control’s data recalculation has been completed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"></see> object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdateAsync">
      <summary>
        <para>Unlocks the Pivot Grid Control after the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.BeginUpdate"/> method call, and starts an asynchronous update.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdateMode">
      <summary>
        <para>Gets or sets whether the PivotGrid will be immediately redrawn after calling the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate"/> method.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotEndUpdateMode"/> enumeration member that specifies whether the PivotGrid will be immediately redrawn after calling the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate"/> method.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.EnsureViewInfoIsCalculated">
      <summary>
        <para>Ensures that the Pivot Grid Control’s view information has been calculated. If not, forces the Pivot Grid Control to calculate the view information.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAll">
      <summary>
        <para>Expands all the columns and rows in the PivotGridControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllAsync">
      <summary>
        <para>Expands all columns and rows in the Pivot Grid Control asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllColumns">
      <summary>
        <para>Expands all columns.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllColumnsAsync">
      <summary>
        <para>Expands all columns asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllRows">
      <summary>
        <para>Expands all rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllRowsAsync">
      <summary>
        <para>Expands all rows asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandValue(System.Boolean,System.Object[])">
      <summary>
        <para>Expands a specific column or row that is identified by the specified values.</para>
      </summary>
      <param name="isColumn">true to expand a column; false to expand a row.</param>
      <param name="values">An array of values that identify the column/row to be expanded.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandValueAsync(System.Boolean,System.Object[])">
      <summary>
        <para>Expands the specified column or row asynchronously.</para>
      </summary>
      <param name="isColumn">true to expand a column; false to expand a row.</param>
      <param name="values">An array of field values that identify the column/row to be expanded.</param>
      <returns></returns>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ExportFinished">
      <summary>
        <para>Occurs after the PivotGrid’s export has been completed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ExportStarted">
      <summary>
        <para>Occurs after the PivotGrid’s export has been started.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data to the specified stream in CSV.</para>
      </summary>
      <param name="stream">A stream to which the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.IO.Stream,DevExpress.XtraPrinting.CsvExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in CSV format using the specified options.</para>
      </summary>
      <param name="stream">A stream to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.String)">
      <summary>
        <para>Exports the control’s data to the specified file in CSV format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> containing the full path to the file to which the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.String,DevExpress.XtraPrinting.CsvExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in CSV format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> containing the full path to the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToDocx(System.IO.Stream)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a DOCX document and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToDocx(System.IO.Stream,DevExpress.XtraPrinting.DocxExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in DOCX format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.DocxExportOptions"/> object which specifies the DOCX export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToDocx(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to the specified DOCX file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the DOCX file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToDocx(System.String,DevExpress.XtraPrinting.DocxExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in DOCX format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.DocxExportOptions"/> object which specifies the DOCX export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.IO.Stream)">
      <summary>
        <para>Exports the Pivot Grid Control’s data as HTML and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in HTML format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.IO.Stream,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to an HTML document with the specified encoding and title and sends it to the specified stream. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> which specifies the encoding name set in the HTML document (e.g. “UTF-8”).</param>
      <param name="title">A <see cref="T:System.String"/> which specifies the name shown as the title of the created HTML document.</param>
      <param name="compressed">true to compress the HTML code (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to the specified file as HTML.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> containing the full path (including the file name and extension) specifying where the HTML file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in HTML format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String,System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a HTML file with the specified encoding.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> containing the full path (including the file name and extension) specifying where the HTML file will be created.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the HTML file (e.g. “UTF-8”).</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to an HTML file with the specified encoding and title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which contains the full path (including the file name and extension) specifying where the HTML file will be created.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the HTML file (e.g. “UTF-8”).</param>
      <param name="title">A <see cref="T:System.String"/> specifying the name shown as the title of the created HTML document.</param>
      <param name="compressed">true to compress the HTML code (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data to the specified stream in BMP format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.IO.Stream,DevExpress.XtraPrinting.ImageExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in Image format using the specified Image-specific options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object that contains the export options.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.String)">
      <summary>
        <para>Exports the control’s data to the specified file in BMP format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.String,DevExpress.XtraPrinting.ImageExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in Image format using the specified Image-specific options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object that contains the export options.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in MHT format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.IO.Stream,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to an MHT document (Web archive, single file) at the specified path and title and sends it to the specified stream. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created MHT document is exported to.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> which specifies the encoding name set in the MHT document (e.g. “UTF-8”).</param>
      <param name="title">A <see cref="T:System.String"/> which specifies the name shown as the title of the created MHT document.</param>
      <param name="compressed">true to compress the MHT code (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to an MHT file (Web archive, single file) at the specified path.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the MHT file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String,DevExpress.XtraPrinting.MhtExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in MHT format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String,System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to an MHT file (Web archive, single file) at the specified path and with the specified encoding.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the MHT file will be created.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> which specifies the encoding name set in the MHT document (e.g. “UTF-8”).</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to an MHT file (Web archive, single file) with the specified path and title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the MHT file will be created.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> which specifies the encoding name set in the MHT file (e.g. “UTF-8”).</param>
      <param name="title">A <see cref="T:System.String"/> which specifies the name shown as the title of the created MHT document.</param>
      <param name="compressed">true to compress the MHT code (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.IO.Stream)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a PDF document and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in PDF format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to the specified PDF file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the PDF file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.String,DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in PDF format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToRtf(System.IO.Stream)">
      <summary>
        <para>Exports the Pivot Grid Control’s data as Rich Text and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToRtf(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to the specified RTF file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the RTF file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a text document and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in Text format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a text document with the specified separator string and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which the created document is exported to.</param>
      <param name="separator">A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements. The default value is “\t”.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,System.String,System.Text.Encoding)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a text document with the specified separator string, text encoding settings and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is “\t”.</param>
      <param name="encoding">A <see cref="T:System.Text.Encoding"/> descendant that specifies the encoding of the created text document.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a text file at the specified path.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the text file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,DevExpress.XtraPrinting.TextExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in Text format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a text file at the specified path and with the specified separator string.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the text file will be created.</param>
      <param name="separator">A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is “\t”.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,System.String,System.Text.Encoding)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to a text document with the specified separator string and text encoding settings, and sends it to the specified file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) where the text file will be created.</param>
      <param name="separator">A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is “\t”.</param>
      <param name="encoding">A <see cref="T:System.Text.Encoding"/> descendant that specifies the encoding of the created text document.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream)">
      <summary>
        <para>Exports the Pivot Grid Control’s data as XLS and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.TextExportMode)">
      <summary>
        <para>Exports the control’s data to the specified stream in XLS format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
      <param name="textExportMode">A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in XLS format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String)">
      <summary>
        <para>Exports the Pivot Grid Control’s data to the specified file as XLS.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the XLS file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String,DevExpress.XtraPrinting.TextExportMode)">
      <summary>
        <para>Exports the control’s data to the specified file in XLS format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="textExportMode">A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String,DevExpress.XtraPrinting.XlsExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in XLS format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data to the specified stream in XLSX (MS Excel 2007) format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.TextExportMode)">
      <summary>
        <para>Exports the control’s data to the specified file in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
      <param name="textExportMode">A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.String)">
      <summary>
        <para>Exports the control’s data to the specified file in XLSX (MS Excel 2007) format.</para>
      </summary>
      <param name="filePath">A string that specifies the full path to the file to which the data is exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.String,DevExpress.XtraPrinting.TextExportMode)">
      <summary>
        <para>Exports the control’s data to the specified file in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="textExportMode">A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="filePath">A string that specifies the full path to the file to which the data is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object that contains export options.</param>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ExpressionEditorCreated">
      <summary />
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanged">
      <summary>
        <para>Occurs after a field location or visibility has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging">
      <summary>
        <para>Enables you to control whether the dragged field header can be dropped at the area it’s currently located over.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaIndexChanged">
      <summary>
        <para>Occurs after a field has been moved.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldExpandedInFieldGroupChanged">
      <summary>
        <para>Fires when the expansion status of fields combined into a field group is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanged">
      <summary>
        <para>Occurs after a specific field’s filter criteria was changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanging">
      <summary>
        <para>Allows you to customize the filter that is being applied or cancel filtering.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldPropertyChanged">
      <summary>
        <para>Occurs after a field’s property has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Fields">
      <summary>
        <para>Provides access to a PivotGrid control’s field collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldCollection"/> object which represents a collection of all the fields within a PivotGridControl.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldTooltipShowing">
      <summary>
        <para>Occurs before a tooltip is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldUnboundExpressionChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.UnboundExpression"/> property’s value has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsed">
      <summary>
        <para>Fires after a field value has been collapsed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing">
      <summary>
        <para>Enables you to control whether field values can be collapsed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText">
      <summary>
        <para>Allows you to change the display text and display field’s data in a more user-friendly format.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanded">
      <summary>
        <para>Fires after a field value has been expanded.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding">
      <summary>
        <para>Enables you to control whether field values can be expanded.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex">
      <summary>
        <para>Enables images to be shown within column and row headers.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueNotExpanded">
      <summary>
        <para>Occurs in OLAP mode, when an end-user clicks an expand button or selects Expand All from the context menu, and the field value cannot be expanded.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldVisibleChanged">
      <summary>
        <para>Occurs after a field has been shown or hidden.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldWidthChanged">
      <summary>
        <para>Provides a notification that the field’s width has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterEditorCreated">
      <summary>
        <para>Allows you to customize the Filter Editor before it is displayed on-screen.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelCustomizeTemplate">
      <summary>
        <para>Allows you to customize templates used by Excel-style filter popups and external editors generated using Filtering UI Context.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelData">
      <summary>
        <para>Allows you to add, remove, and modify data values and customize predefined filters in the Excel style pop-up filter menus. Filter items added manually on this event must be unique and sorted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelParseFilterCriteria">
      <summary>
        <para>Allows you to parse the filter criteria applied to data and select the corresponding values in the filter menu.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelPrepareTemplate">
      <summary>
        <para>Allows you to replace templates used by Excel-style filter popups and external editors generated using Filtering UI Context.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FilterPopupExcelQueryFilterCriteria">
      <summary>
        <para>Fires when a filter criteria is about to be applied to data and allows you to customize the filter criteria.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.FixedColumnFieldValues">
      <summary>
        <para>Provides access to the fixed columns collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.FixedFieldValueCollection"/> object that is a collection of fixed field values.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.FixedLineWidth">
      <summary>
        <para>Gets or sets the width of pinned columns separators.</para>
      </summary>
      <value>An integer value that specifies the width of vertical lines that separate pinned columns from others. The value is set in pixels.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FocusedCellChanged">
      <summary>
        <para>Fires in response to cell focus changing.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Font">
      <summary>
        <para>Overrides the base class Font property.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Font"/> object to apply to the text displayed by the control.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ForceInitialize">
      <summary>
        <para>Forces the Pivot Grid Control to finish its initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatConditions">
      <summary>
        <para>Gets the collection of style format conditions.</para>
      </summary>
      <value>The collection of style format conditions.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatRules">
      <summary>
        <para>Gets the collection of style format rules for a PivotGridControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection"/> object which is the collection of style format rules.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetCellValue(System.Int32,System.Int32)">
      <summary>
        <para>Returns a cell value by the column and row indexes.</para>
      </summary>
      <param name="columnIndex">A zero-based integer value that specifies the index of a column where the cell resides.</param>
      <param name="rowIndex">A zero-based integer value that specifies the index of a row where the cell resides.</param>
      <returns>A value displayed in the specified cell.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetCellValue(System.Object[],System.Object[])">
      <summary>
        <para>Returns a cell value calculated for the specified column and row field values.</para>
      </summary>
      <param name="columnValues">An array of column field values that identify the column where the cell resides.</param>
      <param name="rowValues">An array of row field values that identify the column where the cell resides.</param>
      <returns>A summary value calculated for the specified column and row field values.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetCellValue(System.Object[],System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns a cell value calculated for the specified column and row field values, against the specified data field.</para>
      </summary>
      <param name="columnValues">An array of column field values that identify the column where the cell resides.</param>
      <param name="rowValues">An array of row field values that identify the column where the cell resides.</param>
      <param name="dataField">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the data field, against which the required cell value is calculated.</param>
      <returns>A summary value calculated for the specified column and row field values, against the specified data field.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetColumnIndex(System.Object[])">
      <summary>
        <para>Returns the index of the specified column.</para>
      </summary>
      <param name="values">An array of column field values that identify the column.</param>
      <returns>An integer value that specifies the column index. -1 if the specified column has not been not found.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetColumnIndex(System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the index of the specified column.</para>
      </summary>
      <param name="values">An array of column field values that identify the column.</param>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the Data Field whose values are displayed in the column.</param>
      <returns>An integer value that specifies the column index. -1 if the specified column has not been not found.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldAt(System.Drawing.Point)">
      <summary>
        <para>Returns the field whose header is displayed at the specified point.</para>
      </summary>
      <param name="pt">A <see cref="T:System.Drawing.Point"/> structure which represents the test point. The point is relative to the top left corner of the control.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> field whose header is displayed at the specified point; null if there is no field header located at the specified point.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldByArea(DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
      <summary>
        <para>Returns a field at the specified visual position in the specified area.</para>
      </summary>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area containing the required field.</param>
      <param name="areaIndex">An integer value that specifies the visible index of the field within the specified area.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object at the specified visual position within the specified area.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldList">
      <summary>
        <para>Returns the list of fields that are available in the bound data source.</para>
      </summary>
      <returns>A list of fields in the bound data source.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldsByArea(DevExpress.XtraPivotGrid.PivotArea)">
      <summary>
        <para>Returns a list of fields displayed in the specified area.</para>
      </summary>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the required area.</param>
      <returns>A list of visible fields displayed in the specified area.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Returns the specified column or row field’s value for the cell addressed by its zero-based index in the Data Area.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing a column or row field whose value is to be obtained.</param>
      <param name="lastLevelIndex">A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.</param>
      <returns>An object representing the field’s value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValueOLAPMember(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Returns an OLAP member for the specified field value.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the Pivot Grid Control field.</param>
      <param name="lastLevelIndex">An integer value that specifies the cell’s index.</param>
      <returns>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IOLAPMember"/> interface. null (Nothing) if the specified summary cell doesn’t correspond to the specified field’s value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValueType(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Gets the type of a specific column/row field’s value.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that refers to the required field.</param>
      <param name="lastLevelIndex">A zero-based index of a column/row that identifies the required field value. Indexes are numbered starting from the left, for column fields, and from the top, for row fields.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> value that identifies the type of the required column/row field value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValueType(System.Boolean,System.Int32)">
      <summary>
        <para>Gets the type of a field value in the column or row area.</para>
      </summary>
      <param name="isColumn">true to retrieve information on a field value in the column area; false to retrieve information on a field value in the row area.</param>
      <param name="lastLevelIndex">A zero-based index of a column/row that identifies the required field value. Indexes are numbered starting from the left, for column fields, and from the top, for row fields.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> value that identifies the type of the required column/row field value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetKPIBitmap(DevExpress.XtraPivotGrid.PivotKPIGraphic,System.Int32)">
      <summary>
        <para>Returns an image that corresponds to a specific KPI value.</para>
      </summary>
      <param name="graphic">A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIGraphic"/> enumeration value that specifies the KPI graphic set.</param>
      <param name="state">An integer value that specifies the KPI value (-1, 0 or 1).</param>
      <returns>A Bitmap object that corresponds to the specified KPI value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIList">
      <summary>
        <para>Gets the list of key performance indicators (KPI) in a cube.</para>
      </summary>
      <returns>The list of KPI names.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIMeasures(System.String)">
      <summary>
        <para>Returns the Measures dimension’s members used to calculate the value, goal, status and weight of the specified Key Performance Indicator (KPI).</para>
      </summary>
      <param name="kpiName">A string value that specifies the KPI’s name.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotOLAPKPIMeasures"/> object that contains measures used to determine the KPI’s value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIServerGraphic(System.String,DevExpress.XtraPivotGrid.PivotKPIType)">
      <summary>
        <para>Returns a graphic set defined on the server for the specified KPI’s trend and status.</para>
      </summary>
      <param name="kpiName">A string value that specifies the KPI’s name.</param>
      <param name="kpiType">A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIType"/> enumeration value that identifies the image set. Images can be obtained only for trend and status KPI types.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIGraphic"/> enumeration value that specifies the KPI graphic set.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIValue(System.String)">
      <summary>
        <para>Returns the specified KPI’s value.</para>
      </summary>
      <param name="kpiName">A string value that specifies the KPI’s name.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotOLAPKPIValue"/> object that represents the specified KPI’s value.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetRowIndex(System.Object[])">
      <summary>
        <para>Returns the index of the specified row.</para>
      </summary>
      <param name="values">An array of row field values that identify a row.</param>
      <returns>An integer value that specifies the row index. -1 if the specified row has not been not found.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetRowIndex(System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Returns the index of the specified row.</para>
      </summary>
      <param name="values">An array of row field values that identify a row.</param>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is the Data Field whose values are displayed in the row.</param>
      <returns>An integer value that specifies the row index. -1 if the specified row has not been not found.</returns>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.GridLayout">
      <summary>
        <para>Fires after the control’s layout has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.GroupFilterChanged">
      <summary>
        <para>Occurs after a group filter condition was changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Groups">
      <summary>
        <para>Gets the collection of field groups.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridGroupCollection"/> object which represents a collection of field groups.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.HeaderImages">
      <summary>
        <para>Gets or sets the source of images available for display within field headers.</para>
      </summary>
      <value>An object that is an image collection providing images for the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>‘s field headers.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.HiddenEditor">
      <summary>
        <para>Fires after an in-place editor has been closed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.HideCustomization">
      <summary>
        <para>Closes the Customization Form.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.HideCustomizationForm">
      <summary>
        <para>Fires before the Customization Form is closed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.HideEditor">
      <summary>
        <para>Hides the active editor discarding any changes made.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.HtmlImages">
      <summary>
        <para>Gets or sets a collection of images which can be used in HTML string displayed in the field headers or values.</para>
      </summary>
      <value>An image collection (DevExpress.Utils.ImageCollection or DevExpress.Utils.SvgImageCollection).</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.InvalidValueException">
      <summary>
        <para>Enables you to provide a proper response to entering an invalid cell value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsAsyncInProgress">
      <summary>
        <para>Gets whether an asynchronous operation is in progress.</para>
      </summary>
      <value>true if an asynchronous operation is in progress; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsDragging">
      <summary>
        <para>Indicates whether a field header is being dragged by a user.</para>
      </summary>
      <value>true if a user drags a field header; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.IsObjectCollapsed(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
      <summary>
        <para>Returns whether the specified column field value or row field value is collapsed.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that identifies a column or row field.</param>
      <param name="lastLevelIndex">A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.</param>
      <returns>true if the specified column field value or row field value is collapsed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.IsObjectCollapsed(System.Boolean,System.Int32,System.Int32)">
      <summary>
        <para>Returns whether the specified column field value or row field value is collapsed.</para>
      </summary>
      <param name="isColumn">true if the field value corresponds to a column field; false if the field value corresponds to a row field.</param>
      <param name="lastLevelIndex">A zero-based index of a cell that identifies the required field value. Indices are numbered starting from the left for column fields, and from the top for row fields.</param>
      <param name="level">An integer value that specifies the nesting level of the field value.</param>
      <returns>true if the specified column field value or row field value is collapsed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsOLAPDataSource">
      <summary>
        <para>Returns whether a connection string is specified via the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPConnectionString"/> property, and so the control is bound to a cube in an MS Analysis Services database.</para>
      </summary>
      <value>true if a connection string is specified via  the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPConnectionString"/> property; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsPrintingAvailable">
      <summary>
        <para>Indicates whether the Pivot Grid Control can be printed.</para>
      </summary>
      <value>true if the Pivot Grid Control can be printed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsUpdateLocked">
      <summary>
        <para>Gets whether the PivotGridControl has been locked for updating.</para>
      </summary>
      <value>true if the PivotGridControl is locked; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.LayoutChanged">
      <summary>
        <para>Updates a PivotGridControl.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.LayoutUpgrade">
      <summary>
        <para>Occurs when a layout is restored from a data store (a stream, xml file or the system registry) and its version is different than that of the control’s current layout version.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.LeftTopCellChanged">
      <summary>
        <para>Occurs after the Pivot Grid Control is scrolled.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.LeftTopCoord">
      <summary>
        <para>Gets or sets the column and row indexes of the data cell displayed in the top left corner.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that identifies the column and row indexes of the data cell displayed in the top left corner.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ListSource">
      <summary>
        <para>Gets the actual data source whose data is displayed within the control.</para>
      </summary>
      <value>An <see cref="T:System.Collections.IList"/> object that represents the actual data source that provides data for the control.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.LoadCollapsedStateFromFile(System.String)">
      <summary>
        <para>Restores the collapsed state of field values from the specified file.</para>
      </summary>
      <param name="path">A <see cref="T:System.String"/> value that specifies the path to the file from which the collapsed state of field values is read. If the specified file does not exist, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.LoadCollapsedStateFromStream(System.IO.Stream)">
      <summary>
        <para>Restores the collapsed state of field values from the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant from which the collapsed state of field values is read. If null (Nothing in Visual Basic), an exception is raised.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.LoadingPanelVisible">
      <summary>
        <para>Gets or sets whether the animated loading panel that indicates the data load process is displayed.</para>
      </summary>
      <value>true if the loading panel is displayed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick">
      <summary>
        <para>Allows custom responses to be provided for clicks on context menu items.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.MRUFilters">
      <summary>
        <para>Gets the recently applied filters list (see MRU Filter List article).</para>
      </summary>
      <value>A list of recently applied filters.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPConnectionString">
      <summary>
        <para>Specifies a connection string to a cube in an MS Analysis Services database.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies a connection string to a cube in an MS Analysis Services database.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPDataProvider">
      <summary>
        <para>Gets or sets which data provider should be used to bind to an OLAP cube.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.OLAPDataProvider"/> enumeration member that specifies which data provider should be used to bind to an OLAP cube.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPException">
      <summary>
        <para>Raised when a query processing error occurs on a bound OLAP server, or when the connection to this server is lost.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPQueryData">
      <summary>
        <para>Allows you to get a query used to obtain data in PivotGrid.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPQueryTimeout">
      <summary>
        <para>Fires after a specific interval of time has elapsed when sending a query to an OLAP server.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsBehavior">
      <summary>
        <para>Provides access to the control’s behavior options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior"/> object containing the control’s behavior options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsChartDataSource">
      <summary>
        <para>Provides access to the options controlling the display of the PivotGrid control’s data in a chart control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsChartDataSource"/> object that contains corresponding options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsCustomization">
      <summary>
        <para>Provides access to the Pivot Grid Control’s customization options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx"/> object which contains the Pivot Grid Control’s customization options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsData">
      <summary>
        <para>Provides access to the Pivot Grid Control’s data specific options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsData"/> class that contains the Pivot Grid Control’s data specific options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsDataField">
      <summary>
        <para>Provides access to the options which control the presentation of the data fields in the PivotGrid.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsDataField"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsFilter">
      <summary>
        <para>Provides access to the Pivot Grid’s filter options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilter"/> object that specifies filter settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsFilterPopup">
      <summary>
        <para>Provides access to options that define the Classic filter popup’s appearance and behavior.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup"/> object that specifies the appearance and behavior of the Classic filter popup.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsHint">
      <summary>
        <para>Provides access to the Pivot Grid Control’s hint options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsHint"/> object which contains the Pivot Grid Control’s hint options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsLayout">
      <summary>
        <para>Provides options that control how the Pivot Grid Control layout is stored to/restored from a storage (a stream, XML file or system registry).</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsLayout"/> object that provides options for controlling how the layout is stored and restored.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsMenu">
      <summary>
        <para>Provides access to the Pivot Grid Control’s menu options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsMenu"/> object which contains the Pivot Grid Control’s menu options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsOLAP">
      <summary>
        <para>Provides access to the Pivot Grid Control’s OLAP mode specific options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsOLAP"/> object which contains the Pivot Grid Control’s OLAP mode specific options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsPrint">
      <summary>
        <para>Provides access to the Pivot Grid’s appearance and layout options used when printing or exporting the control in WYSIWYG mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsPrint"/> object which contains the Pivot Grid control’s print options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsSelection">
      <summary>
        <para>Provides access to the Pivot Grid Control’s selection options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsSelection"/> object which contains the Pivot Grid Control’s selection options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsView">
      <summary>
        <para>Provides access to the Pivot Grid Control’s display options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsView"/> object which contains the Pivot Grid Control’s display options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.PaintAppearance">
      <summary>
        <para>Provides access to the appearance settings currently used to paint the Pivot Grid Control’s elements.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearances"/> object which provides the appearance settings currently used to paint the Pivot Grid Control’s elements.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.PaintAppearancePrint">
      <summary>
        <para>Provides access to the appearance settings used to paint the Pivot Grid Control’s elements when it’s printed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint"/> object which provides the appearance settings used to paint the Pivot Grid Control’s elements when it’s printed.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.PaintEx">
      <summary>
        <para>A DirectX-compatible method that replaces the standard Control.Paint method.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.PopupMenuShowing">
      <summary>
        <para>Allows you to customize context menus.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.PostEditor">
      <summary>
        <para>Posts the value being edited to the associated data source without closing the editor.</para>
      </summary>
      <returns>true if the value being edited has been successfully validated and saved to the associated data source; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.PostEditorAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.Print">
      <summary>
        <para>Prints the PivotGridControl.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.QueryCustomFunctions">
      <summary>
        <para>Allows you to add custom-function-based filters (for example, ‘discount is more than 15%’) to Excel-style pop-up filter menus and/or the filter editor.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.QueryException">
      <summary>
        <para>Occurs when a query processing error occurs on a bound server providing data for the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RefreshData">
      <summary>
        <para>Reloads data from the control’s data source and recalculates summaries.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RefreshDataAsync">
      <summary>
        <para>Reloads data from the control data source and recalculates summaries asynchronously.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromRegistry(System.String)">
      <summary>
        <para>Restores the layout stored at the specified system registry path.</para>
      </summary>
      <param name="path">A string value which specifies the system registry path. If the specified path doesn’t exist, this method does nothing.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Restores the Pivot Grid Control’s layout stored at the specified system registry path, using the specified settings.</para>
      </summary>
      <param name="path">A string value specifying the system registry path. If the specified path doesn’t exist, calling this method does nothing.</param>
      <param name="options">A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant which specifies which options should be restored.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromStream(System.IO.Stream)">
      <summary>
        <para>Restores a Pivot Grid Control’s layout from the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant from which the Pivot Grid Control’s settings are read. If null (Nothing in Visual Basic), an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Restores a Pivot Grid Control’s layout from the specified stream, using the specified settings.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant which contains the layout settings. If null (Nothing in Visual Basic), an exception is raised.</param>
      <param name="options">A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant which specifies which options should be restored.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromXml(System.String)">
      <summary>
        <para>Restores a Pivot Grid Control’s layout from the specified XML file.</para>
      </summary>
      <param name="xmlFile">A string value specifying the path to the XML file from which Pivot Grid Control’s settings are read. If the specified file doesn’t exist, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Restores a Pivot Grid Control’s layout using the specified settings from the specified XML file.</para>
      </summary>
      <param name="xmlFile">A string value specifying the path to the XML file from which the Pivot Grid Control’s settings are read. If the specified file doesn’t exist, an exception is raised.</param>
      <param name="options">A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be restored.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RetrieveFields">
      <summary>
        <para>Creates <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects for all columns in the bound data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RetrieveFields(DevExpress.XtraPivotGrid.PivotArea,System.Boolean)">
      <summary>
        <para>Creates <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects for all columns in the bound data source, and moves the fields to the specified area, making them visible or hidden.</para>
      </summary>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that specifies the area to which the created fields are moved.</param>
      <param name="visible">true if the created fields are visible; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RetrieveFieldsAsync">
      <summary>
        <para>Creates <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects for all columns in a data source asynchronously.</para>
      </summary>
      <returns>An asynchronous operation that returns true in case of success.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RetrieveFieldsAsync(DevExpress.XtraPivotGrid.PivotArea,System.Boolean)">
      <summary>
        <para>Creates <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects for all columns in a data source and specifies the area and visibility of the fields. This method executes these actions asynchronously.</para>
      </summary>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the area to which the created fields are moved.</param>
      <param name="visible">true to show the created fields; otherwise, false.</param>
      <returns>An asynchronous operation that returns true in case of success.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveCollapsedStateToFile(System.String)">
      <summary>
        <para>Saves the collapsed state of field values to the specified file.</para>
      </summary>
      <param name="path">A <see cref="T:System.String"/> value which specifies the path to the file to which the collapsed state of field values is saved. If the specified file doesn’t exist, it is created.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveCollapsedStateToStream(System.IO.Stream)">
      <summary>
        <para>Saves the collapsed state of field values to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which the collapsed state of field values is saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToRegistry(System.String)">
      <summary>
        <para>Saves a Pivot Grid Control’s layout to a system registry path.</para>
      </summary>
      <param name="path">A string value which specifies the system registry path to which the layout is saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Saves a Pivot Grid Control’s layout to the specified system registry path, using the specified settings.</para>
      </summary>
      <param name="path">A string value which specifies the system registry path to which the layout is saved.</param>
      <param name="options">A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToStream(System.IO.Stream)">
      <summary>
        <para>Saves a Pivot Grid Control’s layout to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which a Pivot Grid Control’s layout is written.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Saves a Pivot Grid Control’s layout to the specified stream, using the specified settings.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which the Pivot Grid Control’s layout is written.</param>
      <param name="options">A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToXml(System.String)">
      <summary>
        <para>Saves a Pivot Grid Control’s layout to an XML file.</para>
      </summary>
      <param name="xmlFile">A string value specifying the path to the file where a Pivot Grid Control’s layout should be stored. If an empty string is specified, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Saves a Pivot Grid Control’s layout to the specified XML file, using the specified settings.</para>
      </summary>
      <param name="xmlFile">A string value specifying the path to the file where the Pivot Grid Control’s layout settings should be stored. If an empty string is specified, an exception is raised.</param>
      <param name="options">A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToFile(System.String)">
      <summary>
        <para>Saves the Pivot Grid Control’s data and full layout to the specified file.</para>
      </summary>
      <param name="path">A string that specifies the path to the file in which the control’s data and layout will be saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToFile(System.String,System.Boolean)">
      <summary>
        <para>Saves the Pivot Grid Control’s data and full layout to the specified file, and allows the data to be compressed.</para>
      </summary>
      <param name="path">A string that specifies the path to the file in which the control’s data and layout will be saved.</param>
      <param name="compress">true to compress the output file; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToStream(System.IO.Stream)">
      <summary>
        <para>Saves the Pivot Grid Control’s data and full layout to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which data is saved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToStream(System.IO.Stream,System.Boolean)">
      <summary>
        <para>Saves the Pivot Grid Control’s data and full layout to the specified stream, and allows the data to be compressed.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which data is saved.</param>
      <param name="compress">true to compress the output stream; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SetDataSourceAsync(System.Object)">
      <summary>
        <para>Sets a data source for Pivot Grid Control and loads data asynchronously.</para>
      </summary>
      <param name="dataSource">An object that contains data for the PivotGridControl.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SetFieldSortingAsync(DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotSortOrder)">
      <summary>
        <para>Sets the specified sort order for the specified field asynchronously.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies a field whose sort order should be changed.</param>
      <param name="sortOrder">A <see cref="T:DevExpress.XtraPivotGrid.PivotSortOrder"/> enumeration member that specifies the sort order to be set for the field.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SetOLAPConnectionStringAsync(System.String)">
      <summary>
        <para>Sets a connection string to a cube in an MS Analysis Services database, and loads data from the cube asynchronously.</para>
      </summary>
      <param name="olapConnectionString">A <see cref="T:System.String"/> that specifies the connection string to an OLAP cube.</param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SetOLAPFilterByUniqueName(System.Boolean)">
      <summary />
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SetOLAPFilterByUniqueNameAsync(System.Boolean)">
      <summary />
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ShowCustomFunctions">
      <summary>
        <para>Gets or sets whether to show custom function-based filters.</para>
      </summary>
      <value>True to show custom function-based filters; False to not show them; Default to enable/disable custom function-based filters depending on the global <see cref="F:DevExpress.XtraEditors.WindowsFormsSettings.DefaultSettingsCompatibilityMode"></see> setting.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowCustomization">
      <summary>
        <para>Displays the Customization Form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowCustomization(System.Drawing.Point)">
      <summary>
        <para>Displays the Customization Form at the specified point.</para>
      </summary>
      <param name="showPoint">The Customization Form’s top-left corner, in screen coordinates.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowCustomization(System.Windows.Forms.Control)">
      <summary>
        <para>Displays the Customization Form and assigns it to the specified parent.</para>
      </summary>
      <param name="parentControl">A control that owns the created customization form.</param>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowCustomizationForm">
      <summary>
        <para>Fires immediately after the Customization Form has been invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowEditor">
      <summary>
        <para>Invokes the focused cell’s editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowEditor(System.Drawing.Point)">
      <summary>
        <para>Invokes the in-place editor for the cell at the specified position.</para>
      </summary>
      <param name="location">The point where the target cell is located.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowExpressionEditor(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Invokes the Expression Editor where a user can edit the specified field’s binding expression.</para>
      </summary>
      <param name="field">A calculated field whose expression is edited in the Expression Editor.</param>
      <returns>true if the OK button is pressed when a user closes the editor; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowFilterPopupExcel">
      <summary>
        <para>Allows you to customize the Excel-style filter popup (for instance, to hide specific filtering conditions from the “Filters” tab).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm">
      <summary>
        <para>Occurs before the Customization Form is displayed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingEditor">
      <summary>
        <para>Allows you to cancel editor activation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShownEditor">
      <summary>
        <para>Fires after a cell editor has been invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowPrintPreview">
      <summary>
        <para>Opens the Print Preview window with a toolbar-based interface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowRibbonPrintPreview">
      <summary>
        <para>Opens the Print Preview window with a Ribbon-based interface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowUnboundExpressionEditor(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Invokes the Expression Editor that enables editing a specified unbound field’s expression.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> that represents an unbound field whose expression will be edited in the Expression Editor.</param>
      <returns>true if the OK button has been pressed when closing the editor; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Text">
      <summary>
        <para>Gets or sets the string associated with the control.</para>
      </summary>
      <value>A string associated with the control.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.UnboundExpressionEditorCreated">
      <summary>
        <para>Fires after an Expression Editor is created for an unbound field.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.UseDirectXPaint">
      <summary>
        <para>Gets or sets whether the DirectX Hardware Acceleration is enabled for this Pivot Grid Control.</para>
      </summary>
      <value>A DefaultBoolean enumerator value that specifies whether or not this control employs the DirectX Hardware Acceleration.
The DefaultBoolean.Default value is equivalent to DefaultBoolean.True if the static <see cref="M:DevExpress.XtraEditors.WindowsFormsSettings.ForceDirectXPaint"/> method was called, or to DefaultBoolean.False otherwise.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.UseDisabledStatePainter">
      <summary>
        <para>Gets or sets whether the control is painted grayed out, when in the disabled state.</para>
      </summary>
      <value>true if the control is painted grayed out, when in the disabled state; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.UserAction">
      <summary>
        <para>Gets the end-user action performed on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraPivotGrid.UserAction"/> object that is the end-user action performed on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.UserActionChanged">
      <summary>
        <para>Allows you to respond to end-user actions performed on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ValidateEditor">
      <summary>
        <para>Validates the active editor.</para>
      </summary>
      <returns>true if the active editor’s value is valid; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ValidatingEditor">
      <summary>
        <para>Enables you to manually validate cell values.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ValueImages">
      <summary>
        <para>Gets or sets the source of images that are available for display within field values.</para>
      </summary>
      <value>An object that is an image collection providing images for the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>‘s field values.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.XtraFindFieldsItem(DevExpress.Utils.Serializing.XtraItemEventArgs)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="e"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort"/> event.</para>
      </summary>
      <param name="sender">The event sender. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary"/> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the PivotGrid control that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal">
      <summary>
        <para>Represents a custom total which can be calculated for an outer column field or row field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.#ctor(DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> class with the specified summary function type.</para>
      </summary>
      <param name="summaryType">A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> value that specifies the summary function type for the created custom total object. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase.SummaryType"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomTotal.Appearance">
      <summary>
        <para>Gets the appearance settings used to paint the custom total’s cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the custom total’s cells.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.CloneTo(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase)">
      <summary>
        <para>Copies settings of the current object to the object passed as the parameter.</para>
      </summary>
      <param name="clone">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> object to which settings are copied from the current object.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.IsEqual(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase)">
      <summary>
        <para>Returns whether the settings of the current and specified objects match.</para>
      </summary>
      <param name="total">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> object to be compared with the current object.</param>
      <returns>true if the settings of the objects match; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection">
      <summary>
        <para>Represents a collection of custom totals for a field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection"/> class with default settings,</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.#ctor(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection"/> class and adds copies of the specified custom totals to the collection.</para>
      </summary>
      <param name="totals">An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> objects whose copies will be added to the collection.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.#ctor(DevExpress.XtraPivotGrid.PivotGridFieldBase)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="field"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.Add(DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary>
        <para>Appends a new item to the collection that represents a custom total of the specified summary function type.</para>
      </summary>
      <param name="summaryType">A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> value that determines the type of the summary function used to calculate the current total. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase.SummaryType"/> property.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the created custom total.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.AddRange(DevExpress.XtraPivotGrid.PivotGridCustomTotal[])">
      <summary>
        <para>Appends an array of custom totals to the current collection.</para>
      </summary>
      <param name="customSummaries">An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> objects which represent the custom totals to add to the collection.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.Field">
      <summary>
        <para>Gets the field which owns the current collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> field which owns the current collection.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to the elements in the collection.</para>
      </summary>
      <param name="index">A zero-based integer value which represents the desired custom total’s position within the collection. If it’s negative or exceeds the last available index, an exception is raised.</param>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object located at the specified index.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridField">
      <summary>
        <para>A field within the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> class.</para>
      </summary>
      <param name="data">A DevExpress.XtraPivotGrid.Data.PivotGridData object.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.#ctor(System.String,DevExpress.XtraPivotGrid.PivotArea)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> class with the specified field name and location.</para>
      </summary>
      <param name="fieldName">A string identifying the name of the database field. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.FieldName"/> property.</param>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that defines the location for the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object within the control. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.Area"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.AccessibleDescription">
      <summary>
        <para>Gets or sets the object’s description used by accessibility client applications.</para>
      </summary>
      <value>The object’s description used by accessibility client applications.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.AccessibleName">
      <summary>
        <para>Gets or sets the object’s name used by accessibility client applications.</para>
      </summary>
      <value>The object’s name used by accessibility client applications.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.AccessibleRole">
      <summary>
        <para>Gets or sets the object’s accessible role.</para>
      </summary>
      <value>The object’s accessible role.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.Appearance">
      <summary>
        <para>Provides access to the appearance settings used to paint the field’s header, values and value totals.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldAppearances"/> collection that contains the appearance settings used to paint the field’s header, values and value totals.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.Assign(DevExpress.XtraPivotGrid.PivotGridFieldBase)">
      <summary />
      <param name="fieldBase"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.BestFit">
      <summary>
        <para>Resizes the field’s column(s) to fit the longest text currently displayed in the column.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CanEdit">
      <summary>
        <para>Returns whether cells of the current field can be edited.</para>
      </summary>
      <value>true if cells of the current field can be edited; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CanFilterRadioMode">
      <summary>
        <para>Gets whether end-users are allowed to select only a single item in the field’s filter drop-down.</para>
      </summary>
      <value>true if end-users are allowed to select only a single item in the field’s filter drop-down; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CanHide">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CanShowUnboundExpressionMenu">
      <summary>
        <para>Gets whether a menu used to open an Expression Editor for unbound fields is available.</para>
      </summary>
      <value>true if a menu used to open an Expression Editor for unbound fields is available; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.CollapseAllAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.CollapseValueAsync(System.Object)">
      <summary />
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CustomTotals">
      <summary>
        <para>Gets the current field’s custom total collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection"/> object which represent the custom totals’ collection.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.DataType">
      <summary>
        <para>Gets the field’s data type.</para>
      </summary>
      <value>The <see cref="T:System.Type"/>.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.DropDownFilterListSize">
      <summary>
        <para>Gets or sets the width and height of the field’s filter dropdown.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the width and height (in pixels) of the field’s filter dropdown.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ExpandAllAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ExpandValueAsync(System.Object)">
      <summary />
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.FieldEdit">
      <summary>
        <para>Gets or sets the editor used to edit cells corresponding to the current data field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that represents the field’s repository item.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.FieldEditName">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.GetCaption">
      <summary>
        <para>Gets or sets the field’s display caption.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> object that is the field’s display caption.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.GetDescription">
      <summary>
        <para>Gets this field’s description, assigned using the [Display(Description = “. . .”)] data attribute.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value that is the fields’s description.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.GetUniqueValuesAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.GetUniqueValuesAsync(DevExpress.Data.Filtering.CriteriaOperator)">
      <summary />
      <param name="filter"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.GetVisibleValuesAsync">
      <summary />
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.HeaderImages">
      <summary>
        <para>Gets the source of images that can be displayed within field headers.</para>
      </summary>
      <value>An object that is an image collection providing images for the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/>‘s field headers.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ImageIndex">
      <summary>
        <para>Gets or sets the index of the image which is displayed within the field’s header.</para>
      </summary>
      <value>A zero-based integer that represents the index of the image which is displayed within the field’s header.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ImageOptions">
      <summary>
        <para>Provides access to settings that allow you to set up raster and vector icons for this <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldImageOptions"/> object containing settings that allow you to set up raster and vector icons for this <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/>.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.Options">
      <summary>
        <para>Contains the field’s options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx"/> object which contains the field’s options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.OptionsFilter">
      <summary>
        <para>Provides access to the field’s filtering options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter"/> object containing the field’s filtering options.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.PivotGrid">
      <summary>
        <para>Gets the PivotGrid control that owns the current field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> control that owns the current field.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.SetDefaultEditParameters(DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Sets specific settings of the specified repository item (in-place editor) to default. This member supports the internal infrastructure and is not intended to be used directly from your code..</para>
      </summary>
      <param name="edit">A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.SetOLAPFilterByUniqueNameAsync(DevExpress.Utils.DefaultBoolean)">
      <summary />
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ShowExcelFilterPopup">
      <summary>
        <para>Invokes the Excel-style filter popup for the current field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ShowFilterPopup">
      <summary>
        <para>Invokes the Classic filter popup for the current field.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotGridField.SummaryTypeChanged">
      <summary>
        <para>Occurs after the field’s summary type has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ToolTips">
      <summary>
        <para>Gets the field’s hint settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldToolTips"/> object which provides the hint settings for the field.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ToString">
      <summary>
        <para>Returns the text representation of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridField.UnboundExpression">
      <summary>
        <para>Gets or sets an expression used to evaluate values for the current unbound field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies an expression used to evaluate values for the current field.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldAppearances">
      <summary>
        <para>Provides the appearance settings used to paint the elements in a field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.#ctor(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldAppearances"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the owner field.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.Cell">
      <summary>
        <para>Gets the appearance settings used to paint regular cells corresponding to the current data field.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides corresponding settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.CellGrandTotal">
      <summary>
        <para>Gets the appearance settings used to paint grand total cells corresponding to the current data field.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides corresponding settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.CellTotal">
      <summary>
        <para>Gets the appearance settings used to paint total cells corresponding to the current data field.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides corresponding settings.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.Header">
      <summary>
        <para>Gets the appearance settings used to paint the current field’s header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides appearance settings used to paint the field header.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.IsLoading">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.Value">
      <summary>
        <para>Gets the appearance settings used to paint the values of fields.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the field’s values.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.ValueGrandTotal">
      <summary>
        <para>Gets the appearance settings used to paint the Grand Total headers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the Grand Total headers.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.ValueTotal">
      <summary>
        <para>Gets the appearance settings used to paint the field’s totals.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the field’s totals.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldCollection">
      <summary>
        <para>A collection of PivotGrid control fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldCollection"/> class.</para>
      </summary>
      <param name="data">A DevExpress.XtraPivotGrid.Data.PivotGridData object that implements data-aware operations on the data source.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Add">
      <summary>
        <para>Appends a new field to the collection.</para>
      </summary>
      <returns>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object added to the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Add(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Appends the specified field to the collection.</para>
      </summary>
      <param name="field">The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that will be appended to the collection.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.AddDataSourceColumn(System.String,DevExpress.XtraPivotGrid.PivotArea)">
      <summary>
        <para>Adds a field to the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldCollection"/> collection.</para>
      </summary>
      <param name="columnName">A <see cref="T:System.String"/> value that specifies a column name.</param>
      <param name="area">A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area in which the new PivotGridField object will be positioned.</param>
      <returns>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that was added to the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.AddDataSourceColumn(System.String,DevExpress.XtraPivotGrid.PivotArea,DevExpress.XtraPivotGrid.PivotGroupInterval)">
      <summary />
      <param name="columnName"></param>
      <param name="area"></param>
      <param name="groupInterval"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.AddRange(DevExpress.XtraPivotGrid.PivotGridField[])">
      <summary>
        <para>Adds an array of fields to the end of the collection.</para>
      </summary>
      <param name="fields">An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.GetFieldByName(System.String)">
      <summary>
        <para>Returns the field by the name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of the required field.</param>
      <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the field whose name matches the specified string; null (Nothing in Visual Basic) if the field does not exist.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to individual fields in the collection.</para>
      </summary>
      <param name="index">A zero-based integer specifying the desired field’s position within the collection. If negative or exceeds the last available index, an exception is raised.</param>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field at the specified position.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Item(System.String)">
      <summary>
        <para>Gets the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object specified by the bound field name.</para>
      </summary>
      <param name="fieldName">A string value specifying the bound field name of the required <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object.</param>
      <value>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object bound to the specified field.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Remove(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Removes the specified <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object from the collection.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the field to remove.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldImageOptions">
      <summary>
        <para>Contains settings that allow you to set up raster and vector icons for the current <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldImageOptions.#ctor(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldImageOptions"/> class.</para>
      </summary>
      <param name="owner">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that specifies the Pivot Grid field.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx">
      <summary>
        <para>Contains options for a field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.#ctor(DevExpress.XtraPivotGrid.PivotOptionsChangedEventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx"/> class with the specified settings.</para>
      </summary>
      <param name="optionsChanged">A delegate that will receive change notifications.</param>
      <param name="viewBagOwner">An IViewBagOwner object that is used to initialize the created object.</param>
      <param name="objectPath">A string value that is used to initialize the created object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.AllowEdit">
      <summary>
        <para>Gets or sets whether data editing is allowed for the current field.</para>
      </summary>
      <value>true to allow editing cell values that correspond to the current data field; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.AllowHide">
      <summary>
        <para>Gets or sets whether or not an end-user can hide the current field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether or not an end-user can hide the current field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.FieldFilterPopupMode">
      <summary>
        <para>Gets or sets the field’s pop-up filter mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.FieldFilterPopupMode"/> value that specifies the field’s pop-up filter mode. Default value is interpreted as <see cref="F:DevExpress.XtraPivotGrid.FieldFilterPopupMode.Excel"/>.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.IsFilterRadioMode">
      <summary>
        <para>Gets or sets whether an end-user is allowed to select only a single item in the filter drop-down.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether an end-user is allowed to select only a single item in the filter drop-down.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ReadOnly">
      <summary>
        <para>Gets or sets whether end-users can modify cell values.</para>
      </summary>
      <value>true to prevent a cell value from being changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ShowButtonMode">
      <summary>
        <para>Gets or sets which cells corresponding to the current field  display editor buttons.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum"/> value that specifies the current display mode for cell buttons.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ShowExpressionEditorMenu">
      <summary>
        <para>Gets or sets whether a user can open the Expression Editor dialog for the current field in the context menu.</para>
      </summary>
      <value>true if a user can open the Expression Editor dialog for the current field in a context menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ShowInFilter">
      <summary>
        <para>Gets or sets whether the field is available for selection in the Filter Editor.</para>
      </summary>
      <value>True if the field is available in the Filter Editor; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ShowUnboundExpressionMenu">
      <summary>
        <para>Gets or sets whether an end-user can open an Expression Editor for the current unbound field using a context menu.</para>
      </summary>
      <value>true if an end-user can open an Expression Editor for the current unbound field using a context menu; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter">
      <summary>
        <para>Contains the field’s filtering options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies settings of the specified object to the current object.</para>
      </summary>
      <param name="options">An <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are copied to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterDateTimeValuesTabFilterType">
      <summary>
        <para>Gets or sets the type of filtering UI used to create filters in the Values tab in the Excel-style filter popup (if the current field contains date-time values).</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.ExcelFilterDateTimeValuesTabFilterType"/> value that specifies the type of filtering UI used to create filters in the Values tab in the Excel-style filter popup.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterDefaultTab">
      <summary>
        <para>Gets or sets the default tab opened in the Excel-style filter popup.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPivotGrid.ExcelFilterDefaultTab"/> value that specifies the default tab opened in the Excel-style filter popup.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterEnumFilters">
      <summary>
        <para>Gets or sets a set of relational operators (equality and inequalities) used to create a filter condition in the Filters tab of the Excel-style filter popup (if the current field contains enumeration data).</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPivotGrid.ExcelFilterEnumFilters"/> value that specifies a set of relational operators (equality and inequalities) used to create a filter condition in the Filters tab of the Excel-style filter popup.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterImmediateUpdate">
      <summary>
        <para>Gets or sets whether filter conditions are applied to data immediately or only when the drop-down filter is closed. This property is in effect for the Excel-style Filter Dropdown.</para>
      </summary>
      <value>True or Default if filter conditions immediately apply to data; otherwise, False.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterNumericValuesTabFilterType">
      <summary>
        <para>Gets or sets the type of filtering UI used to create filters in the Values tab in the Excel-style filter popup (if the current field contains numeric data).</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPivotGrid.ExcelFilterNumericValuesTabFilterType"/> value that specifies the type of filtering UI used to create filters in the Values tab in the Excel-style filter popup.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterShowAllValues">
      <summary>
        <para>Gets or sets whether or not filters applied to other fields affect filter values displayed for the current field in the Excel-style filter popup.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether or not filters applied to other fields affect filter values displayed for the current field in the Excel-style filter popup.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterShowBlanks">
      <summary>
        <para>Gets or sets whether or not the Excel-style pop-up filter shows NULL values from a current field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether or not the Excel-style filter popup shows NULL values from a current field.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsFilter.PopupExcelFilterTextFilters">
      <summary>
        <para>Gets or sets a set of relational operators (equality and inequalities) used to create a filter condition in the Filters tab of the Excel-style filter popup (if the current field contains text data).</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraPivotGrid.ExcelFilterTextFilters"/> value that specifies a set of relational operators (equality and inequalities) used to create a filter condition in the Filters tab of the Excel-style filter popup.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldToolTips">
      <summary>
        <para>Provides tooltip options for a Pivot Grid field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.#ctor(DevExpress.XtraPivotGrid.PivotGridField)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldToolTips"/> class.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that is a PivotGrid field.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.GetValueText(System.Object)">
      <summary>
        <para>Returns the formatted text representation of the specified value according to the settings used to represent the field value tooltip.</para>
      </summary>
      <param name="value">An object which identifies the value to format.</param>
      <returns>A string that specifies the formatted text representation of the specified value.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.HeaderText">
      <summary>
        <para>Gets or sets a field header tooltip.</para>
      </summary>
      <value>A string value that is a field header tooltip.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.Reset">
      <summary>
        <para>Resets the tooltip settings to their default values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.ShouldSerialize">
      <summary>
        <para>Tests whether the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldToolTips"/> object should be persisted.</para>
      </summary>
      <returns>true if the object should be persisted; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.ValueFormat">
      <summary>
        <para>Provides access to the formatting settings applied to a field value tooltip.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.FormatInfo"/> object that provides the formatting settings applied to a field value tooltip.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldToolTips.ValueText">
      <summary>
        <para>Gets or sets a field value tooltip.</para>
      </summary>
      <value>A string value that is a field value tooltip.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection">
      <summary>
        <para>A collection of style format conditions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.Add(DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition)">
      <summary>
        <para>Appends a style condition object to the collection.</para>
      </summary>
      <param name="condition">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object to be added to the collection.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.AddRange(DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition[])">
      <summary>
        <para>Adds an array of style condition objects to the end of the collection.</para>
      </summary>
      <param name="conditions">An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> objects to be appended to the collection.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.CompareValues(System.Object,System.Object)">
      <summary>
        <para>Performs a comparison of two objects of the same type and returns a value indicating whether one is less than, equal to or greater than the other.</para>
      </summary>
      <param name="val1">The first object to compare.</param>
      <param name="val2">The second object to compare.</param>
      <returns>An integer value indicating whether one is less than, equal to or greater than the other.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.IsLoading">
      <summary>
        <para>Indicates whether the Pivot Grid Control that owns the current collection is currently being initialized.</para>
      </summary>
      <value>true if the Pivot Grid Control is being initialized; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to individual items within the collection.</para>
      </summary>
      <param name="index">An integer value specifying the item’s zero based index within the collection. If its negative or exceeds the last available index, an exception is raised.</param>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object which resides at the specified position within the collection.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.Item(System.Object)">
      <summary>
        <para>Gets a style format condition with the specified tag.</para>
      </summary>
      <param name="tag">An object which contains information associated with the style format condition.</param>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object which represents a style format condition within the collection whose <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property’s value matches the tag parameter. null (Nothing in Visual Basic) if no style format condition is found.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFormatRule">
      <summary>
        <para>A format rule used to apply conditional formatting to Pivot Grid Control data cells.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRule.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatRule"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRule.CheckValue(DevExpress.XtraPivotGrid.Data.PivotGridCellItem)">
      <summary>
        <para>Checks whether the specified value matches the current format rule.</para>
      </summary>
      <param name="item">A PivotGridCellItem object.</param>
      <returns>true if the specified value matches the current format rule; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRule.CheckValue(System.Drawing.Point)">
      <summary>
        <para>Checks whether the value in the specified location matches the current format rule.</para>
      </summary>
      <param name="cell">A System.Drawing.Point which specifies the location of a cell relative to the pivot grid’s top-left corner.</param>
      <returns>true if the specified value matches the current format rule; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRule.CheckValue(System.Int32,System.Int32)">
      <summary>
        <para>Checks whether the value in the specified by index column and row matches the current format rule.</para>
      </summary>
      <param name="rowIndex">An integer that is the zero-based index of the row.</param>
      <param name="columnIndex">An integer that is the zero-based index of the column.</param>
      <returns>true if the specified value matches the current format rule; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatRule.IsValid">
      <summary>
        <para>Determines whether the rule is correct.</para>
      </summary>
      <value>true if the format rule is correct; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatRule.Measure">
      <summary>
        <para>Gets or sets the data field to which the format rule is applied.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object to which the format rule is applied.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatRule.MeasureName">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatRule.Settings">
      <summary>
        <para>Provides access to format rule settings used to apply conditional formatting to data cells.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraPivotGrid.FormatRuleSettings"/> object, that is used to apply conditional formatting to data cells.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatRule.SettingsName">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection">
      <summary>
        <para>A collection of <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatRule"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="data"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection"/> object by preventing visual updates of the object and its elements until the <see cref="M:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection.EndUpdate"/> method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection"/> object after a call to the <see cref="M:DevExpress.XtraPivotGrid.PivotGridFormatRuleCollection.BeginUpdate"/> method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest">
      <summary>
        <para>Lists values that identify the visual elements of a field header.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest.Filter">
      <summary>
        <para>The test point belongs to the filter button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest.None">
      <summary>
        <para>The test point doesn’t belong to the filter button.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo">
      <summary>
        <para>Contains information about a specific point within a Header Area.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotGridHeaderHitTest)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo"/> class.</para>
      </summary>
      <param name="headersViewInfo">A DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase descendant that provides information on the field headers located at the test point.</param>
      <param name="field">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field which the header at the test point corresponds to. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Field"/> property.</param>
      <param name="headerHitTest">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitTest"/> enumeration value which identifies the type of the header’s visual element which is located under the test point. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.HeaderHitTest"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Area">
      <summary>
        <para>Gets the header area of the PivotGrid control which is located under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the area located under the test point.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Field">
      <summary>
        <para>Gets the field that the header located at the test point corresponds to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field header that the test point corresponds to. null (Nothing in Visual Basic) if no field header is located at the test point.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.HeaderHitTest">
      <summary>
        <para>Gets a value which identifies the type of the header’s visual element that is located under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest"/> enumeration value which identifies the type of the header’s visual element that is located under the test point.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridHitInfo">
      <summary>
        <para>Contains information about a specific point within a PivotGridControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,System.Drawing.Point,DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> class with the specified settings.</para>
      </summary>
      <param name="cellViewInfo">A PivotCellViewInfo object.</param>
      <param name="hitPoint">A Point structure that specifies the test point for which the hit information is to be retrieved.</param>
      <param name="viewInfo">A PivotGridViewInfo object.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfo,DevExpress.XtraPivotGrid.PivotGridValueHitTest,System.Drawing.Point)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> class.</para>
      </summary>
      <param name="fieldCellViewInfo">An object that contains the information on the processed cell.</param>
      <param name="valueHitTest">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueHitTest"/> enumeration value which identifies the visual element located under the test point.</param>
      <param name="hitPoint">A Point structure that specifies the test point for which the hit information is to be retrieved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase,DevExpress.XtraPivotGrid.Data.PivotFieldItem,DevExpress.XtraPivotGrid.PivotGridHeaderHitTest,System.Drawing.Point)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> class.</para>
      </summary>
      <param name="headersViewInfo">An object that contains information on the processed field header.</param>
      <param name="field">An object that identifies the field whose header is located under the test point</param>
      <param name="headerHitTest">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest"/> enumeration value which identifies the visual element located under the test point.</param>
      <param name="hitPoint">A <see cref="T:System.Drawing.Point"/> structure that specifies the test point for which the hit information is to be retrieved.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(System.Drawing.Point)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> class with the specified test point.</para>
      </summary>
      <param name="hitPoint">A <see cref="T:System.Drawing.Point"/> structure specifying test point coordinates relative to the Pivot Grid Control’s top-left corner. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitPoint"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.CellInfo">
      <summary>
        <para>Gets information on the cell located at the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object which provides information on the cell located at the test point.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HeaderField">
      <summary>
        <para>Gets the field which the header located at the test point corresponds to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field which the header at the test point corresponds to. null (Nothing in Visual Basic) if there isn’t any field header located at the test point.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HeadersAreaInfo">
      <summary>
        <para>Gets information on the field header located at the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo"/> object which provides information on the field header located at the specified point.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitPoint">
      <summary>
        <para>Gets the test point.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure specifying test point coordinates relative to the Pivot Grid Control’s top-left corner.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitTest">
      <summary>
        <para>Gets a value identifying the type of the visual element located under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitTest"/> enumeration value which identifies the type of the visual element that contains the test point.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.ValueInfo">
      <summary>
        <para>Gets the information on the field value located under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo"/> object which contains information on the field value located under the test point.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridHitTest">
      <summary>
        <para>Lists values that identify a Pivot Grid Control’s elements.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.Cell">
      <summary>
        <para>The test point belongs to a cell.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.FixedLeftDiv">
      <summary>
        <para>The test point belongs to the left fixed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.FixedRightDiv">
      <summary>
        <para>The test point belongs to the right fixed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.HeadersArea">
      <summary>
        <para>The test point belongs to the header area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.None">
      <summary>
        <para>The test point does not belong to any visual element or is outside the PivotGridControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.Value">
      <summary>
        <para>The test point belongs to a field value.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenu">
      <summary />
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.#ctor(DevExpress.XtraPivotGrid.PivotGridControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenu"/> class with specified settings.</para>
      </summary>
      <param name="pivotGrid"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Dispose">
      <summary />
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Find(DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary />
      <param name="tag"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Find(DevExpress.XtraEditors.Controls.StringId)">
      <summary />
      <param name="tag"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Find(DevExpress.XtraPivotGrid.PivotContextMenuIds)">
      <summary />
      <param name="tag"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Find(DevExpress.XtraPivotGrid.PivotGroupInterval)">
      <summary />
      <param name="tag"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Hide(DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Hide(DevExpress.XtraEditors.Controls.StringId)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Hide(DevExpress.XtraPivotGrid.PivotContextMenuIds)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Hide(DevExpress.XtraPivotGrid.PivotGroupInterval)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Remove(DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Remove(DevExpress.XtraEditors.Controls.StringId)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Remove(DevExpress.XtraPivotGrid.PivotContextMenuIds)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenu.Remove(DevExpress.XtraPivotGrid.PivotGroupInterval)">
      <summary />
      <param name="tag"></param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs">
      <summary>
        <para>Provides data for the DevExpress.XtraPivotGrid.PivotGridControl.ShowMenu event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.XtraPivotGrid.PivotGridMenu,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="viewInfo"></param>
      <param name="menuType"></param>
      <param name="menu"></param>
      <param name="field"></param>
      <param name="area"></param>
      <param name="point"></param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase">
      <summary>
        <para>The base class for classes providing data for the PivotGrid control’s menu handling events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfoBase,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.XtraPivotGrid.PivotGridMenu,DevExpress.XtraPivotGrid.PivotGridFieldBase,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="viewInfo"></param>
      <param name="menuType"></param>
      <param name="menu"></param>
      <param name="field"></param>
      <param name="area"></param>
      <param name="point"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Allow">
      <summary>
        <para>Gets or sets whether to allow the action.</para>
      </summary>
      <value>true to allow the action; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Area">
      <summary>
        <para>Gets the area of the field whose header or value has been right-clicked by a user to invoke the context menu.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value which specifies the area of the field whose header or value has been right-clicked by an end-user to invoke the context menu.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Field">
      <summary>
        <para>Gets the field whose header has been right-clicked by an end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object which represents the field whose header has been right-clicked by an end-user.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Menu">
      <summary>
        <para>Gets or sets the Pivot Grid’s context menu.</para>
      </summary>
      <value>An object that specifies the Pivot Grid’s context menu.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.MenuType">
      <summary>
        <para>Gets the type of the invoked menu.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuType"/> enumeration member which specifies the menu’s type.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Point">
      <summary>
        <para>Gets or sets the location at which the context menu has been invoked.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure which specifies the point at which the end-user right-clicked to invoke the menu. The point’s coordinates are set relative to the Pivot Grid Control’s top-left corner.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuEventHandler">
      <summary>
        <para>Represents a method that will handle the DevExpress.XtraPivotGrid.PivotGridControl.ShowMenu event.</para>
      </summary>
      <param name="sender">The event sender. Identifies the PivotGrid control that raised an event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.XtraPivotGrid.PivotGridMenu,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point,DevExpress.Utils.Menu.DXMenuItem)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="viewInfo"></param>
      <param name="menuType"></param>
      <param name="menu"></param>
      <param name="field"></param>
      <param name="area"></param>
      <param name="point"></param>
      <param name="item"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs.Field">
      <summary>
        <para>Gets the field whose header has been right-clicked by an end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> field whose header has been right-clicked.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs.HitInfo">
      <summary>
        <para>Gets details on the grid elements located at the point where an end-user has right-clicked to invoke the context menu.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> object which contains information about the grid elements located at the point that has been clicked.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase">
      <summary>
        <para>The base class for classes that provide data for the PivotGrid control’s menu item clicking events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfoBase,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.XtraPivotGrid.PivotGridMenu,DevExpress.XtraPivotGrid.PivotGridFieldBase,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point,DevExpress.Utils.Menu.DXMenuItem)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="viewInfo"></param>
      <param name="menuType"></param>
      <param name="menu"></param>
      <param name="field"></param>
      <param name="area"></param>
      <param name="point"></param>
      <param name="item"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase.Item">
      <summary>
        <para>Gets the menu item that has been clicked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object which specifies the menu item that has been clicked.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick"/> event.</para>
      </summary>
      <param name="sender">The event sender. Identifies the PivotGrid control that raised an event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuType">
      <summary>
        <para>Lists values that specify the Pivot Grid Control’s context menu types.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.Cell">
      <summary>
        <para>Corresponds to the context menu, allowing end-users to apply conditional formatting to data cells (when the <see cref="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableFormatRulesMenu"/> option is enabled).</para>
        <para></para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.FieldValue">
      <summary>
        <para>Corresponds to the field value context menu.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.Header">
      <summary>
        <para>Corresponds to the field header context menu.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.HeaderArea">
      <summary>
        <para>Corresponds to the header area context menu.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.HeaderSummaries">
      <summary>
        <para>Corresponds to the menu allowing an end-user to select a summary type for a data field (when the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldOptions.AllowRunTimeSummaryChange"/> option is enabled).</para>
        <para></para>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior">
      <summary>
        <para>Provides behavior options for the control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.#ctor(System.EventHandler)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior"/> class with the specified change notifications delegate.</para>
      </summary>
      <param name="optionsChanged">A delegate that will receive change notifications.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.ApplyBestFitOnFieldDragging">
      <summary>
        <para>Gets or sets whether the Best-Fit feature is automatically applied to a field after it has been dragged and dropped at another location.</para>
      </summary>
      <value>true if the Best-Fit feature is applied to a field after it has been dragged and dropped at another location; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.BestFitConsiderCustomAppearance">
      <summary>
        <para>Gets or sets whether custom appearance settings should be taken into account when calculating minimum column width for Best Fit.</para>
      </summary>
      <value>true if custom appearance settings should be taken into account when calculating minimum column width for Best Fit; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.EditorShowMode">
      <summary>
        <para>Gets or sets how in-place editors are activated.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.EditorShowMode"/> value that specifies how in-place editors are activated.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.ExpressionEditorMode">
      <summary>
        <para>Gets or sets the type of editor that end-users can invoke to modify expressions for unbound fields.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraEditors.ExpressionEditorMode"/> object that specifies the editor type.</value>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.GetEditorShowMode">
      <summary>
        <para>Returns the actual editor show mode based on the <see cref="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.EditorShowMode"/> property.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.EditorShowMode"/> value that specifies the actual editor show mode.</returns>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.HorizontalScrolling">
      <summary>
        <para>Gets or sets a value which specifies the Pivot Grid Control’s behavior when it is scrolled horizontally.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridScrolling"/> enumeration member which specifies the Pivot Grid Control’s behavior when it’s scrolled horizontally.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.RepaintGridOnFocusedCellChanged">
      <summary>
        <para>Gets or sets whether the grid control is repainted when focus is moved from one cell to another.</para>
      </summary>
      <value>true if the grid control is repainted when focus is moved from one cell to another; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx">
      <summary>
        <para>Provides customization options for a PivotGrid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.#ctor(System.EventHandler)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx"/> class with the specified settings.</para>
      </summary>
      <param name="optionsChanged">A delegate that will handle firing of the event, as a result of changing the object’s properties.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.#ctor(System.EventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx"/> class with the specified settings.</para>
      </summary>
      <param name="optionsChanged">A delegate that will handle firing of the event, as a result of changing the object’s properties.</param>
      <param name="viewBagOwner">An IViewBagOwner object that is used to initialize the created object.</param>
      <param name="objectPath">A string value that is used to initialize the created object.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.AllowEdit">
      <summary>
        <para>Gets or sets whether data editing is enabled.</para>
      </summary>
      <value>true if data editing is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.AllowResizing">
      <summary>
        <para>Gets or sets whether end-users are allowed to resize the Pivot Grid Control’s elements.</para>
      </summary>
      <value>true if end-users are allowed to resize the Pivot Grid Control’s elements; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.CustomizationFormSearchBoxVisible">
      <summary>
        <para>Gets or sets whether the customization form displays the search box that allows the user to filter the list of available fields.</para>
      </summary>
      <value>True if the Customization Form displays the search box; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.CustomizationFormSnapMode">
      <summary>
        <para>Gets or sets whether or not the Customization Form can automatically snap to specific targets.</para>
      </summary>
      <value>A SnapMode enumeration value that specifies whether or not the Customization Form can automatically snap to specific targets.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilter">
      <summary>
        <para>Contains Pivot Grid’s filter options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsFilter.#ctor(System.EventHandler)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilter"/> class with specified settings.</para>
      </summary>
      <param name="optionsChanged"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilter.AllowMRUFilterList">
      <summary>
        <para>Gets or sets whether users are allowed to invoke the MRU Filter List.</para>
      </summary>
      <value>true if users are allowed to invoke the MRU Filter List; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilter.DefaultFilterEditorView">
      <summary>
        <para>Gets or sets the how a user can edit criteria in the Filter Editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.FilterEditorViewMode"/> value that defines the Filter Editor view.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilter.MRUFilterListCount">
      <summary>
        <para>Gets or sets the MRU Filter List capacity.</para>
      </summary>
      <value>An integer value that specifies the MRU Filter List capacity.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilter.MRUFilterListPopupCount">
      <summary>
        <para>Gets or sets the maximum number of items the Pivot Grid’s MRU Filter List can display at once.</para>
      </summary>
      <value>An integer value that determines the maximum number of items the Pivot Grid’s MRU Filter List can display at once.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilter.ShowCustomFunctions">
      <summary>
        <para>Gets or sets whether to show custom function-based filters.</para>
      </summary>
      <value>True to show custom function-based filters; False to not show them; Default to enable/disable custom function-based filters depending on the global <see cref="F:DevExpress.XtraEditors.WindowsFormsSettings.DefaultSettingsCompatibilityMode"></see> setting.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup">
      <summary>
        <para>Contains options that affect the appearance and behavior of filter dropdown windows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.#ctor(DevExpress.XtraPivotGrid.PivotOptionsFilterEventHandler)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup"/> class.</para>
      </summary>
      <param name="optionsChanged">The <see cref="E:DevExpress.XtraPivotGrid.PivotGridOptionsFilterBase.OptionsChanged"/> event handler.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowContextMenu">
      <summary>
        <para>Gets or sets whether right-clicking a filter popup window invokes a context menu.</para>
      </summary>
      <value>true if a context menu is enabled for a filter popup; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowFilterTypeChanging">
      <summary>
        <para>Gets or sets whether the filter’s type can be changed at runtime, via a combobox displayed at the top of the filter dropdown window.</para>
      </summary>
      <value>true if the filter’s type can be changed at runtime; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowIncrementalSearch">
      <summary>
        <para>Gets or sets whether the incremental searching feature is enabled, allowing an end-user to locate an item in the dropdown by typing the item’s initial characters.</para>
      </summary>
      <value>true if the incremental searching feature is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowMultiSelect">
      <summary>
        <para>Gets or sets whether multiple item selection is enabled.</para>
      </summary>
      <value>true if multiple item selection is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.FieldFilterPopupMode">
      <summary>
        <para>Gets or sets the field’s filter popup mode for all fields.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.FieldFilterPopupMode"/> value that specifies the field’s filter popup mode for all fields. Default value is interpreted as <see cref="F:DevExpress.XtraPivotGrid.FieldFilterPopupMode.Excel"/>.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.IsRadioMode">
      <summary>
        <para>Gets or sets whether an end-user is allowed to select only a single item in the filter drop-down.</para>
      </summary>
      <value>true if an end-user is allowed to select only a single item in the filter drop-down; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.ShowToolbar">
      <summary>
        <para>Gets or sets whether toolbars should be displayed in filter popup windows.</para>
      </summary>
      <value>true to display toolbars in filter popup windows; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.ToolbarButtons">
      <summary>
        <para>Gets or sets which buttons should be displayed in filter popup toolbars.</para>
      </summary>
      <value>A set of <see cref="T:DevExpress.XtraPivotGrid.FilterPopupToolbarButtons"/> flags that identify buttons displayed in filter popup toolbars.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsHint">
      <summary>
        <para>Provides hint options for a PivotGrid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsHint.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsHint"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsHint.ShowCellHints">
      <summary>
        <para>Gets or sets whether hints are displayed for cells with truncated content.</para>
      </summary>
      <value>true to display hints for cells with truncated content; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsHint.ShowHeaderHints">
      <summary>
        <para>Gets or sets whether hints are displayed for field headers that have truncated captions.</para>
      </summary>
      <value>true to display hints for field headers that have truncated captions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsHint.ShowValueHints">
      <summary>
        <para>Gets or sets whether hints are displayed for field values with truncated content.</para>
      </summary>
      <value>true to display hints for field values with truncated content; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsMenu">
      <summary>
        <para>Provides the context menu options for the control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsMenu"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableFieldValueMenu">
      <summary>
        <para>Gets or sets whether end-users can invoke the field value context menu.</para>
      </summary>
      <value>true if end-users can right-click the Field Value to invoke its context menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableFormatRulesMenu">
      <summary>
        <para>Gets or sets a value indicating whether the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatRules"/> context menu is allowed and can be invoked by an end-user.</para>
      </summary>
      <value>true if the context menu is allowed and can be invoked by an end-user; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableHeaderAreaMenu">
      <summary>
        <para>Gets or sets whether end-users can invoke the header area context menu.</para>
      </summary>
      <value>true if end-users can right-click the header area to invoke its context menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableHeaderMenu">
      <summary>
        <para>Gets or sets whether end-users can invoke the field header context menu.</para>
      </summary>
      <value>true if end-users can right-click the field header to invoke its context menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnablePinColumnMenu">
      <summary>
        <para>Gets or sets whether users can invoke the column header context menu.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether users can invoke the column header context menu.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.ShowDateTimeGroupIntervalItems">
      <summary>
        <para>Gets or sets what combination of DateTime group intervals the popup menu displays.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.DateTimeGroupIntervals"/> enumeration member that specifies a set of DateTime group intervals for display.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsView">
      <summary>
        <para>Provides view options for the PivotGrid controls.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsView.#ctor(System.EventHandler)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsView"/> class with specified settings.</para>
      </summary>
      <param name="optionsChanged"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.AllowGlyphSkinning">
      <summary>
        <para>Gets or sets whether <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> icons should be painted according the control’s foreground color.</para>
      </summary>
      <value>true, if <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> icons should be painted according the control’s foreground colors; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.AllowHtmlDrawFieldValues">
      <summary>
        <para>Gets or sets whether the text displayed in the field values support HTML tags.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.AllowHtmlDrawHeaders">
      <summary>
        <para>Gets or sets whether HTML formatting can be applied to column and band captions.</para>
      </summary>
      <value>true if HTML formatting can be applied to column and band captions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.FilterCriteriaDisplayStyle">
      <summary>
        <para>Gets or sets the display style of filter conditions in the Filter Panel, MRU Filter List and built-in Filter Editor.</para>
      </summary>
      <value>The display style of filter conditions in the Filter Panel and built-in Filter Editor. When the FilterCriteriaDisplayStyle property is set to Default, the actual display style is specified by the static <see cref="P:DevExpress.XtraEditors.WindowsFormsSettings.FilterCriteriaDisplayStyle"/> property.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.FilterSeparatorBarPadding">
      <summary>
        <para>Gets or sets a value which specifies the distance from the separator line to the adjacent areas.</para>
      </summary>
      <value>An integer value which specifies the distance from the separator line to the adjacent areas.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.FixedColumnLocation">
      <summary>
        <para>Gets or sets a pinned column’s location.</para>
      </summary>
      <value>A DevExpress.XtraPivotGrid.FixedColumnStyle object that specifies whether to pin columns to the left or the right edge.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.HeaderFilterButtonShowMode">
      <summary>
        <para>Gets or sets how filter buttons are rendered.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.FilterButtonShowMode"/> enumeration value that specifies how filter buttons are rendered.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.HideFixedColumnsFromFieldValues">
      <summary>
        <para>Gets or sets whether pinned columns are displayed in the scrollable area.</para>
      </summary>
      <value>true if pinned columns are hidden in the scrollable area; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.ShowButtonMode">
      <summary>
        <para>Gets or sets a value specifying when buttons of in-place editors are shown in cells.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum"/> value that specifies the current display mode for cell buttons.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridScrolling">
      <summary>
        <para>Contains values that specify how the PivotGrid control is scrolled horizontally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridScrolling.CellsArea">
      <summary>
        <para>Specifies that cells within the Data Area are scrolled when the control is scrolled horizontally. The filter fields and column fields are not moved.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridScrolling.Control">
      <summary>
        <para>Specifies that the filter fields and column fields are scrolled when the control is scrolled horizontally.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition">
      <summary>
        <para>A single style format condition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToCell">
      <summary>
        <para>Gets or sets whether the appearance settings can be applied to data cells.</para>
      </summary>
      <value>true to apply the appearance settings to data cells; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToCustomTotalCell">
      <summary>
        <para>Gets or sets whether the appearance settings are applied to custom total cells.</para>
      </summary>
      <value>true to apply the appearance settings to custom total cells; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToGrandTotalCell">
      <summary>
        <para>Gets or sets whether the appearance settings are applied to grand total cells.</para>
      </summary>
      <value>true to apply the appearance settings to grand total cells; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToTotalCell">
      <summary>
        <para>Gets or sets whether the appearance settings are applied to automatic total cells.</para>
      </summary>
      <value>true to apply the appearance settings to automatic total cells; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.Field">
      <summary>
        <para>Gets or sets the field which identifies the columns whose values are used in conditional formatting.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object representing the field which identifies the columns whose values are used in conditional formatting.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.FieldName">
      <summary>
        <para>Gets or sets the name of the field which identifies the columns whose values are used in conditional formatting.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the field which identifies the columns whose values are used in conditional formatting.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.PivotGrid">
      <summary>
        <para>Gets the PivotGrid control which owns the current style format condition.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> object which represents the PivotGrid control that owns the current style format condition.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGridValueHitTest">
      <summary>
        <para>Lists values that specify which field value cell element is located under the test point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridValueHitTest.ExpandButton">
      <summary>
        <para>The expand/collapse button is located under the test point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotGridValueHitTest.None">
      <summary>
        <para>No element is located under the test point.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGroupEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.GroupFilterChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotGroupEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGroupEventArgs"/> class.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.XtraPivotGrid.PivotGridGroup"/> object that represents the group for which the event has been raised. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGroupEventArgs.Group"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotGroupEventArgs.Group">
      <summary>
        <para>Gets the group for which the event has been raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridGroup"/> object that represents the group for which the event has been raised.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotGroupEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.GroupFilterChanged"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotGroupEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotLeftTopCellChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.LeftTopCellChanged"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotLeftTopCellChangedEventArgs.NewValue">
      <summary>
        <para>Gets the coordinates of the currently left top visible cell.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that represents the coordinates of the currently left top visible cell.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotLeftTopCellChangedEventArgs.OldValue">
      <summary>
        <para>Gets the coordinates of the cell that was visible at the top left before scrolling.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that represents the coordinates of the cell that was visible at the top left before scrolling.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotOlapExceptionEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPException"/> event.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotOlapExceptionEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPException"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotOlapExceptionEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotOlapQueryDataEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPQueryData"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotOlapQueryDataEventArgs.#ctor(System.String)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="query"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotOlapQueryDataEventArgs.MDXQuery">
      <summary>
        <para>Gets or sets the MDX query.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the MDX query.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotQueryExceptionEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.QueryException"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotQueryExceptionEventArgs.ErrorPanelText">
      <summary>
        <para>Gets or sets a text from the error panel.</para>
      </summary>
      <value>A string that contains the exception message.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotQueryExceptionEventArgs.Exception">
      <summary>
        <para>Gets an exception that has been thrown.</para>
      </summary>
      <value>A <see cref="T:System.Exception"/> that has been thrown.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotQueryExceptionEventArgs.Handled">
      <summary>
        <para>Gets or sets whether the default exception handling actions should be performed.</para>
      </summary>
      <value>true, to perform the default exception handling actions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotQueryExceptionEventArgs.ShowErrorPanel">
      <summary>
        <para>Gets or sets whether to show the error panel.</para>
      </summary>
      <value>true, to show the panel; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotQueryExceptionEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.QueryException"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotQueryExceptionEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum">
      <summary>
        <para>Contains values that specify which cells of a data field display editor buttons.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.Default">
      <summary>
        <para>This option is equivalent to the ShowOnlyInEditor option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.ShowAlways">
      <summary>
        <para>Editor buttons are displayed for all cells.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.ShowForFocusedCell">
      <summary>
        <para>Editor buttons are only displayed for the focused cell.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.ShowOnlyInEditor">
      <summary>
        <para>Editor buttons are only displayed when a cell editor is active.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotUserActionEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.UserActionChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotUserActionEventArgs.#ctor(DevExpress.XtraPivotGrid.UserAction)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotUserActionEventArgs"/> class.</para>
      </summary>
      <param name="userAction">A <see cref="T:DevExpress.XtraPivotGrid.UserAction"/> object that specifies the end-user action on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PivotUserActionEventArgs.UserAction">
      <summary>
        <para>Gets the end-user action performed on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.UserAction"/> enumeration value that identifies the end-user action performed on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotUserActionEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.UserActionChanged"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraPivotGrid.PivotUserActionEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotXlsExportOptions">
      <summary>
        <para>Contains Pivot Grid options that define how a document is exported to XLS format in the data-aware export mode.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotXlsExportOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotXlsExportOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotXlsExportOptions.#ctor(DevExpress.XtraPrinting.TextExportMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotXlsExportOptions"/> class with specified text export mode.</para>
      </summary>
      <param name="mode">A  enumeration value, specifying the text export mode in the resulting XLS document.</param>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotXlsExportOptions.CustomizeCell">
      <summary>
        <para>Allows you to customize a PivotGrid’s cell in the exported XLS document.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PivotXlsxExportOptions">
      <summary>
        <para>Contains Pivot Grid options that define how a document is exported to XLSX format in the data-aware export mode.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotXlsxExportOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.#ctor(DevExpress.XtraPrinting.TextExportMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotXlsxExportOptions"/> class with specified text export mode.</para>
      </summary>
      <param name="mode">A  enumeration value, specifying the text export mode in the resulting XLSX document.</param>
    </member>
    <member name="E:DevExpress.XtraPivotGrid.PivotXlsxExportOptions.CustomizeCell">
      <summary>
        <para>Allows you to customize a PivotGrid’s cell in the exported XLSX document.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.XtraPivotGrid.PivotGridMenu,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="viewInfo"></param>
      <param name="menuType"></param>
      <param name="menu"></param>
      <param name="field"></param>
      <param name="area"></param>
      <param name="point"></param>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PopupMenuShowingEventArgs.Field">
      <summary>
        <para>Gets the field whose header or value has been right-clicked by an end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the field whose header or value has been right-clicked by an end-user.</value>
    </member>
    <member name="P:DevExpress.XtraPivotGrid.PopupMenuShowingEventArgs.HitInfo">
      <summary>
        <para>Gets info about the point where the context menu should be invoked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> object that contains info about the point where the context menu should be invoked.</value>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.PopupMenuShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.PopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">The event source.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.RankBinding">
      <summary>
        <para>Defines the calculation of a ranking in the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.RankBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.RankBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.RankBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.RankType,DevExpress.XtraPivotGrid.PivotSortOrder)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.RankBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="rankType">The rank type.</param>
      <param name="order">The sort order</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.RunningTotalBinding">
      <summary>
        <para>Defines the calculation that aggregates values cumulatively in the window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.RunningTotalBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.RunningTotalBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.RunningTotalBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.RunningTotalBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="summaryType">A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration member that specifies the summary function type.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.RunningTotalBinding.#ctor(DevExpress.XtraPivotGrid.DataBindingBase,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.CalculationDirection,DevExpress.Data.PivotGrid.PivotSummaryType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.RunningTotalBinding"/> class with specified settings.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraPivotGrid.DataBindingBase"/> descendant that provides the source data.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="direction">A <see cref="T:DevExpress.XtraPivotGrid.CalculationDirection"/> enumeration member that specifies the direction in which the values are retrieved.</param>
      <param name="summaryType">A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration member that specifies the summary function type.</param>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.UserAction">
      <summary>
        <para>Lists values that specify end-user actions on the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.FieldDrag">
      <summary>
        <para>An end-user drags the field header.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.FieldFilter">
      <summary>
        <para>An end-user invokes the filter drop-down.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.FieldResize">
      <summary>
        <para>An end-user resizes the column.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.FieldUnboundExpression">
      <summary>
        <para>An end-user invokes the Expression Editor dialog.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.MenuOpen">
      <summary>
        <para>An end-user invokes a context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.None">
      <summary>
        <para>No action.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraPivotGrid.UserAction.Prefilter">
      <summary>
        <para>An end-user invokes a Filter Editor.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraPivotGrid.WindowExpressionBinding">
      <summary>
        <para>Defines the calculation that uses a custom string expression with window functions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.WindowExpressionBinding.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.WindowExpressionBinding"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.WindowExpressionBinding.#ctor(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.WindowExpressionBinding"/> class with specified settings.</para>
      </summary>
      <param name="criteria"></param>
      <param name="mode"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.WindowExpressionBinding.#ctor(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.CalculationDirection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.WindowExpressionBinding"/> class with specified settings.</para>
      </summary>
      <param name="criteria"></param>
      <param name="mode"></param>
      <param name="direction"></param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.WindowExpressionBinding.#ctor(System.String,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.WindowExpressionBinding"/> class with specified settings.</para>
      </summary>
      <param name="expression">A string expression with window functions.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
    </member>
    <member name="M:DevExpress.XtraPivotGrid.WindowExpressionBinding.#ctor(System.String,DevExpress.XtraPivotGrid.CalculationPartitioningCriteria,DevExpress.XtraPivotGrid.CalculationDirection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.WindowExpressionBinding"/> class with specified settings.</para>
      </summary>
      <param name="expression">A string expression with window functions.</param>
      <param name="mode">A <see cref="T:DevExpress.XtraPivotGrid.CalculationPartitioningCriteria"/> enumeration member that specifies a window in a window calculation.</param>
      <param name="direction">A <see cref="T:DevExpress.XtraPivotGrid.CalculationDirection"/> enumeration member that specifies the direction in which the values are processed.</param>
    </member>
  </members>
</doc>