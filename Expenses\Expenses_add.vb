﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class Expenses_add

    Private Sub inserte_Click(sender As Object, e As EventArgs) Handles inserte.Click

        If Expenses_name.Text = "" Then
            MsgBox("أدخل اسم المصرف")
            Exit Sub
        End If
        For i2 = 0 To dgv.Rows.Count - 1
            If dgv.Rows(i2).Cells(0).Value = Expenses_name.Text Then
                MsgBox("المصروف موجود مسبقا")
                Exit Sub
            End If
        Next
        dgv.Rows.Add()
        dgv.Rows(dgv.Rows.Count - 1).Cells(0).Value = Expenses_name.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(1).Value = Expenses_price.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(2).Value = Expenses_deital.Text
        plus_invoice()
    End Sub
    Sub plus_invoice()
        Dim sumr As Decimal = 0.0
        For i = 0 To dgv.Rows.Count - 1
            sumr = (sumr) + Val(dgv.Rows(i).Cells(1).Value)
        Next
        total_Expenses.Text = Format(sumr, "0.0")
        Expenses_count.Text = dgv.RowCount

    End Sub
    Private Sub total_damage_TextChanged(sender As Object, e As EventArgs) Handles total_Expenses.TextChanged
        amount_string.Text = NoToTxt(total_Expenses.Text, "جنية", "قرش") & " " & "فقط لاغير"
        show_total.Text = total_Expenses.Text
    End Sub
    Private Sub item_name_KeyDown(sender As Object, e As KeyEventArgs)

        If e.KeyCode = Keys.Enter Then
            Expenses_price.Focus()
        End If
    End Sub

    Private Sub item_count_KeyDown(sender As Object, e As KeyEventArgs) Handles Expenses_price.KeyDown
        If e.KeyCode = Keys.Enter Then
            Expenses_deital.Focus()
        End If
    End Sub
    Private Sub item_price_KeyDown(sender As Object, e As KeyEventArgs) Handles Expenses_deital.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            Expenses_name.Focus()
            plus_invoice()
        End If
    End Sub
    Private Sub dgv_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv.CellPainting
        If e.ColumnIndex = 3 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub
    Private Sub dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv.CellClick
        If e.ColumnIndex = 3 Then
            dgv.Rows.Remove(dgv.CurrentRow)
            plus_invoice()
        End If
    End Sub

    '=========================== show data
    Public Sub show_data(x)
        dgv.Rows.Clear()
        new_btn_Click(Nothing, Nothing)
        Dim sql = "select * from Expenses_add where Expenses_number=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لم يتم العثور علي بيانات")
        Else
            Dim dr = dt.Rows(0)
            store.Text = dr!store
            Expenses_number.Text = dr!Expenses_number
            Expenses_date.Value = dr!Expenses_date
            Expenses_note.Text = dr!Expenses_note
            Expenses_count.Text = dr!Expenses_count
            total_Expenses.Text = dr!total_Expenses
            type_pay.Text = dr!type_pay
            amount_string.Text = dr!total_string
            '========================================
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            adp = New SqlDataAdapter("select * from Expenses_list where Expenses_number=N'" & (x) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                dgv.Rows.Add()
                dgv.Rows(s).Cells(0).Value = dt.Rows(s).Item("Expenses_name")
                dgv.Rows(s).Cells(1).Value = dt.Rows(s).Item("Expenses_price")
                dgv.Rows(s).Cells(2).Value = dt.Rows(s).Item("Expenses_deital")
            Next
            '========================================

            ' تحميل مركز التكلفة
            Try
                Dim costCenterSql = "SELECT cost_center_id FROM Expenses_add WHERE Expenses_number = N'" & x & "'"
                Dim costCenterAdp As New SqlDataAdapter(costCenterSql, sqlconn)
                Dim costCenterDs As New DataSet
                costCenterAdp.Fill(costCenterDs)
                Dim costCenterDt = costCenterDs.Tables(0)
                If costCenterDt.Rows.Count > 0 AndAlso Not IsDBNull(costCenterDt.Rows(0)("cost_center_id")) Then
                    CostCenterCombo.EditValue = costCenterDt.Rows(0)("cost_center_id")
                Else
                    CostCenterCombo.EditValue = DBNull.Value
                End If
                costCenterAdp.Dispose()
                costCenterDs.Dispose()
                costCenterDt.Dispose()
            Catch ex As Exception
                ' في حالة عدم وجود جدول مراكز التكلفة، نتجاهل الخطأ
                CostCenterCombo.EditValue = DBNull.Value
            End Try

            save_btn.Enabled = False
            edit_btn.Enabled = True
            delet_btn.Enabled = True
        End If
    End Sub
    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        fill_store()
        fill_treasury_name()
        fill_item()
        Expenses_note.Text = ""
        store.SelectedIndex = 0
        type_pay.SelectedIndex = 0
        Expenses_number.Text = getlastcode("Expenses_add", "Expenses_number") + 1
        Expenses_date.Text = Now.Date
        Expenses_name.Text = ""
        Expenses_price.Text = 0
        Expenses_deital.Text = ""
        store.SelectedIndex = 0
        Expenses_count.Text = 0
        total_Expenses.Text = 0
        show_total.Text = 0
        dgv.DataSource = Nothing
        dgv.Rows.Clear()
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        save_btn.Enabled = True

        ' إعادة تعيين مركز التكلفة
        Try
            CostCenterCombo.EditValue = DBNull.Value
        Catch ex As Exception
            ' في حالة عدم وجود جدول مراكز التكلفة، نتجاهل الخطأ
        End Try
    End Sub
    Sub fill_item()
        Expenses_name.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Expense_item order by Expense_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            Expenses_name.Properties.Items.Add(dt.Rows(i).Item("Expense_name"))
        Next
    End Sub
    Sub fill_treasury_name()
        Try
            type_pay.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from treasury_name", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                type_pay.Items.Add(dt.Rows(i).Item("treasury_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try

    End Sub
    Sub fill_store()

        store.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from store", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            store.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub
    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        printcode.Text = getlastcode("Expenses_add", "Expenses_number") + 1
        If dgv.Rows.Count = 0 Then
            MsgBox("يجب ادخال مصروف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If
        Dim sql = "select * from Expenses_add where Expenses_number=N'" & (Expenses_number.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            ' damage_number.Text = getlastcode("damage", "damage_number") + 1
        Else
            Dim dr = dt.NewRow
            dr!store = store.Text
            dr!Expenses_number = Expenses_number.Text
            dr!Expenses_date = Expenses_date.Value
            dr!Expenses_note = Expenses_note.Text
            dr!Expenses_count = Expenses_count.Text
            dr!total_Expenses = total_Expenses.Text
            dr!type_pay = type_pay.Text
            dr!code_print = 1
            dr!user_Expenses = XtraForm1.user_name.Text
            dr!total_string = amount_string.Text

            ' حفظ مركز التكلفة مع المصروف
            Try
                If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                    dr!cost_center_id = Convert.ToInt32(CostCenterCombo.EditValue)
                Else
                    dr!cost_center_id = DBNull.Value
                End If
            Catch ex As Exception
                ' في حالة عدم وجود عمود مركز التكلفة، نتجاهل الخطأ
                dr!cost_center_id = DBNull.Value
            End Try

            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        '    '============حفظ السحل================

        adp = New SqlDataAdapter("select * from Expenses_list", sqlconn)
        ds = New DataSet
        adp.Fill(ds)
        dt = ds.Tables(0)
        For i = 0 To dgv.Rows.Count - 1
            Dim Dr = dt.NewRow
            Dr!Expenses_number = Expenses_number.Text
            Dr!store = store.Text
            Dr!Expenses_name = dgv.Rows(i).Cells(0).Value
            Dr!Expenses_price = dgv.Rows(i).Cells(1).Value
            Dr!Expenses_deital = dgv.Rows(i).Cells(2).Value
            dt.Rows.Add(Dr)
            Dim cmdbuilder As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Next
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()

        adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'", sqlconn)
        ds = New DataSet
        adp.Fill(ds)
        dt = ds.Tables(0)
        Dim dr6 = dt.Rows(0)
        dr6!balace = Val(dr6!balace) - Val(show_total.Text)
        Dim cmd6 As New SqlCommandBuilder(adp)
        adp.Update(dt)
        treasury_pay(type_pay.Text, "مصروفات", Expenses_date.Value, Now.ToString("hh:mm:ss:tt"), "مصروفات", XtraForm1.user_name.Text, 0, Val(total_Expenses.Text), Val(treasury_balace.Text) - Val(total_Expenses.Text), Expenses_number.Text)

        new_btn_Click(Nothing, Nothing)
        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.Background)
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
         Dim sql = "select * from Expenses_add where Expenses_number=N'" & Expenses_number.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            sql = "select * from Expenses_list where Expenses_number=N'" & Expenses_number.Text.Trim() & "'"
            adp = New SqlDataAdapter(sql, sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
        delet_treausy()
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        save_btn.Enabled = True

    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        If MsgBox("هل انت متأكد بحذف الفاتورة المحدد؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        Dim sql = "select * from Expenses_add where Expenses_number=N'" & Expenses_number.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            sql = "select * from Expenses_list where Expenses_number=N'" & Expenses_number.Text.Trim() & "'"
            adp = New SqlDataAdapter(sql, sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
      delet_treausy()
        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.Background)
        new_btn_Click(Nothing, Nothing)
    End Sub
    Sub delet_treausy()

        Dim sql = "select * from treasury_pay where movement= 'مصروفات' and code_trans=N'" & Expenses_number.Text & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("فشل في جلب البيانات")
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & type_pay.Text & "'", sqlconn)
        ds = New DataSet
        adp.Fill(ds)
        dt = ds.Tables(0)
        Dim dr6 = dt.Rows(0)
        dr6!balace = Format(Val(dr6!balace) + total_Expenses.Text, "0.0")
        Dim cmd6 As New SqlCommandBuilder(adp)
        adp.Update(dt)

    End Sub
    Private Sub item_name_KeyDown_1(sender As Object, e As KeyEventArgs) Handles Expenses_name.KeyDown
        If e.KeyCode = Keys.Enter Then
            Expenses_price.Focus()
        End If
    End Sub

    Private Sub damage_item_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            new_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            edit_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            delet_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F5 Then
            print_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F6 Then
            wait_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from Expense_print where Expenses_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "Expense_a4.repx"), True)
        rep.DataSource = dt
        rep.ShowPrintMarginsWarning = False
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            If XtraForm1.paper2.Text = 1 Then
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_defult_2.Text)
                End Using
            End If

            If XtraForm1.paper2.Text = 2 Then
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_defult_2.Text)
                End Using
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_defult_2.Text)
                End Using
            End If

        End If
    End Sub
    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub
  
    Private Sub wait_btn_Click(sender As Object, e As EventArgs)

    End Sub

    Private Sub Expenses_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تحميل مراكز التكلفة
        Try
            CostCenterHelper.LoadActiveCostCenters(CostCenterCombo, True)
        Catch ex As Exception
            ' في حالة عدم وجود جدول مراكز التكلفة بعد، نخفي العناصر
            CostCenterCombo.Visible = False
            LabelCostCenter.Visible = False
        End Try

        new_btn_Click(Nothing, Nothing)

    End Sub


    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged
        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)

        Dim dr = dt.Rows(0)
        treasury_balace.Text = dr!balace

    End Sub
    Function gettreasury(subname) As String

        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function

    Private Sub Label37_Click(sender As Object, e As EventArgs) Handles Label37.Click
        Expense_item.Show()
    End Sub

    Private Sub Label46_Click(sender As Object, e As EventArgs) Handles Label46.Click
        fill_item()
    End Sub
End Class