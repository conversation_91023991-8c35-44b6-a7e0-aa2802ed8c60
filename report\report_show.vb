﻿Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Public Class report_show
   
    Private Sub cus_balace_Click(sender As Object, e As EventArgs) Handles cus_balace.Click
        Dim f As cus_balace = New cus_balace()
        f.Text = "أرصدة العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim f As cus_Balance = New cus_Balance()
        f.Text = "ميزان مراجعة العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        Dim f As cus_minus = New cus_minus()
        f.Text = "أرصدة العملاء بالسالب"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        Dim f As cus_bestearn = New cus_bestearn()
        f.Text = "الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        Dim f As cus_litleearn = New cus_litleearn()
        f.Text = "الاقل ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        Dim f As Statistic_cus_earn = New Statistic_cus_earn()
        f.Text = "مقارنة بح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        Dim f As group_best_earn = New group_best_earn()
        f.Text = "المجموعة الاكثر ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub


    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        Dim f As group_cus_earn = New group_cus_earn()
        f.Text = "مقارنة بح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        Dim f As group_litle_earn = New group_litle_earn()
        f.Text = "المجموعة الاقل ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton11_Click(sender As Object, e As EventArgs) Handles SimpleButton11.Click
        cus_trans_show.ShowDialog()
    End Sub

    Private Sub SimpleButton10_Click(sender As Object, e As EventArgs) Handles SimpleButton10.Click
        Dim f As customer_Detailed = New customer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton12_Click(sender As Object, e As EventArgs) Handles SimpleButton12.Click
        Dim f As Statistic_customer = New Statistic_customer()
        f.Text = "احصائيات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton9_Click(sender As Object, e As EventArgs) Handles SimpleButton9.Click
        Dim f As cus_item = New cus_item()
        f.Text = "اصناف عميل"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton15_Click(sender As Object, e As EventArgs) Handles SimpleButton15.Click
        Dim f As city_best_earn = New city_best_earn()
        f.Text = "المدينة الاكثر ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton14_Click(sender As Object, e As EventArgs) Handles SimpleButton14.Click
        Dim f As city_litle_earn = New city_litle_earn()
        f.Text = "المدينة الاقل ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton13_Click(sender As Object, e As EventArgs) Handles SimpleButton13.Click
        Dim f As city_cus_earn = New city_cus_earn()
        f.Text = "مقارنة بح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton18_Click(sender As Object, e As EventArgs) Handles SimpleButton18.Click
        Dim f As gov_best_earn = New gov_best_earn()
        f.Text = "المحافظة الاكثر ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton17_Click(sender As Object, e As EventArgs) Handles SimpleButton17.Click
        Dim f As gov_litle_earn = New gov_litle_earn()
        f.Text = "المحافظة الاقل ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton16_Click(sender As Object, e As EventArgs) Handles SimpleButton16.Click
        Dim f As gov_cus_earn = New gov_cus_earn()
        f.Text = "مقارنة بح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton21_Click(sender As Object, e As EventArgs) Handles SimpleButton21.Click
        Dim f As dele_best_earn = New dele_best_earn()
        f.Text = "المندوب الاكثر ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton20_Click(sender As Object, e As EventArgs) Handles SimpleButton20.Click
        Dim f As dele_best_earn = New dele_best_earn()
        f.Text = "المندوب الاقل ربحا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton19_Click(sender As Object, e As EventArgs) Handles SimpleButton19.Click
        Dim f As dele_cus_earn = New dele_cus_earn()
        f.Text = "مقارنة بح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton24_Click(sender As Object, e As EventArgs) Handles SimpleButton24.Click
        Dim f As imp_balace = New imp_balace()
        f.Text = "أرصدة الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton23_Click(sender As Object, e As EventArgs) Handles SimpleButton23.Click
        Dim f As imp_Balance = New imp_Balance()
        f.Text = "ميزان مراجعة الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton22_Click(sender As Object, e As EventArgs) Handles SimpleButton22.Click
        Dim f As imp_minus = New imp_minus()
        f.Text = "أرصدة الموردين بالسالب"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton28_Click(sender As Object, e As EventArgs) Handles SimpleButton28.Click
        imp_trans_show.ShowDialog()
    End Sub

    Private Sub SimpleButton27_Click(sender As Object, e As EventArgs) Handles SimpleButton27.Click
        Dim f As importer_Detailed = New importer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = XtraForm1
        f.Show()
        f.importername.Focus()
    End Sub

    Private Sub SimpleButton26_Click(sender As Object, e As EventArgs) Handles SimpleButton26.Click
        Dim f As imp_item = New imp_item()
        f.Text = "اصناف مورد"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton29_Click(sender As Object, e As EventArgs) Handles SimpleButton29.Click
        Dim f As item_Deficiencies = New item_Deficiencies()
        f.Text = "تجاوزت  الحد الادني"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton25_Click(sender As Object, e As EventArgs) Handles SimpleButton25.Click
        Dim f As item_zero = New item_zero()
        f.Text = "الكمية الصفرية"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton31_Click(sender As Object, e As EventArgs) Handles SimpleButton31.Click
        Dim f As item_minus = New item_minus()
        f.Text = "الكمية السالبة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton35_Click(sender As Object, e As EventArgs) Handles SimpleButton35.Click
        item_trans_show.ShowDialog()

    End Sub

    Private Sub SimpleButton34_Click(sender As Object, e As EventArgs) Handles SimpleButton34.Click
        Dim f As item_Detailed = New item_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton33_Click(sender As Object, e As EventArgs) Handles SimpleButton33.Click
        Dim f As Statistic_item = New Statistic_item()
        f.Text = " احصائيات صنف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton36_Click(sender As Object, e As EventArgs) Handles SimpleButton36.Click
        Dim f As item_invoice = New item_invoice()
        f.Text = "مبيعات الاصناف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton37_Click(sender As Object, e As EventArgs) Handles SimpleButton37.Click
        Dim f As item_Purchases = New item_Purchases()
        f.Text = "مشتريات الاصناف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton38_Click(sender As Object, e As EventArgs) Handles SimpleButton38.Click
        Dim f As item_inventory = New item_inventory()
        f.Text = "جرد الاصناف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton39_Click(sender As Object, e As EventArgs) Handles SimpleButton39.Click
        Dim f As trans_list = New trans_list()
        f.Text = "تحويلات الاصناف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton40_Click(sender As Object, e As EventArgs) Handles SimpleButton40.Click
        Dim f As Drawings_list = New Drawings_list()
        f.Text = "مسحوبات الاصناف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton41_Click(sender As Object, e As EventArgs) Handles SimpleButton41.Click
        Dim f As damage_list = New damage_list()
        f.Text = "هالك الاصناف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton43_Click(sender As Object, e As EventArgs) Handles SimpleButton43.Click
        Dim f As item_earn1 = New item_earn1()
        f.Text = "الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton42_Click(sender As Object, e As EventArgs) Handles SimpleButton42.Click
        Dim f As item_earn2 = New item_earn2()
        f.Text = "الاقل ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton32_Click(sender As Object, e As EventArgs) Handles SimpleButton32.Click
        Dim f As item_earn = New item_earn()
        f.Text = "مقارنة ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton44_Click(sender As Object, e As EventArgs) Handles SimpleButton44.Click
        Dim f As item_bad = New item_bad()
        f.Text = "اصناف تسبب الخسارة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton47_Click(sender As Object, e As EventArgs) Handles SimpleButton47.Click
        Dim f As company_best_earn = New company_best_earn()
        f.Text = "الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton46_Click(sender As Object, e As EventArgs) Handles SimpleButton46.Click
        Dim f As company_litle_earn = New company_litle_earn()
        f.Text = "الاقل ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton45_Click(sender As Object, e As EventArgs) Handles SimpleButton45.Click

        Dim f As company_earn = New company_earn()
        f.Text = "مقارنة ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton50_Click(sender As Object, e As EventArgs) Handles SimpleButton50.Click
        Dim f As catoregy_best_earn = New catoregy_best_earn()
        f.Text = "الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton49_Click(sender As Object, e As EventArgs) Handles SimpleButton49.Click
        Dim f As catoregy_litle_earn = New catoregy_litle_earn()
        f.Text = "الاقل ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton48_Click(sender As Object, e As EventArgs) Handles SimpleButton48.Click
        Dim f As catoregy_earn = New catoregy_earn()
        f.Text = "مقارنة ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton53_Click(sender As Object, e As EventArgs) Handles SimpleButton53.Click
        Dim f As grou_best_earn = New grou_best_earn()
        f.Text = "الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton52_Click(sender As Object, e As EventArgs) Handles SimpleButton52.Click
        Dim f As grou_litle_earn = New grou_litle_earn()
        f.Text = "الاقل ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton51_Click(sender As Object, e As EventArgs) Handles SimpleButton51.Click
        Dim f As gro_earn = New gro_earn()
        f.Text = "مقارنة ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton56_Click(sender As Object, e As EventArgs) Handles SimpleButton56.Click
        Dim f As sec_best_earn = New sec_best_earn()
        f.Text = "الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton55_Click(sender As Object, e As EventArgs) Handles SimpleButton55.Click
        Dim f As sec_litle_earn = New sec_litle_earn()
        f.Text = "الاقل ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton54_Click(sender As Object, e As EventArgs) Handles SimpleButton54.Click
        Dim f As sec_earn = New sec_earn()
        f.Text = "مقارنة ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton62_Click(sender As Object, e As EventArgs) Handles SimpleButton62.Click
        Dim f As day_inv = New day_inv()
        f.Text = "مبيعات اليوم"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton58_Click(sender As Object, e As EventArgs) Handles SimpleButton58.Click
        Dim f As month_inv = New month_inv()
        f.Text = "مبيعات الشهر"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton57_Click(sender As Object, e As EventArgs) Handles SimpleButton57.Click
        Dim f As year_inv = New year_inv()
        f.Text = "مبيعات السنة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton63_Click(sender As Object, e As EventArgs) Handles SimpleButton63.Click
        Dim f As all_inv = New all_inv()
        f.Text = "اجمالي المبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton76_Click(sender As Object, e As EventArgs) Handles SimpleButton76.Click
        Dim f As Statistic_invoice = New Statistic_invoice()
        f.Text = "مقارنة المبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton77_Click(sender As Object, e As EventArgs) Handles SimpleButton77.Click
        Dim f As Statistic_invoice = New Statistic_invoice()
        f.Text = "مقارنة الارباح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton78_Click(sender As Object, e As EventArgs) Handles SimpleButton78.Click
        Dim f As store_cus_earn = New store_cus_earn()
        f.Text = "مقارنة ارباح مخازن"
        f.MdiParent = XtraForm1
        f.Show()

    End Sub

    Private Sub SimpleButton61_Click(sender As Object, e As EventArgs) Handles SimpleButton61.Click

        Dim f As stoer_best_invoice = New stoer_best_invoice()
        f.Text = "المخزن الاكثر مبيعا"
        f.MdiParent = XtraForm1
        f.Show()

    End Sub

    Private Sub SimpleButton59_Click(sender As Object, e As EventArgs) Handles SimpleButton59.Click
        Dim f As store_cus_invoice = New store_cus_invoice()
        f.Text = "مقارنة مبيعا مخزن"
        f.MdiParent = XtraForm1
        f.Show()

    End Sub

    Private Sub SimpleButton66_Click(sender As Object, e As EventArgs) Handles SimpleButton66.Click
        Dim f As group_best_invoice = New group_best_invoice()
        f.Text = "المحموعة الاكثر مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton64_Click(sender As Object, e As EventArgs) Handles SimpleButton64.Click
        Dim f As group_cus_invoice = New group_cus_invoice()
        f.Text = "مقارنة مبيعات مجموعة"
        f.MdiParent = XtraForm1
        f.Show()

    End Sub

    Private Sub SimpleButton70_Click(sender As Object, e As EventArgs) Handles SimpleButton70.Click

        Dim f As city_best_invoice = New city_best_invoice()
        f.Text = "المدينة الاكثر مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton75_Click(sender As Object, e As EventArgs) Handles SimpleButton75.Click
        Dim f As dele_best_invoice = New dele_best_invoice()
        f.Text = "المندوب الاكثر مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton72_Click(sender As Object, e As EventArgs) Handles SimpleButton72.Click
        Dim f As city_cus_invoice = New city_cus_invoice()
        f.Text = "مقارنة مبيعات مدن"
        f.MdiParent = XtraForm1
        f.Show()

    End Sub

    Private Sub SimpleButton73_Click(sender As Object, e As EventArgs) Handles SimpleButton73.Click

        Dim f As dele_cus_invoice = New dele_cus_invoice()
        f.Text = "مقارنة مبيعات مندوب"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton69_Click(sender As Object, e As EventArgs) Handles SimpleButton69.Click
        Dim f As gov_best_invoice = New gov_best_invoice()
        f.Text = "المحافظة الاكثر مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton67_Click(sender As Object, e As EventArgs) Handles SimpleButton67.Click

        Dim f As gov_cus_invoice = New gov_cus_invoice()
        f.Text = "مقارنة مبيعات محافظات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton82_Click(sender As Object, e As EventArgs) Handles SimpleButton82.Click
        Dim f As day_back_inv = New day_back_inv()
        f.Text = "مرتحع مبيعات اليوم"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton81_Click(sender As Object, e As EventArgs) Handles SimpleButton81.Click
        Dim f As month_back_inv = New month_back_inv()
        f.Text = "مرتحع مبيعات الشهر"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton80_Click(sender As Object, e As EventArgs) Handles SimpleButton80.Click
        Dim f As year_back_inv = New year_back_inv()
        f.Text = "مرتجع مبيعات السنة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton79_Click(sender As Object, e As EventArgs) Handles SimpleButton79.Click
        Dim f As all_back_inv = New all_back_inv()
        f.Text = "اجمالي مرتجع المبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton83_Click(sender As Object, e As EventArgs) Handles SimpleButton83.Click
        Dim f As Statistic_invoice_back = New Statistic_invoice_back()
        f.Text = "مقارنة م.المبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton94_Click(sender As Object, e As EventArgs) Handles SimpleButton94.Click
        Dim f As dele_best_invoice_back = New dele_best_invoice_back()
        f.Text = "الاكثر م.مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton93_Click(sender As Object, e As EventArgs) Handles SimpleButton93.Click
        Dim f As dele_cus_invoice_back = New dele_cus_invoice_back()
        f.Text = "مقارنة مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton86_Click(sender As Object, e As EventArgs) Handles SimpleButton86.Click
        Dim f As stoer_best_invoice_back = New stoer_best_invoice_back()
        f.Text = "الاكثر م.مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton88_Click(sender As Object, e As EventArgs) Handles SimpleButton88.Click
        Dim f As group_best_invoice_back = New group_best_invoice_back()
        f.Text = "الاكثر م.مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton91_Click(sender As Object, e As EventArgs) Handles SimpleButton91.Click
        Dim f As city_best_invoice_back = New city_best_invoice_back()
        f.Text = "الاكثر م.مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton92_Click(sender As Object, e As EventArgs) Handles SimpleButton92.Click
        Dim f As gov_best_invoice_back = New gov_best_invoice_back()
        f.Text = "الاكثر م.مبيعا"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton85_Click(sender As Object, e As EventArgs) Handles SimpleButton85.Click
        Dim f As store_cus_invoice_back = New store_cus_invoice_back()
        f.Text = "مقارنة مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton87_Click(sender As Object, e As EventArgs) Handles SimpleButton87.Click
        Dim f As group_cus_invoice_back = New group_cus_invoice_back()
        f.Text = "مقارنة مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton89_Click(sender As Object, e As EventArgs) Handles SimpleButton89.Click
        Dim f As city_cus_invoice_back = New city_cus_invoice_back()
        f.Text = "مقارنة مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton90_Click(sender As Object, e As EventArgs) Handles SimpleButton90.Click
        Dim f As gov_cus_invoice_back = New gov_cus_invoice_back()
        f.Text = "مقارنة مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton104_Click(sender As Object, e As EventArgs) Handles SimpleButton104.Click
        Dim f As day_pur = New day_pur()
        f.Text = "مشتريات اليوم"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton103_Click(sender As Object, e As EventArgs) Handles SimpleButton103.Click
        Dim f As month_pur = New month_pur()
        f.Text = "مشتريات الشهر"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton102_Click(sender As Object, e As EventArgs) Handles SimpleButton102.Click
        Dim f As year_pur = New year_pur()
        f.Text = "مشتريات السنة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton101_Click(sender As Object, e As EventArgs) Handles SimpleButton101.Click
        Dim f As all_pur = New all_pur()
        f.Text = "اجمالي المشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton68_Click(sender As Object, e As EventArgs) Handles SimpleButton68.Click
        Dim f As Statistic_Purchases = New Statistic_Purchases()
        f.Text = "مقارنة المشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton106_Click(sender As Object, e As EventArgs) Handles SimpleButton106.Click
        Dim f As stoer_best_Purchases = New stoer_best_Purchases()
        f.Text = "الاكثر للشراء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton105_Click(sender As Object, e As EventArgs) Handles SimpleButton105.Click
        Dim f As store_cus_invoice_Purchases = New store_cus_invoice_Purchases()
        f.Text = "مقارنة مشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton95_Click(sender As Object, e As EventArgs) Handles SimpleButton95.Click
        Dim f As day_pur_back = New day_pur_back()
        f.Text = "مشتريات اليوم"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton74_Click(sender As Object, e As EventArgs) Handles SimpleButton74.Click
        Dim f As month_pur_back = New month_pur_back()
        f.Text = "مشتريات الشهر"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton71_Click(sender As Object, e As EventArgs) Handles SimpleButton71.Click
        Dim f As year_pur_back = New year_pur_back()
        f.Text = "مشتريات السنة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton65_Click(sender As Object, e As EventArgs) Handles SimpleButton65.Click
        Dim f As all_pur_back = New all_pur_back()
        f.Text = "اجمالي المشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton60_Click(sender As Object, e As EventArgs) Handles SimpleButton60.Click
        Dim f As Statistic_Purchases_back = New Statistic_Purchases_back()
        f.Text = "مقارنة المشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton97_Click(sender As Object, e As EventArgs) Handles SimpleButton97.Click
        Dim f As stoer_best_Purchases_back = New stoer_best_Purchases_back()
        f.Text = "الاكثر للشراء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton96_Click(sender As Object, e As EventArgs) Handles SimpleButton96.Click
        Dim f As store_cus_invoice_Purchases_back = New store_cus_invoice_Purchases_back()
        f.Text = "مقارنة مشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub report_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If XtraForm1.m84.Checked = False Then
            XtraTabPage3.PageVisible = False
        End If
        If XtraForm1.m85.Checked = False Then
            XtraTabPage4.PageVisible = False
        End If
        If XtraForm1.m86.Checked = False Then
            XtraTabPage5.PageVisible = False
        End If
        If XtraForm1.m87.Checked = False Then
            XtraTabPage6.PageVisible = False
        End If
        If XtraForm1.m88.Checked = False Then
            XtraTabPage7.PageVisible = False
        End If
        If XtraForm1.m89.Checked = False Then
            XtraTabPage8.PageVisible = False
        End If

    End Sub

    Private Sub SimpleButton99_Click(sender As Object, e As EventArgs) Handles SimpleButton99.Click
        Dim f As Expenses_show = New Expenses_show()
        f.Text = "مراجعة المصروفات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton98_Click(sender As Object, e As EventArgs) Handles SimpleButton98.Click
        Dim f As express_all = New express_all()
        f.Text = "اجمالي المصروفات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton100_Click(sender As Object, e As EventArgs) Handles SimpleButton100.Click
        Dim f As balancereview = New balancereview()
        f.Text = "حركة عمل"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton30_Click(sender As Object, e As EventArgs) Handles SimpleButton30.Click
        Dim f As item_stagnet = New item_stagnet()
        f.Text = " الاصناف الراكدة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton84_Click(sender As Object, e As EventArgs) Handles SimpleButton84.Click
        Dim f As user_best_earn = New user_best_earn()
        f.Text = " الاكثر ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton108_Click(sender As Object, e As EventArgs) Handles SimpleButton108.Click
        Dim f As user_cus_earn = New user_cus_earn()
        f.Text = " مقارنة ربح"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton111_Click(sender As Object, e As EventArgs) Handles SimpleButton111.Click
        Dim f As check_late = New check_late()
        f.Text = " شيكات العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton110_Click(sender As Object, e As EventArgs) Handles SimpleButton110.Click
        Dim f As check_at_let = New check_at_let()
        f.Text = " شيكات العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton109_Click(sender As Object, e As EventArgs) Handles SimpleButton109.Click
        Dim f As check_chashing = New check_chashing()
        f.Text = " شيكات العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton114_Click(sender As Object, e As EventArgs) Handles SimpleButton114.Click
        Dim f As imp_check_late = New imp_check_late()
        f.Text = " شيكات الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton113_Click(sender As Object, e As EventArgs) Handles SimpleButton113.Click
        Dim f As imp_check_at_let = New imp_check_at_let()
        f.Text = " شيكات الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub
    Private Sub SimpleButton112_Click(sender As Object, e As EventArgs) Handles SimpleButton112.Click
        Dim f As imp_check_chashing = New imp_check_chashing()
        f.Text = " شيكات الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton115_Click(sender As Object, e As EventArgs)
        MsgBox("مغلقة")
    End Sub

    Private Sub SimpleButton116_Click(sender As Object, e As EventArgs) Handles SimpleButton116.Click
        Dim f As income_list = New income_list()
        f.Text = "قائمة الدخل"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton117_Click(sender As Object, e As EventArgs) Handles SimpleButton117.Click
        Dim f As item_expire = New item_expire()
        f.Text = " منتهي الصلاحية"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton118_Click(sender As Object, e As EventArgs) Handles SimpleButton118.Click
        Dim f As Asset_report = New Asset_report()
        f.Text = "الاصول"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton119_Click(sender As Object, e As EventArgs) Handles SimpleButton119.Click
        Dim f As asstet_all = New asstet_all()
        f.Text = "اهلاك الاصول"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton120_Click(sender As Object, e As EventArgs) Handles SimpleButton120.Click
        Dim f As Balance_Review = New Balance_Review()
        f.Text = "ميزان مراجعة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton115_Click_1(sender As Object, e As EventArgs) Handles SimpleButton115.Click
        Dim f As Company_evaluation = New Company_evaluation()
        f.Text = "تقييم الشركة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton122_Click(sender As Object, e As EventArgs) Handles SimpleButton122.Click
        Dim f As Expenses_show2 = New Expenses_show2()
        f.Text = "مراجعة مصورفات تأسيسية"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton124_Click(sender As Object, e As EventArgs) Handles SimpleButton124.Click
        Dim f As Revenues_show = New Revenues_show()
        f.Text = "مراجعة الايرادات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton121_Click(sender As Object, e As EventArgs) Handles SimpleButton121.Click
        Dim f As express_all2 = New express_all2()
        f.Text = "اجمالي المصروفات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton123_Click(sender As Object, e As EventArgs) Handles SimpleButton123.Click
        Dim f As express_all3 = New express_all3()
        f.Text = "اجمالي الايرادات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton125_Click(sender As Object, e As EventArgs) Handles SimpleButton125.Click
        Dim f As General_budget = New General_budget()
        f.Text = "ميزانية عمومية"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub CostCenterReportBtn_Click(sender As Object, e As EventArgs) Handles CostCenterReportBtn.Click
        Try
            ' التحقق من وجود جدول مراكز التكلفة أولاً
            If Not CheckCostCenterTableExists() Then
                If XtraMessageBox.Show("جدول مراكز التكلفة غير موجود. هل تريد إنشاؤه الآن؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    CreateCostCenterTable()
                    XtraMessageBox.Show("تم إنشاء جدول مراكز التكلفة بنجاح. يمكنك الآن إضافة مراكز التكلفة من شاشة الإدارة.", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
                Return
            End If

            ShowCostCenterReport()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في فتح تقرير مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CheckCostCenterTableExists() As Boolean
        Try
            Dim sql As String = "SELECT COUNT(*) FROM sysobjects WHERE name='cost_centers' AND xtype='U'"
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                Return Convert.ToInt32(cmd.ExecuteScalar()) > 0
            End Using
        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Sub CreateCostCenterTable()
        Try
            Dim sql As String = "
                CREATE TABLE cost_centers (
                    cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                    cost_center_code NVARCHAR(50) NOT NULL UNIQUE,
                    cost_center_name NVARCHAR(255) NOT NULL,
                    description NVARCHAR(500),
                    is_active BIT DEFAULT 1,
                    created_date DATETIME DEFAULT GETDATE()
                );

                -- إضافة أعمدة مراكز التكلفة للجداول الموجودة
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('invoice_add') AND name = 'cost_center_id')
                    ALTER TABLE invoice_add ADD cost_center_id INT;

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Expenses_add') AND name = 'cost_center_id')
                    ALTER TABLE Expenses_add ADD cost_center_id INT;

                -- إدراج بعض البيانات التجريبية
                INSERT INTO cost_centers (cost_center_code, cost_center_name, description) VALUES
                ('CC001', 'قسم المبيعات', 'مركز تكلفة قسم المبيعات'),
                ('CC002', 'قسم الإنتاج', 'مركز تكلفة قسم الإنتاج'),
                ('CC003', 'قسم الإدارة', 'مركز تكلفة قسم الإدارة');"

            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء جدول مراكز التكلفة: " & ex.Message)
        End Try
    End Sub

    Private Sub ShowCostCenterReport()
        ' إنشاء نموذج جديد للتقرير
        Dim reportForm As New XtraForm()
        reportForm.Text = "تقرير مراكز التكلفة"
        reportForm.Size = New System.Drawing.Size(1200, 700)
        reportForm.StartPosition = FormStartPosition.CenterScreen
        reportForm.RightToLeft = RightToLeft.Yes
        reportForm.MdiParent = XtraForm1

        ' إنشاء العناصر
        Dim gridControl As New DevExpress.XtraGrid.GridControl()
        Dim gridView As New DevExpress.XtraGrid.Views.Grid.GridView()
        Dim dateFrom As New DevExpress.XtraEditors.DateEdit()
        Dim dateTo As New DevExpress.XtraEditors.DateEdit()
        Dim costCenterCombo As New DevExpress.XtraEditors.LookUpEdit()
        Dim showBtn As New DevExpress.XtraEditors.SimpleButton()
        Dim exportBtn As New DevExpress.XtraEditors.SimpleButton()
        Dim printBtn As New DevExpress.XtraEditors.SimpleButton()
        Dim closeBtn As New DevExpress.XtraEditors.SimpleButton()

        ' تكوين النموذج
        CType(gridControl, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(gridView, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(dateFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(dateTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(costCenterCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        reportForm.SuspendLayout()

        ' تكوين التواريخ
        dateFrom.Location = New System.Drawing.Point(900, 20)
        dateFrom.Size = New System.Drawing.Size(120, 20)
        dateFrom.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        dateFrom.EditValue = DateTime.Now.AddMonths(-1)

        dateTo.Location = New System.Drawing.Point(750, 20)
        dateTo.Size = New System.Drawing.Size(120, 20)
        dateTo.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        dateTo.EditValue = DateTime.Now

        ' تكوين مراكز التكلفة
        costCenterCombo.Location = New System.Drawing.Point(600, 20)
        costCenterCombo.Size = New System.Drawing.Size(120, 20)
        costCenterCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        LoadCostCentersToCombo(costCenterCombo)

        ' تكوين الأزرار
        showBtn.Location = New System.Drawing.Point(500, 18)
        showBtn.Size = New System.Drawing.Size(80, 25)
        showBtn.Text = "عرض التقرير"

        exportBtn.Location = New System.Drawing.Point(410, 18)
        exportBtn.Size = New System.Drawing.Size(80, 25)
        exportBtn.Text = "تصدير"

        printBtn.Location = New System.Drawing.Point(320, 18)
        printBtn.Size = New System.Drawing.Size(80, 25)
        printBtn.Text = "طباعة"

        closeBtn.Location = New System.Drawing.Point(230, 18)
        closeBtn.Size = New System.Drawing.Size(80, 25)
        closeBtn.Text = "إغلاق"

        ' تكوين الجدول
        gridControl.Location = New System.Drawing.Point(12, 60)
        gridControl.Size = New System.Drawing.Size(1160, 600)
        gridControl.MainView = gridView
        gridView.GridControl = gridControl
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.ShowFooter = True

        ' إضافة العناصر للنموذج
        reportForm.Controls.Add(gridControl)
        reportForm.Controls.Add(dateFrom)
        reportForm.Controls.Add(dateTo)
        reportForm.Controls.Add(costCenterCombo)
        reportForm.Controls.Add(showBtn)
        reportForm.Controls.Add(exportBtn)
        reportForm.Controls.Add(printBtn)
        reportForm.Controls.Add(closeBtn)

        ' إضافة التسميات
        Dim lblFrom As New Label()
        lblFrom.Text = "من تاريخ:"
        lblFrom.Location = New System.Drawing.Point(1030, 23)
        lblFrom.Size = New System.Drawing.Size(50, 20)
        reportForm.Controls.Add(lblFrom)

        Dim lblTo As New Label()
        lblTo.Text = "إلى تاريخ:"
        lblTo.Location = New System.Drawing.Point(880, 23)
        lblTo.Size = New System.Drawing.Size(50, 20)
        reportForm.Controls.Add(lblTo)

        Dim lblCostCenter As New Label()
        lblCostCenter.Text = "مركز التكلفة:"
        lblCostCenter.Location = New System.Drawing.Point(730, 23)
        lblCostCenter.Size = New System.Drawing.Size(70, 20)
        reportForm.Controls.Add(lblCostCenter)

        ' معالجات الأحداث
        AddHandler showBtn.Click, Sub()
                                      LoadCostCenterData(gridView, dateFrom, dateTo, costCenterCombo)
                                  End Sub

        AddHandler exportBtn.Click, Sub()
                                        Try
                                            gridView.ExportToXlsx("تقرير_مراكز_التكلفة_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".xlsx")
                                            XtraMessageBox.Show("تم تصدير التقرير بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                        Catch ex As Exception
                                            XtraMessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                        End Try
                                    End Sub

        AddHandler printBtn.Click, Sub()
                                       Try
                                           gridView.ShowPrintPreview()
                                       Catch ex As Exception
                                           XtraMessageBox.Show("خطأ في الطباعة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                       End Try
                                   End Sub

        AddHandler closeBtn.Click, Sub()
                                       reportForm.Close()
                                   End Sub

        ' إنهاء التكوين
        CType(gridControl, System.ComponentModel.ISupportInitialize).EndInit()
        CType(gridView, System.ComponentModel.ISupportInitialize).EndInit()
        CType(dateFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(dateTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(costCenterCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        reportForm.ResumeLayout(False)

        ' عرض النموذج
        reportForm.Show()

        ' تحميل البيانات تلقائياً
        LoadCostCenterData(gridView, dateFrom, dateTo, costCenterCombo)
    End Sub

    Private Sub LoadCostCentersToCombo(costCenterCombo As DevExpress.XtraEditors.LookUpEdit)
        Try
            Dim sql As String = "SELECT cost_center_id, cost_center_name + ' - ' + cost_center_code AS display_name FROM cost_centers WHERE is_active = 1 ORDER BY cost_center_name"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostCenters")

                ' إضافة خيار "الكل"
                Dim allRow As DataRow = ds.Tables("CostCenters").NewRow()
                allRow("cost_center_id") = DBNull.Value
                allRow("display_name") = "جميع مراكز التكلفة"
                ds.Tables("CostCenters").Rows.InsertAt(allRow, 0)

                costCenterCombo.Properties.DataSource = ds.Tables("CostCenters")
                costCenterCombo.Properties.DisplayMember = "display_name"
                costCenterCombo.Properties.ValueMember = "cost_center_id"
                costCenterCombo.EditValue = DBNull.Value
            End Using
        Catch ex As Exception
            ' في حالة عدم وجود الجدول، نخفي العنصر
            costCenterCombo.Visible = False
        End Try
    End Sub

    Private Sub LoadCostCenterData(gridView As DevExpress.XtraGrid.Views.Grid.GridView, dateFrom As DevExpress.XtraEditors.DateEdit, dateTo As DevExpress.XtraEditors.DateEdit, costCenterCombo As DevExpress.XtraEditors.LookUpEdit)
        Try
            Dim sql As String = BuildCostCenterQuery(dateFrom, dateTo, costCenterCombo)

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostCenterReport")

                gridView.GridControl.DataSource = ds.Tables("CostCenterReport")

                ' تكوين الأعمدة
                ConfigureCostCenterGrid(gridView)
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function BuildCostCenterQuery(dateFrom As DevExpress.XtraEditors.DateEdit, dateTo As DevExpress.XtraEditors.DateEdit, costCenterCombo As DevExpress.XtraEditors.LookUpEdit) As String
        Dim sql As String = "
            SELECT
                ISNULL(cc.cost_center_name, 'غير محدد') AS 'مركز التكلفة',
                'فاتورة مبيعات' AS 'نوع العملية',
                CAST(ia.invoice_number AS VARCHAR(50)) AS 'رقم الفاتورة',
                ia.invoice_date AS 'التاريخ',
                ia.Accounts_name AS 'العميل/المورد',
                ia.total_invoice AS 'المبلغ',
                ia.invoice_count AS 'عدد الأصناف',
                ia.earn_invoice AS 'الربح',
                ia.user_invoice AS 'المستخدم',
                CASE
                    WHEN ia.type_money = 'نقدي' THEN 'نقدي'
                    WHEN ia.type_money = 'أجل' THEN 'آجل'
                    ELSE 'غير محدد'
                END AS 'نوع الدفع',
                ISNULL(ia.invoice_note, '') AS 'ملاحظات'
            FROM invoice_add ia
            LEFT JOIN cost_centers cc ON ia.cost_center_id = cc.cost_center_id
            WHERE ia.invoice_date BETWEEN '" & dateFrom.DateTime.ToString("yyyy-MM-dd") & "' AND '" & dateTo.DateTime.ToString("yyyy-MM-dd") & "'"

        If costCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(costCenterCombo.EditValue) Then
            sql += " AND ia.cost_center_id = " & costCenterCombo.EditValue.ToString()
        End If

        sql += "
            UNION ALL
            SELECT
                ISNULL(cc.cost_center_name, 'غير محدد') AS 'مركز التكلفة',
                'مصروفات' AS 'نوع العملية',
                CAST(ea.Expenses_number AS VARCHAR(50)) AS 'رقم الفاتورة',
                ea.Expenses_date AS 'التاريخ',
                'مصروفات عامة' AS 'العميل/المورد',
                ea.total_Expenses AS 'المبلغ',
                ea.Expenses_count AS 'عدد الأصناف',
                0 AS 'الربح',
                ea.user_Expenses AS 'المستخدم',
                'نقدي' AS 'نوع الدفع',
                ISNULL(ea.Expenses_note, '') AS 'ملاحظات'
            FROM Expenses_add ea
            LEFT JOIN cost_centers cc ON ea.cost_center_id = cc.cost_center_id
            WHERE ea.Expenses_date BETWEEN '" & dateFrom.DateTime.ToString("yyyy-MM-dd") & "' AND '" & dateTo.DateTime.ToString("yyyy-MM-dd") & "'"

        If costCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(costCenterCombo.EditValue) Then
            sql += " AND ea.cost_center_id = " & costCenterCombo.EditValue.ToString()
        End If

        sql += " ORDER BY 'التاريخ' DESC, 'نوع العملية'"

        Return sql
    End Function

    Private Sub ConfigureCostCenterGrid(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            With gridView
                ' تكوين عرض الأعمدة
                If .Columns("مركز التكلفة") IsNot Nothing Then .Columns("مركز التكلفة").Width = 120
                If .Columns("نوع العملية") IsNot Nothing Then .Columns("نوع العملية").Width = 80
                If .Columns("رقم الفاتورة") IsNot Nothing Then .Columns("رقم الفاتورة").Width = 80
                If .Columns("التاريخ") IsNot Nothing Then .Columns("التاريخ").Width = 90
                If .Columns("العميل/المورد") IsNot Nothing Then .Columns("العميل/المورد").Width = 150
                If .Columns("المبلغ") IsNot Nothing Then .Columns("المبلغ").Width = 100
                If .Columns("عدد الأصناف") IsNot Nothing Then .Columns("عدد الأصناف").Width = 80
                If .Columns("الربح") IsNot Nothing Then .Columns("الربح").Width = 80
                If .Columns("المستخدم") IsNot Nothing Then .Columns("المستخدم").Width = 100
                If .Columns("نوع الدفع") IsNot Nothing Then .Columns("نوع الدفع").Width = 70
                If .Columns("ملاحظات") IsNot Nothing Then .Columns("ملاحظات").Width = 200

                ' تنسيق الأعمدة الرقمية
                If .Columns("المبلغ") IsNot Nothing Then
                    .Columns("المبلغ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    .Columns("المبلغ").DisplayFormat.FormatString = "N2"
                End If
                If .Columns("الربح") IsNot Nothing Then
                    .Columns("الربح").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    .Columns("الربح").DisplayFormat.FormatString = "N2"
                End If
                If .Columns("عدد الأصناف") IsNot Nothing Then
                    .Columns("عدد الأصناف").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    .Columns("عدد الأصناف").DisplayFormat.FormatString = "N0"
                End If

                ' تنسيق التاريخ
                If .Columns("التاريخ") IsNot Nothing Then
                    .Columns("التاريخ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                    .Columns("التاريخ").DisplayFormat.FormatString = "dd/MM/yyyy"
                End If

                ' تمكين الخيارات
                .OptionsView.ShowAutoFilterRow = True
                .OptionsCustomization.AllowGroup = True
                .OptionsCustomization.AllowSort = True
                .OptionsCustomization.AllowFilter = True
            End With
        Catch ex As Exception
            ' تجاهل أخطاء التكوين
        End Try
    End Sub
End Class