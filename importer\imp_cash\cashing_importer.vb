﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class cashing_importer
    Private rng As New Random
    Private Sub new_cash()

        fill_treasury_name()
        fillcom()
        chashingdate.Text = Now.Date
        Amount.Text = 0
        Cashingcode.Text = getlastcode("importer_cashing", "Cashingcode") + 1
        Accounts_name.Text = ""
        Accounts_balace.Text = 0
        balace_now.Text = 0
        cash_type.Text = ""
        cash_pay.SelectedIndex = 0
        type_pay.SelectedIndex = 0
        check_number.Text = ""
        check_date.Value = Now.Date
    
        type_type.Text = ""
        Accounts_name.Focus()
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
        treasury_balace.Text = gettreasury(type_pay.Text)
        If XtraForm1.type_pay.Text <> "type_pay" Then
            type_pay.Text = XtraForm1.type_pay.Text
        End If

    End Sub
    Sub fill_treasury_name()

        type_pay.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from treasury_name where cash='true' and Treasury_active='true'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            type_pay.Properties.Items.Add(dt.Rows(i).Item("treasury_name"))
        Next
    End Sub
    Function gettreasury(subname) As String

        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function
    Private Sub fillcom()
        Accounts_name.Properties.DataSource = Nothing
        Dim adp As New SqlDataAdapter("select * from importer where imp_active = 'true' order by impname ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Accounts_name.Properties.DataSource = dt
        Accounts_name.Properties.DisplayMember = "impname"
        Accounts_name.Properties.ValueMember = "impcode"
    End Sub
    Private Sub cashing_customer_Load_1(sender As Object, e As EventArgs) Handles MyBase.Load
        new_cash()
        Accounts_name.Focus()
    End Sub
  
    Public Sub show_data(x)
        Dim sql = "select * from importer_cashing where Cashingcode=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("لم يتم العثور علي بيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim dr = dt.Rows(0)
            Cashingcode.Text = dr!Cashingcode
            chashingdate.Text = dr!chashingdate
            Accounts_name.Text = dr!Accounts_name
            Amount.Text = dr!Amount
            cash_type.Text = dr!cash_type
            cash_pay.Text = dr!cash_pay
            type_pay.Text = dr!type_pay
            check_number.Text = dr!check_number
            check_date.Text = dr!check_date
         
            type_type.Text = dr!type_type
        End If
    End Sub
    Function getbalace(subname) As String
        Dim sql = "select * from importer where impname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Accounts_balace") Else Return ""
    End Function
    Function getCuscusgroup(subname) As String
        Dim sql = "select * from importer where impname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Cuscusgroup") Else Return ""
    End Function
    Function getcusGovernorate(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("cusGovernorate") Else Return ""
    End Function
    Function getcuscity(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("cuscity") Else Return ""
    End Function
    Private Sub cash_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cash_pay.SelectedIndexChanged
        If cash_pay.SelectedIndex = 0 Then
            fill_treasury_name()
            type_pay.SelectedIndex = 0

        End If
        If cash_pay.SelectedIndex = 0 Then
            Label17.Visible = False
            check_number.Visible = False
            Label18.Visible = False
            check_date.Visible = False
        End If
        If cash_pay.SelectedIndex = 1 Then
            Label17.Visible = True
            check_number.Visible = True
            Label18.Visible = True
            check_date.Visible = True
        End If
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
    End Sub
   
    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If Accounts_name.Text = "" Then
            XtraMessageBox.Show("اسم العميل فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If Amount.Text = 0 Then
            XtraMessageBox.Show("المبلغ = 0", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If Amount.Text = "" Then
            XtraMessageBox.Show("المبلغ فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        cash_pay_SelectedIndexChanged(Nothing, Nothing)
        Cashingcode.Text = getlastcode("importer_cashing", "Cashingcode") + 1
        printcode.Text = getlastcode("importer_cashing", "Cashingcode") + 1
        Dim sql = "select * from importer_cashing where Cashingcode=N'" & (Cashingcode.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Cashingcode.Text = getlastcode("importer_cashing", "Cashingcode") + 1
            Exit Sub
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============
            dr!Cashingcode = Cashingcode.Text
            dr!chashingdate = chashingdate.Value
            dr!Accounts_name = Accounts_name.Text
            dr!Amount = Amount.Text
            dr!amount_string = amount_string.Text
            dr!cash_type = cash_type.Text
            dr!cash_pay = cash_pay.Text
            dr!type_pay = type_pay.Text
            dr!check_number = check_number.Text
            dr!check_date = check_date.Text
            dr!type_type = type_type.Text
            dr!code_print = 1
            dr!push = False
            If cash_pay.SelectedIndex = 0 Then
                dr!cash_check = False
            ElseIf cash_pay.SelectedIndex = 1 Then
                dr!cash_check = True
            End If

            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            If cash_pay.SelectedIndex = 0 Then
                '========= الخزينة============
                adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                Dim dr6 = dt.Rows(0)
              
                    dr6!balace = Val(dr6!balace) - Val(Amount.Text)


                Dim cmd6 As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
            '========= الرصيد============
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            adp = New SqlDataAdapter("select * from importer where impcode=N'" & (Accounts_name.EditValue) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr5 = dt.Rows(0)
            dr5!Accounts_balace = Val(dr5!Accounts_balace) + Val(Amount.Text)
            Dim cmd5 As New SqlCommandBuilder(adp)
            adp.Update(dt)

            set_importertrans(Accounts_name.Text, "سداد فواتير", Cashingcode.Text, chashingdate.Value, "", cash_type.Text, Amount.Text, 0, Val(Accounts_balace.Text) + Val(Amount.Text), Amount.Text)
            If cash_pay.SelectedIndex = 0 Then
                treasury_pay(type_pay.Text, "سداد فواتير", chashingdate.Value, Now.ToString("hh:mm:ss:tt"), "صرف نقدية الي " & Accounts_name.Text, XtraForm1.user_name.Text, 0, Val(Amount.Text), Val(treasury_balace.Text) - Val(Amount.Text), Cashingcode.Text)
            End If

            My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
            new_cash()
        End If
    End Sub
   
    Private Sub cash_delegate_SelectedIndexChanged(sender As Object, e As EventArgs)
        Accounts_name.Properties.ImmediatePopup = True
    End Sub

    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged

        Accounts_name.Properties.ImmediatePopup = True
        If cash_pay.SelectedIndex = 0 Then
            Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)

            Dim dr = dt.Rows(0)
            treasury_balace.Text = dr!balace

        End If
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If


    End Sub
    Sub refresh_cash()
        Try
            Accounts_name.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(Accounts_name.Text)
            
            If Accounts_name.Text.Trim() <> Nothing Then

                Accounts_balace.Visible = True
            Else

                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_EditValueChanged(Nothing, Nothing)
        Catch ex As Exception

        End Try
    End Sub
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub cashing_customer_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.Enter Then
            save_print_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub save_print_Click(sender As Object, e As EventArgs) Handles save_print.Click
        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from cash_imp_print where Cashingcode=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet

        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine("D:\hhhh\New folder\beta store v4\beta soft v2.3.1\bin\Debug\print", "cashing_cus_a4.repx"), True)
        rep.DataSource = dt
        Dim frm As New preview
        preview.DocumentViewer1.DocumentSource = rep
        preview.Show()
    End Sub

    Private Sub Amount_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Double.TryParse((Amount.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub check_number_TextChanged(sender As Object, e As EventArgs) Handles check_number.TextChanged
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
    End Sub

    Private Sub check_date_ValueChanged(sender As Object, e As EventArgs) Handles check_date.ValueChanged
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
    End Sub

    Private Sub جديدToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles جديدToolStripMenuItem.Click
        new_cash()
    End Sub

    Private Sub حفظToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظToolStripMenuItem.Click
        save_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub حفظطباعةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظطباعةToolStripMenuItem.Click
        save_print_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        Me.Dispose()
    End Sub
 
    Private Sub Amount_EditValueChanged(sender As Object, e As EventArgs) Handles Amount.EditValueChanged
        Try
            balace_now.Text = Format(Val(Accounts_balace.Text) + Val(Amount.Text), "0.00")
            If Amount.Text.Trim() <> Nothing Then
                balace_now.Visible = True
            Else

                balace_now.Visible = False
            End If
            amount_string.Text = NoToTxt(Amount.Text, "جنية", "قرش") & " " & "فقط لاغير"

            Label5.Text = Val(Amount.Text)
        Catch ex As Exception

        End Try

    End Sub
    Private Sub Amount_KeyUp(sender As Object, e As KeyEventArgs) Handles Amount.KeyUp
        'For q = 0 To dgv.Rows.Count - 1
        '    dgv.Rows(q).Cells(1).Value = 0
        'Next

        'For i = 0 To dgv.Rows.Count - 1
        '    If dgv.Rows(i).Cells(1).Value <> dgv.Rows(i).Cells(3).Value Then
        '        dgv.Rows(i).Cells(1).Value = Val(Label5.Text)
        '        If Val(dgv.Rows(i).Cells(1).Value.ToString) > (dgv.CurrentRow.Cells(3).Value.ToString) Then
        '            dgv.Rows(i).Cells(1).Value = Val(dgv.Rows(i).Cells(3).Value.ToString)
        '        End If
        '        Label5.Text = Val(Label5.Text) - dgv.Rows(i).Cells(1).Value
        '        dgv.Rows(i).Cells(2).Value = Val(dgv.Rows(i).Cells(3).Value.ToString) - Val(dgv.Rows(i).Cells(1).Value.ToString)
        '    End If

        'Next
    End Sub

    Private Sub Accounts_name_EditValueChanged(sender As Object, e As EventArgs) Handles Accounts_name.EditValueChanged
        Try
            Accounts_name.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(Accounts_name.Text)
          
            If Accounts_name.Text.Trim() <> Nothing Then

                Accounts_balace.Visible = True
            Else

                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_EditValueChanged(Nothing, Nothing)

        Catch ex As Exception

        End Try
        Dim str = "select sum(imptrans_Debtor), sum(imptrans_Creditor) from importer_trans where imptomer_name=N'" & Accounts_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("importer_trans")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        Dim sumcredit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
        Accounts_balace.Text = Format(Val(sumdebit), "#,0.00") - Format(Val(sumcredit), "#,0.00")
    End Sub

    Private Sub dgv_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs)
        If e.ColumnIndex = 0 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.check_true
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub

    Private Sub c1_Click(sender As Object, e As EventArgs)

    End Sub


End Class