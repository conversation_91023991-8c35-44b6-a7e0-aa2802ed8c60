Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraCharts
Imports System.Drawing

Public Class AdvancedCostCenterReports
    Inherits XtraForm

    Private TabControl As DevExpress.XtraTab.XtraTabControl
    Private CostAnalysisTab As DevExpress.XtraTab.XtraTabPage
    Private BudgetComparisonTab As DevExpress.XtraTab.XtraTabPage
    Private ProfitLossTab As DevExpress.XtraTab.XtraTabPage
    Private ExpenseDistributionTab As DevExpress.XtraTab.XtraTabPage

    ' تقرير تحليل التكاليف
    Private CostAnalysisGrid As DevExpress.XtraGrid.GridControl
    Private CostAnalysisView As DevExpress.XtraGrid.Views.Grid.GridView
    Private CostAnalysisChart As DevExpress.XtraCharts.ChartControl

    ' تقرير مقارنة الموازنة
    Private BudgetGrid As DevExpress.XtraGrid.GridControl
    Private BudgetView As DevExpress.XtraGrid.Views.Grid.GridView
    Private BudgetChart As DevExpress.XtraCharts.ChartControl

    ' تقرير الأرباح والخسائر
    Private ProfitLossGrid As DevExpress.XtraGrid.GridControl
    Private ProfitLossView As DevExpress.XtraGrid.Views.Grid.GridView
    Private ProfitLossChart As DevExpress.XtraCharts.ChartControl

    ' تقرير توزيع المصروفات
    Private DistributionGrid As DevExpress.XtraGrid.GridControl
    Private DistributionView As DevExpress.XtraGrid.Views.Grid.GridView
    Private DistributionChart As DevExpress.XtraCharts.ChartControl

    ' عناصر التحكم المشتركة
    Private DateFromEdit As DevExpress.XtraEditors.DateEdit
    Private DateToEdit As DevExpress.XtraEditors.DateEdit
    Private CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Private RefreshBtn As DevExpress.XtraEditors.SimpleButton
    Private ExportBtn As DevExpress.XtraEditors.SimpleButton
    Private PrintBtn As DevExpress.XtraEditors.SimpleButton
    Private CloseBtn As DevExpress.XtraEditors.SimpleButton

    Public Sub New()
        InitializeComponent()
        LoadCostCenters()
        LoadAllReports()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "تقارير مراكز التكلفة المتقدمة"
        Me.Size = New Size(1400, 800)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes

        ' إنشاء التبويبات
        CreateTabControl()
        CreateControlPanel()
        CreateCostAnalysisTab()
        CreateBudgetComparisonTab()
        CreateProfitLossTab()
        CreateExpenseDistributionTab()
    End Sub

    Private Sub CreateTabControl()
        TabControl = New DevExpress.XtraTab.XtraTabControl()
        TabControl.Dock = DockStyle.Fill
        TabControl.Location = New Point(0, 60)
        TabControl.Size = New Size(1400, 740)

        ' تبويب تحليل التكاليف
        CostAnalysisTab = New DevExpress.XtraTab.XtraTabPage()
        CostAnalysisTab.Text = "📊 تحليل التكاليف"
        CostAnalysisTab.Name = "CostAnalysisTab"

        ' تبويب مقارنة الموازنة
        BudgetComparisonTab = New DevExpress.XtraTab.XtraTabPage()
        BudgetComparisonTab.Text = "📈 مقارنة الموازنة"
        BudgetComparisonTab.Name = "BudgetComparisonTab"

        ' تبويب الأرباح والخسائر
        ProfitLossTab = New DevExpress.XtraTab.XtraTabPage()
        ProfitLossTab.Text = "📉 الأرباح والخسائر"
        ProfitLossTab.Name = "ProfitLossTab"

        ' تبويب توزيع المصروفات
        ExpenseDistributionTab = New DevExpress.XtraTab.XtraTabPage()
        ExpenseDistributionTab.Text = "📋 توزيع المصروفات"
        ExpenseDistributionTab.Name = "ExpenseDistributionTab"

        TabControl.TabPages.Add(CostAnalysisTab)
        TabControl.TabPages.Add(BudgetComparisonTab)
        TabControl.TabPages.Add(ProfitLossTab)
        TabControl.TabPages.Add(ExpenseDistributionTab)

        Me.Controls.Add(TabControl)
    End Sub

    Private Sub CreateControlPanel()
        Dim panel As New Panel()
        panel.Height = 60
        panel.Dock = DockStyle.Top
        panel.BackColor = Color.LightGray

        ' من تاريخ
        Dim lblFrom As New Label()
        lblFrom.Text = "من تاريخ:"
        lblFrom.Location = New Point(1200, 20)
        lblFrom.Size = New Size(60, 20)
        panel.Controls.Add(lblFrom)

        DateFromEdit = New DevExpress.XtraEditors.DateEdit()
        DateFromEdit.Location = New Point(1100, 18)
        DateFromEdit.Size = New Size(90, 20)
        DateFromEdit.EditValue = DateTime.Now.AddMonths(-1)
        panel.Controls.Add(DateFromEdit)

        ' إلى تاريخ
        Dim lblTo As New Label()
        lblTo.Text = "إلى تاريخ:"
        lblTo.Location = New Point(1000, 20)
        lblTo.Size = New Size(60, 20)
        panel.Controls.Add(lblTo)

        DateToEdit = New DevExpress.XtraEditors.DateEdit()
        DateToEdit.Location = New Point(900, 18)
        DateToEdit.Size = New Size(90, 20)
        DateToEdit.EditValue = DateTime.Now
        panel.Controls.Add(DateToEdit)

        ' مركز التكلفة
        Dim lblCostCenter As New Label()
        lblCostCenter.Text = "مركز التكلفة:"
        lblCostCenter.Location = New Point(800, 20)
        lblCostCenter.Size = New Size(80, 20)
        panel.Controls.Add(lblCostCenter)

        CostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        CostCenterCombo.Location = New Point(680, 18)
        CostCenterCombo.Size = New Size(110, 20)
        panel.Controls.Add(CostCenterCombo)

        ' الأزرار
        RefreshBtn = New DevExpress.XtraEditors.SimpleButton()
        RefreshBtn.Text = "تحديث"
        RefreshBtn.Location = New Point(580, 15)
        RefreshBtn.Size = New Size(80, 25)
        AddHandler RefreshBtn.Click, AddressOf RefreshBtn_Click
        panel.Controls.Add(RefreshBtn)

        ExportBtn = New DevExpress.XtraEditors.SimpleButton()
        ExportBtn.Text = "تصدير"
        ExportBtn.Location = New Point(490, 15)
        ExportBtn.Size = New Size(80, 25)
        AddHandler ExportBtn.Click, AddressOf ExportBtn_Click
        panel.Controls.Add(ExportBtn)

        PrintBtn = New DevExpress.XtraEditors.SimpleButton()
        PrintBtn.Text = "طباعة"
        PrintBtn.Location = New Point(400, 15)
        PrintBtn.Size = New Size(80, 25)
        AddHandler PrintBtn.Click, AddressOf PrintBtn_Click
        panel.Controls.Add(PrintBtn)

        CloseBtn = New DevExpress.XtraEditors.SimpleButton()
        CloseBtn.Text = "إغلاق"
        CloseBtn.Location = New Point(310, 15)
        CloseBtn.Size = New Size(80, 25)
        AddHandler CloseBtn.Click, AddressOf CloseBtn_Click
        panel.Controls.Add(CloseBtn)

        Me.Controls.Add(panel)
    End Sub

    Private Sub CreateCostAnalysisTab()
        ' إنشاء الجدول
        CostAnalysisGrid = New DevExpress.XtraGrid.GridControl()
        CostAnalysisView = New DevExpress.XtraGrid.Views.Grid.GridView()
        CostAnalysisGrid.MainView = CostAnalysisView
        CostAnalysisGrid.Dock = DockStyle.Left
        CostAnalysisGrid.Width = 700

        ' إنشاء الرسم البياني
        CostAnalysisChart = New DevExpress.XtraCharts.ChartControl()
        CostAnalysisChart.Dock = DockStyle.Fill

        CostAnalysisTab.Controls.Add(CostAnalysisChart)
        CostAnalysisTab.Controls.Add(CostAnalysisGrid)
    End Sub

    Private Sub CreateBudgetComparisonTab()
        ' إنشاء الجدول
        BudgetGrid = New DevExpress.XtraGrid.GridControl()
        BudgetView = New DevExpress.XtraGrid.Views.Grid.GridView()
        BudgetGrid.MainView = BudgetView
        BudgetGrid.Dock = DockStyle.Left
        BudgetGrid.Width = 700

        ' إنشاء الرسم البياني
        BudgetChart = New DevExpress.XtraCharts.ChartControl()
        BudgetChart.Dock = DockStyle.Fill

        BudgetComparisonTab.Controls.Add(BudgetChart)
        BudgetComparisonTab.Controls.Add(BudgetGrid)
    End Sub

    Private Sub CreateProfitLossTab()
        ' إنشاء الجدول
        ProfitLossGrid = New DevExpress.XtraGrid.GridControl()
        ProfitLossView = New DevExpress.XtraGrid.Views.Grid.GridView()
        ProfitLossGrid.MainView = ProfitLossView
        ProfitLossGrid.Dock = DockStyle.Left
        ProfitLossGrid.Width = 700

        ' إنشاء الرسم البياني
        ProfitLossChart = New DevExpress.XtraCharts.ChartControl()
        ProfitLossChart.Dock = DockStyle.Fill

        ProfitLossTab.Controls.Add(ProfitLossChart)
        ProfitLossTab.Controls.Add(ProfitLossGrid)
    End Sub

    Private Sub CreateExpenseDistributionTab()
        ' إنشاء الجدول
        DistributionGrid = New DevExpress.XtraGrid.GridControl()
        DistributionView = New DevExpress.XtraGrid.Views.Grid.GridView()
        DistributionGrid.MainView = DistributionView
        DistributionGrid.Dock = DockStyle.Left
        DistributionGrid.Width = 700

        ' إنشاء الرسم البياني
        DistributionChart = New DevExpress.XtraCharts.ChartControl()
        DistributionChart.Dock = DockStyle.Fill

        ExpenseDistributionTab.Controls.Add(DistributionChart)
        ExpenseDistributionTab.Controls.Add(DistributionGrid)
    End Sub

    Private Sub LoadCostCenters()
        Try
            Dim sql As String = "SELECT cost_center_id, cost_center_name + ' - ' + cost_center_code AS display_name FROM cost_centers WHERE is_active = 1 ORDER BY cost_center_name"
            
            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostCenters")
                
                ' إضافة خيار "الكل"
                Dim allRow As DataRow = ds.Tables("CostCenters").NewRow()
                allRow("cost_center_id") = DBNull.Value
                allRow("display_name") = "جميع مراكز التكلفة"
                ds.Tables("CostCenters").Rows.InsertAt(allRow, 0)
                
                CostCenterCombo.Properties.DataSource = ds.Tables("CostCenters")
                CostCenterCombo.Properties.DisplayMember = "display_name"
                CostCenterCombo.Properties.ValueMember = "cost_center_id"
                CostCenterCombo.EditValue = DBNull.Value
            End Using
        Catch ex As Exception
            ' في حالة عدم وجود الجدول
        End Try
    End Sub

    Private Sub LoadAllReports()
        LoadCostAnalysisReport()
        LoadBudgetComparisonReport()
        LoadProfitLossReport()
        LoadExpenseDistributionReport()
    End Sub

    ' 📊 تقرير تحليل التكاليف حسب مركز التكلفة
    Private Sub LoadCostAnalysisReport()
        Try
            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    COUNT(DISTINCT ia.invoice_id) AS 'عدد فواتير المبيعات',
                    ISNULL(SUM(ia.total_invoice), 0) AS 'إجمالي المبيعات',
                    COUNT(DISTINCT ea.Expenses_id) AS 'عدد المصروفات',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'إجمالي المصروفات',
                    ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'صافي النتيجة',
                    CASE
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) = 0 THEN 0
                        ELSE (ISNULL(SUM(ia.total_invoice), 0) / ISNULL(SUM(ea.total_Expenses), 0)) * 100
                    END AS 'نسبة العائد %'
                FROM cost_centers cc
                LEFT JOIN invoice_add ia ON cc.cost_center_id = ia.cost_center_id
                    AND ia.invoice_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                WHERE cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name ORDER BY 'إجمالي المصروفات' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostAnalysis")

                CostAnalysisGrid.DataSource = ds.Tables("CostAnalysis")
                ConfigureCostAnalysisGrid()
                CreateCostAnalysisChart(ds.Tables("CostAnalysis"))
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير تحليل التكاليف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 📈 تقرير مقارنة موازنة مركز تكلفة مقابل الفعلي
    Private Sub LoadBudgetComparisonReport()
        Try
            ' إنشاء جدول الموازنة إذا لم يكن موجوداً
            CreateBudgetTableIfNotExists()

            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    ISNULL(b.budget_amount, 0) AS 'الموازنة المخططة',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'المصروفات الفعلية',
                    ISNULL(b.budget_amount, 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'الفرق',
                    CASE
                        WHEN ISNULL(b.budget_amount, 0) = 0 THEN 0
                        ELSE (ISNULL(SUM(ea.total_Expenses), 0) / ISNULL(b.budget_amount, 0)) * 100
                    END AS 'نسبة الاستخدام %',
                    CASE
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) > ISNULL(b.budget_amount, 0) THEN 'تجاوز الموازنة'
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) = ISNULL(b.budget_amount, 0) THEN 'مطابق للموازنة'
                        ELSE 'أقل من الموازنة'
                    END AS 'الحالة'
                FROM cost_centers cc
                LEFT JOIN cost_center_budget b ON cc.cost_center_id = b.cost_center_id
                    AND b.budget_year = YEAR(GETDATE())
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                WHERE cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name, b.budget_amount ORDER BY 'نسبة الاستخدام %' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "BudgetComparison")

                BudgetGrid.DataSource = ds.Tables("BudgetComparison")
                ConfigureBudgetGrid()
                CreateBudgetChart(ds.Tables("BudgetComparison"))
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير مقارنة الموازنة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 📉 تقرير أرباح وخسائر على مستوى مركز تكلفة
    Private Sub LoadProfitLossReport()
        Try
            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    ISNULL(SUM(ia.total_invoice), 0) AS 'إجمالي الإيرادات',
                    ISNULL(SUM(ia.earn_invoice), 0) AS 'إجمالي الأرباح الإجمالية',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'إجمالي المصروفات',
                    ISNULL(SUM(ia.earn_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'صافي الربح',
                    CASE
                        WHEN ISNULL(SUM(ia.total_invoice), 0) = 0 THEN 0
                        ELSE ((ISNULL(SUM(ia.earn_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0)) / ISNULL(SUM(ia.total_invoice), 0)) * 100
                    END AS 'هامش الربح الصافي %',
                    CASE
                        WHEN ISNULL(SUM(ia.earn_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) > 0 THEN 'ربح'
                        WHEN ISNULL(SUM(ia.earn_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) = 0 THEN 'تعادل'
                        ELSE 'خسارة'
                    END AS 'النتيجة'
                FROM cost_centers cc
                LEFT JOIN invoice_add ia ON cc.cost_center_id = ia.cost_center_id
                    AND ia.invoice_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                WHERE cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name ORDER BY 'صافي الربح' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "ProfitLoss")

                ProfitLossGrid.DataSource = ds.Tables("ProfitLoss")
                ConfigureProfitLossGrid()
                CreateProfitLossChart(ds.Tables("ProfitLoss"))
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير الأرباح والخسائر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' 📋 تقرير توزيع المصروفات حسب مراكز التكلفة
    Private Sub LoadExpenseDistributionReport()
        Try
            Dim sql As String = "
                SELECT
                    'مصروفات عامة' AS 'نوع المصروف',
                    cc.cost_center_name AS 'مركز التكلفة',
                    COUNT(*) AS 'عدد العمليات',
                    SUM(ea.total_Expenses) AS 'إجمالي المبلغ',
                    AVG(ea.total_Expenses) AS 'متوسط المبلغ',
                    (SUM(ea.total_Expenses) * 100.0 / (SELECT SUM(total_Expenses) FROM Expenses_add WHERE Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "')) AS 'النسبة من الإجمالي %'
                FROM Expenses_add ea
                INNER JOIN cost_centers cc ON ea.cost_center_id = cc.cost_center_id
                WHERE ea.Expenses_date BETWEEN '" & DateFromEdit.DateTime.ToString("yyyy-MM-dd") & "' AND '" & DateToEdit.DateTime.ToString("yyyy-MM-dd") & "'
                AND cc.is_active = 1"

            If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
                sql += " AND cc.cost_center_id = " & CostCenterCombo.EditValue.ToString()
            End If

            sql += " GROUP BY cc.cost_center_id, cc.cost_center_name ORDER BY 'إجمالي المبلغ' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "ExpenseDistribution")

                DistributionGrid.DataSource = ds.Tables("ExpenseDistribution")
                ConfigureDistributionGrid()
                CreateDistributionChart(ds.Tables("ExpenseDistribution"))
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل تقرير توزيع المصروفات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CreateBudgetTableIfNotExists()
        Try
            Dim sql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_center_budget' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_center_budget (
                        budget_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_id INT NOT NULL,
                        budget_year INT NOT NULL,
                        budget_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
                        created_date DATETIME DEFAULT GETDATE(),
                        FOREIGN KEY (cost_center_id) REFERENCES cost_centers(cost_center_id)
                    );

                    -- إدراج بيانات تجريبية للموازنة
                    INSERT INTO cost_center_budget (cost_center_id, budget_year, budget_amount)
                    SELECT cost_center_id, YEAR(GETDATE()), 50000
                    FROM cost_centers WHERE is_active = 1;
                END"

            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    Private Sub ConfigureCostAnalysisGrid()
        With CostAnalysisView
            .OptionsView.ShowFooter = True
            .OptionsView.ShowGroupPanel = False

            ' تنسيق الأعمدة الرقمية
            If .Columns("إجمالي المبيعات") IsNot Nothing Then
                .Columns("إجمالي المبيعات").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("إجمالي المبيعات").DisplayFormat.FormatString = "N2"
                .Columns("إجمالي المبيعات").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("إجمالي المصروفات") IsNot Nothing Then
                .Columns("إجمالي المصروفات").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("إجمالي المصروفات").DisplayFormat.FormatString = "N2"
                .Columns("إجمالي المصروفات").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("صافي النتيجة") IsNot Nothing Then
                .Columns("صافي النتيجة").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("صافي النتيجة").DisplayFormat.FormatString = "N2"
                .Columns("صافي النتيجة").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("نسبة العائد %") IsNot Nothing Then
                .Columns("نسبة العائد %").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("نسبة العائد %").DisplayFormat.FormatString = "N1"
            End If
        End With
    End Sub

    Private Sub ConfigureBudgetGrid()
        With BudgetView
            .OptionsView.ShowFooter = True
            .OptionsView.ShowGroupPanel = False

            ' تنسيق الأعمدة الرقمية
            If .Columns("الموازنة المخططة") IsNot Nothing Then
                .Columns("الموازنة المخططة").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("الموازنة المخططة").DisplayFormat.FormatString = "N2"
                .Columns("الموازنة المخططة").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("المصروفات الفعلية") IsNot Nothing Then
                .Columns("المصروفات الفعلية").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("المصروفات الفعلية").DisplayFormat.FormatString = "N2"
                .Columns("المصروفات الفعلية").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("الفرق") IsNot Nothing Then
                .Columns("الفرق").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("الفرق").DisplayFormat.FormatString = "N2"
                .Columns("الفرق").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("نسبة الاستخدام %") IsNot Nothing Then
                .Columns("نسبة الاستخدام %").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("نسبة الاستخدام %").DisplayFormat.FormatString = "N1"
            End If

            ' تلوين الخلايا حسب الحالة
            AddHandler .RowCellStyle, AddressOf BudgetView_RowCellStyle
        End With
    End Sub

    Private Sub ConfigureProfitLossGrid()
        With ProfitLossView
            .OptionsView.ShowFooter = True
            .OptionsView.ShowGroupPanel = False

            ' تنسيق الأعمدة الرقمية
            If .Columns("إجمالي الإيرادات") IsNot Nothing Then
                .Columns("إجمالي الإيرادات").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("إجمالي الإيرادات").DisplayFormat.FormatString = "N2"
                .Columns("إجمالي الإيرادات").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("صافي الربح") IsNot Nothing Then
                .Columns("صافي الربح").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("صافي الربح").DisplayFormat.FormatString = "N2"
                .Columns("صافي الربح").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("هامش الربح الصافي %") IsNot Nothing Then
                .Columns("هامش الربح الصافي %").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("هامش الربح الصافي %").DisplayFormat.FormatString = "N1"
            End If

            ' تلوين الخلايا حسب النتيجة
            AddHandler .RowCellStyle, AddressOf ProfitLossView_RowCellStyle
        End With
    End Sub

    Private Sub ConfigureDistributionGrid()
        With DistributionView
            .OptionsView.ShowFooter = True
            .OptionsView.ShowGroupPanel = False

            ' تنسيق الأعمدة الرقمية
            If .Columns("إجمالي المبلغ") IsNot Nothing Then
                .Columns("إجمالي المبلغ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("إجمالي المبلغ").DisplayFormat.FormatString = "N2"
                .Columns("إجمالي المبلغ").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            End If

            If .Columns("متوسط المبلغ") IsNot Nothing Then
                .Columns("متوسط المبلغ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("متوسط المبلغ").DisplayFormat.FormatString = "N2"
            End If

            If .Columns("النسبة من الإجمالي %") IsNot Nothing Then
                .Columns("النسبة من الإجمالي %").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                .Columns("النسبة من الإجمالي %").DisplayFormat.FormatString = "N1"
            End If
        End With
    End Sub

    Private Sub BudgetView_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs)
        If e.Column.FieldName = "الحالة" Then
            Dim cellValue As String = e.CellValue?.ToString()
            Select Case cellValue
                Case "تجاوز الموازنة"
                    e.Appearance.BackColor = Color.LightCoral
                Case "مطابق للموازنة"
                    e.Appearance.BackColor = Color.LightYellow
                Case "أقل من الموازنة"
                    e.Appearance.BackColor = Color.LightGreen
            End Select
        End If
    End Sub

    Private Sub ProfitLossView_RowCellStyle(sender As Object, e As DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs)
        If e.Column.FieldName = "النتيجة" Then
            Dim cellValue As String = e.CellValue?.ToString()
            Select Case cellValue
                Case "ربح"
                    e.Appearance.BackColor = Color.LightGreen
                Case "تعادل"
                    e.Appearance.BackColor = Color.LightYellow
                Case "خسارة"
                    e.Appearance.BackColor = Color.LightCoral
            End Select
        End If
    End Sub

    ' إنشاء الرسوم البيانية
    Private Sub CreateCostAnalysisChart(dataTable As DataTable)
        Try
            CostAnalysisChart.Series.Clear()

            Dim series As New DevExpress.XtraCharts.Series("إجمالي المصروفات", DevExpress.XtraCharts.ViewType.Bar)

            For Each row As DataRow In dataTable.Rows
                Dim costCenter As String = row("مركز التكلفة").ToString()
                Dim expenses As Decimal = Convert.ToDecimal(row("إجمالي المصروفات"))
                series.Points.Add(New DevExpress.XtraCharts.SeriesPoint(costCenter, expenses))
            Next

            CostAnalysisChart.Series.Add(series)
            CostAnalysisChart.Titles.Clear()
            CostAnalysisChart.Titles.Add(New DevExpress.XtraCharts.ChartTitle() With {.Text = "تحليل التكاليف حسب مركز التكلفة"})
        Catch ex As Exception
            ' تجاهل أخطاء الرسم البياني
        End Try
    End Sub

    Private Sub CreateBudgetChart(dataTable As DataTable)
        Try
            BudgetChart.Series.Clear()

            Dim budgetSeries As New DevExpress.XtraCharts.Series("الموازنة المخططة", DevExpress.XtraCharts.ViewType.Bar)
            Dim actualSeries As New DevExpress.XtraCharts.Series("المصروفات الفعلية", DevExpress.XtraCharts.ViewType.Bar)

            For Each row As DataRow In dataTable.Rows
                Dim costCenter As String = row("مركز التكلفة").ToString()
                Dim budget As Decimal = Convert.ToDecimal(row("الموازنة المخططة"))
                Dim actual As Decimal = Convert.ToDecimal(row("المصروفات الفعلية"))

                budgetSeries.Points.Add(New DevExpress.XtraCharts.SeriesPoint(costCenter, budget))
                actualSeries.Points.Add(New DevExpress.XtraCharts.SeriesPoint(costCenter, actual))
            Next

            BudgetChart.Series.Add(budgetSeries)
            BudgetChart.Series.Add(actualSeries)
            BudgetChart.Titles.Clear()
            BudgetChart.Titles.Add(New DevExpress.XtraCharts.ChartTitle() With {.Text = "مقارنة الموازنة مقابل الفعلي"})
        Catch ex As Exception
            ' تجاهل أخطاء الرسم البياني
        End Try
    End Sub

    Private Sub CreateProfitLossChart(dataTable As DataTable)
        Try
            ProfitLossChart.Series.Clear()

            Dim revenueSeries As New DevExpress.XtraCharts.Series("الإيرادات", DevExpress.XtraCharts.ViewType.Bar)
            Dim profitSeries As New DevExpress.XtraCharts.Series("صافي الربح", DevExpress.XtraCharts.ViewType.Line)

            For Each row As DataRow In dataTable.Rows
                Dim costCenter As String = row("مركز التكلفة").ToString()
                Dim revenue As Decimal = Convert.ToDecimal(row("إجمالي الإيرادات"))
                Dim profit As Decimal = Convert.ToDecimal(row("صافي الربح"))

                revenueSeries.Points.Add(New DevExpress.XtraCharts.SeriesPoint(costCenter, revenue))
                profitSeries.Points.Add(New DevExpress.XtraCharts.SeriesPoint(costCenter, profit))
            Next

            ProfitLossChart.Series.Add(revenueSeries)
            ProfitLossChart.Series.Add(profitSeries)
            ProfitLossChart.Titles.Clear()
            ProfitLossChart.Titles.Add(New DevExpress.XtraCharts.ChartTitle() With {.Text = "الأرباح والخسائر حسب مركز التكلفة"})
        Catch ex As Exception
            ' تجاهل أخطاء الرسم البياني
        End Try
    End Sub

    Private Sub CreateDistributionChart(dataTable As DataTable)
        Try
            DistributionChart.Series.Clear()

            Dim series As New DevExpress.XtraCharts.Series("توزيع المصروفات", DevExpress.XtraCharts.ViewType.Pie)

            For Each row As DataRow In dataTable.Rows
                Dim costCenter As String = row("مركز التكلفة").ToString()
                Dim amount As Decimal = Convert.ToDecimal(row("إجمالي المبلغ"))
                series.Points.Add(New DevExpress.XtraCharts.SeriesPoint(costCenter, amount))
            Next

            DistributionChart.Series.Add(series)
            DistributionChart.Titles.Clear()
            DistributionChart.Titles.Add(New DevExpress.XtraCharts.ChartTitle() With {.Text = "توزيع المصروفات حسب مراكز التكلفة"})
        Catch ex As Exception
            ' تجاهل أخطاء الرسم البياني
        End Try
    End Sub

    ' أحداث الأزرار
    Private Sub RefreshBtn_Click(sender As Object, e As EventArgs)
        LoadAllReports()
    End Sub

    Private Sub ExportBtn_Click(sender As Object, e As EventArgs)
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx|PDF Files|*.pdf"
            saveDialog.Title = "تصدير التقرير"
            saveDialog.FileName = "تقارير_مراكز_التكلفة_" & DateTime.Now.ToString("yyyyMMdd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                Select Case TabControl.SelectedTabPageIndex
                    Case 0 ' تحليل التكاليف
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            CostAnalysisView.ExportToXlsx(saveDialog.FileName)
                        Else
                            CostAnalysisView.ExportToPdf(saveDialog.FileName)
                        End If
                    Case 1 ' مقارنة الموازنة
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            BudgetView.ExportToXlsx(saveDialog.FileName)
                        Else
                            BudgetView.ExportToPdf(saveDialog.FileName)
                        End If
                    Case 2 ' الأرباح والخسائر
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            ProfitLossView.ExportToXlsx(saveDialog.FileName)
                        Else
                            ProfitLossView.ExportToPdf(saveDialog.FileName)
                        End If
                    Case 3 ' توزيع المصروفات
                        If saveDialog.FileName.EndsWith(".xlsx") Then
                            DistributionView.ExportToXlsx(saveDialog.FileName)
                        Else
                            DistributionView.ExportToPdf(saveDialog.FileName)
                        End If
                End Select

                XtraMessageBox.Show("تم تصدير التقرير بنجاح!", "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub PrintBtn_Click(sender As Object, e As EventArgs)
        Try
            Select Case TabControl.SelectedTabPageIndex
                Case 0 ' تحليل التكاليف
                    CostAnalysisGrid.ShowPrintPreview()
                Case 1 ' مقارنة الموازنة
                    BudgetGrid.ShowPrintPreview()
                Case 2 ' الأرباح والخسائر
                    ProfitLossGrid.ShowPrintPreview()
                Case 3 ' توزيع المصروفات
                    DistributionGrid.ShowPrintPreview()
            End Select
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في طباعة التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CloseBtn_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub
End Class
