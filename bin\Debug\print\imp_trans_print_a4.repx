﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.imp_trans_print_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="imp_trans_print_a4" Margins="0, 0, 0, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="imp_trans_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="109.375">
      <Controls>
        <Item1 Ref="6" ControlType="XRLine" Name="XrLine2" SizeF="813.0001,4.791673" LocationFloat="10.00001, 0" />
        <Item2 Ref="7" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="120.6807,98.58337" LocationFloat="15.06916, 4.791673">
          <ExpressionBindings>
            <Item1 Ref="8" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item2>
        <Item3 Ref="9" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="523.5836, 26.99999" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="12" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="523.5836, 58.33332" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="14" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="15" ControlType="PageHeaderBand" Name="PageHeader" HeightF="273.3333">
      <Controls>
        <Item1 Ref="16" ControlType="XRTable" Name="XrTable1" AnchorVertical="Bottom" TextAlignment="MiddleCenter" SizeF="822.0001,33.33333" LocationFloat="2.937238, 240" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White" Borders="All">
          <Rows>
            <Item1 Ref="17" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="18" ControlType="XRTableCell" Name="XrTableCell13" Weight="0.40672567044129865" CanGrow="false" Text="الرصيد" Padding="2,2,0,0,100">
                  <StylePriority Ref="19" UsePadding="false" />
                </Item1>
                <Item2 Ref="20" ControlType="XRTableCell" Name="XrTableCell9" Weight="0.23717896227474722" CanGrow="false" Text="دائن / له" Padding="2,2,0,0,100">
                  <StylePriority Ref="21" UsePadding="false" />
                </Item2>
                <Item3 Ref="22" ControlType="XRTableCell" Name="XrTableCell8" Weight="0.26826169188342636" CanGrow="false" Text="مدين / عليه" Padding="2,2,0,0,100">
                  <StylePriority Ref="23" UsePadding="false" />
                </Item3>
                <Item4 Ref="24" ControlType="XRTableCell" Name="XrTableCell7" Weight="0.88700452614805525" CanGrow="false" Text="البيان" Padding="2,2,0,0,100">
                  <StylePriority Ref="25" UsePadding="false" />
                </Item4>
                <Item5 Ref="26" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.50508789204393634" CanGrow="false" Text="التاريخ" Padding="2,2,0,0,100" />
                <Item6 Ref="27" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.30203908645002669" CanGrow="false" Text="رقم الفاتورة" Padding="2,2,0,0,100" />
                <Item7 Ref="28" ControlType="XRTableCell" Name="XrTableCell3" Weight="0.37526402974941697" CanGrow="false" Text="التعامل" Padding="2,2,0,0,100" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="30" ControlType="XRLine" Name="XrLine1" SizeF="813.0001,4.791673" LocationFloat="5.999832, 0" />
        <Item3 Ref="31" ControlType="XRLabel" Name="XrLabel19" Text="كشف حساب مورد" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="312.9583, 15.20834" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="33" ControlType="XRPageInfo" Name="XrPageInfo1" RightToLeft="Yes" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="32.1145, 163.6667" Padding="2,2,0,0,100">
          <StylePriority Ref="34" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="35" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="أســـــــم مورد :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="692.6458, 112.9167" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="36" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="37" ControlType="XRLabel" Name="XrLabel3" RightToLeft="Yes" Text="XrLabel3" SizeF="395.5833,31.25" LocationFloat="297.0623, 112.9167" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="38" Expression="[importer_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item6>
        <Item7 Ref="40" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="الرصيد :" TextAlignment="TopCenter" SizeF="86.45825,31.24999" LocationFloat="210.6041, 112.9167" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="41" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="42" ControlType="XRLabel" Name="XrLabel5" TextFormatString="{0:#,#}" RightToLeft="Yes" Text="XrLabel5" TextAlignment="TopCenter" SizeF="192.6042,31.24998" LocationFloat="17.99994, 112.9167" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="43" Expression="[Accounts_balace]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="44" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="45" ControlType="XRLabel" Name="XrLabel6" RightToLeft="Yes" Text="في الفترة من :" TextAlignment="TopCenter" SizeF="115.625,23.95833" LocationFloat="523.5836, 55.91665" Font="Droid Arabic Kufi, 9pt" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="46" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="47" ControlType="XRLabel" Name="XrLabel9" TextFormatString="{0:yyyy-MM-dd}" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="115.625,23" LocationFloat="407.9585, 56.87502" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="48" Expression="[Datefrom]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="49" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="50" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="الي :" TextAlignment="TopCenter" SizeF="52.08334,23.95833" LocationFloat="355.8752, 56.87502" Font="Droid Arabic Kufi, 9pt" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="51" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="52" ControlType="XRLabel" Name="XrLabel11" TextFormatString="{0:yyyy-MM-dd}" RightToLeft="Yes" Text="XrLabel11" TextAlignment="TopCenter" SizeF="115.625,23" LocationFloat="240.2501, 57.83332" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="53" Expression="[Dateto]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="54" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="55" ControlType="XRLine" Name="XrLine3" SizeF="823.0001,2.083344" LocationFloat="0, 207.0833" />
      </Controls>
    </Item5>
    <Item6 Ref="56" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="177.25">
      <Controls>
        <Item1 Ref="57" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="491.8752, 152.1667" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="58" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="59" UseFont="false" />
        </Item1>
        <Item2 Ref="60" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="95.83366, 61.45833" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="61" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="63" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="95.83366, 88.62495" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="64" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="66" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="514.5836, 61.45833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="68" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="514.5836, 86.54162" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="69" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="70" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="6.999939, 152.1667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="71" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="72" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="6.999939, 116.6667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="73" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="74" ControlType="XRTable" Name="XrTable3" RightToLeft="Yes" TextAlignment="MiddleCenter" SizeF="139.3472,26.04167" LocationFloat="109.132, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="WhiteSmoke" BackColor="DarkGreen" BorderColor="Black" Borders="All">
          <Rows>
            <Item1 Ref="75" ControlType="XRTableRow" Name="XrTableRow3" Weight="0.*****************">
              <Cells>
                <Item1 Ref="76" ControlType="XRTableCell" Name="XrTableCell15" Weight="0.75630583903029158" TextFormatString="{0:#,#}" Text="XrTableCell15" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="77" Expression="[totalcredit]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="78" ControlType="XRTableCell" Name="XrTableCell16" Weight="0.85542101766390233" TextFormatString="{0:#,#}" Text="XrTableCell16" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="79" Expression="[totaldebit]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                </Item2>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="80" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
      </Controls>
    </Item6>
    <Item7 Ref="81" ControlType="PageFooterBand" Name="PageFooter" HeightF="28.125">
      <Controls>
        <Item1 Ref="82" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="698.5208, 0" Padding="2,2,0,0,100">
          <StylePriority Ref="83" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="84" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="747.4791, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="85" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="86" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="17.99994, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="87" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="88" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="89" ControlType="DetailBand" Name="Detail1" HeightF="29.16667">
          <Controls>
            <Item1 Ref="90" ControlType="XRTable" Name="XrTable2" RightToLeft="Yes" TextAlignment="MiddleCenter" SizeF="822.0001,27.08333" LocationFloat="4.9999, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" BorderColor="Gainsboro" Borders="Top, Bottom">
              <Rows>
                <Item1 Ref="91" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="92" ControlType="XRTableCell" Name="XrTableCell14" Weight="0.40672572987851618" TextFormatString="{0:#,#}" Text="XrTableCell14" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="93" Expression="[imp_trans_print].[importer_balace]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="94" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="95" ControlType="XRTableCell" Name="XrTableCell12" Weight="0.23717900072312484" TextFormatString="{0:#,#}" Text="XrTableCell12" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="96" Expression="[imp_trans_print].[custrans_Creditor]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="97" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="98" ControlType="XRTableCell" Name="XrTableCell11" Weight="0.26826173311703044" TextFormatString="{0:#,#}" Text="XrTableCell11" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="99" Expression="[imp_trans_print].[custrans_Debtor]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="100" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="101" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.87952280023119644" Text="XrTableCell10" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="102" Expression="[imp_trans_print].[custrans_descrebtion]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="103" UseFont="false" UsePadding="false" />
                    </Item4>
                    <Item5 Ref="104" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.50508829331531491" TextFormatString="{0:yyyy-MM-dd}" Text="XrTableCell4" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,96" Borders="Top, Bottom">
                      <ExpressionBindings>
                        <Item1 Ref="105" Expression="[imp_trans_print].[custrans_date]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="106" UseFont="false" UseBorders="false" />
                    </Item5>
                    <Item6 Ref="107" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.30203890998129929" Text="XrTableCell5" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="108" Expression="[imp_trans_print].[bill_number]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item6>
                    <Item7 Ref="109" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.38274578496816647" Text="XrTableCell6" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="110" Expression="[imp_trans_print].[custrans_type]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="111" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="112" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="113" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="114" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="115" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="116" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="117" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="PFNxbERhdGFTb3VyY2U+PE5hbWU+U3FsRGF0YVNvdXJjZTE8L05hbWU+PENvbm5lY3Rpb24gTmFtZT0iZGF0YSIgRnJvbUFwcENvbmZpZz0idHJ1ZSIgLz48UXVlcnkgVHlwZT0iU2VsZWN0UXVlcnkiIE5hbWU9ImltcF90cmFuc19wcmludCI+PFRhYmxlcz48VGFibGUgTmFtZT0iaW1wX3RyYW5zX3ByaW50IiAvPjwvVGFibGVzPjxDb2x1bW5zPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0icmVwb3J0X2NvZGUiIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJpbXBvcnRlcl9uYW1lIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0iQWNjb3VudHNfYmFsYWNlIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0iRGF0ZWZyb20iIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJEYXRldG8iIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJ0b3RhbHZvdWNoZXIiIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJ0b3RhbGRlYml0IiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0idG90YWxjcmVkaXQiIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJwcmludF9jb2RlIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0iY29kZSIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9InNlY19uYW1lIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0ic2VjX3Bob25lIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0ic2VjX2VtYWlsIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0ic2VjX3dlYiIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9InNlY19udW1iZXIiIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJzZWNfYWRkcmVzcyIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9InNlY19zMSIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9InNlY19zMiIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9InNlY19waWMiIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJFeHByMSIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9ImJpbGxfbnVtYmVyIiAvPjxDb2x1bW4gVGFibGU9ImltcF90cmFuc19wcmludCIgTmFtZT0iY3VzdHJhbnNfdHlwZSIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9ImN1c3RyYW5zX2RhdGUiIC8+PENvbHVtbiBUYWJsZT0iaW1wX3RyYW5zX3ByaW50IiBOYW1lPSJjdXN0cmFuc19kZXNjcmVidGlvbiIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9ImN1c3RyYW5zX0RlYnRvciIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9ImltcG9ydGVyX2JhbGFjZSIgLz48Q29sdW1uIFRhYmxlPSJpbXBfdHJhbnNfcHJpbnQiIE5hbWU9ImN1c3RyYW5zX0NyZWRpdG9yIiAvPjwvQ29sdW1ucz48L1F1ZXJ5PjxSZXN1bHRTY2hlbWE+PERhdGFTZXQgTmFtZT0iU3FsRGF0YVNvdXJjZTEiPjxWaWV3IE5hbWU9ImltcF90cmFuc19wcmludCI+PEZpZWxkIE5hbWU9InJlcG9ydF9jb2RlIiBUeXBlPSJJbnQzMiIgLz48RmllbGQgTmFtZT0iaW1wb3J0ZXJfbmFtZSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJBY2NvdW50c19iYWxhY2UiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9IkRhdGVmcm9tIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9IkRhdGV0byIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJ0b3RhbHZvdWNoZXIiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9InRvdGFsZGViaXQiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9InRvdGFsY3JlZGl0IiBUeXBlPSJEZWNpbWFsIiAvPjxGaWVsZCBOYW1lPSJwcmludF9jb2RlIiBUeXBlPSJJbnQzMiIgLz48RmllbGQgTmFtZT0iY29kZSIgVHlwZT0iSW50MzIiIC8+PEZpZWxkIE5hbWU9InNlY19uYW1lIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19waG9uZSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJzZWNfZW1haWwiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX3dlYiIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJzZWNfbnVtYmVyIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19hZGRyZXNzIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19zMSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJzZWNfczIiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX3BpYyIgVHlwZT0iQnl0ZUFycmF5IiAvPjxGaWVsZCBOYW1lPSJFeHByMSIgVHlwZT0iSW50MzIiIC8+PEZpZWxkIE5hbWU9ImJpbGxfbnVtYmVyIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9ImN1c3RyYW5zX3R5cGUiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0iY3VzdHJhbnNfZGF0ZSIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJjdXN0cmFuc19kZXNjcmVidGlvbiIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJjdXN0cmFuc19EZWJ0b3IiIFR5cGU9IkRlY2ltYWwiIC8+PEZpZWxkIE5hbWU9ImltcG9ydGVyX2JhbGFjZSIgVHlwZT0iRGVjaW1hbCIgLz48RmllbGQgTmFtZT0iY3VzdHJhbnNfQ3JlZGl0b3IiIFR5cGU9IkRlY2ltYWwiIC8+PC9WaWV3PjwvRGF0YVNldD48L1Jlc3VsdFNjaGVtYT48Q29ubmVjdGlvbk9wdGlvbnMgQ2xvc2VDb25uZWN0aW9uPSJ0cnVlIiAvPjwvU3FsRGF0YVNvdXJjZT4=" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>