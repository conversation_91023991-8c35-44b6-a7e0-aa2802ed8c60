﻿Imports System.Data.SqlClient

Public Class emp_manged
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_user()
    End Sub

    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from emp_mange order by mange_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("mange_name"))
        Next
    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from emp_mange where mange_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("mange_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        TextBox1.Text = ""
        Dim sql = "select * from emp_mange where mange_id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            TextBox1.Text = dr!mange_name
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub
    Private Sub exit_button_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        TextBox1.Text = ""
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If TextBox1.Text = "" Then
            MsgBox("أدخل اسم الادارة")
            TextBox1.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from emp_mange where mange_name=N'" & (TextBox1.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم الادارة موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!mange_name = TextBox1.Text
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة الادارة")
                new_btn_Click(Nothing, Nothing)
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ الادارة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from emp_mange where mange_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!mange_name = TextBox1.Text

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل الادارة", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل الادارة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from emp_mange where mange_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب المدينة")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف الادارة", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الادارة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub
End Class