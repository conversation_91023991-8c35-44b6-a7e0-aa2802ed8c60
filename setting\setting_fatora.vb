﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports Security.FingerPrint
Imports MicroVisionSerial.Encryption.Activation
Imports System.Management
Imports System.Data.OleDb
Imports System.Drawing.Printing

Public Class setting_fatora
    Sub show_section()
        Dim sql = "select * from section where code=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        sec_name.Text = dr!sec_name

        sec_phone.Text = dr!sec_phone
        sec_address.Text = dr!sec_address
        sec_email.Text = dr!sec_email
        sec_web.Text = dr!sec_web
        sec_number.Text = dr!sec_number
        sec_s1.Text = dr!sec_s1
        sec_s2.Text = dr!sec_s2
        '======================== فك تشفير الصورة------
        If IsDBNull(dr!sec_pic) = False Then
            Dim imagbytearray() As Byte
            imagbytearray = CType(dr!sec_pic, Byte())
            Dim stream As New MemoryStream(imagbytearray)
            Dim bmp As New Bitmap(stream)
            sec_pic.Image = Image.FromStream(stream)
            stream.Close()
        End If
        ''========================================
        code_pic.Text = 0
    End Sub
    Sub fill_printer_dize()
        Try

            Dim sql = "select * from ptinter_pro where windows_name='" & System.Windows.Forms.SystemInformation.ComputerName & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            If dr!print_directry = True Then
                RadioButton2.Checked = True
            Else
                RadioButton1.Checked = True
            End If
            printer_name.Text = dr!printer_name
            report_size.Text = dr!report_size
            report_size_2.Text = dr!report_size_2
            printer_defult.Text = dr!printer_defult
            printer_defult_2.Text = dr!printer_defult_2
            printer1.Text = dr!printer1
            printer2.Text = dr!printer2
            printer3.Text = dr!printer3
            printer4.Text = dr!printer4
            printer5.Text = dr!printer5
            printer6.Text = dr!printer6

            printer12.Text = dr!printer12
            printer22.Text = dr!printer22
            printer32.Text = dr!printer32
            printer42.Text = dr!printer42
            printer52.Text = dr!printer52
            printer62.Text = dr!printer62
            paper1.Text = dr!paper1
            paper2.Text = dr!paper2

        Catch ex As Exception

        End Try
    End Sub
    Private Sub setting_fatora_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        windows_name.Text = System.Windows.Forms.SystemInformation.ComputerName
        Try
            show_section()
        Catch ex As Exception
        End Try
        Try
            show_set_fator()
        Catch ex As Exception
        End Try
        Try
            fill_cobon()
        Catch ex As Exception
        End Try
        Try
            fill_printer()
        Catch ex As Exception
        End Try
        Try
            fill_tax()
        Catch ex As Exception
        End Try

        Try
            fill_printer_dize()
        Catch ex As Exception
        End Try




    End Sub
    Sub fill_tax()
        Dim sql = "select * from tax where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        p1.Checked = dr!p1
        p2.Checked = dr!p2
        p3.Checked = dr!p3
        p4.Checked = dr!p4
        p5.Checked = dr!p5
        p6.Checked = dr!p6
        p7.Checked = dr!p7
        p8.Checked = dr!p8


        p10.Text = dr!p10
        p11.Text = dr!p11
        p12.Text = dr!p12
        p13.Text = dr!p13

    End Sub


    Sub fill_printer()
        Try
            Dim pkInstalledPrinters As String
            For Each pkInstalledPrinters In
                PrinterSettings.InstalledPrinters
                printer_name.Items.Add(pkInstalledPrinters)
                printer_defult.Items.Add(pkInstalledPrinters)
                printer1.Items.Add(pkInstalledPrinters)
                printer2.Items.Add(pkInstalledPrinters)
                printer3.Items.Add(pkInstalledPrinters)
                printer4.Items.Add(pkInstalledPrinters)
                printer5.Items.Add(pkInstalledPrinters)
                printer6.Items.Add(pkInstalledPrinters)

                printer12.Items.Add(pkInstalledPrinters)
                printer22.Items.Add(pkInstalledPrinters)
                printer32.Items.Add(pkInstalledPrinters)
                printer42.Items.Add(pkInstalledPrinters)
                printer52.Items.Add(pkInstalledPrinters)
                printer62.Items.Add(pkInstalledPrinters)
                printer_defult_2.Items.Add(pkInstalledPrinters)
            Next pkInstalledPrinters

            ' Set the combo to the first printer in the list
            printer_name.SelectedIndex = 0
            printer_defult.SelectedIndex = 0
            printer1.SelectedIndex = 0
            printer2.SelectedIndex = 0
            printer3.SelectedIndex = 0
            printer4.SelectedIndex = 0
            printer5.SelectedIndex = 0
            printer6.SelectedIndex = 0

            printer12.SelectedIndex = 0
            printer22.SelectedIndex = 0
            printer32.SelectedIndex = 0
            printer42.SelectedIndex = 0
            printer52.SelectedIndex = 0
            printer62.SelectedIndex = 0
            printer_defult_2.SelectedIndex = 0
            report_size.SelectedIndex = 0
            report_size_2.SelectedIndex = 0
        Catch ex As Exception

        End Try

    End Sub


    Sub show_price1()
        Dim sql = "select * from name_price where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price1.Text = dr!name_price
        price_check1.Checked = dr!price_check
    End Sub
    Sub show_price2()
        Dim sql = "select * from name_price where id=N'" & (2) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price2.Text = dr!name_price
        price_check2.Checked = dr!price_check
    End Sub
    Sub show_price3()
        Dim sql = "select * from name_price where id=N'" & (3) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price3.Text = dr!name_price
        price_check3.Checked = dr!price_check
    End Sub
    Sub show_price4()
        Dim sql = "select * from name_price where id=N'" & (4) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price4.Text = dr!name_price
        price_check4.Checked = dr!price_check
    End Sub
    Sub show_price5()
        Dim sql = "select * from name_price where id=N'" & (5) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price5.Text = dr!name_price
        price_check5.Checked = dr!price_check
    End Sub

    Sub show_set_fator()

        Dim sql = "select * from inv_setting where code=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        w1.Checked = dr!w1
        w2.Checked = dr!w2
        w3.Checked = dr!w3
        w4.Checked = dr!w4
        w5.Checked = dr!w5
        w6.Checked = dr!w6
        w7.Checked = dr!w7
        w8.Checked = dr!w8
        w9.Checked = dr!w9
        w10.Checked = dr!w10
        w11.Checked = dr!w11
        w12.Checked = dr!w12
        w13.Checked = dr!w13
        w14.Checked = dr!w14
        w15.Checked = dr!w15
        w16.Checked = dr!w16
        w17.Checked = dr!w17
        w_desc.Text = dr!w_desc
        w_tax.Text = dr!w_tax
        sale_buy.Text = dr!sale_buy

        w18.Checked = dr!w18
        w19.Checked = dr!w19
        w21.Checked = dr!w21
        w22.Checked = dr!w22
        w23.Text = dr!w23
        w24.Text = dr!w24
        w20.Text = dr!w20
        tax_pur.Text = dr!tax_pur
        tax_invo.Text = dr!tax_invo
        w_desc.Text = dr!w_desc
        w_tax.Text = dr!w_tax

        sale_buy2.Text = dr!sale_buy2
        Try
            balance_check.Checked = dr!balance_check
            balance_w1.Text = dr!balance_w1
            balance_w2.Text = dr!balance_w2
            balance_w3.Text = dr!balance_w3
            balance_w4.Text = dr!balance_w4
        Catch ex As Exception
        End Try
        show_price1()
        show_price2()
        show_price3()
        show_price4()
        show_price5()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If sec_name.Text = "" Then
            XtraMessageBox.Show("اسم المؤسسة فارغ")
            Exit Sub
        End If
        '   OpenFileDialog1.FileName = Nothing
        'Try
        Dim sql = "select * from section where code=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            ''========= بيانات اساسية============
            dr!sec_name = sec_name.Text

            dr!sec_phone = sec_phone.Text
            dr!sec_address = sec_address.Text
            dr!sec_email = sec_email.Text
            dr!sec_web = sec_web.Text
            dr!sec_number = sec_number.Text
            dr!sec_s1 = sec_s1.Text
            dr!sec_s2 = sec_s2.Text
            If code_pic.Text = 1 Then
                ''============حفظ الصورة=============
                If OpenFileDialog1.FileName <> "" Then
                    Dim imagbytearray() As Byte
                    Dim stream As New MemoryStream
                    sec_pic.Image.Save(stream, ImageFormat.Jpeg)
                    imagbytearray = stream.ToArray
                    stream.Close()
                    dr!sec_pic = imagbytearray
                End If
            End If
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        MsgBox("تم الحفظ")
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        Try
            OpenFileDialog1.FileName = ""
            ''   OpenFileDialog1.Filter = "jpeg|*.jpg|bitmap|*.bmp|gif|*.gif|PNG|*.PNG"
            OpenFileDialog1.ShowDialog()
            If OpenFileDialog1.FileName = "" Then Exit Sub
            sec_pic.Image = Image.FromFile(OpenFileDialog1.FileName)
            code_pic.Text = 1
        Catch ex As Exception

        End Try

    End Sub



    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton10_Click(sender As Object, e As EventArgs) Handles SimpleButton10.Click
        sec_pic.Image = My.Resources.white
        code_pic.Text = 1
    End Sub

    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        Dim sql = "select * from inv_setting where code=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            dr!w1 = w1.Checked
            dr!w2 = w2.Checked
            dr!w3 = w3.Checked
            dr!w4 = w4.Checked
            dr!w5 = w5.Checked
            dr!w6 = w6.Checked
            dr!w7 = w7.Checked
            dr!w8 = w8.Checked
            dr!w9 = w9.Checked
            dr!w10 = w10.Checked
            dr!w11 = w11.Checked
            dr!w12 = w12.Checked
            dr!w13 = w13.Checked
            dr!w14 = w14.Checked
            dr!w15 = w15.Checked
            dr!w16 = w16.Checked
            dr!w17 = w17.Checked
            dr!w18 = w18.Checked
            dr!w19 = w19.Checked
            dr!w21 = w21.Checked
            dr!w22 = w22.Checked
            dr!w23 = w23.Text
            dr!w24 = w24.Text
            dr!w20 = w20.Text
            dr!tax_pur = tax_pur.Text
            dr!tax_invo = tax_invo.Text
            dr!w_desc = w_desc.Text
            dr!w_tax = w_tax.Text
            dr!sale_buy = sale_buy.Text
            dr!sale_buy2 = sale_buy2.Text
            Try
                dr!balance_check = balance_check.Checked
                dr!balance_w1 = balance_w1.Text
                dr!balance_w2 = balance_w2.Text
                dr!balance_w3 = balance_w3.Text
                dr!balance_w4 = balance_w4.Text
            Catch ex As Exception

            End Try
          
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            name_price1()
            name_price2()
            name_price3()
            name_price4()
            name_price5()
            MsgBox("تم الحفظ")
            XtraForm1.show_set_fator()
        End If
    End Sub
    Sub name_price1()
        Dim sql = "select * from name_price where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            dr!name_price = price1.Text
            dr!price_check = price_check1.Checked
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub
    Sub name_price2()
        Dim sql = "select * from name_price where id=N'" & (2) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            dr!name_price = price2.Text
            dr!price_check = price_check2.Checked
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub
    Sub name_price3()
        Dim sql = "select * from name_price where id=N'" & (3) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            dr!name_price = price3.Text
            dr!price_check = price_check3.Checked
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub
    Sub name_price4()
        Dim sql = "select * from name_price where id=N'" & (4) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            dr!name_price = price4.Text
            dr!price_check = price_check4.Checked
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub
    Sub name_price5()
        Dim sql = "select * from name_price where id=N'" & (5) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            dr!name_price = price5.Text
            dr!price_check = price_check5.Checked
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub



    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton12_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub SimpleButton15_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        If cobon_code.Text = "" Then
            MsgBox("أدخل كود الكوبون")
            p10.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from cobon where code=N'" & (cobon_code.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                dr!start_date = start_date.Value
                dr!end_date = end_date.Value
                dr!amount_rate = amount_rate.Text
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم تعديل الكوبون")
                cobon_code.Text = ""
                start_date.Value = Now.Date
                end_date.Value = Now.Date
                amount_rate.Text = 0
            Else
                Dim dr = dt.NewRow
                dr!code = cobon_code.Text
                dr!start_date = start_date.Value
                dr!end_date = end_date.Value
                dr!amount_rate = amount_rate.Text
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة الكوبون")
                start_date.Value = Now.Date
                end_date.Value = Now.Date
                amount_rate.Text = 0
            End If
            fill_cobon()
        Catch ex As Exception
            MsgBox("فشل حفظ الكوبون اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub RepositoryItemButtonEdit6_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit6.Click
        Dim sql = "select * from cobon where code=N'" & (GridView2.GetFocusedRowCellValue("code")) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            cobon_code.Text = dr!code
            start_date.Value = dr!start_date
            end_date.Value = dr!end_date
            amount_rate.Text = dr!amount_rate
        End If
    End Sub

 
    Sub fill_cobon()
        Dim adp As New SqlDataAdapter("select * from cobon", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        GridControl1.DataSource = dt
        start_date.Value = Now.Date
        end_date.Value = Now.Date
    End Sub
 

    Private Sub SimpleButton8_Click_1(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton11_Click(sender As Object, e As EventArgs) Handles SimpleButton11.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton12_Click_1(sender As Object, e As EventArgs) Handles SimpleButton12.Click
        SimpleButton7_Click(Nothing, Nothing)
    End Sub
 
    Private Sub SimpleButton9_Click(sender As Object, e As EventArgs) Handles SimpleButton9.Click
        Dim sql = "select * from ptinter_pro where windows_name='" & (windows_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            ''========= بيانات اساسية============
            If RadioButton2.Checked = True Then
                dr!print_directry = True
            ElseIf RadioButton1.Checked = True Then
                dr!print_directry = False
            End If
            dr!printer_name = printer_name.Text
            dr!report_size = report_size.Text
            dr!report_size_2 = report_size_2.Text
            dr!printer_defult = printer_defult.Text
            dr!printer_defult_2 = printer_defult_2.Text
            dr!printer1 = printer1.Text
            dr!printer2 = printer2.Text
            dr!printer3 = printer3.Text
            dr!printer4 = printer4.Text
            dr!printer5 = printer5.Text
            dr!printer6 = printer6.Text
            dr!printer12 = printer12.Text
            dr!printer22 = printer22.Text
            dr!printer32 = printer32.Text
            dr!printer42 = printer42.Text
            dr!printer52 = printer52.Text
            dr!printer62 = printer62.Text
            dr!paper1 = paper1.Text
            dr!paper2 = paper2.Text
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            MsgBox("تم الحفظ")
        Else
            Dim dr = dt.NewRow
            If RadioButton2.Checked = True Then
                dr!print_directry = True
            ElseIf RadioButton1.Checked = True Then
                dr!print_directry = False
            End If
            dr!windows_name = windows_name.Text
            dr!printer_name = printer_name.Text
            dr!report_size = report_size.Text
            dr!report_size_2 = report_size_2.Text
            dr!printer_defult = printer_defult.Text
            dr!printer_defult_2 = printer_defult_2.Text
            dr!printer1 = printer1.Text
            dr!printer2 = printer2.Text
            dr!printer3 = printer3.Text
            dr!printer4 = printer4.Text
            dr!printer5 = printer5.Text
            dr!printer6 = printer6.Text
            dr!printer12 = printer12.Text
            dr!printer22 = printer22.Text
            dr!printer32 = printer32.Text
            dr!printer42 = printer42.Text
            dr!printer52 = printer52.Text
            dr!printer62 = printer62.Text

            dr!paper1 = paper1.Text
            dr!paper2 = paper2.Text
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            MsgBox("تم أضافة الطابعة")
            XtraForm1.fill_printer_dize()
        End If

    End Sub

    Private Sub SimpleButton14_Click(sender As Object, e As EventArgs) Handles SimpleButton14.Click
        Try
            Dim sql = "select * from tax where id=N'" & (1) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                ''========= بيانات اساسية============
                dr!p1 = p1.Checked
                dr!p2 = p2.Checked
                dr!p3 = p3.Checked
                dr!p4 = p4.Checked
                dr!p5 = p5.Checked
                dr!p6 = p6.Checked
                dr!p7 = p7.Checked
                dr!p8 = p8.Checked


                dr!p10 = p10.Text
                dr!p11 = p11.Text
                dr!p12 = p12.Text
                dr!p13 = p13.Text

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Catch ex As Exception

        End Try
    
        MsgBox("تم الحفظ")
    End Sub




    Private Sub RepositoryItemButtonEdit7_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit7.Click
        If XtraMessageBox.Show("هل انت متاكد من حذف هذا الكوبون", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
            Dim sql = "select * from cobon where code=N'" & GridView2.GetFocusedRowCellValue("code") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                fill_cobon()
                XtraMessageBox.Show("تم حذف الكوبون", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        End If
    End Sub
End Class