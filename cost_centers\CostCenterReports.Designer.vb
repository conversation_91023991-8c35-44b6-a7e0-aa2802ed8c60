<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CostCenterReports
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(CostCenterReports))
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit1 = New DevExpress.XtraEditors.PictureEdit()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.CostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.StartDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.EndDateEdit = New DevExpress.XtraEditors.DateEdit()
        Me.SearchButton = New DevExpress.XtraEditors.SimpleButton()
        Me.ExportButton = New DevExpress.XtraEditors.SimpleButton()
        Me.TabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.SummaryTab = New DevExpress.XtraTab.XtraTabPage()
        Me.SummaryGrid = New DevExpress.XtraGrid.GridControl()
        Me.SummaryView = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ColCostCenterCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColCostCenterName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColTotalExpenses = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColTotalRevenues = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColNetProfit = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColProfitMargin = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.DetailsTab = New DevExpress.XtraTab.XtraTabPage()
        Me.DetailsGrid = New DevExpress.XtraGrid.GridControl()
        Me.DetailsView = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ColTransactionDate = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColTransactionType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColTransactionNumber = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColDescription = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColAmount = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColTransactionCostCenter = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ChartsTab = New DevExpress.XtraTab.XtraTabPage()
        Me.SplitContainerControl1 = New DevExpress.XtraEditors.SplitContainerControl()
        Me.ExpensesChart = New DevExpress.XtraCharts.ChartControl()
        Me.RevenuesChart = New DevExpress.XtraCharts.ChartControl()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StartDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StartDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EndDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EndDateEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabControl1.SuspendLayout()
        Me.SummaryTab.SuspendLayout()
        CType(Me.SummaryGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SummaryView, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DetailsTab.SuspendLayout()
        CType(Me.DetailsGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DetailsView, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ChartsTab.SuspendLayout()
        CType(Me.SplitContainerControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SplitContainerControl1.SuspendLayout()
        CType(Me.ExpensesChart, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RevenuesChart, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelControl1
        '
        Me.PanelControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.PanelControl1.Appearance.Options.UseBackColor = True
        Me.PanelControl1.Controls.Add(Me.LabelControl1)
        Me.PanelControl1.Controls.Add(Me.PictureEdit1)
        Me.PanelControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelControl1.Location = New System.Drawing.Point(0, 0)
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(1200, 70)
        Me.PanelControl1.TabIndex = 0
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Segoe UI", 16.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseForeColor = True
        Me.LabelControl1.Location = New System.Drawing.Point(80, 20)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(170, 30)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "تقارير مراكز التكلفة"
        '
        'PictureEdit1
        '
        Me.PictureEdit1.Location = New System.Drawing.Point(20, 15)
        Me.PictureEdit1.Name = "PictureEdit1"
        Me.PictureEdit1.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit1.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit1.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit1.TabIndex = 0
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.TableLayoutPanel1)
        Me.GroupControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.GroupControl1.Location = New System.Drawing.Point(0, 70)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(1200, 80)
        Me.GroupControl1.TabIndex = 1
        Me.GroupControl1.Text = "معايير البحث"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 10.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 10.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl2, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.CostCenterCombo, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl3, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.StartDateEdit, 3, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl4, 4, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.EndDateEdit, 5, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.SearchButton, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.ExportButton, 3, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(2, 23)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.Padding = New System.Windows.Forms.Padding(10)
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 60.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 40.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1196, 55)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl2.Appearance.Options.UseFont = True
        Me.LabelControl2.Location = New System.Drawing.Point(1120, 18)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(63, 19)
        Me.LabelControl2.TabIndex = 0
        Me.LabelControl2.Text = "مركز التكلفة:"
        '
        'CostCenterCombo
        '
        Me.CostCenterCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterCombo.Location = New System.Drawing.Point(827, 16)
        Me.CostCenterCombo.Name = "CostCenterCombo"
        Me.CostCenterCombo.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.CostCenterCombo.Properties.Appearance.Options.UseFont = True
        Me.CostCenterCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CostCenterCombo.Size = New System.Drawing.Size(287, 24)
        Me.CostCenterCombo.TabIndex = 1
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl3.Appearance.Options.UseFont = True
        Me.LabelControl3.Location = New System.Drawing.Point(780, 18)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(24, 19)
        Me.LabelControl3.TabIndex = 2
        Me.LabelControl3.Text = "من:"
        '
        'StartDateEdit
        '
        Me.StartDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.StartDateEdit.EditValue = Nothing
        Me.StartDateEdit.Location = New System.Drawing.Point(604, 16)
        Me.StartDateEdit.Name = "StartDateEdit"
        Me.StartDateEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.StartDateEdit.Properties.Appearance.Options.UseFont = True
        Me.StartDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.StartDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.StartDateEdit.Size = New System.Drawing.Size(170, 24)
        Me.StartDateEdit.TabIndex = 3
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl4.Appearance.Options.UseFont = True
        Me.LabelControl4.Location = New System.Drawing.Point(565, 18)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(21, 19)
        Me.LabelControl4.TabIndex = 4
        Me.LabelControl4.Text = "إلى:"
        '
        'EndDateEdit
        '
        Me.EndDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.EndDateEdit.EditValue = Nothing
        Me.EndDateEdit.Location = New System.Drawing.Point(272, 16)
        Me.EndDateEdit.Name = "EndDateEdit"
        Me.EndDateEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.EndDateEdit.Properties.Appearance.Options.UseFont = True
        Me.EndDateEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EndDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.EndDateEdit.Size = New System.Drawing.Size(287, 24)
        Me.EndDateEdit.TabIndex = 5
        '
        'SearchButton
        '
        Me.SearchButton.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.SearchButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.SearchButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.SearchButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.SearchButton.Appearance.Options.UseBackColor = True
        Me.SearchButton.Appearance.Options.UseFont = True
        Me.SearchButton.Appearance.Options.UseForeColor = True
        Me.SearchButton.Location = New System.Drawing.Point(927, 37)
        Me.SearchButton.Name = "SearchButton"
        Me.SearchButton.Size = New System.Drawing.Size(87, 25)
        Me.SearchButton.TabIndex = 6
        Me.SearchButton.Text = "بحث"
        '
        'ExportButton
        '
        Me.ExportButton.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.ExportButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.ExportButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.ExportButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.ExportButton.Appearance.Options.UseBackColor = True
        Me.ExportButton.Appearance.Options.UseFont = True
        Me.ExportButton.Appearance.Options.UseForeColor = True
        Me.ExportButton.Location = New System.Drawing.Point(645, 37)
        Me.ExportButton.Name = "ExportButton"
        Me.ExportButton.Size = New System.Drawing.Size(87, 25)
        Me.ExportButton.TabIndex = 7
        Me.ExportButton.Text = "تصدير"
        '
        'TabControl1
        '
        Me.TabControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TabControl1.Location = New System.Drawing.Point(0, 150)
        Me.TabControl1.Name = "TabControl1"
        Me.TabControl1.SelectedTabPage = Me.SummaryTab
        Me.TabControl1.Size = New System.Drawing.Size(1200, 450)
        Me.TabControl1.TabIndex = 2
        Me.TabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.SummaryTab, Me.DetailsTab, Me.ChartsTab})
        '
        'SummaryTab
        '
        Me.SummaryTab.Controls.Add(Me.SummaryGrid)
        Me.SummaryTab.Name = "SummaryTab"
        Me.SummaryTab.Size = New System.Drawing.Size(1194, 422)
        Me.SummaryTab.Text = "الملخص"
        '
        'SummaryGrid
        '
        Me.SummaryGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SummaryGrid.Location = New System.Drawing.Point(0, 0)
        Me.SummaryGrid.MainView = Me.SummaryView
        Me.SummaryGrid.Name = "SummaryGrid"
        Me.SummaryGrid.Size = New System.Drawing.Size(1194, 422)
        Me.SummaryGrid.TabIndex = 0
        Me.SummaryGrid.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.SummaryView})
        '
        'SummaryView
        '
        Me.SummaryView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.ColCostCenterCode, Me.ColCostCenterName, Me.ColTotalExpenses, Me.ColTotalRevenues, Me.ColNetProfit, Me.ColProfitMargin})
        Me.SummaryView.GridControl = Me.SummaryGrid
        Me.SummaryView.Name = "SummaryView"
        Me.SummaryView.OptionsView.ShowGroupPanel = False
        '
        'ColCostCenterCode
        '
        Me.ColCostCenterCode.Caption = "كود المركز"
        Me.ColCostCenterCode.FieldName = "cost_center_code"
        Me.ColCostCenterCode.Name = "ColCostCenterCode"
        Me.ColCostCenterCode.Visible = True
        Me.ColCostCenterCode.VisibleIndex = 0
        Me.ColCostCenterCode.Width = 100
        '
        'ColCostCenterName
        '
        Me.ColCostCenterName.Caption = "اسم المركز"
        Me.ColCostCenterName.FieldName = "cost_center_name"
        Me.ColCostCenterName.Name = "ColCostCenterName"
        Me.ColCostCenterName.Visible = True
        Me.ColCostCenterName.VisibleIndex = 1
        Me.ColCostCenterName.Width = 200
        '
        'ColTotalExpenses
        '
        Me.ColTotalExpenses.Caption = "إجمالي المصروفات"
        Me.ColTotalExpenses.FieldName = "total_expenses"
        Me.ColTotalExpenses.Name = "ColTotalExpenses"
        Me.ColTotalExpenses.Visible = True
        Me.ColTotalExpenses.VisibleIndex = 2
        Me.ColTotalExpenses.Width = 150
        '
        'ColTotalRevenues
        '
        Me.ColTotalRevenues.Caption = "إجمالي الإيرادات"
        Me.ColTotalRevenues.FieldName = "total_revenues"
        Me.ColTotalRevenues.Name = "ColTotalRevenues"
        Me.ColTotalRevenues.Visible = True
        Me.ColTotalRevenues.VisibleIndex = 3
        Me.ColTotalRevenues.Width = 150
        '
        'ColNetProfit
        '
        Me.ColNetProfit.Caption = "صافي الربح"
        Me.ColNetProfit.FieldName = "net_profit"
        Me.ColNetProfit.Name = "ColNetProfit"
        Me.ColNetProfit.Visible = True
        Me.ColNetProfit.VisibleIndex = 4
        Me.ColNetProfit.Width = 150
        '
        'ColProfitMargin
        '
        Me.ColProfitMargin.Caption = "هامش الربح %"
        Me.ColProfitMargin.FieldName = "profit_margin"
        Me.ColProfitMargin.Name = "ColProfitMargin"
        Me.ColProfitMargin.Visible = True
        Me.ColProfitMargin.VisibleIndex = 5
        Me.ColProfitMargin.Width = 120
        '
        'DetailsTab
        '
        Me.DetailsTab.Controls.Add(Me.DetailsGrid)
        Me.DetailsTab.Name = "DetailsTab"
        Me.DetailsTab.Size = New System.Drawing.Size(1194, 422)
        Me.DetailsTab.Text = "التفاصيل"
        '
        'DetailsGrid
        '
        Me.DetailsGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.DetailsGrid.Location = New System.Drawing.Point(0, 0)
        Me.DetailsGrid.MainView = Me.DetailsView
        Me.DetailsGrid.Name = "DetailsGrid"
        Me.DetailsGrid.Size = New System.Drawing.Size(1194, 422)
        Me.DetailsGrid.TabIndex = 0
        Me.DetailsGrid.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.DetailsView})
        '
        'DetailsView
        '
        Me.DetailsView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.ColTransactionDate, Me.ColTransactionType, Me.ColTransactionNumber, Me.ColDescription, Me.ColAmount, Me.ColTransactionCostCenter})
        Me.DetailsView.GridControl = Me.DetailsGrid
        Me.DetailsView.Name = "DetailsView"
        Me.DetailsView.OptionsView.ShowGroupPanel = False
        '
        'ColTransactionDate
        '
        Me.ColTransactionDate.Caption = "التاريخ"
        Me.ColTransactionDate.FieldName = "transaction_date"
        Me.ColTransactionDate.Name = "ColTransactionDate"
        Me.ColTransactionDate.Visible = True
        Me.ColTransactionDate.VisibleIndex = 0
        Me.ColTransactionDate.Width = 100
        '
        'ColTransactionType
        '
        Me.ColTransactionType.Caption = "نوع العملية"
        Me.ColTransactionType.FieldName = "transaction_type"
        Me.ColTransactionType.Name = "ColTransactionType"
        Me.ColTransactionType.Visible = True
        Me.ColTransactionType.VisibleIndex = 1
        Me.ColTransactionType.Width = 120
        '
        'ColTransactionNumber
        '
        Me.ColTransactionNumber.Caption = "رقم العملية"
        Me.ColTransactionNumber.FieldName = "transaction_number"
        Me.ColTransactionNumber.Name = "ColTransactionNumber"
        Me.ColTransactionNumber.Visible = True
        Me.ColTransactionNumber.VisibleIndex = 2
        Me.ColTransactionNumber.Width = 120
        '
        'ColDescription
        '
        Me.ColDescription.Caption = "الوصف"
        Me.ColDescription.FieldName = "description"
        Me.ColDescription.Name = "ColDescription"
        Me.ColDescription.Visible = True
        Me.ColDescription.VisibleIndex = 3
        Me.ColDescription.Width = 200
        '
        'ColAmount
        '
        Me.ColAmount.Caption = "المبلغ"
        Me.ColAmount.FieldName = "amount"
        Me.ColAmount.Name = "ColAmount"
        Me.ColAmount.Visible = True
        Me.ColAmount.VisibleIndex = 4
        Me.ColAmount.Width = 150
        '
        'ColTransactionCostCenter
        '
        Me.ColTransactionCostCenter.Caption = "مركز التكلفة"
        Me.ColTransactionCostCenter.FieldName = "cost_center_name"
        Me.ColTransactionCostCenter.Name = "ColTransactionCostCenter"
        Me.ColTransactionCostCenter.Visible = True
        Me.ColTransactionCostCenter.VisibleIndex = 5
        Me.ColTransactionCostCenter.Width = 150
        '
        'ChartsTab
        '
        Me.ChartsTab.Controls.Add(Me.SplitContainerControl1)
        Me.ChartsTab.Name = "ChartsTab"
        Me.ChartsTab.Size = New System.Drawing.Size(1194, 422)
        Me.ChartsTab.Text = "الرسوم البيانية"
        '
        'SplitContainerControl1
        '
        Me.SplitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainerControl1.Horizontal = False
        Me.SplitContainerControl1.Location = New System.Drawing.Point(0, 0)
        Me.SplitContainerControl1.Name = "SplitContainerControl1"
        Me.SplitContainerControl1.Panel1.Controls.Add(Me.ExpensesChart)
        Me.SplitContainerControl1.Panel1.Text = "Panel1"
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.RevenuesChart)
        Me.SplitContainerControl1.Panel2.Text = "Panel2"
        Me.SplitContainerControl1.Size = New System.Drawing.Size(1194, 422)
        Me.SplitContainerControl1.SplitterPosition = 211
        Me.SplitContainerControl1.TabIndex = 0
        '
        'ExpensesChart
        '
        Me.ExpensesChart.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ExpensesChart.Location = New System.Drawing.Point(0, 0)
        Me.ExpensesChart.Name = "ExpensesChart"
        Me.ExpensesChart.SeriesSerializable = New DevExpress.XtraCharts.Series(-1) {}
        Me.ExpensesChart.Size = New System.Drawing.Size(1194, 211)
        Me.ExpensesChart.TabIndex = 0
        '
        'RevenuesChart
        '
        Me.RevenuesChart.Dock = System.Windows.Forms.DockStyle.Fill
        Me.RevenuesChart.Location = New System.Drawing.Point(0, 0)
        Me.RevenuesChart.Name = "RevenuesChart"
        Me.RevenuesChart.SeriesSerializable = New DevExpress.XtraCharts.Series(-1) {}
        Me.RevenuesChart.Size = New System.Drawing.Size(1194, 206)
        Me.RevenuesChart.TabIndex = 0
        '
        'CostCenterReports
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1200, 600)
        Me.Controls.Add(Me.TabControl1)
        Me.Controls.Add(Me.GroupControl1)
        Me.Controls.Add(Me.PanelControl1)
        Me.LookAndFeel.SkinName = "Office 2016 Colorful"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "CostCenterReports"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "تقارير مراكز التكلفة"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        Me.PanelControl1.PerformLayout()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StartDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StartDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EndDateEdit.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EndDateEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabControl1.ResumeLayout(False)
        Me.SummaryTab.ResumeLayout(False)
        CType(Me.SummaryGrid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SummaryView, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DetailsTab.ResumeLayout(False)
        CType(Me.DetailsGrid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DetailsView, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ChartsTab.ResumeLayout(False)
        CType(Me.SplitContainerControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SplitContainerControl1.ResumeLayout(False)
        CType(Me.ExpensesChart, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RevenuesChart, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit1 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents TableLayoutPanel1 As TableLayoutPanel
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents StartDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents EndDateEdit As DevExpress.XtraEditors.DateEdit
    Friend WithEvents SearchButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ExportButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents SummaryTab As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents SummaryGrid As DevExpress.XtraGrid.GridControl
    Friend WithEvents SummaryView As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents ColCostCenterCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColCostCenterName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColTotalExpenses As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColTotalRevenues As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColNetProfit As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColProfitMargin As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents DetailsTab As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents DetailsGrid As DevExpress.XtraGrid.GridControl
    Friend WithEvents DetailsView As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents ColTransactionDate As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColTransactionType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColTransactionNumber As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColDescription As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColAmount As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColTransactionCostCenter As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ChartsTab As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents SplitContainerControl1 As DevExpress.XtraEditors.SplitContainerControl
    Friend WithEvents ExpensesChart As DevExpress.XtraCharts.ChartControl
    Friend WithEvents RevenuesChart As DevExpress.XtraCharts.ChartControl

End Class
