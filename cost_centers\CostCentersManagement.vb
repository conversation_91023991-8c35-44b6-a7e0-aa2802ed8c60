Imports DevExpress.XtraEditors
Imports System.Data.SqlClient
Imports System.Drawing

Public Class CostCentersManagement
    
    Private currentCostCenterId As Integer = 0
    Private isEditMode As Boolean = False
    
    Private Sub CostCentersManagement_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تطبيق نظام الألوان
            ApplyColorScheme()
            
            ' تحميل البيانات
            LoadCostCenters()
            LoadParentCostCenters()
            
            ' إعداد الحالة الأولية
            SetInitialState()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub ApplyColorScheme()
        ' تطبيق الألوان من نظام الألوان الموحد
        Me.BackColor = ColorScheme.BackgroundLight
        
        ' تطبيق الألوان على الأزرار
        ColorScheme.ApplyButtonColors(NewButton, ColorScheme.SuccessGreen, ColorScheme.SuccessGreenHover)
        ColorScheme.ApplyButtonColors(SaveButton, ColorScheme.PrimaryBlue, ColorScheme.PrimaryBlueHover)
        ColorScheme.ApplyButtonColors(EditButton, ColorScheme.WarningYellow, ColorScheme.WarningYellowHover)
        ColorScheme.ApplyButtonColors(DeleteButton, ColorScheme.DangerRed, ColorScheme.DangerRedHover)
        ColorScheme.ApplyButtonColors(RefreshButton, ColorScheme.InfoPurple, ColorScheme.InfoPurpleHover)
    End Sub
    
    Private Sub LoadCostCenters()
        Try
            Dim sql = "SELECT cost_center_id, cost_center_code, cost_center_name, " &
                     "cost_center_description, cost_center_type, budget_amount, " &
                     "is_active, created_date, notes FROM cost_centers ORDER BY cost_center_code"
            
            Dim adapter As New SqlDataAdapter(sql, sqlconn)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            
            CostCentersGrid.DataSource = dataTable
            
            ' تنسيق الأعمدة
            CostCentersView.Columns("budget_amount").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            CostCentersView.Columns("budget_amount").DisplayFormat.FormatString = "N0"
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub LoadParentCostCenters()
        Try
            Dim sql = "SELECT cost_center_id, cost_center_name FROM cost_centers WHERE is_active = 1 ORDER BY cost_center_name"
            
            Dim adapter As New SqlDataAdapter(sql, sqlconn)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            
            ' إضافة صف فارغ للاختيار
            Dim emptyRow = dataTable.NewRow()
            emptyRow("cost_center_id") = DBNull.Value
            emptyRow("cost_center_name") = "-- لا يوجد --"
            dataTable.Rows.InsertAt(emptyRow, 0)
            
            ParentCostCenterCombo.Properties.DataSource = dataTable
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل المراكز الأب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub SetInitialState()
        ' تعيين الحالة الأولية للنموذج
        ClearForm()
        SetFormMode(False)
        
        ' تعيين القيم الافتراضية
        CostCenterTypeCombo.SelectedIndex = 0 ' عام
        IsActiveCheck.Checked = True
        BudgetAmountEdit.Value = 0
    End Sub
    
    Private Sub ClearForm()
        ' مسح جميع الحقول
        CostCenterCodeEdit.Text = ""
        CostCenterNameEdit.Text = ""
        CostCenterDescriptionEdit.Text = ""
        CostCenterTypeCombo.SelectedIndex = -1
        ParentCostCenterCombo.EditValue = DBNull.Value
        BudgetAmountEdit.Value = 0
        NotesEdit.Text = ""
        IsActiveCheck.Checked = True
        
        currentCostCenterId = 0
        isEditMode = False
    End Sub
    
    Private Sub SetFormMode(enabled As Boolean)
        ' تفعيل/إلغاء تفعيل الحقول
        CostCenterCodeEdit.Enabled = enabled
        CostCenterNameEdit.Enabled = enabled
        CostCenterDescriptionEdit.Enabled = enabled
        CostCenterTypeCombo.Enabled = enabled
        ParentCostCenterCombo.Enabled = enabled
        BudgetAmountEdit.Enabled = enabled
        NotesEdit.Enabled = enabled
        IsActiveCheck.Enabled = enabled
        
        ' تفعيل/إلغاء تفعيل الأزرار
        SaveButton.Enabled = enabled
        EditButton.Enabled = Not enabled And CostCentersView.FocusedRowHandle >= 0
        DeleteButton.Enabled = Not enabled And CostCentersView.FocusedRowHandle >= 0
    End Sub
    
    Private Sub NewButton_Click(sender As Object, e As EventArgs) Handles NewButton.Click
        Try
            ClearForm()
            SetFormMode(True)
            isEditMode = False
            
            ' توليد كود جديد
            CostCenterCodeEdit.Text = GenerateNewCode()
            CostCenterNameEdit.Focus()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في إنشاء مركز تكلفة جديد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function GenerateNewCode() As String
        Try
            Dim sql = "SELECT MAX(CAST(SUBSTRING(cost_center_code, 3, LEN(cost_center_code)) AS INT)) FROM cost_centers WHERE cost_center_code LIKE 'CC%'"
            Dim cmd As New SqlCommand(sql, sqlconn)
            Dim result = cmd.ExecuteScalar()
            
            Dim nextNumber As Integer = 1
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                nextNumber = Convert.ToInt32(result) + 1
            End If
            
            Return "CC" & nextNumber.ToString("000")
            
        Catch ex As Exception
            Return "CC001"
        End Try
    End Function
    
    Private Sub SaveButton_Click(sender As Object, e As EventArgs) Handles SaveButton.Click
        Try
            ' التحقق من صحة البيانات
            If Not ValidateData() Then
                Exit Sub
            End If
            
            If isEditMode Then
                UpdateCostCenter()
            Else
                InsertCostCenter()
            End If
            
            ' إعادة تحميل البيانات
            LoadCostCenters()
            LoadParentCostCenters()
            SetFormMode(False)
            
            XtraMessageBox.Show("تم حفظ البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في حفظ البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function ValidateData() As Boolean
        ' التحقق من الحقول المطلوبة
        If String.IsNullOrWhiteSpace(CostCenterCodeEdit.Text) Then
            XtraMessageBox.Show("يجب إدخال كود مركز التكلفة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CostCenterCodeEdit.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(CostCenterNameEdit.Text) Then
            XtraMessageBox.Show("يجب إدخال اسم مركز التكلفة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CostCenterNameEdit.Focus()
            Return False
        End If
        
        If CostCenterTypeCombo.SelectedIndex = -1 Then
            XtraMessageBox.Show("يجب اختيار نوع مركز التكلفة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CostCenterTypeCombo.Focus()
            Return False
        End If
        
        ' التحقق من عدم تكرار الكود
        If Not isEditMode OrElse (isEditMode AndAlso CheckCodeExists(CostCenterCodeEdit.Text, currentCostCenterId)) Then
            If CheckCodeExists(CostCenterCodeEdit.Text, If(isEditMode, currentCostCenterId, 0)) Then
                XtraMessageBox.Show("كود مركز التكلفة موجود مسبقاً", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                CostCenterCodeEdit.Focus()
                Return False
            End If
        End If
        
        Return True
    End Function
    
    Private Function CheckCodeExists(code As String, excludeId As Integer) As Boolean
        Try
            Dim sql = "SELECT COUNT(*) FROM cost_centers WHERE cost_center_code = @code"
            If excludeId > 0 Then
                sql &= " AND cost_center_id <> @excludeId"
            End If
            
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@code", code)
            If excludeId > 0 Then
                cmd.Parameters.AddWithValue("@excludeId", excludeId)
            End If
            
            Return Convert.ToInt32(cmd.ExecuteScalar()) > 0
            
        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Sub InsertCostCenter()
        Dim sql = "INSERT INTO cost_centers (cost_center_code, cost_center_name, cost_center_description, " &
                 "cost_center_type, parent_cost_center_id, budget_amount, is_active, notes, created_date, created_by) " &
                 "VALUES (@code, @name, @description, @type, @parent, @budget, @active, @notes, GETDATE(), @user)"

        Dim cmd As New SqlCommand(sql, sqlconn)
        cmd.Parameters.AddWithValue("@code", CostCenterCodeEdit.Text.Trim())
        cmd.Parameters.AddWithValue("@name", CostCenterNameEdit.Text.Trim())
        cmd.Parameters.AddWithValue("@description", If(String.IsNullOrWhiteSpace(CostCenterDescriptionEdit.Text), DBNull.Value, CostCenterDescriptionEdit.Text.Trim()))
        cmd.Parameters.AddWithValue("@type", CostCenterTypeCombo.Text)
        cmd.Parameters.AddWithValue("@parent", If(ParentCostCenterCombo.EditValue Is Nothing OrElse IsDBNull(ParentCostCenterCombo.EditValue), DBNull.Value, ParentCostCenterCombo.EditValue))
        cmd.Parameters.AddWithValue("@budget", BudgetAmountEdit.Value)
        cmd.Parameters.AddWithValue("@active", IsActiveCheck.Checked)
        cmd.Parameters.AddWithValue("@notes", If(String.IsNullOrWhiteSpace(NotesEdit.Text), DBNull.Value, NotesEdit.Text.Trim()))
        cmd.Parameters.AddWithValue("@user", XtraForm1.username.Text)

        cmd.ExecuteNonQuery()
    End Sub

    Private Sub UpdateCostCenter()
        Dim sql = "UPDATE cost_centers SET cost_center_code = @code, cost_center_name = @name, " &
                 "cost_center_description = @description, cost_center_type = @type, " &
                 "parent_cost_center_id = @parent, budget_amount = @budget, is_active = @active, " &
                 "notes = @notes, modified_date = GETDATE(), modified_by = @user " &
                 "WHERE cost_center_id = @id"

        Dim cmd As New SqlCommand(sql, sqlconn)
        cmd.Parameters.AddWithValue("@code", CostCenterCodeEdit.Text.Trim())
        cmd.Parameters.AddWithValue("@name", CostCenterNameEdit.Text.Trim())
        cmd.Parameters.AddWithValue("@description", If(String.IsNullOrWhiteSpace(CostCenterDescriptionEdit.Text), DBNull.Value, CostCenterDescriptionEdit.Text.Trim()))
        cmd.Parameters.AddWithValue("@type", CostCenterTypeCombo.Text)
        cmd.Parameters.AddWithValue("@parent", If(ParentCostCenterCombo.EditValue Is Nothing OrElse IsDBNull(ParentCostCenterCombo.EditValue), DBNull.Value, ParentCostCenterCombo.EditValue))
        cmd.Parameters.AddWithValue("@budget", BudgetAmountEdit.Value)
        cmd.Parameters.AddWithValue("@active", IsActiveCheck.Checked)
        cmd.Parameters.AddWithValue("@notes", If(String.IsNullOrWhiteSpace(NotesEdit.Text), DBNull.Value, NotesEdit.Text.Trim()))
        cmd.Parameters.AddWithValue("@user", XtraForm1.username.Text)
        cmd.Parameters.AddWithValue("@id", currentCostCenterId)

        cmd.ExecuteNonQuery()
    End Sub

    Private Sub EditButton_Click(sender As Object, e As EventArgs) Handles EditButton.Click
        Try
            If CostCentersView.FocusedRowHandle < 0 Then
                XtraMessageBox.Show("يجب اختيار مركز تكلفة للتعديل", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            LoadSelectedCostCenter()
            SetFormMode(True)
            isEditMode = True

            CostCenterNameEdit.Focus()

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل بيانات مركز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadSelectedCostCenter()
        Dim row = CostCentersView.GetFocusedDataRow()
        If row IsNot Nothing Then
            currentCostCenterId = Convert.ToInt32(row("cost_center_id"))
            CostCenterCodeEdit.Text = row("cost_center_code").ToString()
            CostCenterNameEdit.Text = row("cost_center_name").ToString()
            CostCenterDescriptionEdit.Text = If(IsDBNull(row("cost_center_description")), "", row("cost_center_description").ToString())
            CostCenterTypeCombo.Text = row("cost_center_type").ToString()
            BudgetAmountEdit.Value = If(IsDBNull(row("budget_amount")), 0, Convert.ToDecimal(row("budget_amount")))
            IsActiveCheck.Checked = Convert.ToBoolean(row("is_active"))
            NotesEdit.Text = If(IsDBNull(row("notes")), "", row("notes").ToString())

            ' تحميل المركز الأب إذا وجد
            LoadParentForEdit()
        End If
    End Sub

    Private Sub LoadParentForEdit()
        Try
            Dim sql = "SELECT parent_cost_center_id FROM cost_centers WHERE cost_center_id = @id"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", currentCostCenterId)

            Dim result = cmd.ExecuteScalar()
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                ParentCostCenterCombo.EditValue = result
            Else
                ParentCostCenterCombo.EditValue = DBNull.Value
            End If

        Catch ex As Exception
            ParentCostCenterCombo.EditValue = DBNull.Value
        End Try
    End Sub

    Private Sub DeleteButton_Click(sender As Object, e As EventArgs) Handles DeleteButton.Click
        Try
            If CostCentersView.FocusedRowHandle < 0 Then
                XtraMessageBox.Show("يجب اختيار مركز تكلفة للحذف", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            Dim result = XtraMessageBox.Show("هل أنت متأكد من حذف مركز التكلفة المختار؟" & vbCrLf & "هذا الإجراء لا يمكن التراجع عنه",
                                           "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

            If result = DialogResult.Yes Then
                DeleteSelectedCostCenter()
                LoadCostCenters()
                LoadParentCostCenters()
                ClearForm()
                SetFormMode(False)

                XtraMessageBox.Show("تم حذف مركز التكلفة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في حذف مركز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub DeleteSelectedCostCenter()
        Dim row = CostCentersView.GetFocusedDataRow()
        If row IsNot Nothing Then
            Dim costCenterId = Convert.ToInt32(row("cost_center_id"))

            ' التحقق من وجود مراكز فرعية
            If HasChildCostCenters(costCenterId) Then
                Throw New Exception("لا يمكن حذف مركز التكلفة لأنه يحتوي على مراكز فرعية")
            End If

            ' التحقق من وجود عمليات مرتبطة
            If HasLinkedTransactions(costCenterId) Then
                Throw New Exception("لا يمكن حذف مركز التكلفة لأنه مرتبط بعمليات مالية")
            End If

            Dim sql = "DELETE FROM cost_centers WHERE cost_center_id = @id"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", costCenterId)
            cmd.ExecuteNonQuery()
        End If
    End Sub

    Private Function HasChildCostCenters(parentId As Integer) As Boolean
        Try
            Dim sql = "SELECT COUNT(*) FROM cost_centers WHERE parent_cost_center_id = @parentId"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@parentId", parentId)

            Return Convert.ToInt32(cmd.ExecuteScalar()) > 0

        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Function HasLinkedTransactions(costCenterId As Integer) As Boolean
        Try
            ' التحقق من الفواتير
            Dim sql1 = "SELECT COUNT(*) FROM invoice_add WHERE cost_center_id = @id"
            Dim cmd1 As New SqlCommand(sql1, sqlconn)
            cmd1.Parameters.AddWithValue("@id", costCenterId)
            If Convert.ToInt32(cmd1.ExecuteScalar()) > 0 Then Return True

            ' التحقق من المصروفات
            Dim sql2 = "SELECT COUNT(*) FROM Expenses_add WHERE cost_center_id = @id"
            Dim cmd2 As New SqlCommand(sql2, sqlconn)
            cmd2.Parameters.AddWithValue("@id", costCenterId)
            If Convert.ToInt32(cmd2.ExecuteScalar()) > 0 Then Return True

            ' التحقق من المشتريات
            Dim sql3 = "SELECT COUNT(*) FROM Purchases_add WHERE cost_center_id = @id"
            Dim cmd3 As New SqlCommand(sql3, sqlconn)
            cmd3.Parameters.AddWithValue("@id", costCenterId)
            If Convert.ToInt32(cmd3.ExecuteScalar()) > 0 Then Return True

            Return False

        Catch ex As Exception
            Return True ' في حالة الخطأ، نفترض وجود ارتباطات لمنع الحذف
        End Try
    End Function

    Private Sub RefreshButton_Click(sender As Object, e As EventArgs) Handles RefreshButton.Click
        Try
            LoadCostCenters()
            LoadParentCostCenters()
            ClearForm()
            SetFormMode(False)

            XtraMessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحديث البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CostCentersView_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles CostCentersView.FocusedRowChanged
        ' تحديث حالة الأزرار عند تغيير التحديد
        SetFormMode(False)
    End Sub

    Private Sub CostCentersView_DoubleClick(sender As Object, e As EventArgs) Handles CostCentersView.DoubleClick
        ' تحرير عند النقر المزدوج
        If CostCentersView.FocusedRowHandle >= 0 Then
            EditButton_Click(sender, e)
        End If
    End Sub

End Class
