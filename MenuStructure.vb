''' <summary>
''' هيكل القوائم المحدث للنظام
''' يحتوي على تعريفات وتنظيم القوائم الجديدة
''' </summary>
Public Class MenuStructure
    
    ''' <summary>
    ''' تعريف هيكل قائمة العملاء
    ''' </summary>
    Public Structure CustomersMenu
        Public Const MainTitle As String = "العملاء"
        Public Const CustomersList As String = "فهرس العملاء"
        Public Const CustomerProperties As String = "خصائص العملاء"
        Public Const CustomerReports As String = "تقارير العملاء"
        Public Const CustomerDocuments As String = "سندات العملاء"
        Public Const CustomerReview As String = "مراجعة التعاملات"
        Public Const CustomerVisit As String = "زيارة العميل"
        
        ''' <summary>
        ''' تقارير العملاء الفرعية
        ''' </summary>
        Public Structure Reports
            Public Const DetailedStatement As String = "كشف تفصيلي"
            Public Const CustomerStatistics As String = "إحصائيات العملاء"
            Public Const CustomerItems As String = "أصناف العميل"
        End Structure
    End Structure
    
    ''' <summary>
    ''' تعريف هيكل قائمة الموردين
    ''' </summary>
    Public Structure SuppliersMenu
        Public Const MainTitle As String = "الموردين"
        Public Const SuppliersList As String = "فهرس الموردين"
        Public Const AddSupplier As String = "إضافة مورد"
        Public Const EditSupplier As String = "تعديل مورد"
        Public Const Purchases As String = "المشتريات"
        Public Const SupplierDocuments As String = "سندات الموردين"
        Public Const SupplierReview As String = "مراجعة التعاملات"
        
        ''' <summary>
        ''' المشتريات الفرعية
        ''' </summary>
        Public Structure PurchasesSubmenu
            Public Const PurchaseInvoice As String = "فاتورة مشتريات"
            Public Const PurchaseOrder As String = "طلبية مشتريات"
            Public Const PurchaseReturn As String = "مرتجع مشتريات"
            Public Const PurchaseReview As String = "مراجعة التعاملات"
        End Structure
    End Structure
    
    ''' <summary>
    ''' تعريف هيكل القائمة الرئيسية
    ''' </summary>
    Public Structure MainPageGroups
        ''' <summary>
        ''' البيانات الأساسية
        ''' </summary>
        Public Structure BasicData
            Public Const GroupTitle As String = "البيانات الأساسية"
            Public Const Items As String = "الأصناف"
            Public Const Customers As String = "العملاء"
            Public Const Suppliers As String = "الموردين"
            Public Const Employees As String = "الموظفين"
            Public Const Treasury As String = "الخزينة"
            Public Const Expenses As String = "المصروفات"
        End Structure
        
        ''' <summary>
        ''' المبيعات والمشتريات
        ''' </summary>
        Public Structure SalesAndPurchases
            Public Const GroupTitle As String = "المبيعات والمشتريات"
            Public Const SalesInvoice As String = "فاتورة بيع"
            Public Const PurchaseInvoice As String = "فاتورة مشتريات"
            Public Const SalesReturn As String = "مرتجع مبيعات"
            Public Const PurchaseReturn As String = "مرتجع مشتريات"
            Public Const Quotation As String = "بيان أسعار"
        End Structure
        
        ''' <summary>
        ''' الإدارة والتقارير
        ''' </summary>
        Public Structure ManagementAndReports
            Public Const GroupTitle As String = "الإدارة والتقارير"
            Public Const InvoiceCollection As String = "تحصيل الفواتير"
            Public Const InvoicePayment As String = "سداد الفواتير"
            Public Const StoreTransfers As String = "تحويلات مخازن"
            Public Const Attendance As String = "حضور وانصراف"
        End Structure
    End Structure
    
    ''' <summary>
    ''' ألوان القوائم المختلفة
    ''' </summary>
    Public Structure MenuColors
        Public Shared ReadOnly CustomersColor As Color = ColorScheme.PrimaryBlue
        Public Shared ReadOnly SuppliersColor As Color = ColorScheme.InfoPurple
        Public Shared ReadOnly ItemsColor As Color = ColorScheme.SuccessGreen
        Public Shared ReadOnly SalesColor As Color = ColorScheme.WarningOrange
        Public Shared ReadOnly ReportsColor As Color = ColorScheme.SecondaryGray
        Public Shared ReadOnly TreasuryColor As Color = ColorScheme.WarningYellow
        Public Shared ReadOnly EmployeesColor As Color = ColorScheme.DangerRed
    End Structure
    
    ''' <summary>
    ''' تطبيق ألوان القائمة على عنصر قائمة
    ''' </summary>
    ''' <param name="menuItem">عنصر القائمة</param>
    ''' <param name="menuType">نوع القائمة</param>
    Public Shared Sub ApplyMenuColors(menuItem As ToolStripMenuItem, menuType As String)
        Select Case menuType.ToLower()
            Case "customers", "عملاء"
                menuItem.BackColor = MenuColors.CustomersColor
                menuItem.ForeColor = Color.White
            Case "suppliers", "موردين"
                menuItem.BackColor = MenuColors.SuppliersColor
                menuItem.ForeColor = Color.White
            Case "items", "أصناف"
                menuItem.BackColor = MenuColors.ItemsColor
                menuItem.ForeColor = Color.White
            Case "sales", "مبيعات"
                menuItem.BackColor = MenuColors.SalesColor
                menuItem.ForeColor = Color.White
            Case "reports", "تقارير"
                menuItem.BackColor = MenuColors.ReportsColor
                menuItem.ForeColor = Color.White
            Case "treasury", "خزينة"
                menuItem.BackColor = MenuColors.TreasuryColor
                menuItem.ForeColor = Color.Black
            Case "employees", "موظفين"
                menuItem.BackColor = MenuColors.EmployeesColor
                menuItem.ForeColor = Color.White
            Case Else
                menuItem.BackColor = ColorScheme.SecondaryGray
                menuItem.ForeColor = Color.White
        End Select
    End Sub
    
    ''' <summary>
    ''' إنشاء عنصر قائمة فرعية
    ''' </summary>
    ''' <param name="text">نص العنصر</param>
    ''' <param name="name">اسم العنصر</param>
    ''' <param name="menuType">نوع القائمة</param>
    ''' <returns>عنصر القائمة الجديد</returns>
    Public Shared Function CreateMenuItem(text As String, name As String, menuType As String) As ToolStripMenuItem
        Dim menuItem As New ToolStripMenuItem()
        menuItem.Text = text
        menuItem.Name = name
        ApplyMenuColors(menuItem, menuType)
        Return menuItem
    End Function
    
End Class
