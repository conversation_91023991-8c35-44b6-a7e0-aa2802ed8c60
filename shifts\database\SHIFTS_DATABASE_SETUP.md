# 🕐 دليل إعداد نظام إدارة الشيفتات

## 📋 نظرة عامة

نظام إدارة الشيفتات (الورديات) مصمم لتتبع عمل الكاشيرين وربط الفواتير بالشيفتات المحددة. يوفر النظام:

- **تسجيل بداية ونهاية الشيفت**
- **ربط الفواتير بالشيفت النشط**
- **حساب إجماليات المبيعات والأرباح**
- **تقارير شاملة للشيفتات**

## 🚀 خطوات التشغيل

### الخطوة 1: تشغيل السكريبت
1. افتح **SQL Server Management Studio**
2. اتصل بقاعدة البيانات `مبيعات 2022`
3. افتح ملف `shifts_database_script.sql`
4. تأكد من اختيار قاعدة البيانات الصحيحة
5. اضغط **F5** أو **Execute** لتشغيل السكريبت

### الخطوة 2: التحقق من النتائج
```sql
-- تحقق من إنشاء الجدول الرئيسي
SELECT * FROM shifts

-- تحقق من إضافة الأعمدة
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'shift_id'

-- تحقق من Views
SELECT * FROM vw_shifts_summary

-- تحقق من الإجراءات المخزنة
SELECT name FROM sys.procedures WHERE name LIKE 'sp_%Shift%'
```

## 📊 هيكل قاعدة البيانات

### 1. الجدول الرئيسي: `shifts`

| العمود | النوع | الوصف |
|--------|--------|--------|
| `shift_id` | INT IDENTITY | المعرف الفريد للشيفت |
| `shift_code` | NVARCHAR(20) | كود الشيفت (تلقائي) |
| `cashier_name` | NVARCHAR(100) | اسم الكاشير |
| `cashier_id` | INT | معرف الكاشير |
| `store_name` | NVARCHAR(100) | اسم المخزن |
| `start_date` | DATETIME | تاريخ ووقت بداية الشيفت |
| `end_date` | DATETIME | تاريخ ووقت نهاية الشيفت |
| `start_amount` | DECIMAL(18,2) | مبلغ البداية في الخزينة |
| `end_amount` | DECIMAL(18,2) | مبلغ النهاية في الخزينة |
| `total_sales` | DECIMAL(18,2) | إجمالي المبيعات |
| `total_cash` | DECIMAL(18,2) | إجمالي المبيعات النقدية |
| `total_credit` | DECIMAL(18,2) | إجمالي المبيعات الآجلة |
| `total_profit` | DECIMAL(18,2) | إجمالي الأرباح |
| `total_invoices` | INT | عدد الفواتير |
| `total_items` | INT | عدد الأصناف المباعة |
| `shift_status` | NVARCHAR(20) | حالة الشيفت (مفتوح/مغلق) |
| `notes` | NVARCHAR(500) | ملاحظات |

### 2. جدول التفاصيل: `shift_details`

| العمود | النوع | الوصف |
|--------|--------|--------|
| `detail_id` | INT IDENTITY | المعرف الفريد |
| `shift_id` | INT | معرف الشيفت |
| `invoice_number` | INT | رقم الفاتورة |
| `invoice_total` | DECIMAL(18,2) | إجمالي الفاتورة |
| `invoice_profit` | DECIMAL(18,2) | ربح الفاتورة |
| `payment_type` | NVARCHAR(50) | نوع الدفع |
| `invoice_date` | DATETIME | تاريخ الفاتورة |
| `customer_name` | NVARCHAR(100) | اسم العميل |

### 3. الأعمدة المضافة للجداول الموجودة

- **`invoice_add.shift_id`**: ربط الفاتورة بالشيفت
- **`pos_add.shift_id`**: ربط فاتورة POS بالشيفت
- **`pos_add2.shift_id`**: ربط فاتورة POS2 بالشيفت

## 🔧 الإجراءات المخزنة

### 1. بدء شيفت جديد: `sp_StartNewShift`

```sql
DECLARE @shift_id INT;
EXEC sp_StartNewShift 
    @cashier_name = 'أحمد محمد',
    @cashier_id = 1,
    @store_name = 'المخزن الرئيسي',
    @start_amount = 1000.00,
    @notes = 'شيفت الصباح',
    @shift_id = @shift_id OUTPUT;

PRINT 'تم إنشاء شيفت رقم: ' + CAST(@shift_id AS NVARCHAR);
```

### 2. إنهاء الشيفت: `sp_EndShift`

```sql
EXEC sp_EndShift 
    @shift_id = 1,
    @end_amount = 5000.00,
    @closed_by = 'أحمد محمد',
    @notes = 'انتهاء شيفت الصباح';
```

## 📈 Views المتاحة

### `vw_shifts_summary` - ملخص الشيفتات

```sql
SELECT * FROM vw_shifts_summary 
WHERE CAST(start_date AS DATE) = CAST(GETDATE() AS DATE)
ORDER BY start_date DESC;
```

## 🎯 أمثلة الاستخدام

### مثال 1: عرض الشيفتات النشطة
```sql
SELECT shift_id, shift_code, cashier_name, start_date, total_sales
FROM shifts 
WHERE shift_status = 'مفتوح'
ORDER BY start_date DESC;
```

### مثال 2: تقرير شيفت محدد
```sql
SELECT 
    s.shift_code,
    s.cashier_name,
    s.start_date,
    s.end_date,
    s.total_sales,
    s.total_profit,
    s.total_invoices,
    DATEDIFF(MINUTE, s.start_date, s.end_date) AS duration_minutes
FROM shifts s
WHERE s.shift_id = 1;
```

### مثال 3: فواتير شيفت محدد
```sql
SELECT 
    i.invoice_number,
    i.invoice_date,
    i.Accounts_name,
    i.total_invoice,
    i.earn_invoice,
    i.type_money
FROM invoice_add i
WHERE i.shift_id = 1
ORDER BY i.invoice_date;
```

## 🔍 التحقق من نجاح التشغيل

### اختبار الجداول
```sql
-- عرض هيكل جدول الشيفتات
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'shifts'
ORDER BY ORDINAL_POSITION;

-- عرض الفهارس
SELECT name, type_desc 
FROM sys.indexes 
WHERE object_id = OBJECT_ID('shifts');
```

### اختبار الإجراءات
```sql
-- اختبار بدء شيفت
DECLARE @test_shift_id INT;
EXEC sp_StartNewShift 
    @cashier_name = 'كاشير تجريبي',
    @cashier_id = 999,
    @store_name = 'مخزن تجريبي',
    @start_amount = 500.00,
    @shift_id = @test_shift_id OUTPUT;

SELECT * FROM shifts WHERE shift_id = @test_shift_id;

-- اختبار إنهاء الشيفت
EXEC sp_EndShift 
    @shift_id = @test_shift_id,
    @end_amount = 1500.00,
    @closed_by = 'كاشير تجريبي';

SELECT * FROM shifts WHERE shift_id = @test_shift_id;
```

## ⚠️ ملاحظات مهمة

### قيود النظام:
1. **شيفت واحد نشط لكل كاشير**: لا يمكن للكاشير فتح أكثر من شيفت في نفس الوقت
2. **ربط تلقائي للفواتير**: كل فاتورة جديدة ترتبط بالشيفت النشط للكاشير
3. **حساب تلقائي للإجماليات**: عند إغلاق الشيفت يتم حساب جميع الإجماليات تلقائياً

### أفضل الممارسات:
1. **نسخ احتياطي**: قم بعمل نسخة احتياطية قبل تشغيل السكريبت
2. **اختبار البيئة**: اختبر النظام في بيئة تجريبية أولاً
3. **مراجعة البيانات**: راجع البيانات الموجودة قبل الربط بالشيفتات

## 🛠️ استكشاف الأخطاء

### مشكلة: "الجدول موجود مسبقاً"
**الحل**: هذا طبيعي، السكريبت يتحقق من وجود الجداول قبل الإنشاء

### مشكلة: "خطأ في الصلاحيات"
**الحل**: تأكد من وجود صلاحيات CREATE TABLE و ALTER TABLE

### مشكلة: "لا يمكن إضافة العمود"
**الحل**: تحقق من وجود الجداول المطلوبة (invoice_add, pos_add, pos_add2)

## 📞 الدعم

للحصول على المساعدة:
1. راجع رسائل الخطأ في SQL Server
2. تحقق من سجل التشغيل (Messages)
3. استخدم الاستعلامات التشخيصية المرفقة

---

**تم إنشاء النظام بنجاح! 🎉**
