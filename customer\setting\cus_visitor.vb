﻿Imports System.Data.SqlClient

Public Class cus_visitor
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        new_btn_Click(Nothing, Nothing)
    End Sub
    Sub fill_account()
        Accounts_name.Properties.DataSource = Nothing
        Dim adp As New SqlDataAdapter("select * from customer where cus_active = 'true' order by cusname ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Accounts_name.Properties.DataSource = dt
        Accounts_name.Properties.DisplayMember = "cusname"
        Accounts_name.Properties.ValueMember = "Cuscode"
    End Sub
    Sub fill_delegate()
        delegate_name.Properties.DataSource = Nothing
        Dim adp As New SqlDataAdapter("select * from delegate", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        delegate_name.Properties.DataSource = dt
        delegate_name.Properties.DisplayMember = "delegate_name"
        delegate_name.Properties.ValueMember = "delegate_id"
    End Sub
    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from visitor_name order by Accounts_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("Accounts_name"))
        Next

    End Sub

    Function get_id(subname) As String
        Dim sql = "select * from visitor_name where Accounts_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        address.Text = ""
        Dim sql = "select * from visitor_name where id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            Accounts_name.Text = dr!Accounts_name
            delegate_name.Text = dr!delegate_name
            date_visitor.Text = dr!date_visitor
            address.Text = dr!address
            cus_phone.Text = dr!cus_phone
            visitor_note.Text = dr!visitor_note
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub


    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        fill_user()
        fill_account()
        fill_delegate()
        address.Text = ""
        delegate_name.Text = ""
        Accounts_name.Text = ""
        cus_phone.Text = ""
        visitor_note.Text = ""
        date_visitor.Value = Now.Date
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If address.Text = "" Then
            MsgBox("أدخل اسم الزيارة")
            address.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from visitor_name where Accounts_name=N'" & (Accounts_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم العميل موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!Accounts_name = Accounts_name.Text
                dr!delegate_name = delegate_name.Text
                dr!date_visitor = date_visitor.Text
                dr!address = address.Text
                dr!cus_phone = cus_phone.Text
                dr!visitor_note = visitor_note.Text
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة الزيارة")
                new_btn_Click(Nothing, Nothing)
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ الزيارة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from visitor_name where id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!Accounts_name = Accounts_name.Text
                dr!delegate_name = delegate_name.Text
                dr!date_visitor = date_visitor.Text
                dr!address = address.Text
                dr!cus_phone = cus_phone.Text
                dr!visitor_note = visitor_note.Text

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل الزيارة", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل الزيارة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from visitor_name where id= N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الزيارة")
            Else
                Dim dr = dt.Rows(0)

                dr.Delete()

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف الزيارة", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الزيارة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub
End Class