﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Button7.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        4wAAAOMBD+bfpwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAP8SURBVFhH5Zd/
        TNRlHMev24T7wf3wTg6Rizs4hYMdUNCWhRzcJVQIF5pwQGQC1yYkKCICEY4fE24t6ppIOclAJOksQSuh
        UbZVm2RaS9t0/pN/OLZWsbamdl7z3T5P3rOL7RYn55zr2V7b831ezz7P+27P83zvBAK/JlepXeEi0fVw
        kfhGqBFJpB5RRIRXo9MP+K/p39RSueLaqau/4/ScN+SM/3wTrstzkKnUXoFAoJq/ODX9Cl3cHE2evDyL
        DOuTiEtNh72lC6OzHpiyrOz5TqBawxdn8dZVD7SJyR5aa/7i1HiA3mE3Ep7agOLpS3j+7C+ouuRB6ZdX
        2POdQLWaD7oXHmDP0BhMJVVs4VBAtZoGx/7nAXYOHll4AOehozBY18H24emQQLVq+0f+O4DWsOoPCjB9
        5VdYN5Yj1fxESKBarjM/sQAxiaa/AgbQJSR55p/fULH/4m//BDCabgUM8IBQeEsqV+JuIJYpGEKhEAED
        qKK1c85Pf8TdoG3iO9SPzUAdawi8B/wDbHv7GFKy8mB8NJuRXVKFnsnzzLW7v0JGXhF3j+QVsTFyNMdc
        XMkd1WjYPx58gDRLPmJTUmCyWBhKzXJs3fs+c89sfQVR8fHcUb+orp05mkNzfY5qPGRZF3wAU1Yuks1m
        rCkrY0Tp41HrGmWucEsLdKmp3FGfxsjRHJrrc1SDat1/AXLsDigiNYiMjWNIZAq0jEwzt6ljL6SKpdxR
        /4XOfuZojlgm545qWEodwQfonTqP+oEPUNfvZrSOfs7GfTQOfsRd4zsf/8u1Hv6MO6pBtYIO0PTuSawu
        sCM918bId+xA79QF5jqOzcC8cTN32cWV6ByfuR38AgqqG7Amt4iRXViKpqHJ4AM8vLYAWmMSjJmZDHmk
        BvX7jjK3vn43InV67qhPY+Tq9rmxSrMcR6z5jIaUDDy+1hZ8gMVswhxDIrzV2xgTuTaszsq7DwNkrq+A
        KjoG0SsTGHQKdg1NMVfe9hpk6mXcUf+5tj7mXnrzPVhXGhcfYM8n3+PFVw/C4TzA2HHgOBv3QZ/U53zf
        DNFz8gd8Xdu8+AAvj55CvqMRT1c3MEqaevgi3SfOorCmlTvqd584xxydhsNljsUHoJcN3fH6tDRGhErN
        XlDkNmzvgGpFDHfUf3Z7Z2j3wD3fhPc8wGOFpVj2YCy0SckMiVyJnbevXPsuJxSaKO6ob292MkfXskax
        FJtM6YwcnQE5tvLgA3ROfIOK9jfY8SJqXh/hm5B+dGzuHuCusnuA3fc+v6XvEHcVu13omjizoABqkSTi
        Wtfxb3mhUNI6fg41w19ALFMG/HMqEMvkfUvCwq8vCRfdCDF/hokl3jCx9KZSo+33X/NvPtyRHu7LXPAA
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="Button6.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA68AAAOvAGVvHJJAAAAB3RJ
        TUUH4wwaASc6u1uYlgAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAMJSURBVFhH
        tZdLiI5RGMdnXIZBcsu9Rka5RGhkwcKUS2TBkKgxUkpui1lYSCgbJFnM2LgWG4VBxgLFwqXGkpJbhCIh
        l9yvPz0z5/0873/O982Hb371rZ7feU7v+57zP+crKhKAUcBu4CbwDvgCPAGOA4uAYh1TEIAOwA7gO7m5
        DpTp+P8G2K8z5eApMFh7ZCM83HCgIvwGqmCvVrkIbALWA4eBj1I/n2qSBWAYcFvG1qlk3zvhB7AwJbQ4
        5cBj5xlT1PMA/SKTNwHdvDRChPpUFwdQJe4udRJsEuCa+LeAPipq0zkpwRG+5QfnXlDHADoCp51n2Lpp
        vXiBGhEr1PEAD5zbFKkXAwedY9h2nqBuM8BskWvUSQiv1dZIQmPE2e7qxldghnoZgN7S1L5TZ/UMYIPz
        jI1SXy31X8BS70SJfK8GYJCr27dfAXx2jgVWuXPmyYMYtZlJcgGMk+aGNbsBXAFeSs3I7GWgMjJ+Z3qW
        NgCq84jhBAuprmHcWOCN1I/aW9M52gSYDtyRZh57SjsvSoI/NBxUnktAF+2dN7YAgVnhRDwGnA3baqWs
        i76RlLNE7ZXu2A4ApZGUe9jqkGkPQsqdkslfASPVLThZUu6THkrAADvQgLXh080FenrnnwC2yeS2Vee7
        +njgXAggxRLxiC3cdNc8AVZpR2CNqy+IZEGMF8DkdPc2yJJym13dbjl2d/RYptwFHkXeyHO/o3ICTIs8
        2T5xLku93p/74VZ0Rpy9vkeUkHKvZWAj0Mk5dov2NKS7tGBjQrQn2EP9uRkpWVLObsTdxbNV7pnk6x5g
        ibiV6jSTJeXuA/0j7hbxStVJAEaLW62OSZZyV0V8Zt9RXSPcmj1D1EkApopbpYKl3EmR7Do1MSU6Qsh4
        1qmTEMmRMb5oKXdAhG/AzFQXwdYE8NaNicayrQ3ZTfdUqHVFw+TFKSkLwFYZazfnunDhXWbbNpITy7VJ
        mV0e3e9v/nqVRNZNLuyYL+yfXKAHcEJnEn6GkMrkSMEJ98NDYdva/8n34aa9xw4r9X8DA+l2K61+yiEA
        AAAASUVORK5CYII=
</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn16.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn17.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn18.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn19.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn20.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn21.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn22.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewButtonColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn23.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn24.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn25.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn26.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn27.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn28.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn29.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn30.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn31.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column22.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="Button5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA68AAAOvAGVvHJJAAAAB3RJ
        TUUH4wwaASYQeftgAQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAJ0SURBVFhH
        tZfJalVBEIajYFwYVw7Rhag7B/ABFBzWgiA462s4Qkhi3LgVTYxvIKjoC8QJx2cQh53RnbqIA37yQ7XW
        qTP2PdcPAqGruv4+3VXddUdGOgKsBk4At4DXwCfgB/Ad+Ag8B64Bh+Qb5w8MsAG4AXyjO7eBZTFWFsBK
        YAr4GqO3cBdYEeNlAYwDz2Jk4BewAJwHTgF7gT3AQeAccHUY4juBD0H4CzABrIn+OdhiL8bxv9iXR/E7
        wProm4uJ60PEVLSnM/fb/huY7p1MZXGh6tkenZRwnumCw4DUiB+OTio1n+3a9v/15UVxc1SdJzRh2Gfe
        KD4Wvn4i+uQC7O4kLux6TfwcUql1Exd2tycWoj2HbHEBvHITLlTYdwEvgU3R5hlIXACf3aSTwSZxvXri
        Td0iBhYX9pwm9gfbWWcTpUX0EhdNCzC7Hh7Pe2CL2bpnuwEccP5LjUeQqLgltRPHcsWFvaKJxdYkTFQs
        wtNJXACX3LwXGph3Aw/jBE/NIjqLC+CxmzungeNuoPUiCovIFV9nTU3iiAZXhat4Mk6MWGJmiQvgstNR
        jzmWDNedQYsZj5MjwLY41gSwMTS2s96oTshn9L1hPMcJYDlw38VfAjZHp0nnIGYKDj0AroTY5WYHGAWe
        Oie1ZDN9dsK+XOKKlVAVVHfOakTspvPoOFpzImJn/iDEegesjb4FgB0Vi1BiqvyaJ/8rNTWz8ZfU21Ij
        WocFeRICCNXwI/X2wGlgn/2dsRtO2+vrPKHx1sUXsJzQ+fnHKhdlu2p/NMbvDLAVuJn5G1G+s6VS64Pd
        mHoB56xDWrTd0Z/+15hsR+Ub59fxB98Fg519arrOAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Button4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA68AAAOvAGVvHJJAAAAB3RJ
        TUUH4wwaAQ8frQLiewAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANxSURBVFhH
        zZZJj1VVFIVJBAtQAwPEkcQSJ4QpBMKIgbQJXYIJnZAYkAkkhEbhh9DoRATmhC4QGkMpOAAlzBAmDhCb
        MMDYIETNRz6zyry7qXq8W1iJa3LfO3edc3az9t53zJguAPqAJcBHwNfA98DjPL/Kuu/76t7nAjAW2JqL
        /gBOAbuB9cDbefr/dN7fA7a4r57VGsA04AbwANgLvAxMABYD7wMf5rko668A+4CfE6XX65k9A5gN/Ahc
        AqYA04GjwO/x9BbwZZ6Psu57ea8CnwE/ALPq2c9EPPfyT5ICQ2y+LwPL9bbw9X4F8Hl4u4BxwOEY0Xsk
        cqFh13MPORjv1hbexIT6J+AMsA54IZqQfyD7jYQi7U0TEZw5N+x67mFzKk+4Hr6R0nPz/hYwF3gI7ASm
        xtAtdf9TSKmpdgVnLj204flwSNpMkRHRiA3Z/2aEaXW8WPc1ACyNwFT7MQ+snG6IFi4mEqbyC+BIqsNz
        F9c9DQAfAydzkKFfUTnPQjz2sjXAKuA3YHx0crDyG4hYzLt1bQ4bau8VwHvA/AhVYxYAHwDXK7eBlIyW
        K6xb9X0bDBoP3E6zskruVV4DEc3CiOZqfd8rIsK7+W2z8jzPfVy5DaQC1sbi2/V9G3RE4A6wGXgHuF95
        DXRoQGvN3cTKaQPgpbRpNWB3vFk5DQCHMtWsAtXbugo6kSr4NVVw3JFdOQ1knuu5detgGaicNgCuAJ8C
        k+PQysppIJ3QjqVoBjvh+srrBcCmhL8/4Xe4jau8p5CPCXu3I9WNNqS5ldcNwLz0kR3Aa5kt2ypvSKSF
        KkanmNPMqeZh71buUAA2hr8/Z50D/gZmVO6wcH6nKTnPNcKpZjrs7StrdaTjKThzbtj13Mvd73C6AHxn
        f+jc1xV+ycQII+FINZcKSjF5yTdpMj41znXfyzPseu7lM6Mtq+tuWyOMhJNNTShMq8OSsq7Vit+EPv3v
        +qToxpwbdj3/5ys57V20NsJQeonVYYk61fZ0fBWvTvOyzo2Cat9mzhN2Pffyv7LePhLCj4lMST/RrsWg
        P22vdriMcvXxb6llJmiE2J61kaVjpADeAL4FzpqmrOnMiWis9+oYKf4vRgymw/APCrMzHf11z3+OLkac
        t2Iqf1QwjBHLgF8qd9TQYYR9wssHjELljSrSMQ29vcPntCfniMyxy0nFCwAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="DataGridViewComboBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn14.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn15.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="Button2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA68AAAOvAGVvHJJAAAAB3RJ
        TUUH4wwaARAo2OVJ6gAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFVSURBVFhH
        7ZcxSgNBFIYjIVYaEHIBSy+gVcQLiJ5BwcIb2Km1pY1Y2SjkAtoEVAQLrS1E0QPYi00++WUXHm8nxnV2
        ki3ywStm5583306xyzQadQRYBa6Ax5J1D5wDm8CM7/sngCbwQTx6gTnffyRAxzR5AHol6s6sFbelJZzA
        tp//DWDDrM2R1LzPDiWBgLgGZn0+SKTAulnr2fP5IJECi8CXWW958/kgMQICWAOOgZOsdPw5bZ8vECvg
        AbZMv46fLzAVmJgAsALsAocJBdRbeyzbQAu4MCFL1QIW7dlS4MDPGFIKiH0FXvzThOj4F4CbbPwsgYEL
        pWQpO5WjbDzQYJw8AafAZ/5g3AIFpgK1FzgDdiJLPYYySiD1h+hHoA+8mno381ULqLfdq+/zWjCZv2FO
        3QTKXkxCpR6lBKq6mnnUs+n3CwJ0gcvA5fO/pV5dv08t+AY7nHXJH8BpVAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA68AAAOvAGVvHJJAAAAB3RJ
        TUUH4wwaAQ4axHMntQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAKMSURBVFhH
        xZfLThVBEIbZSCCCD4EQl7hjI4GFb4BC1McwBh+BILrlILgT3YO6R03ES+JtbdiKrlV085kfqmKdyvQ5
        44zIl0wy3f1Xd093dU31wEBNgDPANWAdeAV8AX7Zo3fVqe2KtNm+McA54B7wnfpIuwFM5P5qAwwDK/aF
        kR/ADnAfuGOP3p9aW0S2y8BQ7r8nwDjwMXW2BcwBp7PeAUaAS8B2sn0PnM36SoDzwH4wfglcyLp+ANPm
        F85n9Z11XdiXx8FXgVNZVxfZAp00ieqVsD3/EMQ3sqYpwGLoV9sxnDUSyeGc1dzeFmAt9L+cG3XU3Nt3
        2yx7CduO1zaGxvpzRO2cO7UcDpgCPtkzldurAGbCOBteqQjnQWYrG5UAZkNns7m9BPDIbL4Bo6pQeHXm
        skGJFhO4HOwWVKH4LRTFikEm02ICoyFirqnCg8VOFvei6QQE8MzsdlX4aoXNLHTkmMDF9FwPE9B7bi86
        M/DA7PZV8ON3KwsdYC8MVpe93I8TYs6BCj+tsJKFzjFM4LZpDidwElvw0OwOt0B/O3ESTvhChbtW0NEY
        yeISTSdgx/DA7DqquBo6+t+BaF4VMRRvZ4MSLSbwxGwUio9W3BJIZzobVdHwZxQnvR4bJkI8kFMex+94
        EHhjY+joj2eBslen09X4DwjOLpZyuwRDli45i1nTFOBm6PddMU1XwmiJo6M0ajDr6mLL7n9bob7Hsq4L
        YDJNQmnUTNb1wxzO99wHn8y6Smwl4nYIZTI6w8VgZUFGmsfJ9m3fL89Ymi7HrLqaKZRuhquZfq/PQ4Rz
        5O1LxT2vgx1RxYm/uZwqyGjvu49aG2x5F+ymo9RdNyh9oR69q043qfle25T5Da7HJ5dk0iZGAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="XtraTabPage5.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAARdEVYdFRpdGxlAE1hbjtQZXJzb2478+0UpAAAAf1J
        REFUOE9j+P//PwMIg4CofiIYMzAwMKlGTSlRDp/wUNalqgTER5IDq4frw2EAs5xb7SO1mOn/Ja3zHoH4
        pBrAJKIbWyqqn/BIRDemjCQXMDAwMIJsZGBgYGdgYOBkYGBgA4kRZQBUM6tq7Ix2tcT5X5Qjp/2XcavZ
        hGwIPgMgmmNm9qnHzf2vFj/vv1rcvP9KYZP/yzhXLYAZgs8AZpWo6R1qcXP/S9mW/AJpVo2dC8ayns0g
        Q5ZxCClxgQzBZQCbatzc3/J+HX+F1ANjwZpj5vxXCp38X0Qvbo+sa/1/KdtikEtYcBnAruDf81/KoQzE
        EZD36/mvEjX7v4xr3V82XikJEd3Y9aJ68VdB3sRlACvIJhG9uL0comoiooZJe+U8W/7LejT/ETGMsWZg
        YOCAhgMTLgPAgcjAwMAt5VQ5nV/dXU1EN2aZvF/3f2mXmvt8Ks5SIM34AhEckBJ2eTYKQRP/iFvk7GXj
        lRaTsC68Lh808b+kbckxqAtwGgByAYdi8KRDsj4d/4V1YiaBEpKIXnSafPDk/xK2JXeg3sBpALOUU0WE
        YujU/+LmOe95ZazkQCEuoOJlIh806b+kXfkDaMrEaQC7rHfbJWnX+v/CmmHl0PBg5BTRFJX16vwvaVv6
        HRQ+eA0Q0Ys7I6IXd4ldUEkQKelyQMXPY/MCADlphK30mRMzAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="XtraTabPage6.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAD90RVh0VGl0
        bGUAQ29uZGl0aW9uYWxGb3JtYXR0aW5zSWNvblNldEZsYWdzMztDb25kaXRpb25hbEZvcm1hdHRpbmc7
        fphyFQAAA4lJREFUOE91kl1M22UYxR+ZH3NzMQvT7EbIGIMxoB2ETahTIDAojK7d2q7S1vHR0tHQ8v3R
        wQrtbFbAtoO1ZLWgdLFs9q8MKlQ6JGFBBiN+jLEZo7JgYmbiDcYLE2NcjnkbTEyMF+fqfZ/z/s55XiKi
        GO+sShZYrFm/cbcOHyzqHlwJy51Gx7EEdkZETzH1TZwkzy05uSMyujIjpcFPT9NA+DRR742CVufN4if+
        OTXWfvbi+18+xvRqO+ycCLXO7Bm5KU2mNmckEdEz5veLE9o8BS8zw8WNTnKEJES9weObkW814O4YwH2h
        xNKGFUsbNkzfa0BvUAxd31HonTlofTcXzpCEGQ8T0baFRyayfyQisl7L47ilegSX1fDPlyO0Vo2F9Qvw
        LRRhIFyExkEBXBOnEFzSYWpNDztX9rtYm7aHUVgDQqLWQYF0aFKL6ysn4Z1W4vqKHPM/dGBsRYr+aQGM
        Q+moc6fDwmWhP/wqvDMKtLjz2hhF10ghUTJ/746u4dxf/XdK4Z6UwTcnQmhVg8n71Ri4lYvOsUxo+1Nw
        PpCJixOZGJk/hVbPG98R0fatgunpRlf26NVwJS5PFaGfEyKwLMPUAz2GbhfCOn4E5y4fQoOPD3MwA87I
        a7D4hShv4ZcxCmYQU2HKFNpGz8A1k4MuXwF886XgvqrE6KIIlyaz0TicjtqBQzAFDqNnnA93WIyznRmf
        bFFEMXbo7VmPHdP56LgqQFXPYShNqVCZ0qC18WBw81DjSkHTezx0fcjD4Fw+tBez/so58UoSWfrZVujZ
        t87zPfYxNXwTl6BoOhKJO7grPvlo7N48RZxEqEkYl9Qf+FNtSUG9NxVmjgervxgnapIcZLJ5mMG2M5Xa
        Yz1OF5a/fgRjR+/m7tiXYrd+Isu5PVWwJzFfGd9XVLX/vsRw8A+JIflJqe7ALDV3u2j1m8fM5AV9i219
        9vOHeNs1ConynIoVXFnfTYUV+6JdMVIi2klEu7b0PBlNffTl2k/swnNKTYvDM3IT17g5KDTNIdaNuraT
        8lRxlK+Kp3w10z4q0SVSSU0iCbWJRLpmG92992M0xuvHxVl17X347PZDKLXtm+wVRXUblWpSqLBqP4mM
        yfSmOZ2UFh5J21KprC6ZqMrYTWcNF/7Zxk5pRUNEUd3yW4lc+w5DlFY0EYD/FTFEpa6DymvaoxQMm4he
        jOYjihGr6/8z9G/9DTEMwhiarhs8AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn11.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn12.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn13.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="XtraTabPage7.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAMdEVYdFRpdGxlAE9yZGVyO5LTEh4AAALjSURBVDhP
        hZJdSJNRGMePn1OXmybOnY1tOXcxVpJg4RcOxYsm22TaHBlLG6Uz5janm9v7bk4ynStqW7SJBoFZF4kJ
        WQOhG6+kGyUvvDAFjWJEF0ZilgT6xPuiyw+oi9+5Oc//d56H8yAAQP9ims/nT2KsfM7JTXqSfRpFWCwU
        zMhAfgaDvj8ROIxMJkNTPF7ZUw7n3VhOzvcRNvtFiMmsv5eWdmogJeX/AoFAgCby8hCXyx2ry80tf5SZ
        2XY/PX3Gl5qy1ZeUdCku8PvvosFBH+rvv4O83j5EEiSyOxzIau1EKpW6UigUzrBYrMRARgYaYjBQPocT
        rWKzz8cFVDgcjhzBYrEqjEbjW4IgwGK1gEajea9QKNxSqbSMzWZ/ysrKSo4Lbvf30yGCINnd3Xar2WxZ
        tdvt4PMNwfb2Ns3S0hKMjIyAUqWEc4WFGxjjv4LeXq/E5SKGHQ7HD6/XC4FAEMLhCM2vnR3Y2cfv9+81
        NupALBZPYIxT4wKXi3g9NzcXGx9/thcMBiEUekhDCTY2vkE0GgU36QZnjxNKSkogP/9MFcY4KS7osnUN
        Px4dhVgsBsvLHyAcicDLqSlYWVmFXk8veNweIAkSOkwmkEgKvmCMszHGCXGByWRSejxuiEbfwMLCPOzu
        7kLgQYAOUrh6XNBls4FapQKhQDiJMc48+Gr60Ol0zObmlp/T069oCdUJFXSTJDjsdrCYLaBWqanZN3k8
        Xj3GmHFEQM2j0WimQsEgLVhcXASn0wm2ThsYDAYoLi6GM+KC+Zq6Kx8l0rMsjHHicUFiTU1NU0NDw+7g
        4ADMzs5CW2sr1CoU1Ktf+Xx+e63OsKa+atysVjYmc7nc+LYeCBKotpT1TZ9LK+S/5XI5FBUVbV0ordgU
        CEUyjDGzTt++Vqe/dUOrvUyt9lFBZaWclmivta2rtHqzSCSqVmr1MX179+bFcnkaFTjOEcFh9rtJbulw
        rV83u24aLOSJmsP8AXvhBxU4+KY4AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="Column9.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column10.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column11.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="XtraTabPage8.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAPdEVYdFRpdGxlAEN1c3RvbWVyO5zd1eIAAAK3SURB
        VDhPZZJbSJNxGMZfm35L+7Zvc1tzTnf62jzM4be5OQ/zsIaaaRioQ1TMIxkVqBmCUqmNDLSbICsiSgyE
        LrILUyS6sMCLsosIoi6CLgoSDbIyLcMnhAlze+GBlz+/53fz/gkAhabELKUiA1uVnxS7kK0W/7QrYj5Y
        paI6IooKZ3cS8VCsZ1NLzbKNDq8Fp8vS0VxgghDP/EuTRA+ZYkXScD5C4EmOu17v1qOlkIfXwMKhFKO1
        MGM7JVYEnom6Gs5HCHI0sY8acvUotxkxcq4Tr+Zu4tnNHuQq96/oRTQTzkcIHCpmMEcTt/19+TO2Vl5i
        7e04Fu91otetWtOJaCKcjxAoxPt01YL2z69vX/H74xS+PA/gxe1mdDvka1wUZYXzEQIikg/VupbeLzzG
        6tI43k134WGfD40W9jURaSP43eXYcf9OOZqIdO1l9qnpQDsWJy9h5nIFRipMKEjmnhCRlYjEBiO/VxAc
        sTxeYXPnFS36So7ibqAX460+XKvzYLSrDYLDBZ3e+IZhxEVEdGD3X+wKotUJGru3pHy1p28QgdEb2NjY
        QCAtHd08j/X1dVTVNKGyyg+dwfiDYcQ+ImJCBZzDmTPZ3tmFK2O30HG2H/PzT3G/sQljlZWYnZ2DM68U
        6YIHqVYBUk42Q0TqUEGSO6/w0/mBAE71XERlbRvqG5owcaIFF1wuVNf4YRUKYLLYodVZwHHyZSJKCRUc
        ys0v2hwYGkV1w0l4y/1Is2Zi2O7AmcREGHkzDGYBWl0q1IkmSKTcFhHZ9whc7vzN3v5hFB+phdtTDrv7
        MKQyBVhWBj1vgybJAlWCEUq1HizL/iUiW6hAaeTNdzLtTlgzBFhS02HiLdDpTUjUJuOgWgOFQgWOk4GV
        SMAwzAMiSthzBSJSBe+cFYyTiFzBZIdE2CkTUcxO9z8V/t8nlSfZSgAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="ContextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>146, 17</value>
  </metadata>
  <data name="Label10.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAn4AAABECAYAAAAfrjiCAAAABGdBTUEAALGPC/xhBQAAAf9JREFUeF7t
        2bFNAzEAheEgBqCBjjkyhWtoI1oG8DopUQrqFCDhgj0iKujY4JAjDqHoFojfV3yN37n/dV5N07TqSm2X
        pbbHUttbqe271DYBAHCWPktt+1LbQ6ntYu69OfpuS23vC5cAADhvr6W2m2P4/f7pE30AAON6nsOvP++e
        jgAAjOW+h19//z0dAAAYy1MPv6+FAQCAsXz08Ds9BABgQMIPACCE8AMACCH8AABCCD8AgBDCDwAghPAD
        AAgh/AAAQgg/AIAQwg8AIITwAwAIIfwAAEIIPwCAEMIPACCE8AMACCH8AABCCD8AgBDCDwAghPADAAgh
        /AAAQgg/AIAQwg8AIITwAwAIIfwAAEIIPwCAEMIPACCE8AMACCH8AABCCD8AgBCr9WY3AQAwPuEHABBC
        +AEAhBB+AAAhhB8AQAjhBwAQQvgBAIQQfgAAIYQfAEAI4QcAEEL4AQCEEH4AACGEHwBACOEHABBC+AEA
        hBB+AAAhhB8AQAjhBwAQQvgBAIQQfgAAIYQfAEAI4QcAEEL4AQCEEH4AACGEHwBACOEHABBC+AEAhOjh
        93l6CADAcA49/PYLAwAAY9n28HtYGAAAGEtZTdPxufdlYQQAYAzb3nxz+F2vN7unhY8AADhv2/Vmd/UX
        frP1Znf3G4CHhUsAAJyH3nI9+Mr/1vsBTiPg7Zvp+IwAAAAASUVORK5CYII=
</value>
  </data>
  <data name="PictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAn4AAABECAYAAAAfrjiCAAAABGdBTUEAALGPC/xhBQAAAf9JREFUeF7t
        2bFNAzEAheEgBqCBjjkyhWtoI1oG8DopUQrqFCDhgj0iKujY4JAjDqHoFojfV3yN37n/dV5N07TqSm2X
        pbbHUttbqe271DYBAHCWPktt+1LbQ6ntYu69OfpuS23vC5cAADhvr6W2m2P4/f7pE30AAON6nsOvP++e
        jgAAjOW+h19//z0dAAAYy1MPv6+FAQCAsXz08Ds9BABgQMIPACCE8AMACCH8AABCCD8AgBDCDwAghPAD
        AAgh/AAAQgg/AIAQwg8AIITwAwAIIfwAAEIIPwCAEMIPACCE8AMACCH8AABCCD8AgBDCDwAghPADAAgh
        /AAAQgg/AIAQwg8AIITwAwAIIfwAAEIIPwCAEMIPACCE8AMACCH8AABCCD8AgBCr9WY3AQAwPuEHABBC
        +AEAhBB+AAAhhB8AQAjhBwAQQvgBAIQQfgAAIYQfAEAI4QcAEEL4AQCEEH4AACGEHwBACOEHABBC+AEA
        hBB+AAAhhB8AQAjhBwAQQvgBAIQQfgAAIYQfAEAI4QcAEEL4AQCEEH4AACGEHwBACOEHABBC+AEAhOjh
        93l6CADAcA49/PYLAwAAY9n28HtYGAAAGEtZTdPxufdlYQQAYAzb3nxz+F2vN7unhY8AADhv2/Vmd/UX
        frP1Znf3G4CHhUsAAJyH3nI9+Mr/1vsBTiPg7Zvp+IwAAAAASUVORK5CYII=
</value>
  </data>
  <data name="new_btn.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="new_btn.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAF
        QAAABUABQbe+gQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAABsSURBVDhPY/j/
        /z8DNqy6yN9ceZHvKRAGsdHlYRhDAIaVF/nuU17k+x+K96HLE2PACyQDXqDLDycDoKENCjCQBmT8F8kA
        EBtdfh9IL8gmUFTBFJKKT1FuAMVeQA8UkgMRXWB4GUBZZiI2OwMAFk2JJ+9fvh0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="save_btn.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="save_btn.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        bQAAAG0BjlwfygAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFvSURBVDhPnZM/
        SwJxHIfvTfQOTD3kNAwygogyzTwvkf6BimSZUppD5Wq0RLVki7VVKlgEtmSLDaGD1iuQRocajYa0wT7x
        Dbw81KMcnu35PHzv4Mco+9k0xxleNGrd29jo+KfA22ut8BZbTa8daJgmJhsaVvfOqriyQqHoA8AQDI2d
        wTgsjiBuMhlUq1UJlUoF3iUvzhMpCHMh2N3RL1atfW5GGK12+PU/AXJbIz0FxIiKK0sC4VAYsaNjCQf7
        hzAbTVhfC0KY/w0QtBUDi4EYrAsRWcjpGugFSWDWu4cpx8afILctMC24cRo/wVX6UhZyyG0LWGwubG9G
        sBPdlYUcctsCVE0mEsjeZiUU8nmUikU8lkq4y2Z/nK4XuJ0e+Fb8Es4u0kimrvFQeMKqLwC3y9P5AjGw
        7JeFnI4BOot+0n0uJws5kk9oPiarcQZbZgOi/Igs5JBLG9oy4nNWsh9WPVd3DOlqcvCDXJ1c2tD2G796
        Quknuw2KAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="edit_btn.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="edit_btn.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        bQAAAG0BjlwfygAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEoSURBVDhPlY+x
        TsJAHIf7ACY6uhvdTFyYzgABBoGlJF4ujA6OTK4uxHcw8RWMs2HhPYxGwZ4JJdVo25PWQvyZtqnSu7Po
        8P2Hu/u+9m8AMAqhdB+M3aDbrSl3gKEcaGQfjAGMvesiqqSXU0wzRLN5sjqgkzsdoFJJWYqslilFRAgW
        hADlckocabX21AClm7I8JwRhqfQjx9TrDqrVNSUQDYe95d9eNBp62TQ3lBVmvn8YeGIeXQ/yOxfI34EP
        IXZjOfAEEi6v9DKl67mVs0DoioNMdp9fMb57hNc/K/yyNpDJo9txgnt+EX2224Pf5FxAlvkDn02t6ZYs
        yCTjzX45lmXO+bb8WEcybMs+zWTrngd/lZVALDuOsyM/KiIZE2tyxEdP4r9yzBcvHuNlLsAauQAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="delet_btn.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="delet_btn.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        VAAAAFQBo63EFQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAC5SURBVDhPY2DA
        AZIaev4jY3R5ggBZE1EGlE+dd7R86rz/ROLD6PpBBqArwovR9VNuAAg8zwv5TwxG1wcH6ApxYXR9cICu
        EBdG1wcHyIp+v3jy/+2k+v8va1P//37++P/rllzSDABp/vvpw/8/b178/7RxEekuANkM0vzv+zewYSQb
        AHI2yGaYS141ZpFmALKfQZpfFEaQZgA+jK4PDp7nhfxEV4wF/0DXBwfPckNaCRjy80VecAuyHgCP+p30
        K46nGQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="print_btn.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="print_btn.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        cAAAAHABznhikwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIISURBVDhPnZLN
        SxtBGMb3Vrz23zBetKeURtNsYg5+RKKS1ngoQtNbLUj6STHjBzFG3XXNUr20HirS0sYWEuquAemku9FE
        vLTHlqzQ3pReilptXnkn3UDWPYgvPMzM+/yelxkYjrNUNpu9kqP6aY7qYNEJelb+XGma1mATZkLPyp+r
        Sw+YWzd6BdVYldTyO2vQFHrICGo5aAnvBQTFOBUUA6JLKkzLy7ZCDxlBMf6JqtHPwuKnsltQjKP/Bjxb
        zkN0UbEVeiYnKMbxvLLn47oSBT04s/WtT9j+ERKLv25Lpd8DUul4MLUDpsKpncrAQunwllQ6CInFn31z
        29+Ds1tfuxK6znXENQiJReAJvbA85DO0PV1ne84/8QW6E4Wa6Rp+Ddd6o9AcuF8n7N0YXmHMzec5aI4s
        VQf0JAtw58UuO7gffoCmtiA4XAFboed+9JGxoSfVleuM67UnOCMyOFp7oJEPQyM/aFGYedfvyYztnH5T
        HcATemJe33lXAod/CBzhKXv5hxiDrG92Dde/nJfQGZ7QfZ7QA2ck9cc18ha8hELHZL2whx4yyGLGE6PJ
        ug/lJXQKpz9+mYfMxia8Wk0zZdRN1mM3jdF4XcgsTyzv5QmttI9RSKs6xJIyREZGmUhShvSGBr5x9tQK
        stY81z6qXfUR2rK4pj/Af7/yPlO7Ae6xhx4yyJq5M4atH0QEMvUNAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="PictureBox3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button8.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button8.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        cAAAAHABznhikwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIISURBVDhPnZLN
        SxtBGMb3Vrz23zBetKeURtNsYg5+RKKS1ngoQtNbLUj6STHjBzFG3XXNUr20HirS0sYWEuquAemku9FE
        vLTHlqzQ3pReilptXnkn3UDWPYgvPMzM+/yelxkYjrNUNpu9kqP6aY7qYNEJelb+XGma1mATZkLPyp+r
        Sw+YWzd6BdVYldTyO2vQFHrICGo5aAnvBQTFOBUUA6JLKkzLy7ZCDxlBMf6JqtHPwuKnsltQjKP/Bjxb
        zkN0UbEVeiYnKMbxvLLn47oSBT04s/WtT9j+ERKLv25Lpd8DUul4MLUDpsKpncrAQunwllQ6CInFn31z
        29+Ds1tfuxK6znXENQiJReAJvbA85DO0PV1ne84/8QW6E4Wa6Rp+Ddd6o9AcuF8n7N0YXmHMzec5aI4s
        VQf0JAtw58UuO7gffoCmtiA4XAFboed+9JGxoSfVleuM67UnOCMyOFp7oJEPQyM/aFGYedfvyYztnH5T
        HcATemJe33lXAod/CBzhKXv5hxiDrG92Dde/nJfQGZ7QfZ7QA2ck9cc18ha8hELHZL2whx4yyGLGE6PJ
        ug/lJXQKpz9+mYfMxia8Wk0zZdRN1mM3jdF4XcgsTyzv5QmttI9RSKs6xJIyREZGmUhShvSGBr5x9tQK
        stY81z6qXfUR2rK4pj/Af7/yPlO7Ae6xhx4yyJq5M4atH0QEMvUNAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Button9.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button9.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        cAAAAHABznhikwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFKSURBVDhPY2BA
        ADkGBoZwJBoZIIuDaATw1tPz89XX36woInJdVlDwDowGicEwsjiIBomB9IENcNfW3vHayf7/TG3N/1uN
        DeA0SAyGU2SkUWgQ1lFW/q6srDwRbgCpOCss7L+ahsYCBgdtnU21Vpb/ScWZUdEQA/QMDBa8fPfpP6k4
        LSMTtwFzZk3+XxJn9r86VuV/RaLF/2VL5hFvAEjz0nyB/1/nMvzvi2YA0ysKBTEMwWkAyOYf8xn+V/gy
        /F+Zw/C/NZQBzK9MsiTOgKoYFbCGDYUM/8u8ITSIDxInyoCCKBOwhpZQhv8rchj+F3tBDMiLNCbOgPlz
        p/9fUQgJg/4YSBgsyBEAixNlAAgvmDf9f2GM6f+SCGWwzSA+uhq8BhCD4Qbo6Oi4aWppfYlPTPpPCp45
        ew7EABBQUFCYpqysvIxUrKio6AYAk2RCy8hnhYAAAAAASUVORK5CYII=
</value>
  </data>
  <data name="Button11.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button11.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        bQAAAG0BjlwfygAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEZSURBVDhPpZBP
        SsNAGMXnAG5djFsvoag3EJIuxDu4EDyROUAWbkIpCOoB7EbQaKpmZHRi2lRQtK3988k8OiV1phDtwI/H
        PN73Fo/R3upKvbZ92KhtHZG/5ldBZ/WNvmXk8eaZv9nXkMepCqV8UxfQub9xrPkdXEQ5jwLy+HCKFV7A
        LM/S0+h2GVjcErQMs4IiDUDyKCnvvEGlagOXZxXQNQdZXpB+WoejEXB5VoFKI3D38ETPWRuaSgXgqdc5
        zyr46vWBfMlpPJ5AjddphET761DjWQXvH59AyIwGg2+o8bKLOk0OdqDGswpcgxWXAag0omswCjioNOLc
        YNMR1VUEXMOWC7rm8w+67OZe7MaJCOOWOPkTiQj17Q+TizRlUswmsgAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="DefaultLookAndFeel1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>303, 17</value>
  </metadata>
  <data name="XtraTabPage1.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAD90RVh0VGl0
        bGUAQ29uZGl0aW9uYWxGb3JtYXR0aW5zSWNvblNldEZsYWdzMztDb25kaXRpb25hbEZvcm1hdHRpbmc7
        fphyFQAAA4lJREFUOE91kl1M22UYxR+ZH3NzMQvT7EbIGIMxoB2ETahTIDAojK7d2q7S1vHR0tHQ8v3R
        wQrtbFbAtoO1ZLWgdLFs9q8MKlQ6JGFBBiN+jLEZo7JgYmbiDcYLE2NcjnkbTEyMF+fqfZ/z/s55XiKi
        GO+sShZYrFm/cbcOHyzqHlwJy51Gx7EEdkZETzH1TZwkzy05uSMyujIjpcFPT9NA+DRR742CVufN4if+
        OTXWfvbi+18+xvRqO+ycCLXO7Bm5KU2mNmckEdEz5veLE9o8BS8zw8WNTnKEJES9weObkW814O4YwH2h
        xNKGFUsbNkzfa0BvUAxd31HonTlofTcXzpCEGQ8T0baFRyayfyQisl7L47ilegSX1fDPlyO0Vo2F9Qvw
        LRRhIFyExkEBXBOnEFzSYWpNDztX9rtYm7aHUVgDQqLWQYF0aFKL6ysn4Z1W4vqKHPM/dGBsRYr+aQGM
        Q+moc6fDwmWhP/wqvDMKtLjz2hhF10ghUTJ/746u4dxf/XdK4Z6UwTcnQmhVg8n71Ri4lYvOsUxo+1Nw
        PpCJixOZGJk/hVbPG98R0fatgunpRlf26NVwJS5PFaGfEyKwLMPUAz2GbhfCOn4E5y4fQoOPD3MwA87I
        a7D4hShv4ZcxCmYQU2HKFNpGz8A1k4MuXwF886XgvqrE6KIIlyaz0TicjtqBQzAFDqNnnA93WIyznRmf
        bFFEMXbo7VmPHdP56LgqQFXPYShNqVCZ0qC18WBw81DjSkHTezx0fcjD4Fw+tBez/so58UoSWfrZVujZ
        t87zPfYxNXwTl6BoOhKJO7grPvlo7N48RZxEqEkYl9Qf+FNtSUG9NxVmjgervxgnapIcZLJ5mMG2M5Xa
        Yz1OF5a/fgRjR+/m7tiXYrd+Isu5PVWwJzFfGd9XVLX/vsRw8A+JIflJqe7ALDV3u2j1m8fM5AV9i219
        9vOHeNs1ConynIoVXFnfTYUV+6JdMVIi2klEu7b0PBlNffTl2k/swnNKTYvDM3IT17g5KDTNIdaNuraT
        8lRxlK+Kp3w10z4q0SVSSU0iCbWJRLpmG92992M0xuvHxVl17X347PZDKLXtm+wVRXUblWpSqLBqP4mM
        yfSmOZ2UFh5J21KprC6ZqMrYTWcNF/7Zxk5pRUNEUd3yW4lc+w5DlFY0EYD/FTFEpa6DymvaoxQMm4he
        jOYjihGr6/8z9G/9DTEMwhiarhs8AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn39.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="CardView2.Appearance.Card.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAATdEVYdFRpdGxlAERlc2lnbjtSZXBvcnQcg+XoAAAJ
        +0lEQVRYR6WXC1CTVxqGz+62dtva2nbXttvWrcVq1+1N3bG77c600+m0nb1oW0UpIBflIqgIIoLFC/eg
        iCJGQJQgEASCIHcSBBJCuBkIkATCHeQWCAQSwCDU7rw75ydhAlanMz0z75x//hzO+3zf+c45P4Qsbb8h
        hPyOEPKUZ1Sph+elCvGh6Ip7rpFCtSNLUGAXlBewzSNuE/2dEPIEIeS39G/sQgTELrSEOLBKiCPrNnE6
        U0Y82JWm+egYOvZJ49z0HQHAaLn5E9ZH2WuOXZGUXS/rhEilgWJkBrW9k8iquYtLuUp4RIthczo/b9vh
        hC8IIU8TQlYYJ2ZglokJ5mgU74Pwm/IpIzgd9xAAM9jSI+zVoOT6HknHGORj9yDqm8StljHwmkeQ1ahG
        VsMwsqVDYBe04mCUCLtP5NVt97xuTQh5lhDye2OUNFoqCvbs/pDkTznlHaPhNxXUjY6jUA8BUKqngnjN
        BeK2MdSPTuN2zyTyVePIbNaAKx1GbEUvAm+1wocrQ1BGM+JKOnAxVwkvthhWp/K7d/rywv7tHvPFa+u3
        rn59w0errY5xtnldKk2KLW7/n0ozg/BMOXV7zgSwvFGAZ0KzWiEfN0DUr4egexI5qjGkN40gsW4YZ/id
        OJmlhHdyA9zjquF4QYiDlytxsbAVnPIuBN+QwYtdCfuQEuwNK0VIagNy6wfRNmZA/cg0WJnNFGCVMUtM
        HZg3SrUyJLMFdeppCO/qUNw1geyWMXBlalwS9yO4sAP+mUp4JdVjf0wVHCKFsGaVwD6yHF4Jd3CR3w6e
        dBhVvXrIhmZQNzCNil49BF2TqBmaAovHALxorANTzSwBeC44Q4la9TTK7upQ1KEFT67BlZoBnBf2IbCg
        A8d5CngmSuFyuQr2kUJYhZXA7lw5nNgSHOJI4ZMmx8mcNsRW9iOlXo0shQbFnVpUDU4hLIMBeLn41NbQ
        yrCP6TOtk8VMUIDngzIUqFZP4XavDjlt40hqGMZlyQDOlffidF47fNMV8OBI4cyWYE9EOaxCBUy/L1qC
        A9ek8E6V43iWCqH8bsRJBpHRPIr8Ni3E/XqEZjRByPHjdZexURH8DwpAi3QxC5RmVUCaHFXDUyjsngBP
        OYbE+mGwKwdwtrQXp3La4ZMux8EEKfZdksAmohy7QgSwOVuGvdGVcLt6B54pzfDNbEVQcTciy/sQXzsI
        nkKDsj4dA9AjK8W8QQNR4N8pwOKWZPY/IeSFU6nNEA1MIb9TiwyFBhzpMKLF/QgX9MA/uw3eqc1wvyZl
        DK3PlMEymM/0DhfF2B9fB8/kJvjwWhFQ2I2Isl7EVQ+C2zSCoq4JhKQ1YXz0Lub1PSg//REFoNt2SQZe
        PJnSCOGAHnmdWqTLNUi4M4wo0V2E8buZ1HpxmxkjakiNdwbx8X14GeyjxHCJq8Wh5EZ4p7fgdEEXk7WY
        qgGkNI6gsHMCwWmN0AwqMTepQumJrRSAHmJLAF7yT5JB2K9HHi1ApQZJDWrESgYRWdaHk3QJbsjhFl/H
        GFqxSrEjiM/0ey6I4RxbiwNJMnilKXEyrxPhtxcAkmUUQIugtEaM9jdibkKB2z/8jQI8Y34m0L35hx+S
        GlDer0dRlxaReS1wOy/CTr8cbPe5BUv/XNiE8uHKlsDlcjWsz5Tiu8Bi7Aorhe35CuyLrYF7ogyeN5Tw
        z+1klo0tGUCSTI2CDi0Cb8ig7pVidkwGvu+WJafiIsBxTj2K2sfhG1+Nbz25GZ9Zh1q9tuHjt9/5p+3m
        LdtOuWz5JuzKh9vO9n3mlADL0/mwDi9llsAmsgJ7L9fALaEBHqkK/JDTgTB+Dy5V9jNZZABSZVD31MKg
        uYNin80UYKUJgBYhBXjRN6Eeh6KE+i8dI+wpkHEQXSsqSkxPspctPjngvuFz/+atuy/iO/8cOLGr4BJX
        BzeODIe4ChzPbgdL0IMYyQBSm0aQKR9hADR9dZgdrUGh96aHAGgNrPS5Wjf16W7/T+iZsOzEMomOo+/p
        mb56zWY7y41fBQg/sY3FjhN5cImpwbGMFgTkdyEgS4nQbCW8YiRwDMhHuaQSuqEGzI1IUOD54ZJ7wXRn
        04OBFgaNlmaEuV7/dZhHvnDlmsYtuWaNk7z6+nvbv3bYH9j5vScHnzsn40s3Lhz90+DFuonUrGL0tEkx
        0V+Ne3fzMT8iQoHH+xSABkkDYpoJwiST0aOaaTyFeCEn0PJaR1UWRgdVGOqQYLBdiKEOIUa7xWgXXsf0
        kAQzHSmY7UzG/HApcg++Z7qYFgHMJ36csaktLluay7tnVLnhuD8uw0wXD3oVB/rWBMx088APdwTfbwvm
        1CIY2hIw256A+z03kOP2LgV44ecAfkkzmT+b6rzxbFsuCw+0jZjtSYOh7RoMqquY7U6DgOUI9/Ur2AK/
        LfhRUw1DaxxmZMEwtJzHLde/mm7GJxa/TJZ/nxUc/oD8pFcapSA/Tbebm6/k7tt4VnUrDA+0Utzv4jIG
        s62xmO1MQQlrDw6uXxFFCFkj8N2CebUIUzW+0IvdcU9xDllOf6FmLz0WIP/g++SBXr4os+26MtnhnYjW
        7FA80NRitv06DMpoGBTRMLQngh9qgwPrVlwwbuNXBD6bmYzoRPsZzTSxcHPvO9SM/v7kIwHyDrxHHkw2
        MTIzfy7JbkNEC+8E5gcLca/5AmZkYZiRnYGhNR5FQbux32JFtHFyWuWvFB/dBENnInTlzpgsd8aMLBQ8
        h18AcMv1XfJgQkYfabUz5hzbDeeUGf6Y67uJ6Vpf6ESu0AtdMVV7DIUnvoLT2idjCSFvEUL+RLcnIWRt
        sfcmGNqvYbJ0L6NpaQDS92ygZn98LEC280Zz8+cTrNefU6Yfx1wfD1PVR5lodGVO0FcdRWPcLlCjRR1Z
        6IuOfMjIoIrHRIkDJkrsMXXnJNJt36Zmqx8LYG4eb7UuUnHDD/d70qGXHFmMhomoPgAG1RXMq8vw41j1
        gjRVjOaZXoJ7LTHQ8vdAy7fFVI0f0qwZgJeZw+8RAKaTcVX87nWR8lRf3O9Kg67S0xiJAyYECxFNCOyM
        2oMJ/oK0xVS2RtkwGi+isoauygepVuuo2SuPAjAV3KorlhaRcq4PZru4mKw4DK3AHlqBHRMNY8Q3mdhC
        W2TDiJosPFtjvNAa2sLvMV5gkhV0lUfA3WXxWAAa/dNXLS0impN9MNeXhenGcOir/YzyXVDVMeioJD7Q
        SY4uqNLbqCPQVXpBJz4CXYUXJqlEhzEp8mD6lJ0MwCOXgAI8Q9OUamUB7m4LhjiFyvItpOxYi+Qda5H0
        HdWbSPr2TVyn+ubPSNxu0hokblsDjkn/pXoDCVT/eQOXv35dvHgULwMwvxnpTUf3Kq1WSmsumj5z0S23
        XHQrmus1Y0/HU/OFr+KfyYAJwvQvNYX5NaJG5qLv6NzMrft/m2CYjjVP1WAAAAAASUVORK5CYII=
</value>
  </data>
  <data name="XtraTabPage2.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAARdEVYdFRpdGxlAE1hbjtQZXJzb2478+0UpAAAAf1J
        REFUOE9j+P//PwMIg4CofiIYMzAwMKlGTSlRDp/wUNalqgTER5IDq4frw2EAs5xb7SO1mOn/Ja3zHoH4
        pBrAJKIbWyqqn/BIRDemjCQXMDAwMIJsZGBgYGdgYOBkYGBgA4kRZQBUM6tq7Ix2tcT5X5Qjp/2XcavZ
        hGwIPgMgmmNm9qnHzf2vFj/vv1rcvP9KYZP/yzhXLYAZgs8AZpWo6R1qcXP/S9mW/AJpVo2dC8ayns0g
        Q5ZxCClxgQzBZQCbatzc3/J+HX+F1ANjwZpj5vxXCp38X0Qvbo+sa/1/KdtikEtYcBnAruDf81/KoQzE
        EZD36/mvEjX7v4xr3V82XikJEd3Y9aJ68VdB3sRlACvIJhG9uL0comoiooZJe+U8W/7LejT/ETGMsWZg
        YOCAhgMTLgPAgcjAwMAt5VQ5nV/dXU1EN2aZvF/3f2mXmvt8Ks5SIM34AhEckBJ2eTYKQRP/iFvk7GXj
        lRaTsC68Lh808b+kbckxqAtwGgByAYdi8KRDsj4d/4V1YiaBEpKIXnSafPDk/xK2JXeg3sBpALOUU0WE
        YujU/+LmOe95ZazkQCEuoOJlIh806b+kXfkDaMrEaQC7rHfbJWnX+v/CmmHl0PBg5BTRFJX16vwvaVv6
        HRQ+eA0Q0Ys7I6IXd4ldUEkQKelyQMXPY/MCADlphK30mRMzAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn35.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn38.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column3.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn32.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn34.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column2.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="PictureBox5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAKgAAAAbCAYAAADoFcRvAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAG1JREFUeF7t0qEBgDAQwMCfinmYj+FqcJjiO0HEiTPRmet+NlTNGaDEoKQZlDSD
        kmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiVt1vtt
        qPoBhNTcWXtH6NEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="Label46.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        qAAAAKgBefSzxgAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFKSURBVDhPpVM9
        SwNBED1OQX+MjaBYuLteFfAHBFRQEEEbC3/C/gYrhexsoVU6dza2aW0NIVUq2yBGrMPJ3G3C3tyKiMXj
        mHfz3g7zkZVlmf0HLYJQDPW6euptKwdXAuG88L2tTOuc5yUNpLdHEmEmEUqGN4Wmw/ObYgSIBOM6to8S
        YRq4xf7A7CQNFNqTkPQl0Jw1krTOpTc3ypmLZAWHz7cbEuGdDLiYQwxgTyK80HdlQGXVr5tXLkiI56HS
        OcX1Dw+XRCoH91wUQyJMWGMnRI4YOeLCJbr9/pp08BDyrinOBBpBjYsaKLgwVcWBs7sUV2QwGf8mFs6e
        hoc+iqHdXBnEoFHRyPjmhTF/VpNycLzkG+IwjUV4ZRqWiJaLlqrqkfBwF2vaFaDp0NqyxhJmwtsuz28Z
        VKTWOR0QHRIdFB0WHRjP+9HgL/gGgnCiHUTJmXcAAAAASUVORK5CYII=
</value>
  </data>
  <data name="Label45.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        cAAAAHABznhikwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFhSURBVDhPpZNN
        S8NAEIbjfzFFPHoRPCl49+DBkxe9q1cvWhA8CaI/wD9RmkVEqQe/hRa2NsaqKcHWigUvbWiwzSuz3cTN
        xgpi4IFkPt7MzM4aAAyVsXwpk2E8a1o8ZzLeEFg8Rzby6fHqy8ioVV4zGfdNxjEEn2IoNiFABpPxvBo8
        fljGwuUTlm9rmDy2daF8JCIE5J/jgNWiB7/Xh/rsV98SIpQjBKgvtey5sypCAK3gEytFD0s3LmqdQIgs
        Xj0n2qFcQwxMUd62X0XwOn+JbbsPTWHbcZrJKhjfNOS0YyP1S31H31MnNtx2d1DBdaICUK4hjyrpkMwU
        HHiy/D1tBpLGrwIXrbZI3qo0Ur5vAa2FiAzj6PZDlD78lC+GWtCHqLJRrmO6cJ+yR4gh6seocuC+I3tX
        T9klg2P8aZGIiaMKemEItxPoiYJ4kYatMjF//ojZUyeVnFrlf18mlb9e5y/b5sGNEiUchAAAAABJRU5E
        rkJggg==
</value>
  </data>
  <data name="Label4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAKgAAAAbCAYAAADoFcRvAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAG1JREFUeF7t0qEBgDAQwMCfinmYj+FqcJjiO0HEiTPRmet+NlTNGaDEoKQZlDSD
        kmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiVt1vtt
        qPoBhNTcWXtH6NEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="Label2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAKgAAAAbCAYAAADoFcRvAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAG1JREFUeF7t0qEBgDAQwMCfinmYj+FqcJjiO0HEiTPRmet+NlTNGaDEoKQZlDSD
        kmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiVt1vtt
        qPoBhNTcWXtH6NEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="PictureBox4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAKgAAAAbCAYAAADoFcRvAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAG1JREFUeF7t0qEBgDAQwMCfinmYj+FqcJjiO0HEiTPRmet+NlTNGaDEoKQZlDSD
        kmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiXNoKQZlDSDkmZQ0gxKmkFJMyhpBiVt1vtt
        qPoBhNTcWXtH6NEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="EditorButtonImageOptions1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAx0RVh0VGl0
        bGUAQWJvdXQ7VURnGwAAANRJREFUOE+lk0sKwkAQRLNSFI+iIATES/Sl+gK68gNCcoxeiDu9gqAinkJR
        1EjBNMSmkwlk8RZJ1dTMJF0JsSSGlFjWxHIhlg+xPIjlTCwbYplYf/mhGxZ+iaWoISOWng3oEMvOMVex
        J5Z+OWDpmGLkGjAMd7UGoBvY98oU4twRmpIj4OgISuwEV4hPR2ga8GobULS9wg3izBGaBmQQ8RvfjhgL
        wMSmalg4hhjoxt8obx1TFQc7ygAFWdVMJYCGMg10XTlAGYVuoML4NndiOYUjj63/B9nzQZBTBRHKAAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="EditorButtonImageOptions2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABt0RVh0VGl0
        bGUAQWRkO1BsdXM7QmFycztSaWJib247lQYzLwAAA4RJREFUOE9Vk2tM22UUxs9Y4tz8IPrRD86YmMxb
        XZgxm8lidF52CYuGsTKdwahzKs7NAuvKuMmazQ5cGRsQHU0qkynj0tKy/SlQtlSQtFIpt0IZtKWlUP6w
        lt6hYB7zf50mfniSk/Oe33OeD+elyegqCSKiDUSUklNpeCGvzqyQqqwDUrUtdEY9GJCqBqy5P/5enl16
        42ki2ijMOqKrJIimYkkGp+3J2nSqpl8pqx9ar+/1oGN8EdaFKP7kozBMLOF6nxeyn2zhL77vOk5EDwnM
        A5Y2bH9dvOmr6v4Opd6BkfsJ2AIrMC8m0OuPo88fh2Xpn57VH0W5ZhxfVpn0W5/b+ciD1LTxc6WpqkI7
        jpHgCgzuCDhXBNkXDXgtp4Ep+2InDDNRGD1RjIWSqNQ78InCUMGSZEobnj9RY14f8MfQZA+idSIE7WSY
        geZBJ/4YcrFaNxWB9l4ILRMhWPkEJLWW5L7PLonoqLyzVtU1jcaRAOoHA2gYDqDRvsygeX4Za+t/sfqm
        PYRfx5bZ+y+jQdSb3BAXaZT0fpnR1mxdQE0/j8Ny7r/Ygnx8CJF48n89sZzDDxYeLUOLyCrtGKXMku4l
        tXkBih4fG7AMuzEwMoPBMS9m+TCCkRU4XDxGJ+fgcC7gzW9uouLuPFQWHpnFnQF6t8AQVt7xQt41ywzm
        FiNsqz8YB7+cQCyxhmgiiUgiiXA8ibckTbhg9KG8x4f3ZFyA9kt0w+f0U/iW8+LI+U7sOdXIht7Obcbc
        /Rgzeye/BfvPaHGgoA0fXzbhnGEWxW1O7D3ZaqM3jt+oPnnNilLOA4XRh8umeVz5zc/SOOdCCMVWWeyr
        vX7Wr7jjQwk3g6/rbNj90bVyEqWXpO2VtCeLdC4U6Fw4q3ej6JabGfDBGAKRFZao+LYHhe1unG13Q6Z1
        4kD+7bVndh97STikh185qqrOKO1Bod6N01oXpG1OHCrj2GYBPvJdN2Q6F6SCtE5klN3FjqzqKiLaIhik
        bHn8qdSXP1Ab0gu7IW2dxmmtE2WcBxe6vDjf7UUZN4M8jROSpns4WGzEjqy6W0SUKrDEr4Gd8+bUJx8T
        ZdTUvvppc1Ks6MOJ63bka6aRp5lGzs92HFb0Ydexpti2ffJcInpUYPwCy6/jX5MUIdLWXTk7nz1YeVWU
        qRoSidW8SKz2v3iozrYt/dKVJ9I+3E5Em4VPJMCC/gYgLpTXZa5FzAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="EditorButtonImageOptions3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0VGl0
        bGUARGVsZXRlO1JlbW92ZTtNaW51c+upj0EAAANqSURBVDhPfZNdTJNnFMePmMzpLsZ2uYu5LFnivjqD
        i9ElxmzuQzGYLQzLNpctbs4pc7oC1iK0TJstDcwiUsgmTTocblg+SouUUoum0hEIneXDllJo3xY72res
        WPqWaiH5L+9bzO528UvOOc/5nZyL89A095B4iGgdEeWU1FleKWseVkm1zlGpzpU4o7sTl2pHnaW//Fnz
        WfXV54loPd/r5R4SD82kMoKct6d4w6nGIbWsZWy1ZTCEPk8MziiHv1gOlqkFXHHMQfara+nYT9ajRPQY
        76y5tG7rm+IN32iG+tQmLyb+ScMVf4DhWBqDkWU4IssYWcjWnBEONV0eHK+3mza/tOOJta1p/ddqe32t
        wYOJxQewMEmYA0lYghyscylYQ1n43BbicDeRQZ3Jiy9UllphkyJp68snGodXRyMp6N2L6JxKwDC9BNNM
        Ej3+/zDOJGHwJdAxlYCTTUPSNJLZ99UFER1S9jdprbNom4ij5U4creNxtLnvQ+/JNj/imjuBP+7eF95/
        n1xEi52BuKpLTR+fs7nanVE0DrE4qDRjd0nr/yJWmvHzCIuOsRiKq/smqUhxY0E3HIVqICw0jHv/xqRv
        Hh5/FF6GxTTDwheMwcfEEAzH8fZ311B7ax7aERZF8v44vV9hWVLfnIPSek8YEI2nkFzOIJnOgEuvIJVe
        AZfO5kvLGbwj0eNHWxg1A2F8IDPHKV9iHD9vmsH35jl89EM/9pxqE5reLW1HvswgxO+VdyD/jAH7K7px
        +KId5y33IO/2Y+/JThe9dfSq5uRlJ6rNIahsYVy0z+PS7QgaBiNocESgGczC53y99mYYCnMQ3za7sOvz
        yzUkKlDk7ZX0ZKqMAVQYAzhrYlB1nYG8NwhFbwgKcxZ5bwiVPQzO9jCQGfzYX9678sKuI6/xh/T49kNa
        TWH1ACpNDE4bApB2+yEzBiAzMagwMpAJBCDlMfhReO4WthVr6oloEz8gZ9PTz+W+/onOUlB5A9LOWZw2
        +FHelaVMiGdR1uWHRO/DAbkN24qbrxNRLu8SuwLhnDfmPvuUqLCx6Y0v2zNilQMnrrjXxFmU/ObGQZUD
        O4/oU1v2KUuJ6EneifAuu4pHQ3L4lTbvLNnx4oG6BlGRdkwk1rEisS7y6ofNri0FFy49k/fpViLayH8i
        Xub5F9KCmj/UpEwBAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="EditorButtonImageOptions4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAjdEVYdFRpdGxlAENhbmNlbDtTdG9wO0V4aXQ7QmFy
        cztSaWJib247TJaWsgAAAKdJREFUOE+Nk0sKwlAMRbu4QEe6iWxA/CBUrNvMShRHTy68wGuaj4MLIbnn
        0A7e1FqbhOkgTAvmfyJMDzCYFf4KUxOmpy3bCNOrd8Ecsbj1hSaUDLDmrId7JXHgK/ZjIZRE8EYQSNYM
        3gkCSQi7gkSygzOB/WxktT1XEMChpIIvzu9sJCk83EJJCVcSHPAwUjiRLFjOwvSuYEfyAatLSE62HKU/
        wBnzD9JAnMxlnre0AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn6.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn7.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn8.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn9.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column4.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn10.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn33.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn36.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn37.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn41.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn43.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn2.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn3.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn4.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn5.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column20.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="inserte.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA3QAAAN0AEQbD0HAAAAB3RJ
        TUUH4wQYDiYnUGMBeQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIjSURBVEhL
        rdRJ6E5hFAbwn3meLSSyQiQkZMECmZLYGkoSpSxIiVgohSwQC1ORCMnGsDAtDGUMZSqExMZOShILnX/n
        1nXp68/9nnrr6zvvPcNznuel+WiHobiF69VgXXTEJDzDT3yrXqiDLpiN1/iEu/hevfS/KJI/zTMfB5pV
        oFsmfIg7mIL+2NuMApF8AR5k8mnohO7NKBALnZFKuZ9TdM5Y7QJtMRnncpmLMmmB2gVG4WBSswq9K/Fa
        BQZiU9KyFQMq8djBdNzDh0qsBcFjLGtdctyrFAveF6ZLj2FEKRbomXRdSbOtKQfbYCzW54W3uJBch/0D
        43AS1zAnvynQFytwOxtYij5FcHD+cQpPcAb78QI7MCQn2ZijRxM9Ssn7ZfKg7TLmphBaMBW704HR2WaM
        yaSHUyWLMQsXs4nRpeRhquXpg0tJ628Iqf3AWcxE11IsLt/E6TyRJFTTIeORfFlSEsmjiT9wBJ+xE8NL
        Hwdi4THRc3zNJmIPgZBmLPQGruZO/org/yg+YleOH2opMBLH8a7Efeg8nBtdR4H43RCxpH14j0OYWLJ9
        IF7KkNz4koxjmlj4kvJCGyEerm0pz+A7XsV4hssI+iakBx5hJdpX7jREULMBr3A+HVksPboMY8U7H4pb
        W5my1YhEq9MDhaFiukG5o5fYUnH4PyMcGvIL04Uzwwfb8QZ7slhTEDJ8jC95wnjDqpfqYl5K8UTJA63G
        L9KjdRgeUtVLAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Button1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA3QAAAN0AEQbD0HAAAAB3RJ
        TUUH4wQYDiYnUGMBeQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIjSURBVEhL
        rdRJ6E5hFAbwn3meLSSyQiQkZMECmZLYGkoSpSxIiVgohSwQC1ORCMnGsDAtDGUMZSqExMZOShILnX/n
        1nXp68/9nnrr6zvvPcNznuel+WiHobiF69VgXXTEJDzDT3yrXqiDLpiN1/iEu/hevfS/KJI/zTMfB5pV
        oFsmfIg7mIL+2NuMApF8AR5k8mnohO7NKBALnZFKuZ9TdM5Y7QJtMRnncpmLMmmB2gVG4WBSswq9K/Fa
        BQZiU9KyFQMq8djBdNzDh0qsBcFjLGtdctyrFAveF6ZLj2FEKRbomXRdSbOtKQfbYCzW54W3uJBch/0D
        43AS1zAnvynQFytwOxtYij5FcHD+cQpPcAb78QI7MCQn2ZijRxM9Ssn7ZfKg7TLmphBaMBW704HR2WaM
        yaSHUyWLMQsXs4nRpeRhquXpg0tJ628Iqf3AWcxE11IsLt/E6TyRJFTTIeORfFlSEsmjiT9wBJ+xE8NL
        Hwdi4THRc3zNJmIPgZBmLPQGruZO/org/yg+YleOH2opMBLH8a7Efeg8nBtdR4H43RCxpH14j0OYWLJ9
        IF7KkNz4koxjmlj4kvJCGyEerm0pz+A7XsV4hssI+iakBx5hJdpX7jREULMBr3A+HVksPboMY8U7H4pb
        W5my1YhEq9MDhaFiukG5o5fYUnH4PyMcGvIL04Uzwwfb8QZ7slhTEDJ8jC95wnjDqpfqYl5K8UTJA63G
        L9KjdRgeUtVLAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="Timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>471, 17</value>
  </metadata>
  <data name="Button13.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGwAAAAcCAYAAACERFoMAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAAIRJREFUaEPt0cERgkAUBcGXf4getEoUWNkFxAC+cUzVHDqCzjh/JY704ypxGAaT
        7bhKHPmMb4kjrZ8ljqz9LHFk2Y4SR+a2lzjybqPEkdc6ShyZll7iyDT3Ekee81biyH1qJY7cHkuJwzAY
        w2AMgzEMxjAYw2AMgzEMxjAYw2AMgzEM5g8RB5Oi+Hz08wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button13.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        bQAAAG0BjlwfygAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAD3SURBVDhPpZEx
        agJBFIYHws5DRDHEwsJDKJaew8oD2KTwBqm1EM+RGyjK7BsR06TU0oQgJLap7H7RYdw1b5bFWHww8/j+
        f3Z2FAB1D2KAWHfAtBbzDJLNq3qApQGYcCYgh3CLWekJTNNL+KYCEzXA9HEVDvML1kuwfsFEFZMCS58B
        OY8dFlHLFXDU/HeJKdbcXUypCqb5lfD3vu4/9cC0vziWBolweoWYRpkFHlfiD9oEBN0F01bMPaZQTxX8
        SCEPS8+pgncpZPGmyrDUB9N3qmAsxeSkvJfZw6iKDHpkIM0XbKF98mQw+wsOYL1CTEMs1KP3ZPBGjvu8
        zJ9XhGx5AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>75</value>
  </metadata>
</root>