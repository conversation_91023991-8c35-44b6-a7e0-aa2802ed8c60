﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.cashing_cus_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="cashing_cus_a4" Margins="0, 0, 7, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="cash_cus_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="304.2499" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLine" Name="XrLine4" SizeF="813.0001,3.125" LocationFloat="0, 301.1249" />
        <Item2 Ref="4" ControlType="XRLabel" Name="XrLabel18" TextFormatString="{0:yyyy-MM-dd}" RightToLeft="Yes" Text="XrLabel18" TextAlignment="MiddleLeft" SizeF="220.3542,31.25" LocationFloat="23.75008, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="5" Expression="[chashingdate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="6" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="7" ControlType="XRLabel" Name="XrLabel16" RightToLeft="Yes" Text="XrLabel16" TextAlignment="MiddleCenter" SizeF="296.3126,31.25" LocationFloat="376.3958, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="8" Expression="[Cashingcode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="9" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="10" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="XrLabel14" TextAlignment="MiddleLeft" SizeF="648.9583,31.25" LocationFloat="23.75008, 57.8125" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="11" Expression="[customername]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="12" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="13" ControlType="XRLabel" Name="XrLabel12" RightToLeft="Yes" Text="XrLabel12" TextAlignment="MiddleLeft" SizeF="504.1667,31.25" LocationFloat="23.75008, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="14" Expression="[amount_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="15" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="16" ControlType="XRLabel" Name="XrLabel11" TextFormatString="{0:#,#}" RightToLeft="Yes" Text="XrLabel11" TextAlignment="MiddleCenter" SizeF="144.7916,31.25" LocationFloat="527.9167, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="17" Expression="[Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="18" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="19" ControlType="XRLabel" Name="XrLabel9" RightToLeft="Yes" Text="XrLabel9" TextAlignment="MiddleLeft" SizeF="648.9583,31.25" LocationFloat="23.75008, 151.5625" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="20" Expression="[type_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="21" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="22" ControlType="XRLabel" Name="XrLabel6" RightToLeft="Yes" Text="XrLabel6" TextAlignment="MiddleLeft" SizeF="648.9583,31.25" LocationFloat="23.75008, 198.9583" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="23" Expression="[cash_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="24" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="25" ControlType="XRLabel" Name="XrLabel3" RightToLeft="Yes" Text="XrLabel3" TextAlignment="MiddleLeft" SizeF="648.9583,31.24992" LocationFloat="23.75008, 244.2708" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="26" Expression="[cash_delegate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="27" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="28" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="كود الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="672.7084, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="29" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="30" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="أســـــــم العميل :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="672.7084, 57.8125" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="31" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="32" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="مـبلغ وقدره :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="672.7084, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="33" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="34" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="نقدأ او شيك :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="672.7084, 151.5625" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="35" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="36" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="وذلك عن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="672.7084, 198.9583" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="37" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="38" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="مندوب التسليم :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="672.7084, 244.2708" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="39" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="40" ControlType="XRLabel" Name="XrLabel5" RightToLeft="Yes" Text="تـــاريخ الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="244.1042, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1">
          <StylePriority Ref="41" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item16>
      </Controls>
    </Item1>
    <Item2 Ref="42" ControlType="TopMarginBand" Name="TopMargin" HeightF="7" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="43" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="44" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="109.375">
      <Controls>
        <Item1 Ref="45" ControlType="XRLine" Name="XrLine3" SizeF="802.9999,10.08335" LocationFloat="9.999998, 0" />
        <Item2 Ref="46" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="251.75,31.33333" LocationFloat="561.2501, 20.79164" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="47" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="48" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="49" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="251.75,41.04168" LocationFloat="561.2501, 52.12497" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="50" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="51" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="52" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="126.1459,99.29164" LocationFloat="23.75008, 10.08336">
          <ExpressionBindings>
            <Item1 Ref="53" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="54" ControlType="PageHeaderBand" Name="PageHeader" HeightF="130">
      <Controls>
        <Item1 Ref="55" ControlType="XRLine" Name="XrLine1" SizeF="802.9999,10.08335" LocationFloat="9.999998, 0" />
        <Item2 Ref="56" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="83.75009, 41.66663">
          <ExpressionBindings>
            <Item1 Ref="57" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item2>
        <Item3 Ref="58" ControlType="XRLine" Name="XrLine2" SizeF="802.9999,10.08335" LocationFloat="9.999998, 119.9166" />
        <Item4 Ref="59" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="117.8126,106.0417" LocationFloat="672.7084, 13.87498">
          <ExpressionBindings>
            <Item1 Ref="60" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item4>
        <Item5 Ref="61" ControlType="XRLabel" Name="XrLabel19" Text="إشعار بإستلام نقدية" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="23.75008, 10.33331" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="63" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="23.75008, 86.91663" Padding="2,2,0,0,100">
          <StylePriority Ref="64" UseTextAlignment="false" />
        </Item6>
      </Controls>
    </Item5>
    <Item6 Ref="65" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="139.75">
      <Controls>
        <Item1 Ref="66" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="462.3961, 106.3334" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="67" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="68" UseFont="false" />
        </Item1>
        <Item2 Ref="69" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="83.75009, 4.166698" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="70" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="71" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="72" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="83.75009, 31.33335" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="73" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="74" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="75" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="502.5001, 4.166698" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="76" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="77" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="502.5001, 29.25002" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="78" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="79" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="0, 106.3334" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="81" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="0, 70.83334" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="82" UseFont="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="83" ControlType="PageFooterBand" Name="PageFooter" HeightF="33.33333">
      <Controls>
        <Item1 Ref="84" ControlType="XRLine" Name="XrLine5" SizeF="813,8.249982" LocationFloat="0, 0" />
        <Item2 Ref="85" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="690.5208, 10.33333" Padding="2,2,0,0,100">
          <StylePriority Ref="86" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="87" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="739.4792, 8.250008" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="88" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="89" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="9.999998, 8.250008" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="90" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="91" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="92" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="93" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="94" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="95" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="96" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>