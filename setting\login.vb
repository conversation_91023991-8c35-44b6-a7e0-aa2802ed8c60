﻿Imports System.Data.SqlClient

Public Class login


    Private Sub Button2_Click(sender As Object, e As EventArgs)
        End
    End Sub
    Sub no_user()
        XtraForm1.m1.Checked = True
        XtraForm1.m2.Checked = True
        XtraForm1.m3.Checked = True
        XtraForm1.m4.Checked = True
        XtraForm1.m5.Checked = True
        XtraForm1.m6.Checked = True
        XtraForm1.m7.Checked = True
        XtraForm1.m8.Checked = True
        XtraForm1.m9.Checked = True

        XtraForm1.m10.Checked = True
        XtraForm1.m11.Checked = True
        XtraForm1.m12.Checked = True
        XtraForm1.m13.Checked = True
        XtraForm1.m14.Checked = True
        XtraForm1.m15.Checked = True
        XtraForm1.m16.Checked = True
        XtraForm1.m17.Checked = True
        XtraForm1.m18.Checked = True
        XtraForm1.m19.Checked = True

        XtraForm1.m20.Checked = True
        XtraForm1.m21.Checked = True
        XtraForm1.m22.Checked = True
        XtraForm1.m23.Checked = True
        XtraForm1.m24.Checked = True
        XtraForm1.m25.Checked = True
        XtraForm1.m26.Checked = True
        XtraForm1.m27.Checked = True
        XtraForm1.m28.Checked = True
        XtraForm1.m29.Checked = True

        XtraForm1.m30.Checked = True
        XtraForm1.m31.Checked = True
        XtraForm1.m32.Checked = True
        XtraForm1.m33.Checked = True
        XtraForm1.m34.Checked = True
        XtraForm1.m35.Checked = True
        XtraForm1.m36.Checked = True
        XtraForm1.m37.Checked = True
        XtraForm1.m38.Checked = True
        XtraForm1.m39.Checked = True

        XtraForm1.m40.Checked = True
        '    main.m41.Checked = dr!m41
        XtraForm1.m42.Checked = True
        XtraForm1.m43.Checked = True
        XtraForm1.m44.Checked = True
        XtraForm1.m45.Checked = True
        XtraForm1.m46.Checked = True
        XtraForm1.m47.Checked = True
        XtraForm1.m48.Checked = True
        XtraForm1.m49.Checked = True

        XtraForm1.m50.Checked = True
        XtraForm1.m51.Checked = True
        XtraForm1.m52.Checked = True
        XtraForm1.m53.Checked = True
        XtraForm1.m54.Checked = True
        XtraForm1.m55.Checked = True
        XtraForm1.m56.Checked = True
        XtraForm1.m57.Checked = True
        XtraForm1.m58.Checked = True
        XtraForm1.m59.Checked = True

        XtraForm1.m60.Checked = True
        XtraForm1.m61.Checked = True
        XtraForm1.m62.Checked = True
        XtraForm1.m63.Checked = True
        XtraForm1.m64.Checked = True
        XtraForm1.m65.Checked = True
        XtraForm1.m66.Checked = True
        XtraForm1.m67.Checked = True
        XtraForm1.m68.Checked = True
        XtraForm1.m69.Checked = True

        XtraForm1.m70.Checked = True
        XtraForm1.m71.Checked = True
        XtraForm1.m72.Checked = True
        XtraForm1.m73.Checked = True
        XtraForm1.m74.Checked = True
        XtraForm1.m75.Checked = True
        XtraForm1.m76.Checked = True
        XtraForm1.m77.Checked = True
        XtraForm1.m78.Checked = True
        XtraForm1.m79.Checked = True

        XtraForm1.m80.Checked = True
        XtraForm1.m81.Checked = True
        XtraForm1.m82.Checked = True
        XtraForm1.m83.Checked = True
        XtraForm1.m84.Checked = True
        XtraForm1.m85.Checked = True
        XtraForm1.m86.Checked = True
        XtraForm1.m87.Checked = True
        XtraForm1.m88.Checked = True
        XtraForm1.m89.Checked = True

        XtraForm1.o1.Checked = True
        XtraForm1.o2.Checked = True
        XtraForm1.o3.Checked = True
        XtraForm1.o4.Checked = True
        XtraForm1.o5.Checked = True
        XtraForm1.o6.Checked = True
        XtraForm1.o7.Checked = True
        XtraForm1.o8.Checked = True
        XtraForm1.o9.Checked = True
        XtraForm1.o10.Checked = True

        XtraForm1.o11.Checked = True
        XtraForm1.o12.Checked = True
        XtraForm1.o13.Checked = True
        XtraForm1.o14.Checked = True
        XtraForm1.o15.Checked = True
        XtraForm1.o16.Checked = True
        XtraForm1.o17.Checked = True
        XtraForm1.o18.Checked = True
        XtraForm1.o19.Checked = True
        XtraForm1.w20.Checked = True

        XtraForm1.w21.Checked = True
        XtraForm1.w22.Checked = True
        XtraForm1.w23.Checked = True
        XtraForm1.w24.Checked = True
        XtraForm1.w25.Checked = True
        XtraForm1.w26.Checked = True
        XtraForm1.w27.Checked = True
        XtraForm1.w28.Checked = True
        XtraForm1.w29.Checked = True
        XtraForm1.w30.Checked = True

        XtraForm1.w31.Checked = True
        XtraForm1.w32.Checked = True
        XtraForm1.w33.Checked = True
        XtraForm1.w34.Checked = True
    End Sub

    Sub fill_user()
        Try
            users.Properties.Items.Clear()
            Dim adp As New SqlDataAdapter("select user_name from users order by user_name ", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                fillstore()
                store.SelectedIndex = 0
                no_user()
                current_user = 0
                XtraForm1.store.Text = store.Text
                XtraForm1.BarStaticItem1.Caption = "لايوجد مستخدم "
                XtraForm1.BarStaticItem2.Caption = "المخزن : " + store.Text
                XtraForm1.BarStaticItem3.Caption = "التاريخ : " + Now.Date

                Me.Dispose()
                XtraForm1.Show()
            Else

                For i = 0 To dt.Rows.Count - 1
                    users.Properties.Items.Add(dt.Rows(i).Item("user_name"))
                Next
                users.SelectedIndex = 0
            End If
        Catch ex As Exception

        End Try


    End Sub

    Private Sub login_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        fill_user()
    End Sub
    Private Sub users_Click(sender As Object, e As EventArgs) Handles users.Click
        If users.Text = "اسم المستخدم" Then
            users.Text = ""
        End If
    End Sub
    Private Sub password_Click(sender As Object, e As EventArgs) Handles password.Click
        If password.Text = "كلمة المرور" Then
            password.Text = ""
            password.Properties.UseSystemPasswordChar = True
        End If
    End Sub
    Private Sub upassword_KeyDown(sender As Object, e As KeyEventArgs) Handles password.KeyDown
        If e.KeyCode = Keys.Enter Then
            login_btn_Click(Nothing, Nothing)
        End If
    End Sub
    Private Sub users_KeyDown(sender As Object, e As KeyEventArgs) Handles users.KeyDown
        If e.KeyCode = Keys.Enter Then
            password.Text = ""
            password.Properties.UseSystemPasswordChar = True
            password.Focus()
        End If
    End Sub

    Private Sub fillstore()
        store.Items.Clear()
        Dim adp As New SqlDataAdapter("select (store_name) from store where store_active=N'true'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            store.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub
    Sub login_user()
        Dim sql = "select * from users where user_name=N'" & (users.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        '   ========= بيانات اساسية============
        dr!user_time = Now.ToString("hh:mm")
        dr!user_seen = Now
        dr!user_page = "الشاشه الرئيسية"
        Dim cmd As New SqlCommandBuilder(adp)
        adp.Update(dt)
    End Sub

    Private Sub user_power_TextChanged(sender As Object, e As EventArgs) Handles user_power.TextChanged
        Try
            Dim sql = "select * from power_user where power_name=N'" & (user_power.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                XtraForm1.m1.Checked = dr!m1
                XtraForm1.m2.Checked = dr!m2
                XtraForm1.m3.Checked = dr!m3
                XtraForm1.m4.Checked = dr!m4
                XtraForm1.m5.Checked = dr!m5
                XtraForm1.m6.Checked = dr!m6
                XtraForm1.m7.Checked = dr!m7
                XtraForm1.m8.Checked = dr!m8
                XtraForm1.m9.Checked = dr!m9

                XtraForm1.m10.Checked = dr!m10
                XtraForm1.m11.Checked = dr!m11
                XtraForm1.m12.Checked = dr!m12
                XtraForm1.m13.Checked = dr!m13
                XtraForm1.m14.Checked = dr!m14
                XtraForm1.m15.Checked = dr!m15
                XtraForm1.m16.Checked = dr!m16
                XtraForm1.m17.Checked = dr!m17
                XtraForm1.m18.Checked = dr!m18
                XtraForm1.m19.Checked = dr!m19

                XtraForm1.m20.Checked = dr!m20
                XtraForm1.m21.Checked = dr!m21
                XtraForm1.m22.Checked = dr!m22
                XtraForm1.m23.Checked = dr!m23
                XtraForm1.m24.Checked = dr!m24
                XtraForm1.m25.Checked = dr!m25
                XtraForm1.m26.Checked = dr!m26
                XtraForm1.m27.Checked = dr!m27
                XtraForm1.m28.Checked = dr!m28
                XtraForm1.m29.Checked = dr!m29

                XtraForm1.m30.Checked = dr!m30
                XtraForm1.m31.Checked = dr!m31
                XtraForm1.m32.Checked = dr!m32
                XtraForm1.m33.Checked = dr!m33
                XtraForm1.m34.Checked = dr!m34
                XtraForm1.m35.Checked = dr!m35
                XtraForm1.m36.Checked = dr!m36
                XtraForm1.m37.Checked = dr!m37
                XtraForm1.m38.Checked = dr!m38
                XtraForm1.m39.Checked = dr!m39

                XtraForm1.m40.Checked = dr!m40
                XtraForm1.m42.Checked = dr!m42
                XtraForm1.m43.Checked = dr!m43
                XtraForm1.m44.Checked = dr!m44
                XtraForm1.m45.Checked = dr!m45
                XtraForm1.m46.Checked = dr!m46
                XtraForm1.m47.Checked = dr!m47
                XtraForm1.m48.Checked = dr!m48
                XtraForm1.m49.Checked = dr!m49

                XtraForm1.m50.Checked = dr!m50
                XtraForm1.m51.Checked = dr!m51
                XtraForm1.m52.Checked = dr!m52
                XtraForm1.m53.Checked = dr!m53
                XtraForm1.m54.Checked = dr!m54
                XtraForm1.m55.Checked = dr!m55
                XtraForm1.m56.Checked = dr!m56
                XtraForm1.m57.Checked = dr!m57
                XtraForm1.m58.Checked = dr!m58
                XtraForm1.m59.Checked = dr!m59

                XtraForm1.m60.Checked = dr!m60
                XtraForm1.m61.Checked = dr!m61
                XtraForm1.m62.Checked = dr!m62
                XtraForm1.m63.Checked = dr!m63
                XtraForm1.m64.Checked = dr!m64
                XtraForm1.m65.Checked = dr!m65
                XtraForm1.m66.Checked = dr!m66
                XtraForm1.m67.Checked = dr!m67
                XtraForm1.m68.Checked = dr!m68
                XtraForm1.m69.Checked = dr!m69

                XtraForm1.m70.Checked = dr!m70
                XtraForm1.m71.Checked = dr!m71
                XtraForm1.m72.Checked = dr!m72
                XtraForm1.m73.Checked = dr!m73
                XtraForm1.m74.Checked = dr!m74
                XtraForm1.m75.Checked = dr!m75
                XtraForm1.m76.Checked = dr!m76
                XtraForm1.m77.Checked = dr!m77
                XtraForm1.m78.Checked = dr!m78
                XtraForm1.m79.Checked = dr!m79

                XtraForm1.m80.Checked = dr!m80
                XtraForm1.m81.Checked = dr!m81
                XtraForm1.m82.Checked = dr!m82
                XtraForm1.m83.Checked = dr!m83
                XtraForm1.m84.Checked = dr!m84
                XtraForm1.m85.Checked = dr!m85
                XtraForm1.m86.Checked = dr!m86
                XtraForm1.m87.Checked = dr!m87
                XtraForm1.m88.Checked = dr!m88
                XtraForm1.m89.Checked = dr!m89

                XtraForm1.o1.Checked = dr!w1
                XtraForm1.o2.Checked = dr!w2
                XtraForm1.o3.Checked = dr!w3
                XtraForm1.o4.Checked = dr!w4
                XtraForm1.o5.Checked = dr!w5
                XtraForm1.o6.Checked = dr!w6
                XtraForm1.o7.Checked = dr!w7
                XtraForm1.o8.Checked = dr!w8
                XtraForm1.o9.Checked = dr!w9
                XtraForm1.o10.Checked = dr!w10

                XtraForm1.o11.Checked = dr!w11
                XtraForm1.o12.Checked = dr!w12
                XtraForm1.o13.Checked = dr!w13
                XtraForm1.o14.Checked = dr!w14
                XtraForm1.o15.Checked = dr!w15
                XtraForm1.o16.Checked = dr!w16
                XtraForm1.o17.Checked = dr!w17
                XtraForm1.o18.Checked = dr!w18
                XtraForm1.o19.Checked = dr!w19
                XtraForm1.w20.Checked = dr!w20

                XtraForm1.w21.Checked = dr!w21
                XtraForm1.w22.Checked = dr!w22
                XtraForm1.w23.Checked = dr!w23
                XtraForm1.w24.Checked = dr!w24
                XtraForm1.w25.Checked = dr!w25
                XtraForm1.w26.Checked = dr!w26
                XtraForm1.w27.Checked = dr!w27
                XtraForm1.w28.Checked = dr!w28
                XtraForm1.w29.Checked = dr!w29
                XtraForm1.w30.Checked = dr!w30

                XtraForm1.w31.Checked = dr!w31
                XtraForm1.w32.Checked = dr!w32
                XtraForm1.w33.Checked = dr!w33
                XtraForm1.w34.Checked = dr!w34
            End If
        Catch ex As Exception

        End Try

    End Sub
    Private Sub users_SelectedIndexChanged(sender As Object, e As EventArgs) Handles users.SelectedIndexChanged
        Dim sql = "select * from users where user_name=N'" & (users.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            user_id.Text = dr!user_id
            user_pass.Text = dr!user_pass
            user_power.Text = dr!user_power
            store.Text = dr!store
            type_pay.Text = dr!type_pay
        End If

        password.Text = ""
        password.Properties.UseSystemPasswordChar = True
        password.Focus()
    End Sub

    Private Sub login_btn_Click(sender As Object, e As EventArgs) Handles login_btn.Click
        If password.Text <> "spaxet" Then
            If password.Text <> user_pass.Text Then
                MsgBox("كلمة السر غير صحيحة", MsgBoxStyle.Critical, "خطأ")
                password.Text = ""
                password.Focus()
                Exit Sub
            End If
        End If
       
        Try
            current_user = user_id.Text
            XtraForm1.user_name.Text = users.Text
            XtraForm1.store.Text = store.Text
            XtraForm1.type_pay.Text = type_pay.Text
            XtraForm1.BarStaticItem1.Caption = users.Text
            XtraForm1.BarStaticItem2.Caption = "المخزن : " + store.Text
            XtraForm1.BarStaticItem3.Caption = "التاريخ : " + Now.Date
        Catch ex As Exception

        End Try


        Me.Dispose()
        XtraForm1.Show()
    End Sub

    Private Sub login_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        End
    End Sub

End Class