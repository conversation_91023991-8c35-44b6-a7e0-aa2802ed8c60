﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class Drawings_item
    Dim check As Integer = 0

    Sub fill_item()

        item_name.Properties.DataSource = Nothing
        item_name.EditValue = ""
        Dim adp As New SqlDataAdapter("select * from item where itemstore=N'" & (store.Text) & "' and item_active = 'true' order by itemnamearabic ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        item_name.Properties.DataSource = dt
        item_name.Properties.DisplayMember = "itemnamearabic"
        item_name.Properties.ValueMember = "itemcode"
    End Sub

    Function getcode(subname) As String
        Dim sql = "select * from item where itemstore=N'" & (store.Text) & "' and itemnamearabic=N'" & (item_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("itemcode") Else Return ""
    End Function
    Function showcode(subname) As String
        Dim sql = "select * from item where itemstore=N'" & (store.Text) & "' and itemnamearabic=N'" & (show_item.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("itemcode") Else Return ""
    End Function
    Private Sub code_TextChanged(sender As Object, e As EventArgs) Handles code.TextChanged
        item_count.Text = 1
        count_text.Text = 0
        show_buy.Text = ""

        show_datebuy.Text = ""
        show_datesale.Text = ""
        dgv_2.DataSource = Nothing
        dgv_2.Rows.Clear()
        Dim sql = "select * from item where itemcode=N'" & (code.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            code2.Text = dr!itemcode
            item_name.Text = dr!itemnamearabic
            show_count.Text = dr!itemcount
            item_notes.Text = dr!item_notes

        End If
        fill_unit()
        fill_unit_item()
        If Alternativeitem.Checked = True Then
            fill_itemalternavite()
        End If
        buybeta()
        salebeta()

        If XtraForm1.sale_buy.Text = "متوسط الشراء" Then
            Dim str = "select sum(price), count(price) from avg_item where code=N'" & code.Text & "'"
            Dim cmd As New SqlCommand(str, sqlconn)
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            Dim dta As New DataTable("avg_item")
            da.Fill(dta)

            Dim sumdebit As Decimal = 0.0
            Dim sumcredit As Decimal = 0.0
            If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
            If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
            show_buy.Text = Format((Val(sumdebit) / Val(sumcredit)), "0.00")
        End If
    End Sub
    Sub fill_unit()
        Try
            item_unit.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from unit_item where CODE=N'" & (code.Text) & "'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                item_unit.Items.Add(dt.Rows(i).Item("unit"))
            Next
            item_unit.SelectedIndex = 0
        Catch ex As Exception

        End Try

    End Sub
    Sub fill_unit_item()
        Dim adp As New SqlDataAdapter("select * from unit_item where code=N'" & (code.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        DataGridView1.AutoGenerateColumns = False
        DataGridView1.DataSource = dt
        Try

            If DataGridView1.Rows.Count = 3 Then
                threeunit()
            End If
            If DataGridView1.Rows.Count = 2 Then
                twounit()
            End If
            If DataGridView1.Rows.Count = 1 Then
                oneunit()
            End If

        Catch ex As Exception

        End Try
        For i = 0 To DataGridView1.Rows.Count - 1
            If DataGridView1.Rows(i).Cells(2).Value <> 0 Then
                count_text.Text = count_text.Text & " و " & DataGridView1.Rows(i).Cells(2).Value & " " & DataGridView1.Rows(i).Cells(0).Value
            End If
        Next
    End Sub
    Private Sub threeunit()
        Try


            Dim unit1, unit2, unit3, qty1, qty2, qty3, qtyo2, qtyo3, fin1, fin2 As Double
            Dim qty = Val(show_count.Text)
            unit1 = Val(DataGridView1.Rows(0).Cells(1).Value)
            unit2 = Val(DataGridView1.Rows(1).Cells(1).Value)
            unit3 = Val(DataGridView1.Rows(2).Cells(1).Value)

            qty3 = Format(qty / unit3, "0000.")
            If qty3 * unit3 > qty Then
                qty3 = qty3 - 1
            End If

            qtyo3 = qty3 * unit3
            fin1 = qty - qtyo3



            qty2 = Format(fin1 / unit2, "00.")
            If qty2 * unit2 > fin1 Then
                qty2 = qty2 - 1
            End If

            qtyo2 = qty2 * unit2
            fin2 = fin1 - qtyo2
            qty1 = fin2 / unit1




            DataGridView1.Rows(0).Cells(2).Value = qty1

            DataGridView1.Rows(1).Cells(2).Value = qty2

            DataGridView1.Rows(2).Cells(2).Value = qty3
        Catch ex As Exception

        End Try
    End Sub
    Private Sub twounit()
        Try


            Dim unit2, unit3, qty2, qty3, qtyo3, fin1 As Double
            Dim qty = Val(show_count.Text)

            unit2 = Val(DataGridView1.Rows(0).Cells(1).Value)
            unit3 = Val(DataGridView1.Rows(1).Cells(1).Value)

            qty3 = Format(qty / unit3, "0000.")
            If qty3 * unit3 > qty Then
                qty3 = qty3 - 1
            End If

            qtyo3 = qty3 * unit3
            fin1 = qty - qtyo3
            qty2 = fin1 / unit2




            DataGridView1.Rows(0).Cells(2).Value = qty2

            DataGridView1.Rows(1).Cells(2).Value = qty3


        Catch ex As Exception

        End Try
    End Sub
    Private Sub oneunit()
        Try


            Dim unit1, qty1 As Double
            Dim qty = Val(show_count.Text)
            unit1 = Val(DataGridView1.Rows(0).Cells(1).Value)



            qty1 = qty / unit1


            DataGridView1.Rows(0).Cells(2).Value = qty1


        Catch ex As Exception

        End Try

    End Sub
    Sub fill_itemalternavite()
        Dim adp As New SqlDataAdapter("select * from Alternative_item where itemname=N'" & (item_name.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_2.AutoGenerateColumns = False
        dgv_2.DataSource = dt
    End Sub

    Sub buybeta()

        Dim str = "select max(invoice_number) from purchases_list where item_name=N'" & item_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("purchases_list")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        code_pharche.Text = Val(sumdebit)


        Dim sql = "select * from purchases_list where invoice_number=N'" & (code_pharche.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            show_datebuy.Text = dr!item_date
        End If
    End Sub
    Sub salebeta()

        Dim str = "select max(invoice_number) from invoice_list where item_name=N'" & item_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("invoice_list")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        code_invoice.Text = Val(sumdebit)


        Dim sql = "select * from invoice_list where invoice_number=N'" & (code_invoice.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            show_datesale.Text = dr!item_date
        End If
    End Sub

    Private Sub damage_item_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        new_btn_Click(Nothing, Nothing)
        set_fatore()
        SplitContainerControl1.SplitterPosition = My.Settings.f_size
    End Sub

    Sub set_fatore()
        If XtraForm1.w8.Text = False Then
            XtraTabPage2.PageVisible = False
        End If
    End Sub

    Private Sub fillstore()
        store.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from store where store_active=N'true'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            store.Properties.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub
    Private Sub inserte_Click(sender As Object, e As EventArgs) Handles inserte.Click
        code.Text = getcode(item_name.Text)
        If item_name.Text = "" Then
            MsgBox("أدخل اسم الصنف")
            Exit Sub
        End If
        If XtraForm1.w2.Text = False And Val(item_count.Text) * Val(number_unit.Text) > show_count.Text Then
            MsgBox("غير مسموح بان يكون الرصيد يساوي سالب")
            Exit Sub
        End If
       
        dgv.Rows.Add()
        dgv.Rows(dgv.Rows.Count - 1).Cells(0).Value = code2.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(1).Value = item_name.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(2).Value = item_unit.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(3).Value = item_count.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(4).Value = item_price.Text
        Dim x = Val(item_price.Text) * Val(item_count.Text)
        dgv.Rows(dgv.Rows.Count - 1).Cells(5).Value = Format(x, "0.0")
        dgv.Rows(dgv.Rows.Count - 1).Cells(6).Value = reason_damage.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(8).Value = show_count.Text
        dgv.Rows(dgv.Rows.Count - 1).Cells(9).Value = Val(item_count.Text) * Val(number_unit.Text)
        plus_invoice()
    End Sub
    Sub plus_invoice()
        Dim sumr As Decimal = 0.0
        For i = 0 To dgv.Rows.Count - 1
            sumr = (sumr) + Val(dgv.Rows(i).Cells(5).Value)
        Next
        total_damage.Text = Format(sumr, "0.0")
        damage_count.Text = dgv.RowCount

    End Sub
    Private Sub total_damage_TextChanged(sender As Object, e As EventArgs) Handles total_damage.TextChanged

        show_total.Text = total_damage.Text

    End Sub
    Private Sub item_name_KeyDown(sender As Object, e As KeyEventArgs)

        If e.KeyCode = Keys.Enter Then
            item_count.Focus()
        End If
    End Sub

    Private Sub item_count_KeyDown(sender As Object, e As KeyEventArgs) Handles item_count.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_price.Focus()
        End If
    End Sub
    Private Sub item_price_KeyDown(sender As Object, e As KeyEventArgs) Handles item_price.KeyDown
        If e.KeyCode = Keys.Enter Then
           
            inserte_Click(Nothing, Nothing)
            If check = 1 Then
                TextEdit1.Text = ""
                TextEdit1.Focus()
            ElseIf check = 2 Then
                item_name.Text = ""
                item_name.Focus()
            End If

            plus_invoice()
        End If
    End Sub
    Private Sub reason_damage_KeyDown(sender As Object, e As KeyEventArgs) Handles reason_damage.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            item_name.Focus()
            plus_invoice()
        End If
    End Sub
    Private Sub dgv_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv.CellPainting
        If e.ColumnIndex = 7 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub
    Private Sub dgv_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv.CellClick
        If e.ColumnIndex = 7 Then
            dgv.Rows.Remove(dgv.CurrentRow)
            plus_invoice()
        End If
    End Sub
    Private Sub Label16_Click(sender As Object, e As EventArgs) Handles Label16.Click
        code3.Text = showcode(show_item.Text)
    End Sub
    Private Sub code3_TextChanged(sender As Object, e As EventArgs) Handles code3.TextChanged
        show_buy.Text = ""
        show_count.Text = ""

        show_datebuy.Text = ""
        show_datesale.Text = ""
        dgv_2.DataSource = Nothing
        dgv_2.Rows.Clear()
        Dim sql = "select * from item where itemcode=N'" & (code3.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            show_buy.Text = dr!itembuyprice
            show_count.Text = dr!itemcount

            item_notes.Text = dr!item_notes
        End If
        salebeta()
        buybeta()
    End Sub
    '=========================== show data
    Public Sub show_data(x)
        dgv.Rows.Clear()
        new_btn_Click(Nothing, Nothing)
        Dim sql = "select * from Drawings where damage_number=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لم يتم العثور علي بيانات")
        Else
            Dim dr = dt.Rows(0)
            damage_number.Text = dr!damage_number
            damage_date.Text = dr!damage_date
            store.Text = dr!store
            total_damage.Text = dr!total_damage
            damage_count.Text = dr!damage_count
            '========================================
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            adp = New SqlDataAdapter("select * from Drawings_list where damage_number=N'" & (x) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                dgv.Rows.Add()
                dgv.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
                dgv.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_name")
                dgv.Rows(s).Cells(2).Value = dt.Rows(s).Item("item_unit")
                dgv.Rows(s).Cells(3).Value = dt.Rows(s).Item("item_count")
                dgv.Rows(s).Cells(4).Value = dt.Rows(s).Item("item_price")
                dgv.Rows(s).Cells(5).Value = dt.Rows(s).Item("item_total")
                dgv.Rows(s).Cells(6).Value = dt.Rows(s).Item("reason_damage")
                dgv.Rows(s).Cells(8).Value = dt.Rows(s).Item("new_count")
                dgv.Rows(s).Cells(9).Value = dt.Rows(s).Item("item_balace")
            Next
            '========================================
            save_btn.Enabled = False
            edit_btn.Enabled = True
            print_btn.Enabled = False
            delet_btn.Enabled = True
        End If
    End Sub
    Private Sub barcode_show_CheckedChanged(sender As Object, e As EventArgs) Handles barcode_show.CheckedChanged
        If barcode_show.Checked = False Then
            item_barcode.Visible = False
        End If
        If barcode_show.Checked = True Then
            item_barcode.Visible = True
        End If
    End Sub
    Private Sub Button1_Click(sender As Object, e As EventArgs)
        save_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs)
        dgv.Rows.Remove(dgv.CurrentRow)
        plus_invoice()
    End Sub
    Private Sub customername_SelectedIndexChanged(sender As Object, e As EventArgs)
        item_name.Properties.ImmediatePopup = True
        code.Text = getcode(item_name.Text)
    End Sub
    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        damage_note.Text = ""
        fillstore()
        store.SelectedIndex = 0
        damage_number.Text = getlastcode("Drawings", "damage_number") + 1
        damage_date.Text = Now.Date
        item_name.Text = ""
        item_count.Text = 0
        item_price.Text = 0
        reason_damage.Text = ""
        show_item.Text = ""
        show_count.Text = 0
        show_buy.Text = ""
        print_btn.Enabled = True
        fillstore()
        store.SelectedIndex = 0
        show_datebuy.Text = ""
        show_datesale.Text = ""
        damage_count.Text = 0
        total_damage.Text = 0
        item_notes.Text = ""
        '===========
        show_total.Text = 0
        dgv_2.DataSource = Nothing
        dgv_2.Rows.Clear()
        dgv.DataSource = Nothing
        dgv.Rows.Clear()
        fill_item()
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        save_btn.Enabled = True
        If XtraForm1.m58.Checked = False Then
            show_buy.Visible = False
        End If
        If XtraForm1.m59.Checked = False Then
            show_count.Visible = False
        End If
        show_buy.Text = ""
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        printcode.Text = getlastcode("Drawings", "damage_number") + 1
        If dgv.Rows.Count = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If
        Dim sql = "select * from Drawings where damage_number=N'" & (damage_number.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            ' damage_number.Text = getlastcode("damage", "damage_number") + 1
        Else
            Dim dr = dt.NewRow
            dr!damage_number = damage_number.Text
            dr!store = store.Text
            dr!damage_date = damage_date.Value
            dr!damage_note = damage_note.Text
            dr!total_damage = total_damage.Text
            dr!damage_count = damage_count.Text
            dr!code_print = 1
            dr!user_damage = XtraForm1.user_name.Text
            dr!total_string = amount_string.Text

            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        '    '============حفظ السحل================
        adp = New SqlDataAdapter("select * from Drawings_list", sqlconn)
        ds = New DataSet
        adp.Fill(ds)
        dt = ds.Tables(0)
        For i = 0 To dgv.Rows.Count - 1
            Dim Dr = dt.NewRow
            Dr!damage_number = damage_number.Text
            Dr!store = store.Text
            Dr!item_code = dgv.Rows(i).Cells(0).Value
            Dr!item_name = dgv.Rows(i).Cells(1).Value
            Dr!item_unit = dgv.Rows(i).Cells(2).Value
            Dr!item_count = dgv.Rows(i).Cells(3).Value
            Dr!item_price = dgv.Rows(i).Cells(4).Value
            Dr!item_date = damage_date.Value
            Dr!item_total = Convert.ToDecimal(dgv.Rows(i).Cells(5).Value)
            Dr!reason_damage = dgv.Rows(i).Cells(6).Value
            Dr!new_count = dgv.Rows(i).Cells(8).Value
            Dr!item_balace = dgv.Rows(i).Cells(9).Value
            dt.Rows.Add(Dr)
            Dim cmdbuilder As New SqlCommandBuilder(adp)
            adp.Update(dt)
            set_itemtrans(dgv.Rows(i).Cells(1).Value, damage_date.Value, damage_number.Text, "مسحوبات", 0, dgv.Rows(i).Cells(3).Value, dgv.Rows(i).Cells(8).Value - dgv.Rows(i).Cells(9).Value)
        Next
        '    '======================================== حفظ الحركات
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            adp = New SqlDataAdapter("select * from item where itemcode=N'" & (dgv.Rows(a).Cells(0).Value) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            dr!itemcount = Val(dr!itemcount) - Val(dgv.Rows(a).Cells(9).Value)
            dr!total_price = Val(dr!total_price) - Val(dgv.Rows(a).Cells(5).Value)
            dr!Profits = Val(dr!Profits) - Val(dgv.Rows(a).Cells(4).Value)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Next

        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
        new_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If XtraForm1.m57.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim sql = "select * from Drawings where damage_number=N'" & damage_number.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            sql = "select * from Drawings_list where damage_number=N'" & damage_number.Text.Trim() & "'"
            adp = New SqlDataAdapter(sql, sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            adp = New SqlDataAdapter("select * from item where itemcode=N'" & (dgv.Rows(a).Cells(0).Value) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            dr!itemcount = Val(dr!itemcount) + Val(dgv.Rows(a).Cells(9).Value)
            dr!total_price = Val(dr!total_price) + Val(dgv.Rows(a).Cells(5).Value)
            dr!Profits = Val(dr!Profits) + Val(dgv.Rows(a).Cells(4).Value)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Next
        For a = 0 To dgv.Rows.Count - 1
            adp = New SqlDataAdapter("select * from item_trans where item_name=N'" & dgv.Rows(a).Cells(1).Value & "' and  item_tybe= 'مسحوبات' and invoice_number=N'" & damage_number.Text & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        save_btn.Enabled = True

    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        If XtraForm1.m56.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If MsgBox("هل انت متأكد بحذف الفاتورة المحدد؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        Dim sql = "select * from Drawings where damage_number=N'" & damage_number.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            sql = "select * from Drawings_list where damage_number=N'" & damage_number.Text.Trim() & "'"
            adp = New SqlDataAdapter(sql, sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        For a = 0 To dgv.Rows.Count - 1
            adp = New SqlDataAdapter("select * from item where itemcode=N'" & (dgv.Rows(a).Cells(0).Value) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            dr!itemcount = Val(dr!itemcount) + Val(dgv.Rows(a).Cells(9).Value)
            dr!total_price = Val(dr!total_price) + Val(dgv.Rows(a).Cells(5).Value)
            dr!Profits = Val(dr!Profits) + Val(dgv.Rows(a).Cells(4).Value)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Next
        For a = 0 To dgv.Rows.Count - 1
            adp = New SqlDataAdapter("select * from item_trans where item_name=N'" & dgv.Rows(a).Cells(1).Value & "' and  item_tybe= 'مسحوبات' and invoice_number=N'" & damage_number.Text & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
        new_btn_Click(Nothing, Nothing)
    End Sub
    Private Sub item_name_KeyDown_1(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            item_unit.Focus()
        End If
    End Sub

    Private Sub damage_item_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            new_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            edit_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            delet_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F5 Then
            print_btn_Click(Nothing, Nothing)
        End If

        If e.KeyCode = Keys.F12 Then
            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from Drawings_print where damage_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "Drawings_a4.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub


    Private Sub code2_TextChanged(sender As Object, e As EventArgs) Handles code2.TextChanged
        code3.Text = code2.Text
    End Sub

    Private Sub Label37_Click(sender As Object, e As EventArgs) Handles Label37.Click
        If XtraForm1.m39.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تسجيل صنف"
        f.MdiParent = XtraForm1
        f.new_item()
        f.Show()
    End Sub

    Private Sub Label45_Click(sender As Object, e As EventArgs) Handles Label45.Click
        If XtraForm1.m54.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تعديل صنف "
        f.MdiParent = XtraForm1
        f.show_data(code.Text)
        f.Show()
    End Sub
    Private Sub Label46_Click(sender As Object, e As EventArgs) Handles Label46.Click
        fill_item()
    End Sub
    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub

    Private Sub store_SelectedIndexChanged(sender As Object, e As EventArgs)
        fill_item()
    End Sub
    Private Sub Alternativeitem_CheckedChanged(sender As Object, e As EventArgs) Handles Alternativeitem.CheckedChanged
        code_TextChanged(Nothing, Nothing)
    End Sub

    Private Sub PictureBox1_Click(sender As Object, e As EventArgs) Handles PictureBox1.Click
        Process.Start("https://www.facebook.com/Beta-store-361440301309862/?ref=aymt_homepage_panel&eid=ARDbnMA3vmMHk2XGLGVvVV8J1evZLRO5o7Tvq5jTv8YqAAkiBzn4VxmaIZi4mobvru532j8T3ry184b9")
    End Sub

    Private Sub item_unit_KeyDown(sender As Object, e As KeyEventArgs) Handles item_unit.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_count.Focus()
        End If
    End Sub

    Private Sub item_unit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles item_unit.SelectedIndexChanged
        item_price.Text = ""
        Dim sql = "select * from unit_item where code=N'" & (code.Text) & "' and unit=N'" & (item_unit.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            number_unit.Text = dr!count
            item_price.Text = dr!price_buy
            show_buy.Text = dr!price_buy
        End If
        If XtraForm1.sale_buy2.Text = "متوسط الشراء" Then
            Dim str = "select sum(item_price), count(item_count) from purchases_list where item_code=N'" & code.Text & "' and item_unit=N'" & item_unit.Text & "'"
            Dim cmd As New SqlCommand(str, sqlconn)
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            Dim dta As New DataTable("purchases_list")
            da.Fill(dta)

            Dim sumdebit As Decimal = 0.0
            Dim sumcredit As Decimal = 0.0
            If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
            If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
            show_buy.Text = Format((Val(sumdebit) / Val(sumcredit)), "0.00")
            item_price.Text = Format((Val(sumdebit) / Val(sumcredit)), "0.00")
        End If
    End Sub

    Private Sub store_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles store.SelectedIndexChanged
        fill_item()
    End Sub

    Private Sub item_name_EditValueChanged(sender As Object, e As EventArgs) Handles item_name.EditValueChanged
        code.Text = item_name.EditValue

    End Sub

    Private Sub item_barcode_KeyDown(sender As Object, e As KeyEventArgs) Handles item_barcode.KeyDown
        If e.KeyCode = Keys.Enter Then

            Dim sql = "select * from unit_item where item_unit=N'" & (item_barcode.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                code.Text = dr!code
            Else
                MsgBox("لايوجد باركود لهذا الرقم")
                item_barcode.Text = ""
                item_barcode.Focus()
                Exit Sub
            End If

            Try
                Dim sql7 = "select * from unit_item where item_unit=N'" & (item_barcode.Text) & "'"
                Dim adp7 As New SqlDataAdapter(sql7, sqlconn)
                Dim ds7 As New DataSet
                adp7.Fill(ds7)
                Dim dt7 = ds7.Tables(0)
                If dt7.Rows.Count > 0 Then
                    Dim dr7 = dt7.Rows(0)
                    item_unit.Text = dr7!unit
                End If
            Catch ex As Exception

            End Try


            inserte_Click(Nothing, Nothing)
            item_barcode.Text = ""
            item_barcode.Focus()
        End If
    End Sub
    Private Sub TextEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles TextEdit1.EditValueChanged
        If TextEdit1.Text = "" Then
            GridControl2.DataSource = Nothing
            Exit Sub
        End If



        Dim adp As New SqlDataAdapter("select (itemcode),(itemnamearabic),(itemcount),(itembuyprice) from item where itemstore=N'" & (store.Text) & "' and itemnamearabic like N'%" & (TextEdit1.Text) & "%'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            GridControl2.DataSource = dt

    End Sub

    Private Sub TextEdit1_KeyDown_2(sender As Object, e As KeyEventArgs) Handles TextEdit1.KeyDown
        If e.KeyCode = Keys.Enter Then

            If TextEdit1.Text = "" Then
                Exit Sub
            End If
            item_name.Text = ""
            item_name.EditValue = 0

            code.Text = 0
            If GridView1.RowCount = 0 Then
                Dim sss As Integer = 0
                Dim sql = "select code from unit_item where item_unit=N'" & (TextEdit1.Text) & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count > 0 Then
                    Dim dr = dt.Rows(0)
                    code.Text = dr!code
                Else
                    If XtraForm1.balance_check.Text = True Then
                        sss = 1
                        Dim sql4 = "select code from unit_item where item_unit=N'" & Mid(TextEdit1.Text, 3, XtraForm1.balance_w1.Text) & "'"
                        Dim adp4 As New SqlDataAdapter(sql4, sqlconn)
                        Dim ds4 As New DataSet
                        adp4.Fill(ds4)
                        Dim dt4 = ds4.Tables(0)
                        If dt4.Rows.Count > 0 Then
                            Dim dr4 = dt4.Rows(0)
                            code.Text = dr4!code

                        Else
                            MsgBox("لايوجد باركود لهذا الرقم")
                            TextEdit1.Text = ""
                            TextEdit1.Focus()

                            Exit Sub
                        End If
                    Else
                        MsgBox("لايوجد باركود لهذا الرقم")
                        TextEdit1.Text = ""
                        TextEdit1.Focus()

                        Exit Sub
                    End If


                End If
                Try
                    Dim sql7 = "select unit from unit_item where item_unit=N'" & (TextEdit1.Text) & "'"
                    Dim adp7 As New SqlDataAdapter(sql7, sqlconn)
                    Dim ds7 As New DataSet
                    adp7.Fill(ds7)
                    Dim dt7 = ds7.Tables(0)
                    If dt7.Rows.Count > 0 Then
                        Dim dr7 = dt7.Rows(0)
                        item_unit.Text = dr7!unit
                    End If
                Catch ex As Exception
                End Try
                Try
                    If sss = 1 Then
                        Dim aa As Decimal = Mid(TextEdit1.Text, XtraForm1.balance_w1.Text + 3, XtraForm1.balance_w2.Text)
                        item_count.Text = aa / 1000
                    End If
                Catch ex As Exception

                End Try


                inserte_Click(Nothing, Nothing)
                TextEdit1.Text = ""
                TextEdit1.Focus()
                Exit Sub
            Else
                GridControl2.DefaultView.Focus()
            End If
        End If

        If e.KeyCode = Keys.Down Then
            GridControl2.DefaultView.Focus()
        End If
    End Sub

    Private Sub GridView1_KeyDown(sender As Object, e As KeyEventArgs) Handles GridView1.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_name.Text = ""
            item_name.Text = GridView1.GetFocusedRowCellValue("itemnamearabic")
            check = 1
            code_TextChanged(Nothing, Nothing)
            item_unit.Focus()
        End If
    End Sub

    Private Sub item_name_KeyDown_2(sender As Object, e As KeyEventArgs) Handles item_name.KeyDown
        If e.KeyCode = Keys.Enter Then
            check = 2
            code.Text = item_name.EditValue
            item_unit.Focus()

        End If

    End Sub

End Class