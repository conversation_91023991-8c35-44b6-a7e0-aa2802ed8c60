﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="22.1.3.0" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="invoice_a4" Landscape="true" Margins="0, 0, 2, 2" PaperKind="A4" PageWidth="1169" PageHeight="827" Version="22.1" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="2" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="2" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="139.625076">
      <Controls>
        <Item1 Ref="6" ControlType="XRLabel" Name="label5" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354126,32.291687" LocationFloat="880.6179,75.04171" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[sec_phone]" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="label6" Text="XrLabel24" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354126,31.3333244" LocationFloat="876.8313,2.24998784" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[sec_name]" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="12" ControlType="XRLabel" Name="label7" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354248,30.62503" LocationFloat="876.8313,33.5833244" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[sec_s1]" />
          </ExpressionBindings>
          <StylePriority Ref="14" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="15" ControlType="XRPictureBox" Name="pictureBox1" Sizing="StretchImage" SizeF="145.416565,94.87499" LocationFloat="616.8595,23.1666565">
          <ExpressionBindings>
            <Item1 Ref="16" EventName="BeforePrint" PropertyName="ImageSource" Expression="[sec_pic]" />
          </ExpressionBindings>
        </Item4>
        <Item5 Ref="17" ControlType="XRLabel" Name="label8" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354126,32.2917023" LocationFloat="880.6179,107.333374" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[sec_address]" />
          </ExpressionBindings>
          <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="20" ControlType="XRLabel" Name="label4" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.3541,32.2917023" LocationFloat="324.836151,107.333374" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[sec_address]" />
          </ExpressionBindings>
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="23" ControlType="XRLabel" Name="label1" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354065,32.2917023" LocationFloat="324.836151,75.04167" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="24" EventName="BeforePrint" PropertyName="Text" Expression="[sec_phone]" />
          </ExpressionBindings>
          <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="26" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="145.4165,94.8749847" LocationFloat="26.1145267,23.1666565">
          <ExpressionBindings>
            <Item1 Ref="27" EventName="BeforePrint" PropertyName="ImageSource" Expression="[sec_pic]" />
          </ExpressionBindings>
        </Item8>
        <Item9 Ref="28" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354126,30.6250267" LocationFloat="321.049622,33.5833244" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[sec_s1]" />
          </ExpressionBindings>
          <StylePriority Ref="30" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="31" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="321.049622,2.24998784" Font="Microsoft Sans Serif, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[sec_name]" />
          </ExpressionBindings>
          <StylePriority Ref="33" UseFont="false" UseTextAlignment="false" />
        </Item10>
      </Controls>
    </Item4>
    <Item5 Ref="34" ControlType="PageHeaderBand" Name="PageHeader" HeightF="165.541718">
      <Controls>
        <Item1 Ref="35" ControlType="XRTable" Name="table1" TextAlignment="MiddleCenter" SizeF="534.113037,33.3333435" LocationFloat="616.8595,132.208374" Font="Microsoft Sans Serif, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="36" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="37" ControlType="XRTableCell" Name="tableCell1" Weight="0.*****************" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="38" UsePadding="false" />
                </Item1>
                <Item2 Ref="39" ControlType="XRTableCell" Name="tableCell2" Weight="0.*****************" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="40" ControlType="XRTableCell" Name="tableCell3" Weight="0.542263146654033" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="41" UsePadding="false" />
                </Item3>
                <Item4 Ref="42" ControlType="XRTableCell" Name="tableCell4" Weight="0.64086488759827032" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="43" ControlType="XRTableCell" Name="tableCell5" Weight="2.5482460841812116" Text="اسم الصنف" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="44" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="45" ControlType="XRLabel" Name="label9" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="157.104309,31.24998" LocationFloat="616.8595,10.0000067" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_adress]" />
          </ExpressionBindings>
          <StylePriority Ref="47" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="48" ControlType="XRLabel" Name="label10" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="157.104309,31.2499771" LocationFloat="616.8595,49.9583244" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_phone1]" />
          </ExpressionBindings>
          <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="51" ControlType="XRLabel" Name="label11" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="79.166626,31.2499924" LocationFloat="773.9638,49.95826" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="52" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="53" ControlType="XRLabel" Name="label12" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="79.166626,31.2500076" LocationFloat="773.9638,9.999879" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="54" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="55" ControlType="XRLabel" Name="label13" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="115.862854,31.2499733" LocationFloat="960.856262,49.9581337" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="56" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_name]" />
          </ExpressionBindings>
          <StylePriority Ref="57" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="58" ControlType="XRLabel" Name="label14" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="74.2532959,31.25" LocationFloat="1076.719,49.9581337" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="59" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="60" ControlType="XRLabel" Name="label15" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="74.25305,31.2500038" LocationFloat="1076.719,9.999879" Font="Microsoft Sans Serif, 8.25pt, charSet=0" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="61" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="62" ControlType="XRLabel" Name="label16" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="115.862854,31.2500324" LocationFloat="960.856262,9.999816" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="63" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_date]" />
          </ExpressionBindings>
          <StylePriority Ref="64" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="65" ControlType="XRLabel" Name="label17" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="86.3375244,31.25" LocationFloat="865.01825,49.95823" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="66" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_number]" />
          </ExpressionBindings>
          <StylePriority Ref="67" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="68" ControlType="XRLabel" Name="label18" Text="بيان اسعار" TextAlignment="MiddleCenter" SizeF="86.3375244,31.33332" LocationFloat="865.01825,10.00007" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="69" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="70" ControlType="XRLabel" Name="label19" RightToLeft="Yes" Text="ملاحظات :" TextAlignment="TopCenter" SizeF="69.20386,31.25" LocationFloat="1081.76855,88.45838" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="71" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="72" ControlType="XRLabel" Name="label20" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="464.909119,31.24997" LocationFloat="616.8595,88.45838" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="73" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_adress]" />
          </ExpressionBindings>
          <StylePriority Ref="74" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="75" ControlType="XRLabel" Name="label2" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="494.822418,31.2499771" LocationFloat="26.1145267,88.45838" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="76" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_note]" />
          </ExpressionBindings>
          <StylePriority Ref="77" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="78" ControlType="XRLabel" Name="label3" RightToLeft="Yes" Text="ملاحظات :" TextAlignment="TopCenter" SizeF="74.25336,31.2500076" LocationFloat="520.936951,88.45834" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="79" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="80" ControlType="XRLabel" Name="XrLabel19" Text="بيان اسعار" TextAlignment="MiddleCenter" SizeF="86.33746,31.33332" LocationFloat="274.273315,10.00007" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="81" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="82" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="86.33746,31.25" LocationFloat="274.273315,49.95823" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="83" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_number]" />
          </ExpressionBindings>
          <StylePriority Ref="84" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="85" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="141.576172,31.2500381" LocationFloat="379.360779,9.99978352" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="86" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_date]" />
          </ExpressionBindings>
          <StylePriority Ref="87" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="88" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="74.2531738,31.25" LocationFloat="520.936951,9.999847" Font="Microsoft Sans Serif, 8.25pt, charSet=0" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="89" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="90" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="74.2532959,31.2499924" LocationFloat="520.9369,49.9581032" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="91" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item20>
        <Item21 Ref="92" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="141.576172,31.2499733" LocationFloat="379.360779,49.9581032" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="93" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_name]" />
          </ExpressionBindings>
          <StylePriority Ref="94" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item21>
        <Item22 Ref="95" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="157.1043,31.2499847" LocationFloat="26.1145267,10.0000067" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="96" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_adress]" />
          </ExpressionBindings>
          <StylePriority Ref="97" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item22>
        <Item23 Ref="98" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="79.16667,31.2500076" LocationFloat="183.218826,9.999879" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="99" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="100" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="79.16667,31.2499924" LocationFloat="183.218826,49.95826" Font="Microsoft Sans Serif, 8.25pt, charSet=0" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="101" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item24>
        <Item25 Ref="102" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="157.1043,31.2499733" LocationFloat="26.1145267,49.9583244" Font="Microsoft Sans Serif, 8.25pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="103" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_phone1]" />
          </ExpressionBindings>
          <StylePriority Ref="104" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item25>
        <Item26 Ref="105" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="569.075745,33.3333435" LocationFloat="26.1145267,132.208374" Font="Microsoft Sans Serif, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="106" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="107" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.*****************" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="108" UsePadding="false" />
                </Item1>
                <Item2 Ref="109" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.*****************" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="110" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.542263146654033" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="111" UsePadding="false" />
                </Item3>
                <Item4 Ref="112" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.64086488759827032" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="113" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5482460841812116" Text="اسم الصنف" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="114" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item26>
      </Controls>
    </Item5>
    <Item6 Ref="115" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="138.083252">
      <Controls>
        <Item1 Ref="116" ControlType="XRLabel" Name="label22" Text="............................." TextAlignment="MiddleRight" SizeF="128.749878,25.0833035" LocationFloat="619.969,102.999939" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="117" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="118" ControlType="XRLabel" Name="label21" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.749878,25.0832977" LocationFloat="619.969,77.58334" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="119" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="120" ControlType="XRLabel" Name="label23" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="291.72937,24.9999924" LocationFloat="859.243042,77.6666641" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="121" EventName="BeforePrint" PropertyName="Text" Expression="[total_string]" />
          </ExpressionBindings>
          <StylePriority Ref="122" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="123" ControlType="XRTable" Name="table4" TextAlignment="MiddleCenter" SizeF="534.1123,62.5" LocationFloat="616.8595,0" Font="Microsoft Sans Serif, 6.75pt, charSet=0" Borders="All">
          <Rows>
            <Item1 Ref="124" ControlType="XRTableRow" Name="tableRow4" Weight="1">
              <Cells>
                <Item1 Ref="125" ControlType="XRTableCell" Name="tableCell16" Weight="1.1721783522022122" Text="الباقي" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="126" UseBackColor="false" UsePadding="false" />
                </Item1>
                <Item2 Ref="127" ControlType="XRTableCell" Name="tableCell17" Weight="1.1480901595333732" Text="المدفوع" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="128" UseBackColor="false" UsePadding="false" />
                </Item2>
                <Item3 Ref="129" ControlType="XRTableCell" Name="tableCell18" Weight="1.1925784949628904" Text="المطلوب" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="130" UseBackColor="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="131" ControlType="XRTableCell" Name="tableCell19" Weight="1.2675382422227925" Text="السابق" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="132" UseBackColor="false" />
                </Item4>
                <Item5 Ref="133" ControlType="XRTableCell" Name="tableCell20" Weight="1.373977689752139" Text="صافي الفاتورة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="134" UseBackColor="false" UsePadding="false" />
                </Item5>
                <Item6 Ref="135" ControlType="XRTableCell" Name="tableCell21" Weight="1.0821246348483362" Text="الضريبة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="136" UseBackColor="false" UsePadding="false" />
                </Item6>
                <Item7 Ref="137" ControlType="XRTableCell" Name="tableCell22" Weight="1.0551685273331897" Text="الخصم" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="138" UseBackColor="false" />
                </Item7>
                <Item8 Ref="139" ControlType="XRTableCell" Name="tableCell23" Weight="1.0527793583745975" Text="الاجمالي" BackColor="WhiteSmoke" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <StylePriority Ref="140" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="141" ControlType="XRTableCell" Name="tableCell24" Weight="1.1895429603081362" Text="عدد الاصناف" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="142" UseBackColor="false" />
                </Item9>
              </Cells>
            </Item1>
            <Item2 Ref="143" ControlType="XRTableRow" Name="tableRow5" Weight="1">
              <Cells>
                <Item1 Ref="144" ControlType="XRTableCell" Name="tableCell25" Weight="1.1721785833909919" TextFormatString="{0:n2}" Text="XrTableCell23" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="145" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[new_balace]" />
                  </ExpressionBindings>
                  <StylePriority Ref="146" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="147" ControlType="XRTableCell" Name="tableCell26" Weight="1.1480894386640024" TextFormatString="{0:n2}" Text="XrTableCell24" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="148" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[pay]" />
                  </ExpressionBindings>
                  <StylePriority Ref="149" UsePadding="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="150" ControlType="XRTableCell" Name="tableCell27" Weight="1.1925780613556163" TextFormatString="{0:n2}" Text="XrTableCell25" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="151" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[money_plus]" />
                  </ExpressionBindings>
                  <StylePriority Ref="152" UsePadding="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="153" ControlType="XRTableCell" Name="tableCell28" Weight="1.2675386957322281" TextFormatString="{0:n2}" Text="XrTableCell26" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="154" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[past_balace]" />
                  </ExpressionBindings>
                  <StylePriority Ref="155" UsePadding="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="156" ControlType="XRTableCell" Name="tableCell29" Weight="1.3739798780977919" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="157" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[total_invoice]" />
                  </ExpressionBindings>
                  <StylePriority Ref="158" UsePadding="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="159" ControlType="XRTableCell" Name="tableCell30" Weight="1.0821229162811132" Text="XrTableCell28" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="160" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_tax]" />
                  </ExpressionBindings>
                  <StylePriority Ref="161" UsePadding="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="162" ControlType="XRTableCell" Name="tableCell31" Weight="1.0551685273331894" TextFormatString="{0:n2}" Text="XrTableCell29" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="163" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_descound]" />
                  </ExpressionBindings>
                  <StylePriority Ref="164" UsePadding="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="165" ControlType="XRTableCell" Name="tableCell32" Weight="1.052780689722121" TextFormatString="{0:n2}" Text="XrTableCell31" TextAlignment="MiddleCenter" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="166" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_pound]" />
                  </ExpressionBindings>
                  <StylePriority Ref="167" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="168" ControlType="XRTableCell" Name="tableCell33" Weight="1.1895416289606127" Text="XrTableCell12" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="169" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_count]" />
                  </ExpressionBindings>
                  <StylePriority Ref="170" UsePadding="false" UseTextAlignment="false" />
                </Item9>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="171" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="172" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="569.0758,62.5" LocationFloat="26.1145267,0" Font="Microsoft Sans Serif, 6.75pt, charSet=0" Borders="All">
          <Rows>
            <Item1 Ref="173" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="174" ControlType="XRTableCell" Name="XrTableCell22" Weight="1.1721783522022122" Text="الباقي" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="175" UseBackColor="false" UsePadding="false" />
                </Item1>
                <Item2 Ref="176" ControlType="XRTableCell" Name="XrTableCell21" Weight="1.1480901595333732" Text="المدفوع" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="177" UseBackColor="false" UsePadding="false" />
                </Item2>
                <Item3 Ref="178" ControlType="XRTableCell" Name="XrTableCell14" Weight="1.1925784949628904" Text="المطلوب" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="179" UseBackColor="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="180" ControlType="XRTableCell" Name="XrTableCell11" Weight="1.2675382422227925" Text="السابق" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="181" UseBackColor="false" />
                </Item4>
                <Item5 Ref="182" ControlType="XRTableCell" Name="XrTableCell19" Weight="1.373977689752139" Text="صافي الفاتورة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="183" UseBackColor="false" UsePadding="false" />
                </Item5>
                <Item6 Ref="184" ControlType="XRTableCell" Name="XrTableCell20" Weight="1.0821246348483362" Text="الضريبة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="185" UseBackColor="false" UsePadding="false" />
                </Item6>
                <Item7 Ref="186" ControlType="XRTableCell" Name="XrTableCell12" Weight="1.0551685273331897" Text="الخصم" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="187" UseBackColor="false" />
                </Item7>
                <Item8 Ref="188" ControlType="XRTableCell" Name="XrTableCell18" Weight="1.0527793583745975" Text="الاجمالي" BackColor="WhiteSmoke" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <StylePriority Ref="189" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="190" ControlType="XRTableCell" Name="XrTableCell13" Weight="1.1895429603081362" Text="عدد الاصناف" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="191" UseBackColor="false" />
                </Item9>
              </Cells>
            </Item1>
            <Item2 Ref="192" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="193" ControlType="XRTableCell" Name="XrTableCell23" Weight="1.1721785833909919" TextFormatString="{0:n2}" Text="XrTableCell23" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="194" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[new_balace]" />
                  </ExpressionBindings>
                  <StylePriority Ref="195" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="196" ControlType="XRTableCell" Name="XrTableCell24" Weight="1.1480894386640024" TextFormatString="{0:n2}" Text="XrTableCell24" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="197" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[pay]" />
                  </ExpressionBindings>
                  <StylePriority Ref="198" UsePadding="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="199" ControlType="XRTableCell" Name="XrTableCell25" Weight="1.1925780613556163" TextFormatString="{0:n2}" Text="XrTableCell25" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="200" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[money_plus]" />
                  </ExpressionBindings>
                  <StylePriority Ref="201" UsePadding="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="202" ControlType="XRTableCell" Name="XrTableCell26" Weight="1.2675386957322281" TextFormatString="{0:n2}" Text="XrTableCell26" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="203" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[past_balace]" />
                  </ExpressionBindings>
                  <StylePriority Ref="204" UsePadding="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="205" ControlType="XRTableCell" Name="XrTableCell27" Weight="1.3739798780977919" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="206" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[total_invoice]" />
                  </ExpressionBindings>
                  <StylePriority Ref="207" UsePadding="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="208" ControlType="XRTableCell" Name="XrTableCell28" Weight="1.0821229162811132" Text="XrTableCell28" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="209" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_tax]" />
                  </ExpressionBindings>
                  <StylePriority Ref="210" UsePadding="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="211" ControlType="XRTableCell" Name="XrTableCell29" Weight="1.0551685273331894" TextFormatString="{0:n2}" Text="XrTableCell29" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="212" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_descound]" />
                  </ExpressionBindings>
                  <StylePriority Ref="213" UsePadding="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="214" ControlType="XRTableCell" Name="XrTableCell31" Weight="1.052780689722121" TextFormatString="{0:n2}" Text="XrTableCell31" TextAlignment="MiddleCenter" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="215" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_pound]" />
                  </ExpressionBindings>
                  <StylePriority Ref="216" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="217" ControlType="XRTableCell" Name="XrTableCell32" Weight="1.1895416289606127" Text="XrTableCell12" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="218" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[invoice_count]" />
                  </ExpressionBindings>
                  <StylePriority Ref="219" UsePadding="false" UseTextAlignment="false" />
                </Item9>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="220" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="221" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="346.3231,25" LocationFloat="248.867233,77.58334" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="222" EventName="BeforePrint" PropertyName="Text" Expression="[total_string]" />
          </ExpressionBindings>
          <StylePriority Ref="223" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="224" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="38.9599,77.50005" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="225" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="226" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="38.9599,112.999947" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="227" UseFont="false" UseTextAlignment="false" />
        </Item8>
      </Controls>
    </Item6>
    <Item7 Ref="228" ControlType="PageFooterBand" Name="PageFooter" HeightF="27.1666527">
      <Controls>
        <Item1 Ref="229" ControlType="XRLabel" Name="label24" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.604248,25.0833187" LocationFloat="619.969,2.08333325" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="230" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="231" ControlType="XRPageInfo" Name="pageInfo1" TextAlignment="TopRight" SizeF="48.958252,23.0000038" LocationFloat="1032.81018,2.08333325" Padding="2,2,0,0,100">
          <StylePriority Ref="232" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="233" ControlType="XRLabel" Name="label25" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.083313" LocationFloat="1081.76855,2.08333325" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="234" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="235" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="37,0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="236" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="237" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="525.883,2.08333325" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="238" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="239" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,23" LocationFloat="476.924683,2.08333325" Padding="2,2,0,0,100">
          <StylePriority Ref="240" UseTextAlignment="false" />
        </Item6>
      </Controls>
    </Item7>
    <Item8 Ref="241" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="242" ControlType="DetailBand" Name="Detail1" HeightF="26.04167">
          <Controls>
            <Item1 Ref="243" ControlType="XRTable" Name="table3" TextAlignment="MiddleCenter" SizeF="534.1124,26.041666" LocationFloat="616.8595,0" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="244" ControlType="XRTableRow" Name="tableRow3" Weight="1">
                  <Cells>
                    <Item1 Ref="245" ControlType="XRTableCell" Name="tableCell11" Weight="1.0005448213163186" TextFormatString="{0:n2}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="246" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_total]" />
                      </ExpressionBindings>
                      <StylePriority Ref="247" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="248" ControlType="XRTableCell" Name="tableCell12" Weight="0.93220917081262" TextFormatString="{0:n2}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="249" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_price]" />
                      </ExpressionBindings>
                      <StylePriority Ref="250" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="251" ControlType="XRTableCell" Name="tableCell13" Weight="0.85685326008936014" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="252" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_count]" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="253" ControlType="XRTableCell" Name="tableCell14" Weight="1.0126570260107606" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="254" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_unit]" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="255" ControlType="XRTableCell" Name="tableCell15" Weight="4.0265895569054457" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="256" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_name]" />
                      </ExpressionBindings>
                      <StylePriority Ref="257" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="258" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="259" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="569.0758,25" LocationFloat="26.1145267,0" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="260" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="261" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.0005448213163186" TextFormatString="{0:n2}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="262" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_total]" />
                      </ExpressionBindings>
                      <StylePriority Ref="263" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="264" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.93220917081262" TextFormatString="{0:n2}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="265" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_price]" />
                      </ExpressionBindings>
                      <StylePriority Ref="266" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="267" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.85685326008936014" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="268" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_count]" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="269" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.0126570260107606" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="270" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_unit]" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="271" ControlType="XRTableCell" Name="XrTableCell8" Weight="4.0265895569054457" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="272" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_name]" />
                      </ExpressionBindings>
                      <StylePriority Ref="273" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="274" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item2>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="275" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="1" />
    <Item2 Ref="276" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item3 Ref="277" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item4 Ref="278" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="279" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="2" />
    <Item6 Ref="280" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;GenericDefault" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v22.1" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>