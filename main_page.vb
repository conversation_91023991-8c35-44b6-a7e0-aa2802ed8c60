﻿Imports System.Management
Imports DevExpress.XtraBars
Imports DevExpress.XtraTabbedMdi
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Data.SqlClient

Public Class main_page
    Private Sub TileItem1_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem1.ItemClick
        If XtraForm1.m39.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_show = New item_show()
        f.Text = "فهرس الاصناف "
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem2_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem2.ItemClick
        If XtraForm1.m8.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_show = New customer_show()
        f.Text = "فهرس العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem3_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem3.ItemClick
        If XtraForm1.m26.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As importer_show = New importer_show()
        f.Text = "فهرس الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem7_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem7.ItemClick
        If XtraForm1.m68.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_add = New invoice_add()
        f.Text = "فاتورة مبيعات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem8_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem8.ItemClick
        If XtraForm1.m60.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        Dim f As Purchases_add = New Purchases_add()
        f.Text = "  فاتورة مشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem6_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem6.ItemClick
        If XtraForm1.m78.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_add = New Expenses_add()
        f.Text = "المصروفان"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem12_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem12.ItemClick
        If XtraForm1.m16.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_customer = New cashing_customer()
        f.Text = "تحصيل الفواتير"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Focus()
    End Sub

    Private Sub TileItem13_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem13.ItemClick
        If XtraForm1.m29.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_importer = New cashing_importer()
        f.Text = "سداد الفواتير"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Focus()
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles Button5.Click
        help_system.Show()
    End Sub
    Private Sub TileItem9_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem9.ItemClick
        If XtraForm1.m69.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        item_bac.ShowDialog()
    End Sub

    Private Sub TileItem5_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem5.ItemClick
        If XtraForm1.m77.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Treasury = New Treasury()
        f.Text = "الخزينة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem4_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem4.ItemClick
        Dim f As Employee_show = New Employee_show()
        f.Text = "فهري الموظفين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem10_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem10.ItemClick
        If XtraForm1.m61.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_back = New Purchases_back()
        f.Text = "  مرتجع مشتريات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem11_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem11.ItemClick
        Dim f As suplier_add = New suplier_add()
        f.Text = "عرض أسعار"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem16_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem16.ItemClick
        Attending_leaving.ShowDialog()
    End Sub

    Private Sub TileItem15_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem15.ItemClick
        If XtraForm1.m48.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As trans_item = New trans_item()
        f.Text = "  تحويلات مخازن "
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub Label8_Click(sender As Object, e As EventArgs) Handles Label8.Click

    End Sub

    Private Sub TileControl1_Click(sender As Object, e As EventArgs) Handles TileControl1.Click

    End Sub

    Private Sub TileItemCustomerStats_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItemCustomerStats.ItemClick
        If XtraForm1.m15.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        'Try
        '    Dim f As New CustomerStatisticsDashboard()
        '    f.Text = "لوحة إحصائيات العملاء"
        '    f.MdiParent = XtraForm1
        '    f.Show()
        'Catch ex As Exception
        '    XtraMessageBox.Show("خطأ في فتح لوحة الإحصائيات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        'End Try
    End Sub

    Private Sub TileItemCostCenters_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItemCostCenters.ItemClick
        If XtraForm1.m1.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        'Try
        '    Dim f As New CostCentersManagement()
        '    f.Text = "إدارة مراكز التكلفة"
        '    f.MdiParent = XtraForm1
        '    f.Show()
        'Catch ex As Exception
        '    XtraMessageBox.Show("خطأ في فتح شاشة مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        'End Try
    End Sub

End Class