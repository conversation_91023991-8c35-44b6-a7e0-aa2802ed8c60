﻿Imports System.Management
Imports DevExpress.XtraBars
Imports DevExpress.XtraTabbedMdi
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Data.SqlClient

Public Class main_page
    Private Sub TileItem1_ItemClick(sender As Object, e As TileItemEventArgs)
        If XtraForm1.m39.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_show = New item_show()
        f.Text = "قائمة السلع "
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem2_ItemClick(sender As Object, e As TileItemEventArgs)
        If XtraForm1.m8.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_show = New customer_show()
        f.Text = "قائمة العملاء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem3_ItemClick(sender As Object, e As TileItemEventArgs)
        If XtraForm1.m26.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As importer_show = New importer_show()
        f.Text = "فهرس الموردين"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem7_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem7.ItemClick
        If XtraForm1.m68.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_add = New invoice_add()
        f.Text = "فاتورة بيع"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem8_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem8.ItemClick
        If XtraForm1.m60.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        Dim f As Purchases_add = New Purchases_add()
        f.Text = "  فاتورة شراء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem6_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem6.ItemClick
        If XtraForm1.m78.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_add = New Expenses_add()
        f.Text = "المصاريف"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem12_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem12.ItemClick
        If XtraForm1.m16.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_customer = New cashing_customer()
        f.Text = "تحصيل الفواتير"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Focus()
    End Sub

    Private Sub TileItem13_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem13.ItemClick
        If XtraForm1.m29.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_importer = New cashing_importer()
        f.Text = "سداد الفواتير"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Focus()
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles Button5.Click
        help_system.Show()
    End Sub
    Private Sub TileItem9_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem9.ItemClick
        If XtraForm1.m69.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        item_bac.ShowDialog()
    End Sub

    Private Sub TileItem5_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem5.ItemClick
        If XtraForm1.m77.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Treasury = New Treasury()
        f.Text = "الخزينة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem4_ItemClick(sender As Object, e As TileItemEventArgs)
        Dim f As Employee_show = New Employee_show()
        f.Text = "قائمة الخدامة"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem10_ItemClick(sender As Object, e As TileItemEventArgs) Handles TileItem10.ItemClick
        If XtraForm1.m61.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_back = New Purchases_back()
        f.Text = "  مرتجع الشراء"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem11_ItemClick(sender As Object, e As TileItemEventArgs)
        Dim f As suplier_add = New suplier_add()
        f.Text = "عرض الأسعار"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub TileItem16_ItemClick(sender As Object, e As TileItemEventArgs)
        Attending_leaving.ShowDialog()
    End Sub

    Private Sub TileItem15_ItemClick(sender As Object, e As TileItemEventArgs)
        If XtraForm1.m48.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As trans_item = New trans_item()
        f.Text = "  تحويلات مخازن "
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub Label8_Click(sender As Object, e As EventArgs) Handles Label8.Click

    End Sub

    Private Sub TileControl1_Click(sender As Object, e As EventArgs) Handles TileControl1.Click

    End Sub

    Private Sub TileItemCustomerStats_ItemClick(sender As Object, e As TileItemEventArgs)
        If XtraForm1.m15.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        'Try
        '    Dim f As New CustomerStatisticsDashboard()
        '    f.Text = "لوحة إحصائيات العملاء"
        '    f.MdiParent = XtraForm1
        '    f.Show()
        'Catch ex As Exception
        '    XtraMessageBox.Show("خطأ في فتح لوحة الإحصائيات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        'End Try
    End Sub

    Private Sub TileItemCostCenters_ItemClick(sender As Object, e As TileItemEventArgs)
        If XtraForm1.m1.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        Try
            Dim f As New CostCentersManagement()
            f.Text = "إدارة مراكز التكلفة"
            f.MdiParent = XtraForm1
            f.Show()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في فتح شاشة مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' فتح تقارير مراكز التكلفة المتقدمة
    Public Sub OpenAdvancedCostCenterReports()
        Try
            ' التحقق من وجود مراكز التكلفة أولاً
            If Not CheckCostCentersExist() Then
                If XtraMessageBox.Show("لا توجد مراكز تكلفة. هل تريد إنشاء مراكز تكلفة أولاً؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    OpenCostCentersManagement()
                End If
                Return
            End If

            ' فتح نافذة التقارير المبسطة
            ShowCostCenterReports()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في فتح تقارير مراكز التكلفة المتقدمة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CheckCostCentersExist() As Boolean
        Try
            Dim sql As String = "SELECT COUNT(*) FROM cost_centers WHERE is_active = 1"
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                Return count > 0
            End Using
        Catch
            Return False
        End Try
    End Function

    ' فتح إدارة مراكز التكلفة
    Public Sub OpenCostCentersManagement()
        Try
            Dim f As New CostCentersManagement()
            f.Text = "إدارة مراكز التكلفة"
            f.MdiParent = XtraForm1
            f.Show()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في فتح إدارة مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' عرض تقارير مراكز التكلفة
    Private Sub ShowCostCenterReports()
        Try
            ' إنشاء نموذج بسيط للتقارير
            Dim reportsForm As New XtraForm()
            reportsForm.Text = "تقارير مراكز التكلفة المتقدمة"
            reportsForm.Size = New Size(1200, 700)
            reportsForm.StartPosition = FormStartPosition.CenterScreen
            reportsForm.RightToLeft = RightToLeft.Yes
            reportsForm.WindowState = FormWindowState.Maximized
            reportsForm.MdiParent = XtraForm1

            ' إنشاء التبويبات
            Dim tabControl As New DevExpress.XtraTab.XtraTabControl()
            tabControl.Dock = DockStyle.Fill

            ' تبويب تحليل التكاليف
            Dim costAnalysisTab As New DevExpress.XtraTab.XtraTabPage()
            costAnalysisTab.Text = "📊 تحليل التكاليف"
            CreateCostAnalysisReport(costAnalysisTab)

            ' تبويب مقارنة الموازنة
            Dim budgetTab As New DevExpress.XtraTab.XtraTabPage()
            budgetTab.Text = "📈 مقارنة الموازنة"
            CreateBudgetReport(budgetTab)

            ' تبويب الأرباح والخسائر
            Dim profitTab As New DevExpress.XtraTab.XtraTabPage()
            profitTab.Text = "📉 الأرباح والخسائر"
            CreateProfitLossReport(profitTab)

            ' تبويب توزيع المصروفات
            Dim distributionTab As New DevExpress.XtraTab.XtraTabPage()
            distributionTab.Text = "📋 توزيع المصروفات"
            CreateDistributionReport(distributionTab)

            tabControl.TabPages.Add(costAnalysisTab)
            tabControl.TabPages.Add(budgetTab)
            tabControl.TabPages.Add(profitTab)
            tabControl.TabPages.Add(distributionTab)

            reportsForm.Controls.Add(tabControl)
            reportsForm.Show()

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض التقارير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' فتح شاشة اختبار تقارير مراكز التكلفة
    Public Sub OpenTestCostCenterReports()
        Try
            ShowSimpleTestForm()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في فتح شاشة الاختبار: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إنشاء تقرير تحليل التكاليف
    Private Sub CreateCostAnalysisReport(tab As DevExpress.XtraTab.XtraTabPage)
        Try
            Dim grid As New DevExpress.XtraGrid.GridControl()
            Dim view As New DevExpress.XtraGrid.Views.Grid.GridView()
            grid.MainView = view
            grid.Dock = DockStyle.Fill

            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    COUNT(DISTINCT ea.Expenses_id) AS 'عدد المصروفات',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'إجمالي المصروفات',
                    CASE
                        WHEN COUNT(DISTINCT ea.Expenses_id) = 0 THEN 0
                        ELSE ISNULL(SUM(ea.total_Expenses), 0) / COUNT(DISTINCT ea.Expenses_id)
                    END AS 'متوسط المصروف',
                    cc.description AS 'الوصف'
                FROM cost_centers cc
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
                WHERE cc.is_active = 1
                GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code, cc.description
                ORDER BY 'إجمالي المصروفات' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostAnalysis")
                grid.DataSource = ds.Tables("CostAnalysis")
            End Using

            ConfigureGridView(view)
            tab.Controls.Add(grid)

        Catch ex As Exception
            Dim lbl As New Label()
            lbl.Text = "خطأ في تحميل تقرير تحليل التكاليف: " & ex.Message
            lbl.Dock = DockStyle.Fill
            lbl.TextAlign = ContentAlignment.MiddleCenter
            tab.Controls.Add(lbl)
        End Try
    End Sub

    ' إنشاء تقرير مقارنة الموازنة
    Private Sub CreateBudgetReport(tab As DevExpress.XtraTab.XtraTabPage)
        Try
            ' إنشاء جدول الموازنة إذا لم يكن موجوداً
            CreateBudgetTableIfNotExists()

            Dim grid As New DevExpress.XtraGrid.GridControl()
            Dim view As New DevExpress.XtraGrid.Views.Grid.GridView()
            grid.MainView = view
            grid.Dock = DockStyle.Fill

            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    ISNULL(b.budget_amount, 0) AS 'الموازنة المخططة',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'المصروفات الفعلية',
                    ISNULL(b.budget_amount, 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'الفرق',
                    CASE
                        WHEN ISNULL(b.budget_amount, 0) = 0 THEN 0
                        ELSE (ISNULL(SUM(ea.total_Expenses), 0) / ISNULL(b.budget_amount, 0)) * 100
                    END AS 'نسبة الاستخدام %',
                    CASE
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) > ISNULL(b.budget_amount, 0) THEN 'تجاوز الموازنة'
                        WHEN ISNULL(SUM(ea.total_Expenses), 0) = ISNULL(b.budget_amount, 0) THEN 'مطابق للموازنة'
                        ELSE 'أقل من الموازنة'
                    END AS 'الحالة'
                FROM cost_centers cc
                LEFT JOIN cost_center_budget b ON cc.cost_center_id = b.cost_center_id
                    AND b.budget_year = YEAR(GETDATE())
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
                WHERE cc.is_active = 1
                GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code, b.budget_amount
                ORDER BY 'نسبة الاستخدام %' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "BudgetComparison")
                grid.DataSource = ds.Tables("BudgetComparison")
            End Using

            ConfigureGridView(view)
            tab.Controls.Add(grid)

        Catch ex As Exception
            Dim lbl As New Label()
            lbl.Text = "خطأ في تحميل تقرير مقارنة الموازنة: " & ex.Message
            lbl.Dock = DockStyle.Fill
            lbl.TextAlign = ContentAlignment.MiddleCenter
            tab.Controls.Add(lbl)
        End Try
    End Sub

    ' إنشاء تقرير الأرباح والخسائر
    Private Sub CreateProfitLossReport(tab As DevExpress.XtraTab.XtraTabPage)
        Try
            Dim grid As New DevExpress.XtraGrid.GridControl()
            Dim view As New DevExpress.XtraGrid.Views.Grid.GridView()
            grid.MainView = view
            grid.Dock = DockStyle.Fill

            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    ISNULL(SUM(ia.total_invoice), 0) AS 'إجمالي الإيرادات',
                    ISNULL(SUM(ea.total_Expenses), 0) AS 'إجمالي المصروفات',
                    ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) AS 'صافي النتيجة',
                    CASE
                        WHEN ISNULL(SUM(ia.total_invoice), 0) = 0 THEN 0
                        ELSE ((ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0)) / ISNULL(SUM(ia.total_invoice), 0)) * 100
                    END AS 'هامش الربح %',
                    CASE
                        WHEN ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) > 0 THEN 'ربح'
                        WHEN ISNULL(SUM(ia.total_invoice), 0) - ISNULL(SUM(ea.total_Expenses), 0) = 0 THEN 'تعادل'
                        ELSE 'خسارة'
                    END AS 'النتيجة'
                FROM cost_centers cc
                LEFT JOIN invoice_add ia ON cc.cost_center_id = ia.cost_center_id
                    AND ia.invoice_date BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
                LEFT JOIN Expenses_add ea ON cc.cost_center_id = ea.cost_center_id
                    AND ea.Expenses_date BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
                WHERE cc.is_active = 1
                GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code
                ORDER BY 'صافي النتيجة' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "ProfitLoss")
                grid.DataSource = ds.Tables("ProfitLoss")
            End Using

            ConfigureGridView(view)
            tab.Controls.Add(grid)

        Catch ex As Exception
            Dim lbl As New Label()
            lbl.Text = "خطأ في تحميل تقرير الأرباح والخسائر: " & ex.Message
            lbl.Dock = DockStyle.Fill
            lbl.TextAlign = ContentAlignment.MiddleCenter
            tab.Controls.Add(lbl)
        End Try
    End Sub

    ' إنشاء تقرير توزيع المصروفات
    Private Sub CreateDistributionReport(tab As DevExpress.XtraTab.XtraTabPage)
        Try
            Dim grid As New DevExpress.XtraGrid.GridControl()
            Dim view As New DevExpress.XtraGrid.Views.Grid.GridView()
            grid.MainView = view
            grid.Dock = DockStyle.Fill

            Dim sql As String = "
                SELECT
                    cc.cost_center_name AS 'مركز التكلفة',
                    cc.cost_center_code AS 'الكود',
                    COUNT(*) AS 'عدد العمليات',
                    SUM(ea.total_Expenses) AS 'إجمالي المبلغ',
                    AVG(ea.total_Expenses) AS 'متوسط المبلغ',
                    (SUM(ea.total_Expenses) * 100.0 / (SELECT SUM(total_Expenses) FROM Expenses_add WHERE Expenses_date BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE())) AS 'النسبة من الإجمالي %'
                FROM Expenses_add ea
                INNER JOIN cost_centers cc ON ea.cost_center_id = cc.cost_center_id
                WHERE ea.Expenses_date BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
                AND cc.is_active = 1
                GROUP BY cc.cost_center_id, cc.cost_center_name, cc.cost_center_code
                ORDER BY 'إجمالي المبلغ' DESC"

            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "ExpenseDistribution")
                grid.DataSource = ds.Tables("ExpenseDistribution")
            End Using

            ConfigureGridView(view)
            tab.Controls.Add(grid)

        Catch ex As Exception
            Dim lbl As New Label()
            lbl.Text = "خطأ في تحميل تقرير توزيع المصروفات: " & ex.Message
            lbl.Dock = DockStyle.Fill
            lbl.TextAlign = ContentAlignment.MiddleCenter
            tab.Controls.Add(lbl)
        End Try
    End Sub

    ' تكوين عرض الجدول
    Private Sub ConfigureGridView(view As DevExpress.XtraGrid.Views.Grid.GridView)
        With view
            .OptionsView.ShowFooter = True
            .OptionsView.ShowGroupPanel = False
            .BestFitColumns()

            ' تنسيق الأعمدة الرقمية
            For Each column As DevExpress.XtraGrid.Columns.GridColumn In .Columns
                If column.FieldName.Contains("إجمالي") OrElse column.FieldName.Contains("متوسط") OrElse column.FieldName.Contains("الموازنة") OrElse column.FieldName.Contains("المبلغ") Then
                    column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    column.DisplayFormat.FormatString = "N2"
                    column.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
                ElseIf column.FieldName.Contains("نسبة") OrElse column.FieldName.Contains("%") Then
                    column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    column.DisplayFormat.FormatString = "N1"
                End If
            Next
        End With
    End Sub

    ' إنشاء جدول الموازنة إذا لم يكن موجوداً
    Private Sub CreateBudgetTableIfNotExists()
        Try
            Dim sql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_center_budget' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_center_budget (
                        budget_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_id INT NOT NULL,
                        budget_year INT NOT NULL,
                        budget_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
                        created_date DATETIME DEFAULT GETDATE()
                    );

                    -- إدراج بيانات تجريبية للموازنة
                    INSERT INTO cost_center_budget (cost_center_id, budget_year, budget_amount)
                    SELECT cost_center_id, YEAR(GETDATE()),
                        CASE cost_center_id
                            WHEN 1 THEN 100000
                            WHEN 2 THEN 75000
                            WHEN 3 THEN 80000
                            WHEN 4 THEN 50000
                            WHEN 5 THEN 120000
                            ELSE 60000
                        END
                    FROM cost_centers WHERE is_active = 1;
                END"

            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' عرض نموذج الاختبار
    Private Sub ShowTestForm()
        Try
            Dim testForm As New XtraForm()
            testForm.Text = "اختبار تقارير مراكز التكلفة"
            testForm.Size = New Size(700, 500)
            testForm.StartPosition = FormStartPosition.CenterScreen
            testForm.RightToLeft = RightToLeft.Yes
            testForm.MdiParent = XtraForm1

            ' زر اختبار قاعدة البيانات
            Dim btnTest As New DevExpress.XtraEditors.SimpleButton()
            btnTest.Text = "اختبار قاعدة البيانات"
            btnTest.Location = New Point(20, 20)
            btnTest.Size = New Size(150, 30)
            AddHandler btnTest.Click, Sub() TestDatabase(testForm)
            testForm.Controls.Add(btnTest)

            ' زر إنشاء بيانات تجريبية
            Dim btnCreate As New DevExpress.XtraEditors.SimpleButton()
            btnCreate.Text = "إنشاء بيانات تجريبية"
            btnCreate.Location = New Point(190, 20)
            btnCreate.Size = New Size(150, 30)
            AddHandler btnCreate.Click, Sub() CreateSampleData(testForm)
            testForm.Controls.Add(btnCreate)

            ' زر فتح التقارير
            Dim btnReports As New DevExpress.XtraEditors.SimpleButton()
            btnReports.Text = "فتح التقارير المتقدمة"
            btnReports.Location = New Point(360, 20)
            btnReports.Size = New Size(150, 30)
            AddHandler btnReports.Click, Sub() OpenAdvancedCostCenterReports()
            testForm.Controls.Add(btnReports)

            ' منطقة النتائج
            Dim memo As New DevExpress.XtraEditors.MemoEdit()
            memo.Location = New Point(20, 70)
            memo.Size = New Size(640, 400)
            memo.Properties.ReadOnly = True
            memo.Properties.ScrollBars = ScrollBars.Both
            memo.Text = "مرحباً بك في اختبار تقارير مراكز التكلفة!" & vbCrLf & vbCrLf & "الخطوات:" & vbCrLf & "1. اضغط 'اختبار قاعدة البيانات' للتحقق من الجداول" & vbCrLf & "2. اضغط 'إنشاء بيانات تجريبية' إذا لم توجد بيانات" & vbCrLf & "3. اضغط 'فتح التقارير المتقدمة' لعرض التقارير"
            testForm.Controls.Add(memo)

            testForm.Show()

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض نموذج الاختبار: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' اختبار قاعدة البيانات
    Private Sub TestDatabase(parentForm As Form)
        Try
            Dim memo As DevExpress.XtraEditors.MemoEdit = Nothing
            For Each ctrl As Control In parentForm.Controls
                If TypeOf ctrl Is DevExpress.XtraEditors.MemoEdit Then
                    memo = DirectCast(ctrl, DevExpress.XtraEditors.MemoEdit)
                    Exit For
                End If
            Next

            If memo IsNot Nothing Then
                memo.Text = "🔍 جاري اختبار قاعدة البيانات..." & vbCrLf & vbCrLf

                ' اختبار جدول مراكز التكلفة
                Dim costCentersCount As Integer = GetTableCount("cost_centers")
                memo.Text += "📋 مراكز التكلفة: " & costCentersCount.ToString() & " مركز" & vbCrLf

                ' اختبار جدول الفواتير
                Dim invoicesCount As Integer = GetTableCount("invoice_add")
                memo.Text += "🧾 فواتير المبيعات: " & invoicesCount.ToString() & " فاتورة" & vbCrLf

                ' اختبار جدول المصروفات
                Dim expensesCount As Integer = GetTableCount("Expenses_add")
                memo.Text += "💰 المصروفات: " & expensesCount.ToString() & " مصروف" & vbCrLf

                ' اختبار جدول الموازنة
                Dim budgetCount As Integer = GetTableCount("cost_center_budget")
                memo.Text += "📊 بنود الموازنة: " & budgetCount.ToString() & " بند" & vbCrLf & vbCrLf

                If costCentersCount = 0 Then
                    memo.Text += "⚠️ تحذير: لا توجد مراكز تكلفة. استخدم زر 'إنشاء بيانات تجريبية'" & vbCrLf
                Else
                    memo.Text += "✅ قاعدة البيانات جاهزة للاستخدام!" & vbCrLf
                    memo.Text += "📊 يمكنك الآن فتح التقارير المتقدمة" & vbCrLf
                End If
            End If

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في اختبار قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إنشاء بيانات تجريبية
    Private Sub CreateSampleData(parentForm As Form)
        Try
            Dim memo As DevExpress.XtraEditors.MemoEdit = Nothing
            For Each ctrl As Control In parentForm.Controls
                If TypeOf ctrl Is DevExpress.XtraEditors.MemoEdit Then
                    memo = DirectCast(ctrl, DevExpress.XtraEditors.MemoEdit)
                    Exit For
                End If
            Next

            If memo IsNot Nothing Then
                memo.Text = "🚀 جاري إنشاء البيانات التجريبية..." & vbCrLf & vbCrLf

                ' إنشاء مراكز التكلفة
                CreateSampleCostCenters(memo)

                ' إنشاء بيانات الموازنة
                CreateBudgetTableIfNotExists()

                ' تحديث الجداول الموجودة
                UpdateExistingTables(memo)

                memo.Text += vbCrLf & "✅ تم إنشاء البيانات التجريبية بنجاح!" & vbCrLf
                memo.Text += "📊 يمكنك الآن اختبار التقارير المتقدمة" & vbCrLf
            End If

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في إنشاء البيانات التجريبية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إنشاء مراكز التكلفة التجريبية
    Private Sub CreateSampleCostCenters(memo As DevExpress.XtraEditors.MemoEdit)
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            Dim createTableSql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_centers (
                        cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_code NVARCHAR(20) NOT NULL,
                        cost_center_name NVARCHAR(100) NOT NULL,
                        description NVARCHAR(255),
                        is_active BIT DEFAULT 1,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                END"

            Using cmd As New SqlCommand(createTableSql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using

            ' إدراج مراكز تكلفة تجريبية
            Dim insertSql As String = "
                IF NOT EXISTS (SELECT * FROM cost_centers WHERE cost_center_code = 'CC001')
                BEGIN
                    INSERT INTO cost_centers (cost_center_code, cost_center_name, description) VALUES
                    ('CC001', 'فرع الرئيسي', 'المكتب الرئيسي للشركة'),
                    ('CC002', 'فرع الشمال', 'فرع المنطقة الشمالية'),
                    ('CC003', 'فرع الجنوب', 'فرع المنطقة الجنوبية'),
                    ('CC004', 'قسم التسويق', 'قسم التسويق والمبيعات'),
                    ('CC005', 'قسم الإنتاج', 'قسم الإنتاج والتصنيع');
                END"

            Using cmd As New SqlCommand(insertSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memo.Text += "✅ تم إنشاء مراكز التكلفة التجريبية" & vbCrLf

        Catch ex As Exception
            memo.Text += "❌ خطأ في إنشاء مراكز التكلفة: " & ex.Message & vbCrLf
        End Try
    End Sub

    ' تحديث الجداول الموجودة
    Private Sub UpdateExistingTables(memo As DevExpress.XtraEditors.MemoEdit)
        Try
            ' إضافة عمود cost_center_id للفواتير إذا لم يكن موجوداً
            Dim updateInvoicesSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                              WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE invoice_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(updateInvoicesSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            ' إضافة عمود cost_center_id للمصروفات إذا لم يكن موجوداً
            Dim updateExpensesSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                              WHERE TABLE_NAME = 'Expenses_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE Expenses_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(updateExpensesSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memo.Text += "✅ تم تحديث الجداول الموجودة" & vbCrLf

        Catch ex As Exception
            memo.Text += "⚠️ تحذير في تحديث الجداول: " & ex.Message & vbCrLf
        End Try
    End Sub

    ' الحصول على عدد السجلات في جدول
    Private Function GetTableCount(tableName As String) As Integer
        Try
            Dim sql As String = $"SELECT COUNT(*) FROM {tableName}"
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                Return Convert.ToInt32(cmd.ExecuteScalar())
            End Using
        Catch
            Return 0
        End Try
    End Function

    ' عرض نموذج اختبار مبسط
    Private Sub ShowSimpleTestForm()
        Try
            Dim testForm As New XtraForm()
            testForm.Text = "اختبار تقارير مراكز التكلفة"
            testForm.Size = New Size(600, 400)
            testForm.StartPosition = FormStartPosition.CenterScreen
            testForm.RightToLeft = RightToLeft.Yes
            testForm.MdiParent = XtraForm1

            ' زر اختبار قاعدة البيانات
            Dim btnTest As New DevExpress.XtraEditors.SimpleButton()
            btnTest.Text = "اختبار قاعدة البيانات"
            btnTest.Location = New Point(20, 20)
            btnTest.Size = New Size(150, 30)
            AddHandler btnTest.Click, Sub() TestDatabaseSimple()
            testForm.Controls.Add(btnTest)

            ' زر إنشاء بيانات تجريبية
            Dim btnCreate As New DevExpress.XtraEditors.SimpleButton()
            btnCreate.Text = "إنشاء بيانات تجريبية"
            btnCreate.Location = New Point(190, 20)
            btnCreate.Size = New Size(150, 30)
            AddHandler btnCreate.Click, Sub() CreateSampleDataSimple()
            testForm.Controls.Add(btnCreate)

            ' زر فتح التقارير
            Dim btnReports As New DevExpress.XtraEditors.SimpleButton()
            btnReports.Text = "فتح التقارير المتقدمة"
            btnReports.Location = New Point(360, 20)
            btnReports.Size = New Size(150, 30)
            AddHandler btnReports.Click, Sub() OpenAdvancedCostCenterReports()
            testForm.Controls.Add(btnReports)

            ' تسمية توضيحية
            Dim lblInstructions As New Label()
            lblInstructions.Text = "مرحباً بك في اختبار تقارير مراكز التكلفة!" & vbCrLf & vbCrLf &
                                  "الخطوات:" & vbCrLf &
                                  "1. اضغط 'اختبار قاعدة البيانات' للتحقق من الجداول" & vbCrLf &
                                  "2. اضغط 'إنشاء بيانات تجريبية' إذا لم توجد بيانات" & vbCrLf &
                                  "3. اضغط 'فتح التقارير المتقدمة' لعرض التقارير"
            lblInstructions.Location = New Point(20, 70)
            lblInstructions.Size = New Size(540, 300)
            lblInstructions.Font = New Font("Tahoma", 10)
            testForm.Controls.Add(lblInstructions)

            testForm.Show()

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض نموذج الاختبار: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' اختبار قاعدة البيانات مبسط
    Private Sub TestDatabaseSimple()
        Try
            Dim costCentersCount As Integer = GetTableCount("cost_centers")
            Dim invoicesCount As Integer = GetTableCount("invoice_add")
            Dim expensesCount As Integer = GetTableCount("Expenses_add")
            Dim budgetCount As Integer = GetTableCount("cost_center_budget")

            Dim message As String = "نتائج اختبار قاعدة البيانات:" & vbCrLf & vbCrLf &
                                   "📋 مراكز التكلفة: " & costCentersCount.ToString() & " مركز" & vbCrLf &
                                   "🧾 فواتير المبيعات: " & invoicesCount.ToString() & " فاتورة" & vbCrLf &
                                   "💰 المصروفات: " & expensesCount.ToString() & " مصروف" & vbCrLf &
                                   "📊 بنود الموازنة: " & budgetCount.ToString() & " بند" & vbCrLf & vbCrLf

            If costCentersCount = 0 Then
                message += "⚠️ تحذير: لا توجد مراكز تكلفة. استخدم زر 'إنشاء بيانات تجريبية'"
            Else
                message += "✅ قاعدة البيانات جاهزة للاستخدام!"
            End If

            XtraMessageBox.Show(message, "نتائج الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في اختبار قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إنشاء بيانات تجريبية مبسط
    Private Sub CreateSampleDataSimple()
        Try
            ' إنشاء مراكز التكلفة
            CreateSampleCostCentersSimple()

            ' إنشاء بيانات الموازنة
            CreateBudgetTableIfNotExists()

            ' تحديث الجداول الموجودة
            UpdateExistingTablesSimple()

            XtraMessageBox.Show("✅ تم إنشاء البيانات التجريبية بنجاح!" & vbCrLf &
                               "📊 يمكنك الآن اختبار التقارير المتقدمة",
                               "نجح الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في إنشاء البيانات التجريبية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إنشاء مراكز التكلفة التجريبية مبسط
    Private Sub CreateSampleCostCentersSimple()
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            Dim createTableSql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_centers (
                        cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_code NVARCHAR(20) NOT NULL,
                        cost_center_name NVARCHAR(100) NOT NULL,
                        description NVARCHAR(255),
                        is_active BIT DEFAULT 1,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                END"

            Using cmd As New SqlCommand(createTableSql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using

            ' إدراج مراكز تكلفة تجريبية
            Dim insertSql As String = "
                IF NOT EXISTS (SELECT * FROM cost_centers WHERE cost_center_code = 'CC001')
                BEGIN
                    INSERT INTO cost_centers (cost_center_code, cost_center_name, description) VALUES
                    ('CC001', 'فرع الرئيسي', 'المكتب الرئيسي للشركة'),
                    ('CC002', 'فرع الشمال', 'فرع المنطقة الشمالية'),
                    ('CC003', 'فرع الجنوب', 'فرع المنطقة الجنوبية'),
                    ('CC004', 'قسم التسويق', 'قسم التسويق والمبيعات'),
                    ('CC005', 'قسم الإنتاج', 'قسم الإنتاج والتصنيع');
                END"

            Using cmd As New SqlCommand(insertSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء مراكز التكلفة: " & ex.Message)
        End Try
    End Sub

    ' تحديث الجداول الموجودة مبسط
    Private Sub UpdateExistingTablesSimple()
        Try
            ' إضافة عمود cost_center_id للفواتير إذا لم يكن موجوداً
            Dim updateInvoicesSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                              WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE invoice_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(updateInvoicesSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            ' إضافة عمود cost_center_id للمصروفات إذا لم يكن موجوداً
            Dim updateExpensesSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                              WHERE TABLE_NAME = 'Expenses_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE Expenses_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(updateExpensesSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            ' تجاهل أخطاء تحديث الجداول
        End Try
    End Sub

End Class