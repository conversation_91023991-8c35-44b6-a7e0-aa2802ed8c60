<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CustomerStatisticsDashboard
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(CustomerStatisticsDashboard))
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit1 = New DevExpress.XtraEditors.PictureEdit()
        Me.RefreshButton = New DevExpress.XtraEditors.SimpleButton()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.StatsPanel1 = New DevExpress.XtraEditors.PanelControl()
        Me.TotalCustomersLabel = New DevExpress.XtraEditors.LabelControl()
        Me.TotalCustomersValue = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit2 = New DevExpress.XtraEditors.PictureEdit()
        Me.StatsPanel2 = New DevExpress.XtraEditors.PanelControl()
        Me.TotalSalesLabel = New DevExpress.XtraEditors.LabelControl()
        Me.TotalSalesValue = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit3 = New DevExpress.XtraEditors.PictureEdit()
        Me.StatsPanel3 = New DevExpress.XtraEditors.PanelControl()
        Me.TotalBalanceLabel = New DevExpress.XtraEditors.LabelControl()
        Me.TotalBalanceValue = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit4 = New DevExpress.XtraEditors.PictureEdit()
        Me.StatsPanel4 = New DevExpress.XtraEditors.PanelControl()
        Me.ActiveCustomersLabel = New DevExpress.XtraEditors.LabelControl()
        Me.ActiveCustomersValue = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit5 = New DevExpress.XtraEditors.PictureEdit()
        Me.ChartPanel = New DevExpress.XtraEditors.PanelControl()
        Me.SalesChart = New DevExpress.XtraCharts.ChartControl()
        Me.TopCustomersPanel = New DevExpress.XtraEditors.PanelControl()
        Me.TopCustomersGrid = New DevExpress.XtraGrid.GridControl()
        Me.TopCustomersView = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ColCustomerName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColTotalSales = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColBalance = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.StatsPanel1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.StatsPanel1.SuspendLayout()
        CType(Me.PictureEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StatsPanel2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.StatsPanel2.SuspendLayout()
        CType(Me.PictureEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StatsPanel3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.StatsPanel3.SuspendLayout()
        CType(Me.PictureEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.StatsPanel4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.StatsPanel4.SuspendLayout()
        CType(Me.PictureEdit5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ChartPanel, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ChartPanel.SuspendLayout()
        CType(Me.SalesChart, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TopCustomersPanel, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TopCustomersPanel.SuspendLayout()
        CType(Me.TopCustomersGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TopCustomersView, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelControl1
        '
        Me.PanelControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.PanelControl1.Appearance.Options.UseBackColor = True
        Me.PanelControl1.Controls.Add(Me.RefreshButton)
        Me.PanelControl1.Controls.Add(Me.LabelControl1)
        Me.PanelControl1.Controls.Add(Me.PictureEdit1)
        Me.PanelControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelControl1.Location = New System.Drawing.Point(0, 0)
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(1200, 80)
        Me.PanelControl1.TabIndex = 0
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Segoe UI", 18.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseForeColor = True
        Me.LabelControl1.Location = New System.Drawing.Point(80, 25)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(180, 32)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "إحصائيات العملاء"
        '
        'PictureEdit1
        '
        Me.PictureEdit1.Location = New System.Drawing.Point(20, 20)
        Me.PictureEdit1.Name = "PictureEdit1"
        Me.PictureEdit1.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit1.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit1.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit1.TabIndex = 0
        '
        'RefreshButton
        '
        Me.RefreshButton.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RefreshButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.RefreshButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.RefreshButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.RefreshButton.Appearance.Options.UseBackColor = True
        Me.RefreshButton.Appearance.Options.UseFont = True
        Me.RefreshButton.Appearance.Options.UseForeColor = True
        Me.RefreshButton.Location = New System.Drawing.Point(1100, 25)
        Me.RefreshButton.Name = "RefreshButton"
        Me.RefreshButton.Size = New System.Drawing.Size(80, 30)
        Me.RefreshButton.TabIndex = 2
        Me.RefreshButton.Text = "تحديث"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 4
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.StatsPanel1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.StatsPanel2, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.StatsPanel3, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.StatsPanel4, 3, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 80)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.Padding = New System.Windows.Forms.Padding(10)
        Me.TableLayoutPanel1.RowCount = 1
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1200, 140)
        Me.TableLayoutPanel1.TabIndex = 1
        '
        'StatsPanel1
        '
        Me.StatsPanel1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.StatsPanel1.Appearance.Options.UseBackColor = True
        Me.StatsPanel1.Controls.Add(Me.PictureEdit2)
        Me.StatsPanel1.Controls.Add(Me.TotalCustomersValue)
        Me.StatsPanel1.Controls.Add(Me.TotalCustomersLabel)
        Me.StatsPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.StatsPanel1.Location = New System.Drawing.Point(15, 15)
        Me.StatsPanel1.Margin = New System.Windows.Forms.Padding(5)
        Me.StatsPanel1.Name = "StatsPanel1"
        Me.StatsPanel1.Size = New System.Drawing.Size(285, 110)
        Me.StatsPanel1.TabIndex = 0
        '
        'TotalCustomersLabel
        '
        Me.TotalCustomersLabel.Appearance.Font = New System.Drawing.Font("Segoe UI", 12.0!, System.Drawing.FontStyle.Bold)
        Me.TotalCustomersLabel.Appearance.ForeColor = System.Drawing.Color.White
        Me.TotalCustomersLabel.Appearance.Options.UseFont = True
        Me.TotalCustomersLabel.Appearance.Options.UseForeColor = True
        Me.TotalCustomersLabel.Location = New System.Drawing.Point(20, 20)
        Me.TotalCustomersLabel.Name = "TotalCustomersLabel"
        Me.TotalCustomersLabel.Size = New System.Drawing.Size(88, 21)
        Me.TotalCustomersLabel.TabIndex = 0
        Me.TotalCustomersLabel.Text = "إجمالي العملاء"
        '
        'TotalCustomersValue
        '
        Me.TotalCustomersValue.Appearance.Font = New System.Drawing.Font("Segoe UI", 24.0!, System.Drawing.FontStyle.Bold)
        Me.TotalCustomersValue.Appearance.ForeColor = System.Drawing.Color.White
        Me.TotalCustomersValue.Appearance.Options.UseFont = True
        Me.TotalCustomersValue.Appearance.Options.UseForeColor = True
        Me.TotalCustomersValue.Location = New System.Drawing.Point(20, 50)
        Me.TotalCustomersValue.Name = "TotalCustomersValue"
        Me.TotalCustomersValue.Size = New System.Drawing.Size(18, 45)
        Me.TotalCustomersValue.TabIndex = 1
        Me.TotalCustomersValue.Text = "0"
        '
        'PictureEdit2
        '
        Me.PictureEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureEdit2.Location = New System.Drawing.Point(230, 20)
        Me.PictureEdit2.Name = "PictureEdit2"
        Me.PictureEdit2.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit2.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit2.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit2.TabIndex = 2
        '
        'StatsPanel2
        '
        Me.StatsPanel2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.StatsPanel2.Appearance.Options.UseBackColor = True
        Me.StatsPanel2.Controls.Add(Me.PictureEdit3)
        Me.StatsPanel2.Controls.Add(Me.TotalSalesValue)
        Me.StatsPanel2.Controls.Add(Me.TotalSalesLabel)
        Me.StatsPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.StatsPanel2.Location = New System.Drawing.Point(310, 15)
        Me.StatsPanel2.Margin = New System.Windows.Forms.Padding(5)
        Me.StatsPanel2.Name = "StatsPanel2"
        Me.StatsPanel2.Size = New System.Drawing.Size(285, 110)
        Me.StatsPanel2.TabIndex = 1
        '
        'TotalSalesLabel
        '
        Me.TotalSalesLabel.Appearance.Font = New System.Drawing.Font("Segoe UI", 12.0!, System.Drawing.FontStyle.Bold)
        Me.TotalSalesLabel.Appearance.ForeColor = System.Drawing.Color.White
        Me.TotalSalesLabel.Appearance.Options.UseFont = True
        Me.TotalSalesLabel.Appearance.Options.UseForeColor = True
        Me.TotalSalesLabel.Location = New System.Drawing.Point(20, 20)
        Me.TotalSalesLabel.Name = "TotalSalesLabel"
        Me.TotalSalesLabel.Size = New System.Drawing.Size(99, 21)
        Me.TotalSalesLabel.TabIndex = 0
        Me.TotalSalesLabel.Text = "إجمالي المبيعات"
        '
        'TotalSalesValue
        '
        Me.TotalSalesValue.Appearance.Font = New System.Drawing.Font("Segoe UI", 24.0!, System.Drawing.FontStyle.Bold)
        Me.TotalSalesValue.Appearance.ForeColor = System.Drawing.Color.White
        Me.TotalSalesValue.Appearance.Options.UseFont = True
        Me.TotalSalesValue.Appearance.Options.UseForeColor = True
        Me.TotalSalesValue.Location = New System.Drawing.Point(20, 50)
        Me.TotalSalesValue.Name = "TotalSalesValue"
        Me.TotalSalesValue.Size = New System.Drawing.Size(18, 45)
        Me.TotalSalesValue.TabIndex = 1
        Me.TotalSalesValue.Text = "0"
        '
        'PictureEdit3
        '
        Me.PictureEdit3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureEdit3.Location = New System.Drawing.Point(230, 20)
        Me.PictureEdit3.Name = "PictureEdit3"
        Me.PictureEdit3.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit3.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit3.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit3.TabIndex = 2
        '
        'StatsPanel3
        '
        Me.StatsPanel3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.StatsPanel3.Appearance.Options.UseBackColor = True
        Me.StatsPanel3.Controls.Add(Me.PictureEdit4)
        Me.StatsPanel3.Controls.Add(Me.TotalBalanceValue)
        Me.StatsPanel3.Controls.Add(Me.TotalBalanceLabel)
        Me.StatsPanel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.StatsPanel3.Location = New System.Drawing.Point(605, 15)
        Me.StatsPanel3.Margin = New System.Windows.Forms.Padding(5)
        Me.StatsPanel3.Name = "StatsPanel3"
        Me.StatsPanel3.Size = New System.Drawing.Size(285, 110)
        Me.StatsPanel3.TabIndex = 2
        '
        'TotalBalanceLabel
        '
        Me.TotalBalanceLabel.Appearance.Font = New System.Drawing.Font("Segoe UI", 12.0!, System.Drawing.FontStyle.Bold)
        Me.TotalBalanceLabel.Appearance.ForeColor = System.Drawing.Color.Black
        Me.TotalBalanceLabel.Appearance.Options.UseFont = True
        Me.TotalBalanceLabel.Appearance.Options.UseForeColor = True
        Me.TotalBalanceLabel.Location = New System.Drawing.Point(20, 20)
        Me.TotalBalanceLabel.Name = "TotalBalanceLabel"
        Me.TotalBalanceLabel.Size = New System.Drawing.Size(95, 21)
        Me.TotalBalanceLabel.TabIndex = 0
        Me.TotalBalanceLabel.Text = "إجمالي الأرصدة"
        '
        'TotalBalanceValue
        '
        Me.TotalBalanceValue.Appearance.Font = New System.Drawing.Font("Segoe UI", 24.0!, System.Drawing.FontStyle.Bold)
        Me.TotalBalanceValue.Appearance.ForeColor = System.Drawing.Color.Black
        Me.TotalBalanceValue.Appearance.Options.UseFont = True
        Me.TotalBalanceValue.Appearance.Options.UseForeColor = True
        Me.TotalBalanceValue.Location = New System.Drawing.Point(20, 50)
        Me.TotalBalanceValue.Name = "TotalBalanceValue"
        Me.TotalBalanceValue.Size = New System.Drawing.Size(18, 45)
        Me.TotalBalanceValue.TabIndex = 1
        Me.TotalBalanceValue.Text = "0"
        '
        'PictureEdit4
        '
        Me.PictureEdit4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureEdit4.Location = New System.Drawing.Point(230, 20)
        Me.PictureEdit4.Name = "PictureEdit4"
        Me.PictureEdit4.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit4.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit4.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit4.TabIndex = 2
        '
        'StatsPanel4
        '
        Me.StatsPanel4.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.StatsPanel4.Appearance.Options.UseBackColor = True
        Me.StatsPanel4.Controls.Add(Me.PictureEdit5)
        Me.StatsPanel4.Controls.Add(Me.ActiveCustomersValue)
        Me.StatsPanel4.Controls.Add(Me.ActiveCustomersLabel)
        Me.StatsPanel4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.StatsPanel4.Location = New System.Drawing.Point(900, 15)
        Me.StatsPanel4.Margin = New System.Windows.Forms.Padding(5)
        Me.StatsPanel4.Name = "StatsPanel4"
        Me.StatsPanel4.Size = New System.Drawing.Size(285, 110)
        Me.StatsPanel4.TabIndex = 3
        '
        'ActiveCustomersLabel
        '
        Me.ActiveCustomersLabel.Appearance.Font = New System.Drawing.Font("Segoe UI", 12.0!, System.Drawing.FontStyle.Bold)
        Me.ActiveCustomersLabel.Appearance.ForeColor = System.Drawing.Color.White
        Me.ActiveCustomersLabel.Appearance.Options.UseFont = True
        Me.ActiveCustomersLabel.Appearance.Options.UseForeColor = True
        Me.ActiveCustomersLabel.Location = New System.Drawing.Point(20, 20)
        Me.ActiveCustomersLabel.Name = "ActiveCustomersLabel"
        Me.ActiveCustomersLabel.Size = New System.Drawing.Size(103, 21)
        Me.ActiveCustomersLabel.TabIndex = 0
        Me.ActiveCustomersLabel.Text = "العملاء النشطين"
        '
        'ActiveCustomersValue
        '
        Me.ActiveCustomersValue.Appearance.Font = New System.Drawing.Font("Segoe UI", 24.0!, System.Drawing.FontStyle.Bold)
        Me.ActiveCustomersValue.Appearance.ForeColor = System.Drawing.Color.White
        Me.ActiveCustomersValue.Appearance.Options.UseFont = True
        Me.ActiveCustomersValue.Appearance.Options.UseForeColor = True
        Me.ActiveCustomersValue.Location = New System.Drawing.Point(20, 50)
        Me.ActiveCustomersValue.Name = "ActiveCustomersValue"
        Me.ActiveCustomersValue.Size = New System.Drawing.Size(18, 45)
        Me.ActiveCustomersValue.TabIndex = 1
        Me.ActiveCustomersValue.Text = "0"
        '
        'PictureEdit5
        '
        Me.PictureEdit5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureEdit5.Location = New System.Drawing.Point(230, 20)
        Me.PictureEdit5.Name = "PictureEdit5"
        Me.PictureEdit5.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit5.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit5.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit5.TabIndex = 2
        '
        'ChartPanel
        '
        Me.ChartPanel.Appearance.BackColor = System.Drawing.Color.White
        Me.ChartPanel.Appearance.Options.UseBackColor = True
        Me.ChartPanel.Controls.Add(Me.SalesChart)
        Me.ChartPanel.Location = New System.Drawing.Point(20, 240)
        Me.ChartPanel.Name = "ChartPanel"
        Me.ChartPanel.Size = New System.Drawing.Size(750, 350)
        Me.ChartPanel.TabIndex = 2
        '
        'SalesChart
        '
        Me.SalesChart.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SalesChart.Location = New System.Drawing.Point(2, 2)
        Me.SalesChart.Name = "SalesChart"
        Me.SalesChart.SeriesSerializable = New DevExpress.XtraCharts.Series(-1) {}
        Me.SalesChart.Size = New System.Drawing.Size(746, 346)
        Me.SalesChart.TabIndex = 0
        '
        'TopCustomersPanel
        '
        Me.TopCustomersPanel.Appearance.BackColor = System.Drawing.Color.White
        Me.TopCustomersPanel.Appearance.Options.UseBackColor = True
        Me.TopCustomersPanel.Controls.Add(Me.LabelControl2)
        Me.TopCustomersPanel.Controls.Add(Me.TopCustomersGrid)
        Me.TopCustomersPanel.Location = New System.Drawing.Point(790, 240)
        Me.TopCustomersPanel.Name = "TopCustomersPanel"
        Me.TopCustomersPanel.Size = New System.Drawing.Size(390, 350)
        Me.TopCustomersPanel.TabIndex = 3
        '
        'TopCustomersGrid
        '
        Me.TopCustomersGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TopCustomersGrid.Location = New System.Drawing.Point(2, 35)
        Me.TopCustomersGrid.MainView = Me.TopCustomersView
        Me.TopCustomersGrid.Name = "TopCustomersGrid"
        Me.TopCustomersGrid.Size = New System.Drawing.Size(386, 313)
        Me.TopCustomersGrid.TabIndex = 0
        Me.TopCustomersGrid.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.TopCustomersView})
        '
        'TopCustomersView
        '
        Me.TopCustomersView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.ColCustomerName, Me.ColTotalSales, Me.ColBalance})
        Me.TopCustomersView.GridControl = Me.TopCustomersGrid
        Me.TopCustomersView.Name = "TopCustomersView"
        Me.TopCustomersView.OptionsView.ShowGroupPanel = False
        '
        'ColCustomerName
        '
        Me.ColCustomerName.Caption = "اسم العميل"
        Me.ColCustomerName.FieldName = "CustomerName"
        Me.ColCustomerName.Name = "ColCustomerName"
        Me.ColCustomerName.Visible = True
        Me.ColCustomerName.VisibleIndex = 0
        Me.ColCustomerName.Width = 150
        '
        'ColTotalSales
        '
        Me.ColTotalSales.Caption = "إجمالي المبيعات"
        Me.ColTotalSales.FieldName = "TotalSales"
        Me.ColTotalSales.Name = "ColTotalSales"
        Me.ColTotalSales.Visible = True
        Me.ColTotalSales.VisibleIndex = 1
        Me.ColTotalSales.Width = 120
        '
        'ColBalance
        '
        Me.ColBalance.Caption = "الرصيد"
        Me.ColBalance.FieldName = "Balance"
        Me.ColBalance.Name = "ColBalance"
        Me.ColBalance.Visible = True
        Me.ColBalance.VisibleIndex = 2
        Me.ColBalance.Width = 100
        '
        'LabelControl2
        '
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Segoe UI", 14.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.LabelControl2.Appearance.Options.UseFont = True
        Me.LabelControl2.Appearance.Options.UseForeColor = True
        Me.LabelControl2.Dock = System.Windows.Forms.DockStyle.Top
        Me.LabelControl2.Location = New System.Drawing.Point(2, 2)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Padding = New System.Windows.Forms.Padding(10)
        Me.LabelControl2.Size = New System.Drawing.Size(130, 33)
        Me.LabelControl2.TabIndex = 1
        Me.LabelControl2.Text = "أفضل العملاء"
        '
        'CustomerStatisticsDashboard
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1200, 600)
        Me.Controls.Add(Me.TopCustomersPanel)
        Me.Controls.Add(Me.ChartPanel)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.PanelControl1)
        Me.LookAndFeel.SkinName = "Office 2016 Colorful"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "CustomerStatisticsDashboard"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "إحصائيات العملاء"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        Me.PanelControl1.PerformLayout()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.StatsPanel1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.StatsPanel1.ResumeLayout(False)
        Me.StatsPanel1.PerformLayout()
        CType(Me.PictureEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StatsPanel2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.StatsPanel2.ResumeLayout(False)
        Me.StatsPanel2.PerformLayout()
        CType(Me.PictureEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StatsPanel3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.StatsPanel3.ResumeLayout(False)
        Me.StatsPanel3.PerformLayout()
        CType(Me.PictureEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.StatsPanel4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.StatsPanel4.ResumeLayout(False)
        Me.StatsPanel4.PerformLayout()
        CType(Me.PictureEdit5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ChartPanel, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ChartPanel.ResumeLayout(False)
        CType(Me.SalesChart, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TopCustomersPanel, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TopCustomersPanel.ResumeLayout(False)
        Me.TopCustomersPanel.PerformLayout()
        CType(Me.TopCustomersGrid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TopCustomersView, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit1 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents RefreshButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TableLayoutPanel1 As TableLayoutPanel
    Friend WithEvents StatsPanel1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents TotalCustomersLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TotalCustomersValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit2 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents StatsPanel2 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents TotalSalesLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TotalSalesValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit3 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents StatsPanel3 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents TotalBalanceLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TotalBalanceValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit4 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents StatsPanel4 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents ActiveCustomersLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ActiveCustomersValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit5 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ChartPanel As DevExpress.XtraEditors.PanelControl
    Friend WithEvents SalesChart As DevExpress.XtraCharts.ChartControl
    Friend WithEvents TopCustomersPanel As DevExpress.XtraEditors.PanelControl
    Friend WithEvents TopCustomersGrid As DevExpress.XtraGrid.GridControl
    Friend WithEvents TopCustomersView As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents ColCustomerName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColTotalSales As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColBalance As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl

End Class
