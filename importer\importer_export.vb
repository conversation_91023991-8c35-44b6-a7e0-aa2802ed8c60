﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Data.OleDb

Public Class importer_export
    Private Sub customer_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Sub new_imp()
        GroupBox2.Enabled = True
        Accounts_balace.Enabled = True
        impcode.Text = getlastcode("importer", "impcode") + 1
        impname.Text = ""
        Accounts_balace.Text = 0
        imp_active.Checked = True
        imp_address.Text = ""
        imp_phone1.Text = ""
        imp_phone2.Text = ""
        Administrator.Text = ""
        CusCreditor.Checked = True
        imp_fax.Text = ""
        imp_post.Text = ""
        imp_email.Text = ""
        imp_pic.Image = My.Resources.talibov180200087
        impname.Focus()
    End Sub
    Sub erorr_number()
        If Accounts_balace.Text = "" Then
            Accounts_balace.Text = 0
        End If
    End Sub
    '============================ show data
    Public Sub show_data(x)
        Dim sql = "select * from importer where impcode=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لم يتم العثور علي بيانات")
        Else
            Dim dr = dt.Rows(0)
            impcode.Text = dr!impcode
            impname.Text = dr!impname
            Accounts_balace.Text = dr!Accounts_balace
            imp_active.Checked = dr!imp_active
            imp_address.Text = dr!imp_address
            imp_phone1.Text = dr!imp_phone1
            imp_phone2.Text = dr!imp_phone2
            Administrator.Text = dr!Administrator
            imp_fax.Text = dr!imp_fax
            imp_pic.Image = My.Resources.talibov180200087
            imp_post.Text = dr!imp_post
            imp_email.Text = dr!imp_email
            '========================================
            '======================== فك تشفير الصورة------
            If IsDBNull(dr!imp_pic) = False Then
                Dim imagbytearray() As Byte
                imagbytearray = CType(dr!imp_pic, Byte())
                Dim stream As New MemoryStream(imagbytearray)
                Dim bmp As New Bitmap(stream)
                imp_pic.Image = Image.FromStream(stream)
                stream.Close()
            End If
            code_pic.Text = 0
            Accounts_balace.Enabled = False
            GroupBox2.Enabled = False
            impcode.Enabled = False
            impname.Focus()
        End If
    End Sub
    Private Sub customer_add_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.Enter Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub
    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If Accounts_balace.Enabled = True Then
            impcode.Text = getlastcode("importer", "impcode") + 1
        End If
        erorr_number()
        If impname.Text = "" Then
            XtraMessageBox.Show("اسم المورد فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ErrorProvider1.SetError(impname, "اسم المورد فارغ")
            impname.Focus()
            Exit Sub
        End If
        If Accounts_balace.Enabled = True Then
            Dim sql1 = "select * from importer where impname=N'" & (impname.Text) & "'"
            Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
            Dim ds1 As New DataSet
            adp1.Fill(ds1)
            Dim dt1 = ds1.Tables(0)
            If dt1.Rows.Count > 0 Then
                XtraMessageBox.Show("يوجد مورد بهذا الاسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                ErrorProvider1.SetError(impname, "يوجد مورد بهذا الاسم")
                Exit Sub
            End If
        End If

        Dim sql = "select * from importer where impcode=N'" & (impcode.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            '========= بيانات اساسية============
            dr!impname = impname.Text
            dr!imp_active = imp_active.Checked
            dr!imp_address = imp_address.Text
            dr!imp_phone1 = imp_phone1.Text
            dr!imp_phone2 = imp_phone2.Text
            dr!Administrator = Administrator.Text
            dr!imp_fax = imp_fax.Text
            dr!imp_post = imp_post.Text
            dr!imp_email = imp_email.Text
            If code_pic.Text = 1 Then
                ''============حفظ الصورة=============
                If OpenFileDialog1.FileName.Length > 0 Then
                    Dim imagbytearray() As Byte
                    Dim stream As New MemoryStream
                    imp_pic.Image.Save(stream, ImageFormat.Jpeg)
                    imagbytearray = stream.ToArray
                    dr!imp_pic = imagbytearray
                    stream.Close()
                End If
            End If
            '============================
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============
            dr!impcode = impcode.Text
            dr!impname = impname.Text
            If CusDebtor.Checked = True Then
                dr!Accounts_balace = Accounts_balace.Text
            End If
            If CusCreditor.Checked = True Then
                dr!Accounts_balace = Val(Accounts_balace.Text) * -1
            End If
            dr!imp_active = imp_active.Checked
            dr!imp_address = imp_address.Text
            dr!imp_phone1 = imp_phone1.Text
            dr!imp_phone2 = imp_phone2.Text
            dr!Administrator = Administrator.Text
            dr!imp_fax = imp_fax.Text
            dr!imp_post = imp_post.Text
            dr!imp_email = imp_email.Text
            ''============حفظ الصورة=============
            If OpenFileDialog1.FileName.Length > 0 Then
                Dim imagbytearray() As Byte
                Dim stream As New MemoryStream
                imp_pic.Image.Save(stream, ImageFormat.Jpeg)
                imagbytearray = stream.ToArray
                dr!imp_pic = imagbytearray
                stream.Close()
            End If
            '============================
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        If CusDebtor.Checked = True Then
            set_importertrans(impname.Text, "رصيد افتتاحي", "", Now.Date, "", "رصيد افتتاحي", Val(Accounts_balace.Text), 0, Val(Accounts_balace.Text), Val(Accounts_balace.Text))
        End If
        If CusCreditor.Checked = True Then
            set_importertrans(impname.Text, "رصيد افتتاحي", "", Now.Date, "", "رصيد افتتاحي", 0, Val(Accounts_balace.Text), Val(Accounts_balace.Text) * -1, Val(Accounts_balace.Text) * -1)
        End If
        If Val(Accounts_balace.Text) <> 0 Then
            int_invoice()
        End If

        new_imp()
    End Sub
    Sub int_invoice()
        Dim sql = "select * from Purchases_add"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
        Else
            Dim dr = dt.NewRow
            dr!invoice_number = 0
            dr!store = ""
            dr!invoice_date = Now.Date
            dr!Accounts_name = impname.Text
            dr!Accounts_code = impcode.Text
            dr!total_invoice = Val(Accounts_balace.Text)
            dr!pay_money = 0
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub

    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        OpenFileDialog1.FileName = ""
        OpenFileDialog1.Filter = "jpeg|*.jpg|bitmap|*.bmp|gif|*.gif"
        OpenFileDialog1.ShowDialog()
        If OpenFileDialog1.FileName = "" Then Exit Sub
        imp_pic.Image = Image.FromFile(OpenFileDialog1.FileName)
        code_pic.Text = 1
    End Sub
    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        imp_pic.Image = My.Resources.talibov180200087
    End Sub
    Private Sub impname_TextChanged(sender As Object, e As EventArgs) Handles impname.TextChanged
        If impname.Text <> Nothing Then
            Dim sql = "select * from importer where impname=N'" & (impname.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                PictureBox1.Image = My.Resources.wrong
            Else
                PictureBox1.Image = My.Resources.check_true
            End If
        Else
            PictureBox1.Image = Nothing
        End If
        ErrorProvider1.SetError(impname, "")
    End Sub
    Private Sub جديدToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles جديدToolStripMenuItem.Click
        new_imp()
    End Sub
    Private Sub حفظToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظToolStripMenuItem.Click
        save_btn_Click(Nothing, Nothing)
    End Sub
    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        Me.Dispose()
    End Sub
    Private Sub Accounts_balace_KeyPress(sender As Object, e As KeyPressEventArgs) Handles Accounts_balace.KeyPress
        If Not Double.TryParse((Accounts_balace.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        ofd.Filter = "Excel 2007|*.xlsx|Excel 2003|*.xls"
        If ofd.ShowDialog = DialogResult.OK Then
            grd.DataSource = Nothing
            grd.Columns.Clear()
            Dim path As String = ofd.FileName
            Dim conn As New OleDbConnection("PROVIDER=MICROSOFT.ACE.OLEDB.12.0;DATA SOURCE=" & path & "; EXTENDED PROPERTIES=EXCEL 12.0;")
            Dim ds As New DataSet
            Dim da As New OleDbDataAdapter("SELECT * FROM [Sheet1$]", conn)
            da.Fill(ds, "[Sheet1$]")
            grd.DataSource = ds.Tables("[Sheet1$]")
        Else
            End
        End If
        Label40.Text = grd.RowCount - 2
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        new_imp()
        For i = 0 To grd.Rows.Count - 2
            Label36.Text = Val(Label36.Text) + 1
            impname.Text = grd.Rows(i).Cells(0).Value.ToString
            Accounts_balace.Text = grd.Rows(i).Cells(1).Value.ToString
            imp_address.Text = grd.Rows(i).Cells(2).Value.ToString
            imp_phone2.Text = grd.Rows(i).Cells(3).Value.ToString

            save_btn_Click(Nothing, Nothing)
        Next
        Label40.Text = grd.RowCount - 2
        MsgBox("تم استيراد الموردين بنجاح")
    End Sub
End Class