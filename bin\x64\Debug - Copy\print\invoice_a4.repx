﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="22.1.3.0" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v22.1, Version=22.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="invoice_a4" Margins="0, 0, 2, 2" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="22.1" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="2" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="2" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="165.041763">
      <Controls>
        <Item1 Ref="6" ControlType="XRLabel" Name="label4" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.3541,32.2917023" LocationFloat="542.8594,132.750061" Font="Microsoft Sans Serif, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[sec_address]" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="label1" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354065,32.2917023" LocationFloat="542.8594,100.458366" Font="Microsoft Sans Serif, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[sec_phone]" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="12" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="145.4165,94.8749847" LocationFloat="52.76578,48.5833473">
          <ExpressionBindings>
            <Item1 Ref="13" EventName="BeforePrint" PropertyName="ImageSource" Expression="[sec_pic]" />
          </ExpressionBindings>
        </Item3>
        <Item4 Ref="14" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.354126,30.6250267" LocationFloat="539.0729,59.0000153" Font="Microsoft Sans Serif, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[sec_s1]" />
          </ExpressionBindings>
          <StylePriority Ref="16" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="17" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" AnchorVertical="Top" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="539.0729,27.6666641" Font="Microsoft Sans Serif, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[sec_name]" />
          </ExpressionBindings>
          <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item4>
    <Item5 Ref="20" ControlType="PageHeaderBand" Name="PageHeader" HeightF="165.541687">
      <Controls>
        <Item1 Ref="21" ControlType="XRLabel" Name="label2" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="648.4214,31.2499771" LocationFloat="8.95834,88.45844" Font="Microsoft Sans Serif, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_note]" />
          </ExpressionBindings>
          <StylePriority Ref="23" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="24" ControlType="XRLabel" Name="label3" RightToLeft="Yes" Text="ملاحظات :" TextAlignment="TopCenter" SizeF="104.166687,31.2500076" LocationFloat="657.379639,88.4585" Font="Microsoft Sans Serif, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="25" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="26" ControlType="XRLabel" Name="XrLabel19" Text="بيان اسعار" TextAlignment="MiddleCenter" SizeF="132.916809,31.3333187" LocationFloat="321.049622,10.00007" Font="Microsoft Sans Serif, 12pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="27" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="28" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="132.916809,31.25" LocationFloat="321.049622,49.9582939" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_number]" />
          </ExpressionBindings>
          <StylePriority Ref="30" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="31" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="186.1041,31.2500381" LocationFloat="471.275574,10.0000381" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_date]" />
          </ExpressionBindings>
          <StylePriority Ref="33" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="34" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="104.166504,31.25" LocationFloat="657.379761,10.000165" Font="Microsoft Sans Serif, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="35" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="36" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="104.166626,31.2499962" LocationFloat="657.379639,49.95842" Font="Microsoft Sans Serif, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="37" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="38" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="186.1041,31.2499733" LocationFloat="471.275574,49.9583549" Font="Microsoft Sans Serif, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_name]" />
          </ExpressionBindings>
          <StylePriority Ref="40" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="41" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="175.854279,31.2499847" LocationFloat="26.1145267,10.0000067" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_adress]" />
          </ExpressionBindings>
          <StylePriority Ref="43" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="44" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="94.79169,31.2500057" LocationFloat="201.968964,9.999974" Font="Microsoft Sans Serif, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="45" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="46" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="94.79169,31.2499962" LocationFloat="201.968964,49.9583549" Font="Microsoft Sans Serif, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="47" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="48" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="175.854279,31.2499771" LocationFloat="26.1145267,49.9583244" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_phone1]" />
          </ExpressionBindings>
          <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="51" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="735.5208,33.3333435" LocationFloat="26.1145267,132.208344" Font="Microsoft Sans Serif, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="52" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="53" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.*****************" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="54" UsePadding="false" />
                </Item1>
                <Item2 Ref="55" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.*****************" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="56" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.542263146654033" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="57" UsePadding="false" />
                </Item3>
                <Item4 Ref="58" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.64086488759827032" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="59" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5482460841812116" Text="اسم الصنف" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="60" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item13>
      </Controls>
    </Item5>
    <Item6 Ref="61" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="325.7803">
      <Controls>
        <Item1 Ref="62" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="475.406769,25" LocationFloat="272.28598,10.0000067" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="63" EventName="BeforePrint" PropertyName="Text" Expression="[total_string]" />
          </ExpressionBindings>
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="65" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="37,2.083333" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="66" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="67" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="68" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_count]" />
                  </ExpressionBindings>
                  <StylePriority Ref="69" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="70" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="71" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="72" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="73" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="74" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_pound]" />
                  </ExpressionBindings>
                  <StylePriority Ref="75" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="76" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="77" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="78" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="79" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="36.99999,265.1971" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="81" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="36.99999,300.69696" Font="Microsoft Sans Serif, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="82" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="83" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="200,100" LocationFloat="37,52.08333" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="84" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
              <Cells>
                <Item1 Ref="85" ControlType="XRTableCell" Name="XrTableCell17" Weight="1" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="86" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_descound]" />
                  </ExpressionBindings>
                  <StylePriority Ref="87" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="88" ControlType="XRTableCell" Name="XrTableCell18" Weight="1" Text="الخصم" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="89" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="90" ControlType="XRTableRow" Name="XrTableRow6" Weight="1">
              <Cells>
                <Item1 Ref="91" ControlType="XRTableCell" Name="XrTableCell19" Weight="1" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="92" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_tax]" />
                  </ExpressionBindings>
                  <StylePriority Ref="93" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="94" ControlType="XRTableCell" Name="XrTableCell20" Weight="1" Text="الضريبة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="95" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
            <Item3 Ref="96" ControlType="XRTableRow" Name="XrTableRow10" Weight="1">
              <Cells>
                <Item1 Ref="97" ControlType="XRTableCell" Name="XrTableCell27" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="98" EventName="BeforePrint" PropertyName="Text" Expression="[total_invoice]" />
                  </ExpressionBindings>
                  <StylePriority Ref="99" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="100" ControlType="XRTableCell" Name="XrTableCell28" Weight="1" Text="صافي الفاتورة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="101" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item3>
            <Item4 Ref="102" ControlType="XRTableRow" Name="XrTableRow11" Weight="1">
              <Cells>
                <Item1 Ref="103" ControlType="XRTableCell" Name="XrTableCell29" Weight="1" Text="XrTableCell29" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="104" EventName="BeforePrint" PropertyName="Text" Expression="[past_balace]" />
                  </ExpressionBindings>
                  <StylePriority Ref="105" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="106" ControlType="XRTableCell" Name="XrTableCell30" Weight="1" Text="السابق" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="107" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item4>
          </Rows>
          <StylePriority Ref="108" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="109" ControlType="XRTable" Name="XrTable5" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="36.99997,153.9469" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="110" ControlType="XRTableRow" Name="XrTableRow7" Weight="1">
              <Cells>
                <Item1 Ref="111" ControlType="XRTableCell" Name="XrTableCell21" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="112" EventName="BeforePrint" PropertyName="Text" Expression="[money_plus]" />
                  </ExpressionBindings>
                  <StylePriority Ref="113" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="114" ControlType="XRTableCell" Name="XrTableCell22" Weight="1" Text="المطلوب" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="115" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="116" ControlType="XRTableRow" Name="XrTableRow8" Weight="1">
              <Cells>
                <Item1 Ref="117" ControlType="XRTableCell" Name="XrTableCell23" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="118" EventName="BeforePrint" PropertyName="Text" Expression="[pay]" />
                  </ExpressionBindings>
                  <StylePriority Ref="119" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="120" ControlType="XRTableCell" Name="XrTableCell24" Weight="1" Text="المدفوع" TextAlignment="MiddleRight" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="121" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="122" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="123" ControlType="XRTable" Name="XrTable6" TextAlignment="MiddleCenter" SizeF="200,25" LocationFloat="36.99997,207.4886" Font="Microsoft Sans Serif, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" Borders="Bottom">
          <Rows>
            <Item1 Ref="124" ControlType="XRTableRow" Name="XrTableRow9" Weight="1">
              <Cells>
                <Item1 Ref="125" ControlType="XRTableCell" Name="XrTableCell25" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" ForeColor="White" BackColor="Chocolate" Padding="2,2,0,0,100" BorderColor="Black">
                  <ExpressionBindings>
                    <Item1 Ref="126" EventName="BeforePrint" PropertyName="Text" Expression="[new_balace]" />
                  </ExpressionBindings>
                  <StylePriority Ref="127" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="128" ControlType="XRTableCell" Name="XrTableCell26" Weight="1" Text="الباقي" TextAlignment="MiddleRight" BackColor="Chocolate" Padding="2,2,0,0,100">
                  <StylePriority Ref="129" UseBackColor="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="130" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="131" ControlType="PageFooterBand" Name="PageFooter" HeightF="27.16662">
      <Controls>
        <Item1 Ref="132" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="37,0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="133" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="134" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="747.6927,0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="135" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="136" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,23" LocationFloat="698.7344,0" Padding="2,2,0,0,100">
          <StylePriority Ref="137" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="138" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="139" ControlType="DetailBand" Name="Detail1" HeightF="26.04167">
          <Controls>
            <Item1 Ref="140" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="735.5208,25" LocationFloat="26.1145267,0" Font="Microsoft Sans Serif, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="141" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="142" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.0005448213163186" TextFormatString="{0:n2}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="143" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_total]" />
                      </ExpressionBindings>
                      <StylePriority Ref="144" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="145" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.93220917081262" TextFormatString="{0:n2}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="146" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_price]" />
                      </ExpressionBindings>
                      <StylePriority Ref="147" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="148" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.85685326008936014" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="149" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_count]" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="150" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.0126570260107606" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="151" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_unit]" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="152" ControlType="XRTableCell" Name="XrTableCell8" Weight="4.0265895569054457" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="153" EventName="BeforePrint" PropertyName="Text" Expression="[invoice_print].[item_name]" />
                      </ExpressionBindings>
                      <StylePriority Ref="154" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="155" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="156" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="1" />
    <Item2 Ref="157" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item3 Ref="158" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item4 Ref="159" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="160" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="2" />
    <Item6 Ref="161" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;GenericDefault" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v22.1" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>