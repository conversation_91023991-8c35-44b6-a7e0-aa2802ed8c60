﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.desc_cus_s4vb, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="desc_cus_s4vb" Margins="0, 1, 2, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="desc_cus_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="208.1458" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLine" Name="XrLine3" SizeF="813.0001,4.791673" LocationFloat="10.00003, 203.3541" />
        <Item2 Ref="4" ControlType="XRLabel" Name="XrLabel13" TextFormatString="{0:yyyy-MM-dd}" RightToLeft="Yes" Text="XrLabel13" SizeF="205.875,31.25" LocationFloat="29.61456, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="5" Expression="[descdate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="6" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item2>
        <Item3 Ref="7" ControlType="XRLabel" Name="XrLabel12" RightToLeft="Yes" Text="XrLabel12" SizeF="461.7605,31.25" LocationFloat="29.61458, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="8" Expression="[state_Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="9" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item3>
        <Item4 Ref="10" ControlType="XRLabel" Name="XrLabel11" RightToLeft="Yes" Text="XrLabel11" SizeF="634.4791,31.25" LocationFloat="29.61458, 57.8125" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="11" Expression="[customername]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="12" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item4>
        <Item5 Ref="13" ControlType="XRLabel" Name="XrLabel9" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="296.3126,31.25" LocationFloat="367.7812, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="14" Expression="[desccode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="15" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="16" ControlType="XRLabel" Name="XrLabel6" RightToLeft="Yes" Text="XrLabel6" SizeF="634.4792,31.25" LocationFloat="29.61456, 152.5" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="17" Expression="[cash_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="18" UseFont="false" UseBorderColor="false" UseBorders="false" />
        </Item6>
        <Item7 Ref="19" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:#,#}" RightToLeft="Yes" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="172.7187,31.25" LocationFloat="491.3751, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="20" Expression="[Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="21" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="22" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="كود الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.0938, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="23" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="24" ControlType="XRLabel" Name="XrLabel5" RightToLeft="Yes" Text="تـــاريخ الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="235.4896, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1">
          <StylePriority Ref="25" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="26" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="أســـــــم العميل :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.0938, 57.8125" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="27" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="28" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="مـبلغ وقدره :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.0938, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="29" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="30" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="وذلك عن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.0938, 152.5" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="31" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
      </Controls>
    </Item1>
    <Item2 Ref="32" ControlType="TopMarginBand" Name="TopMargin" HeightF="2" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="33" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="34" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="106.6667">
      <Controls>
        <Item1 Ref="35" ControlType="XRLine" Name="XrLine4" SizeF="813.0001,4.791673" LocationFloat="12.99987, 0" />
        <Item2 Ref="36" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="123.3436,98.58335" LocationFloat="29.61458, 4.791673">
          <ExpressionBindings>
            <Item1 Ref="37" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item2>
        <Item3 Ref="38" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="545.6459, 57.54166" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="39" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="40" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="41" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="545.6459, 26.20833" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="42" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="43" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="44" ControlType="PageHeaderBand" Name="PageHeader" HeightF="132.3334">
      <Controls>
        <Item1 Ref="45" ControlType="XRLine" Name="XrLine2" SizeF="813.0001,4.791673" LocationFloat="10.00003, 127.5417" />
        <Item2 Ref="46" ControlType="XRLine" Name="XrLine1" SizeF="813.0001,4.791673" LocationFloat="9.999998, 0" />
        <Item3 Ref="47" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="29.61458, 87.91666" Padding="2,2,0,0,100">
          <StylePriority Ref="48" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="49" ControlType="XRLabel" Name="XrLabel19" Text="إشعار بإستلام خصم" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="29.61458, 10.00001" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="50" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="51" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="694.4791, 27.37497">
          <ExpressionBindings>
            <Item1 Ref="52" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item5>
        <Item6 Ref="53" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="90.25009, 41.33332">
          <ExpressionBindings>
            <Item1 Ref="54" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item6>
      </Controls>
    </Item5>
    <Item6 Ref="55" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="127.25">
      <Controls>
        <Item1 Ref="56" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="6.5, 66.66665" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="58" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="6.5, 102.1667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="60" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="509.0001, 25.08332" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="62" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="509.0001, 0" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="63" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="64" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="90.25009, 27.16665" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="65" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="66" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="67" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="90.25009, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="68" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="69" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="70" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="491.3751, 102.1667" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="71" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="72" UseFont="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="73" ControlType="PageFooterBand" Name="PageFooter" HeightF="38.54167">
      <Controls>
        <Item1 Ref="74" ControlType="XRLine" Name="XrLine5" SizeF="813.0001,4.791673" LocationFloat="9.99999, 0" />
        <Item2 Ref="75" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="29.16667, 7.916641" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="76" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="77" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="743.4375, 9.999974" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="78" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="79" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="694.4791, 15.54167" Padding="2,2,0,0,100">
          <StylePriority Ref="80" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="81" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="82" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="83" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="84" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="85" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="86" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>