﻿Imports System.Data.SqlClient
Imports DevExpress.XtraEditors

Public Class item_bac


    Private Sub order_item_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_item()
        fill_item2()
        ImageComboBoxEdit1.SelectedIndex = 0
        dgv_total.DataSource = Nothing
        dgv_total.Rows.Clear()
    End Sub
    Sub fill_item()
        invoice_name.Properties.DataSource = Nothing
        invoice_name.EditValue = ""
        Dim adp As New SqlDataAdapter("select (invoice_number),(store),(invoice_date),(Accounts_name),(invoice_count),(total_invoice) from invoice_add", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        invoice_name.Properties.DataSource = dt
        invoice_name.Properties.DisplayMember = "Accounts_name"
        invoice_name.Properties.ValueMember = "invoice_number"
    End Sub
    Sub fill_item2()
        invoice_code.Properties.DataSource = Nothing
        invoice_code.EditValue = ""
        Dim adp As New SqlDataAdapter("select (invoice_number),(store),(invoice_date),(Accounts_name),(invoice_count),(total_invoice) from invoice_add", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        invoice_code.Properties.DataSource = dt
        invoice_code.Properties.DisplayMember = "invoice_number"
        invoice_code.Properties.ValueMember = "invoice_number"
    End Sub


    Private Sub dgv_total_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv_total.CellPainting
        If e.ColumnIndex = 4 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.check
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub

    Private Sub dgv_total_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_total.CellClick
        Try

        Catch ex As Exception

        End Try
        If e.ColumnIndex = 4 Then
            dgv_total.CurrentRow.Cells(3).Value = Val(dgv_total.CurrentRow.Cells(2).Value.ToString)
        End If

    End Sub

    Private Sub ImageComboBoxEdit1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ImageComboBoxEdit1.SelectedIndexChanged
        If ImageComboBoxEdit1.SelectedIndex = 0 Then
            invoice_code.BringToFront()

        Else
            invoice_name.BringToFront()
        End If

    End Sub

    Private Sub Label21_Click(sender As Object, e As EventArgs) Handles Label21.Click
        Me.Dispose()
    End Sub

    Private Sub item_name_EditValueChanged(sender As Object, e As EventArgs) Handles invoice_name.EditValueChanged

        Dim adp As New SqlDataAdapter("select * from invoice_list where invoice_number='" & invoice_name.EditValue & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_total.AutoGenerateColumns = False
        dgv_total.DataSource = dt
    End Sub

    Private Sub Label16_Click(sender As Object, e As EventArgs) Handles Label16.Click
        invoice_name.Focus()

        If XtraForm1.m69.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        Dim f As invoice_back = New invoice_back()
        f.Text = "مرتجع مبيعات"
        f.MdiParent = XtraForm1
        f.Show()

        For i = 0 To dgv_total.Rows.Count - 1
            Try
                If Val(dgv_total.Rows(i).Cells(3).Value.ToString) > 0 Then
                    f.item_name.Text = dgv_total.Rows(i).Cells(0).Value
                    f.jjj()
                    f.ggggg()
                    f.item_price.Text = Val(dgv_total.Rows(i).Cells(1).Value.ToString)
                    f.item_count.Text = Val(dgv_total.Rows(i).Cells(3).Value.ToString)
                    If Val(f.code.Text) > 0 Then
                        f.add_item()
                    End If

                End If
            Catch ex As Exception

            End Try
        Next
        Me.Dispose()
    End Sub

    Private Sub invoice_code_EditValueChanged(sender As Object, e As EventArgs) Handles invoice_code.EditValueChanged
        Dim adp As New SqlDataAdapter("select (item_name),(item_price),(item_count) from invoice_list where invoice_number='" & invoice_code.EditValue & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_total.AutoGenerateColumns = False
        dgv_total.DataSource = dt
    End Sub
End Class