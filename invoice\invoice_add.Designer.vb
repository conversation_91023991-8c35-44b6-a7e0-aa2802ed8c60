﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class invoice_add
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(invoice_add))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle11 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle8 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle9 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle10 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle12 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle13 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle14 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle15 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle16 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle17 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle18 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle19 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle21 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle20 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim GridLevelNode1 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridFormatRule1 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleIconSet1 As DevExpress.XtraEditors.FormatConditionRuleIconSet = New DevExpress.XtraEditors.FormatConditionRuleIconSet()
        Dim FormatConditionIconSet1 As DevExpress.XtraEditors.FormatConditionIconSet = New DevExpress.XtraEditors.FormatConditionIconSet()
        Dim FormatConditionIconSetIcon1 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon2 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon3 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim DataGridViewCellStyle22 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim GridLevelNode2 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridFormatRule2 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleIconSet2 As DevExpress.XtraEditors.FormatConditionRuleIconSet = New DevExpress.XtraEditors.FormatConditionRuleIconSet()
        Dim FormatConditionIconSet2 As DevExpress.XtraEditors.FormatConditionIconSet = New DevExpress.XtraEditors.FormatConditionIconSet()
        Dim FormatConditionIconSetIcon4 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon5 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon6 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim DataGridViewCellStyle23 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle24 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle27 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle25 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle26 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle28 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle29 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle34 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle30 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle31 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle32 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle33 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim GridLevelNode3 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridFormatRule3 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleIconSet3 As DevExpress.XtraEditors.FormatConditionRuleIconSet = New DevExpress.XtraEditors.FormatConditionRuleIconSet()
        Dim FormatConditionIconSet3 As DevExpress.XtraEditors.FormatConditionIconSet = New DevExpress.XtraEditors.FormatConditionIconSet()
        Dim FormatConditionIconSetIcon7 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon8 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon9 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim GridLevelNode4 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Dim GridFormatRule4 As DevExpress.XtraGrid.GridFormatRule = New DevExpress.XtraGrid.GridFormatRule()
        Dim FormatConditionRuleIconSet4 As DevExpress.XtraEditors.FormatConditionRuleIconSet = New DevExpress.XtraEditors.FormatConditionRuleIconSet()
        Dim FormatConditionIconSet4 As DevExpress.XtraEditors.FormatConditionIconSet = New DevExpress.XtraEditors.FormatConditionIconSet()
        Dim FormatConditionIconSetIcon10 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon11 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim FormatConditionIconSetIcon12 As DevExpress.XtraEditors.FormatConditionIconSetIcon = New DevExpress.XtraEditors.FormatConditionIconSetIcon()
        Dim EditorButtonImageOptions1 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject2 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject3 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject4 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions2 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject5 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject6 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject7 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject8 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions3 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject9 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject10 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject11 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject12 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions4 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject13 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject14 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject15 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject16 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim DataGridViewCellStyle35 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle36 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle38 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle37 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle39 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle40 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle43 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle41 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle42 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle44 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle45 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle47 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle46 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle48 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle49 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle51 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle50 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.RepositoryItemCalcEdit5 = New DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit()
        Me.RepositoryItemCalcEdit6 = New DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit()
        Me.XtraTabControl2 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage5 = New DevExpress.XtraTab.XtraTabPage()
        Me.Button7 = New System.Windows.Forms.Button()
        Me.money_plus = New System.Windows.Forms.Label()
        Me.Button6 = New System.Windows.Forms.Button()
        Me.printcode = New System.Windows.Forms.TextBox()
        Me.dgv_10 = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn16 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn17 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn18 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn19 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn20 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn21 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn22 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewButtonColumn1 = New System.Windows.Forms.DataGridViewButtonColumn()
        Me.DataGridViewTextBoxColumn23 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn24 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn25 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn26 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn27 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn28 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn29 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn30 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn31 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column22 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Button5 = New System.Windows.Forms.Button()
        Me.code3 = New System.Windows.Forms.TextBox()
        Me.code2 = New System.Windows.Forms.TextBox()
        Me.Button4 = New System.Windows.Forms.Button()
        Me.code = New System.Windows.Forms.TextBox()
        Me.dgv_total = New System.Windows.Forms.DataGridView()
        Me.DataGridViewComboBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn14 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn15 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.Button3 = New System.Windows.Forms.Button()
        Me.XtraTabPage6 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.item_from = New System.Windows.Forms.DateTimePicker()
        Me.Label44 = New System.Windows.Forms.Label()
        Me.Label35 = New System.Windows.Forms.Label()
        Me.item_to = New System.Windows.Forms.DateTimePicker()
        Me.show_itemcus = New System.Windows.Forms.CheckBox()
        Me.show_itembuy = New System.Windows.Forms.CheckBox()
        Me.block_sale = New System.Windows.Forms.TextBox()
        Me.itemcouta = New System.Windows.Forms.TextBox()
        Me.item_tax_add = New System.Windows.Forms.TextBox()
        Me.tax_add = New System.Windows.Forms.TextBox()
        Me.CusCreditlimit = New System.Windows.Forms.TextBox()
        Me.Cuscusgroup = New System.Windows.Forms.TextBox()
        Me.cusGovernorate = New System.Windows.Forms.TextBox()
        Me.cuscity = New System.Windows.Forms.TextBox()
        Me.cus_price_private = New System.Windows.Forms.TextBox()
        Me.item_group = New System.Windows.Forms.TextBox()
        Me.item_catorgey = New System.Windows.Forms.TextBox()
        Me.item_company = New System.Windows.Forms.TextBox()
        Me.rebh = New System.Windows.Forms.CheckBox()
        Me.XtraTabPage7 = New DevExpress.XtraTab.XtraTabPage()
        Me.dgv_2 = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TextBox23 = New System.Windows.Forms.TextBox()
        Me.count_text = New System.Windows.Forms.TextBox()
        Me.Alternativeitem = New System.Windows.Forms.CheckBox()
        Me.code_invoice = New System.Windows.Forms.Label()
        Me.code_pharche = New System.Windows.Forms.Label()
        Me.item_notes = New System.Windows.Forms.TextBox()
        Me.TextBox9 = New System.Windows.Forms.TextBox()
        Me.show_count = New System.Windows.Forms.TextBox()
        Me.show_buy = New System.Windows.Forms.TextBox()
        Me.show_datebuy = New System.Windows.Forms.TextBox()
        Me.show_datesale = New System.Windows.Forms.TextBox()
        Me.TextBox17 = New System.Windows.Forms.TextBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.TextBox11 = New System.Windows.Forms.TextBox()
        Me.TextBox12 = New System.Windows.Forms.TextBox()
        Me.TextBox21 = New System.Windows.Forms.TextBox()
        Me.DataGridView1 = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn11 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn12 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn13 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.XtraTabPage8 = New DevExpress.XtraTab.XtraTabPage()
        Me.dgv_3 = New System.Windows.Forms.DataGridView()
        Me.Column9 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column10 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column11 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Label40 = New System.Windows.Forms.Label()
        Me.report_imp = New System.Windows.Forms.CheckBox()
        Me.Label50 = New System.Windows.Forms.Label()
        Me.Accounts_phone2 = New System.Windows.Forms.TextBox()
        Me.day_sale = New System.Windows.Forms.TextBox()
        Me.TextBox28 = New System.Windows.Forms.TextBox()
        Me.Accounts_phone1 = New System.Windows.Forms.TextBox()
        Me.Label43 = New System.Windows.Forms.Label()
        Me.Accounts_adress = New System.Windows.Forms.TextBox()
        Me.Label42 = New System.Windows.Forms.Label()
        Me.Label38 = New System.Windows.Forms.Label()
        Me.balace_show = New System.Windows.Forms.TextBox()
        Me.Label41 = New System.Windows.Forms.Label()
        Me.TextBox3 = New System.Windows.Forms.TextBox()
        Me.earn_invoice = New System.Windows.Forms.TextBox()
        Me.ContextMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.A4ToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.شكل1ToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.شكل2ToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.A5ToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.شكل1ToolStripMenuItem1 = New System.Windows.Forms.ToolStripMenuItem()
        Me.شكل2ToolStripMenuItem1 = New System.Windows.Forms.ToolStripMenuItem()
        Me.PosToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.شكل1ToolStripMenuItem2 = New System.Windows.Forms.ToolStripMenuItem()
        Me.شكل2ToolStripMenuItem2 = New System.Windows.Forms.ToolStripMenuItem()
        Me.lastpay = New System.Windows.Forms.TextBox()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.new_btn = New System.Windows.Forms.Button()
        Me.save_btn = New System.Windows.Forms.Button()
        Me.edit_btn = New System.Windows.Forms.Button()
        Me.delet_btn = New System.Windows.Forms.Button()
        Me.print_btn = New System.Windows.Forms.Button()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Button8 = New System.Windows.Forms.Button()
        Me.Button9 = New System.Windows.Forms.Button()
        Me.Button11 = New System.Windows.Forms.Button()
        Me.DefaultLookAndFeel1 = New DevExpress.LookAndFeel.DefaultLookAndFeel(Me.components)
        Me.delegate_name = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Dealing = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Date_pay = New DevExpress.XtraEditors.DateEdit()
        Me.SplitContainerControl1 = New DevExpress.XtraEditors.SplitContainerControl()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn27 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn26 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn28 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn29 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl26 = New DevExpress.XtraEditors.LabelControl()
        Me.item_name = New DevExpress.XtraEditors.LookUpEdit()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.dgv_catarogy = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn39 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.dgv_touch = New DevExpress.XtraGrid.GridControl()
        Me.CardView2 = New DevExpress.XtraGrid.Views.Card.CardView()
        Me.GridColumn24 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.itemnamearabic = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.itemcount = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.itemcode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GroupBox4 = New System.Windows.Forms.GroupBox()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.serial_search = New DevExpress.XtraEditors.TextEdit()
        Me.SimpleButton6 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton7 = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.dgv_serial = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn35 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn38 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column3 = New System.Windows.Forms.DataGridViewButtonColumn()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton4 = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.dgv_expire = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn32 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn34 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column2 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.GroupBox6 = New System.Windows.Forms.GroupBox()
        Me.SimpleButton10 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.GridControl3 = New DevExpress.XtraGrid.GridControl()
        Me.GridView3 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn25 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn30 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn31 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.amount_string = New System.Windows.Forms.TextBox()
        Me.Yup = New System.Windows.Forms.RadioButton()
        Me.monetary = New System.Windows.Forms.RadioButton()
        Me.LabelControl16 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl19 = New DevExpress.XtraEditors.LabelControl()
        Me.Accounts_name = New System.Windows.Forms.ComboBox()
        Me.GroupBox5 = New System.Windows.Forms.GroupBox()
        Me.LabelControl13 = New DevExpress.XtraEditors.LabelControl()
        Me.amount_rate = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl12 = New DevExpress.XtraEditors.LabelControl()
        Me.end_date = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.start_date = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.cobon_code = New DevExpress.XtraEditors.TextEdit()
        Me.SimpleButton8 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton9 = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.invoice_date = New DevExpress.XtraEditors.DateEdit()
        Me.ComboBoxEdit2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.store = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.invoice_number = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.Accounts_balace = New DevExpress.XtraEditors.LabelControl()
        Me.Label19 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.TextBox8 = New System.Windows.Forms.TextBox()
        Me.pay_money = New DevExpress.XtraEditors.CalcEdit()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.PictureBox5 = New System.Windows.Forms.PictureBox()
        Me.new_balace = New System.Windows.Forms.TextBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label46 = New System.Windows.Forms.Label()
        Me.Label45 = New System.Windows.Forms.Label()
        Me.Accounts_code = New System.Windows.Forms.TextBox()
        Me.invoice_tax = New System.Windows.Forms.TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.invoice_descound = New System.Windows.Forms.TextBox()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.check_order = New DevExpress.XtraEditors.LabelControl()
        Me.check_suplier = New DevExpress.XtraEditors.LabelControl()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn7 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemComboBox1 = New DevExpress.XtraEditors.Repository.RepositoryItemComboBox()
        Me.GridColumn8 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemCalcEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit()
        Me.GridColumn9 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemCalcEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit()
        Me.GridColumn10 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemCalcEdit3 = New DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit()
        Me.GridColumn13 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemCalcEdit4 = New DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit()
        Me.GridColumn14 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn11 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn15 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn16 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn17 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn18 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn19 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn20 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemButtonEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn21 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn22 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemButtonEdit3 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn23 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemButtonEdit4 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn12 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemButtonEdit5 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.invoice_count = New System.Windows.Forms.TextBox()
        Me.total_invoice = New System.Windows.Forms.TextBox()
        Me.after_total = New System.Windows.Forms.TextBox()
        Me.invoice_note = New DevExpress.XtraEditors.TextEdit()
        Me.invoice_pound = New System.Windows.Forms.TextBox()
        Me.dgv_5 = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn6 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn7 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn8 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn9 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column4 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn10 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.dgv_final_expire = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn33 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn36 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn37 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.dgv_serial_no = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn41 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn43 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.descound_Values = New DevExpress.XtraEditors.CalcEdit()
        Me.descound_Rate = New DevExpress.XtraEditors.CalcEdit()
        Me.show_total = New System.Windows.Forms.TextBox()
        Me.curency_item = New DevExpress.XtraEditors.LookUpEdit()
        Me.dgv_4 = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn2 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn3 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn4 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn5 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.Column20 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.data_sale = New System.Windows.Forms.DateTimePicker()
        Me.item_barcode = New System.Windows.Forms.TextBox()
        Me.tax_Rate = New DevExpress.XtraEditors.CalcEdit()
        Me.tax_Values = New DevExpress.XtraEditors.CalcEdit()
        Me.inserte = New System.Windows.Forms.Button()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.item_descound = New System.Windows.Forms.TextBox()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.item_total = New DevExpress.XtraEditors.CalcEdit()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.item_unit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.number_unit = New System.Windows.Forms.Label()
        Me.Label47 = New System.Windows.Forms.Label()
        Me.item_count = New DevExpress.XtraEditors.CalcEdit()
        Me.item_price = New DevExpress.XtraEditors.CalcEdit()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.item_type = New System.Windows.Forms.TextBox()
        Me.disc_rate = New DevExpress.XtraEditors.CalcEdit()
        Me.item_total2 = New DevExpress.XtraEditors.CalcEdit()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.Button13 = New System.Windows.Forms.Button()
        Me.treasury_balace = New System.Windows.Forms.TextBox()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.type_pay = New System.Windows.Forms.ComboBox()
        Me.CostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        Me.LabelCostCenter = New DevExpress.XtraEditors.LabelControl()
        CType(Me.RepositoryItemCalcEdit5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCalcEdit6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl2.SuspendLayout()
        Me.XtraTabPage5.SuspendLayout()
        CType(Me.dgv_10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_total, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage6.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.XtraTabPage7.SuspendLayout()
        CType(Me.dgv_2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DataGridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage8.SuspendLayout()
        CType(Me.dgv_3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ContextMenuStrip1.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.delegate_name.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Dealing.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_pay.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Date_pay.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitContainerControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitContainerControl1.Panel1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SplitContainerControl1.Panel1.SuspendLayout()
        CType(Me.SplitContainerControl1.Panel2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SplitContainerControl1.Panel2.SuspendLayout()
        Me.SplitContainerControl1.SuspendLayout()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item_name.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        CType(Me.dgv_catarogy, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_touch, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CardView2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox4.SuspendLayout()
        CType(Me.serial_search.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_serial, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox3.SuspendLayout()
        CType(Me.dgv_expire, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox6.SuspendLayout()
        CType(Me.GridControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox5.SuspendLayout()
        CType(Me.amount_rate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.end_date.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.start_date.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cobon_code.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.invoice_date.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.invoice_date.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.store.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.invoice_number.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pay_money.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemComboBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCalcEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCalcEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCalcEdit3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCalcEdit4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.invoice_note.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_final_expire, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_serial_no, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.descound_Values.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.descound_Rate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.curency_item.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.dgv_4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tax_Rate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.tax_Values.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.item_total.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item_unit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item_count.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item_price.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.disc_rate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.item_total2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RepositoryItemCalcEdit5
        '
        Me.RepositoryItemCalcEdit5.AutoHeight = False
        Me.RepositoryItemCalcEdit5.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemCalcEdit5.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.RepositoryItemCalcEdit5.Name = "RepositoryItemCalcEdit5"
        '
        'RepositoryItemCalcEdit6
        '
        Me.RepositoryItemCalcEdit6.AutoHeight = False
        Me.RepositoryItemCalcEdit6.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemCalcEdit6.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.RepositoryItemCalcEdit6.Name = "RepositoryItemCalcEdit6"
        '
        'XtraTabControl2
        '
        Me.XtraTabControl2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl2.Appearance.Options.UseFont = True
        Me.XtraTabControl2.Location = New System.Drawing.Point(5, 44)
        Me.XtraTabControl2.LookAndFeel.SkinName = "Seven Classic"
        Me.XtraTabControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl2.MultiLine = DevExpress.Utils.DefaultBoolean.[True]
        Me.XtraTabControl2.Name = "XtraTabControl2"
        Me.XtraTabControl2.SelectedTabPage = Me.XtraTabPage5
        Me.XtraTabControl2.Size = New System.Drawing.Size(207, 570)
        Me.XtraTabControl2.TabIndex = 1240
        Me.XtraTabControl2.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage5, Me.XtraTabPage6, Me.XtraTabPage7, Me.XtraTabPage8})
        '
        'XtraTabPage5
        '
        Me.XtraTabPage5.Appearance.PageClient.BackColor = System.Drawing.Color.LightGray
        Me.XtraTabPage5.Appearance.PageClient.Options.UseBackColor = True
        Me.XtraTabPage5.Controls.Add(Me.Button7)
        Me.XtraTabPage5.Controls.Add(Me.money_plus)
        Me.XtraTabPage5.Controls.Add(Me.Button6)
        Me.XtraTabPage5.Controls.Add(Me.printcode)
        Me.XtraTabPage5.Controls.Add(Me.dgv_10)
        Me.XtraTabPage5.Controls.Add(Me.Button5)
        Me.XtraTabPage5.Controls.Add(Me.code3)
        Me.XtraTabPage5.Controls.Add(Me.code2)
        Me.XtraTabPage5.Controls.Add(Me.Button4)
        Me.XtraTabPage5.Controls.Add(Me.code)
        Me.XtraTabPage5.Controls.Add(Me.dgv_total)
        Me.XtraTabPage5.Controls.Add(Me.Button2)
        Me.XtraTabPage5.Controls.Add(Me.Button3)
        Me.XtraTabPage5.ImageOptions.Image = CType(resources.GetObject("XtraTabPage5.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage5.Name = "XtraTabPage5"
        Me.XtraTabPage5.Size = New System.Drawing.Size(204, 542)
        '
        'Button7
        '
        Me.Button7.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button7.BackColor = System.Drawing.Color.Gray
        Me.Button7.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button7.FlatAppearance.BorderSize = 0
        Me.Button7.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Gold
        Me.Button7.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button7.ForeColor = System.Drawing.Color.White
        Me.Button7.Image = CType(resources.GetObject("Button7.Image"), System.Drawing.Image)
        Me.Button7.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button7.Location = New System.Drawing.Point(3, 424)
        Me.Button7.Name = "Button7"
        Me.Button7.Size = New System.Drawing.Size(197, 50)
        Me.Button7.TabIndex = 1260
        Me.Button7.Text = "اله حاسبة"
        Me.Button7.UseVisualStyleBackColor = False
        '
        'money_plus
        '
        Me.money_plus.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.money_plus.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.money_plus.Location = New System.Drawing.Point(49, 511)
        Me.money_plus.Name = "money_plus"
        Me.money_plus.Size = New System.Drawing.Size(28, 21)
        Me.money_plus.TabIndex = 1178
        Me.money_plus.Text = "0"
        Me.money_plus.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.money_plus.Visible = False
        '
        'Button6
        '
        Me.Button6.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button6.BackColor = System.Drawing.Color.CornflowerBlue
        Me.Button6.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button6.FlatAppearance.BorderSize = 0
        Me.Button6.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Gold
        Me.Button6.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button6.ForeColor = System.Drawing.Color.White
        Me.Button6.Image = CType(resources.GetObject("Button6.Image"), System.Drawing.Image)
        Me.Button6.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button6.Location = New System.Drawing.Point(3, 368)
        Me.Button6.Name = "Button6"
        Me.Button6.Size = New System.Drawing.Size(197, 50)
        Me.Button6.TabIndex = 1259
        Me.Button6.Text = "كوبون الخصم"
        Me.Button6.UseVisualStyleBackColor = False
        '
        'printcode
        '
        Me.printcode.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.printcode.BackColor = System.Drawing.Color.White
        Me.printcode.Location = New System.Drawing.Point(139, 440)
        Me.printcode.Margin = New System.Windows.Forms.Padding(4)
        Me.printcode.Name = "printcode"
        Me.printcode.ReadOnly = True
        Me.printcode.Size = New System.Drawing.Size(17, 22)
        Me.printcode.TabIndex = 1170
        Me.printcode.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.printcode.Visible = False
        '
        'dgv_10
        '
        Me.dgv_10.AllowUserToAddRows = False
        Me.dgv_10.AllowUserToDeleteRows = False
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.Color.SeaGreen
        Me.dgv_10.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.dgv_10.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgv_10.BackgroundColor = System.Drawing.Color.White
        Me.dgv_10.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.dgv_10.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SunkenVertical
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.DarkOrange
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_10.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.dgv_10.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_10.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn16, Me.DataGridViewTextBoxColumn17, Me.DataGridViewTextBoxColumn18, Me.DataGridViewTextBoxColumn19, Me.DataGridViewTextBoxColumn20, Me.DataGridViewTextBoxColumn21, Me.DataGridViewTextBoxColumn22, Me.DataGridViewButtonColumn1, Me.DataGridViewTextBoxColumn23, Me.DataGridViewTextBoxColumn24, Me.DataGridViewTextBoxColumn25, Me.DataGridViewTextBoxColumn26, Me.DataGridViewTextBoxColumn27, Me.DataGridViewTextBoxColumn28, Me.DataGridViewTextBoxColumn29, Me.DataGridViewTextBoxColumn30, Me.DataGridViewTextBoxColumn31, Me.Column22})
        Me.dgv_10.Cursor = System.Windows.Forms.Cursors.Default
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle5.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Tahoma", 8.25!)
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.SeaGreen
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_10.DefaultCellStyle = DataGridViewCellStyle5
        Me.dgv_10.GridColor = System.Drawing.Color.GhostWhite
        Me.dgv_10.Location = New System.Drawing.Point(23, 384)
        Me.dgv_10.Name = "dgv_10"
        Me.dgv_10.ReadOnly = True
        Me.dgv_10.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        Me.dgv_10.RowHeadersVisible = False
        Me.dgv_10.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_10.Size = New System.Drawing.Size(113, 76)
        Me.dgv_10.TabIndex = 1255
        Me.dgv_10.Visible = False
        '
        'DataGridViewTextBoxColumn16
        '
        Me.DataGridViewTextBoxColumn16.HeaderText = "كود"
        Me.DataGridViewTextBoxColumn16.Name = "DataGridViewTextBoxColumn16"
        Me.DataGridViewTextBoxColumn16.ReadOnly = True
        Me.DataGridViewTextBoxColumn16.Visible = False
        '
        'DataGridViewTextBoxColumn17
        '
        Me.DataGridViewTextBoxColumn17.HeaderText = "أسم الصنف"
        Me.DataGridViewTextBoxColumn17.Name = "DataGridViewTextBoxColumn17"
        Me.DataGridViewTextBoxColumn17.ReadOnly = True
        Me.DataGridViewTextBoxColumn17.Width = 250
        '
        'DataGridViewTextBoxColumn18
        '
        Me.DataGridViewTextBoxColumn18.HeaderText = "الوحدة"
        Me.DataGridViewTextBoxColumn18.Name = "DataGridViewTextBoxColumn18"
        Me.DataGridViewTextBoxColumn18.ReadOnly = True
        '
        'DataGridViewTextBoxColumn19
        '
        Me.DataGridViewTextBoxColumn19.HeaderText = "الكمية"
        Me.DataGridViewTextBoxColumn19.Name = "DataGridViewTextBoxColumn19"
        Me.DataGridViewTextBoxColumn19.ReadOnly = True
        Me.DataGridViewTextBoxColumn19.Width = 80
        '
        'DataGridViewTextBoxColumn20
        '
        DataGridViewCellStyle3.Format = "N2"
        DataGridViewCellStyle3.NullValue = Nothing
        Me.DataGridViewTextBoxColumn20.DefaultCellStyle = DataGridViewCellStyle3
        Me.DataGridViewTextBoxColumn20.HeaderText = "السعر"
        Me.DataGridViewTextBoxColumn20.Name = "DataGridViewTextBoxColumn20"
        Me.DataGridViewTextBoxColumn20.ReadOnly = True
        Me.DataGridViewTextBoxColumn20.Width = 80
        '
        'DataGridViewTextBoxColumn21
        '
        Me.DataGridViewTextBoxColumn21.HeaderText = "الخصم"
        Me.DataGridViewTextBoxColumn21.Name = "DataGridViewTextBoxColumn21"
        Me.DataGridViewTextBoxColumn21.ReadOnly = True
        Me.DataGridViewTextBoxColumn21.Visible = False
        Me.DataGridViewTextBoxColumn21.Width = 80
        '
        'DataGridViewTextBoxColumn22
        '
        DataGridViewCellStyle4.Format = "N2"
        DataGridViewCellStyle4.NullValue = Nothing
        Me.DataGridViewTextBoxColumn22.DefaultCellStyle = DataGridViewCellStyle4
        Me.DataGridViewTextBoxColumn22.HeaderText = "الأجمالي"
        Me.DataGridViewTextBoxColumn22.Name = "DataGridViewTextBoxColumn22"
        Me.DataGridViewTextBoxColumn22.ReadOnly = True
        '
        'DataGridViewButtonColumn1
        '
        Me.DataGridViewButtonColumn1.HeaderText = "  "
        Me.DataGridViewButtonColumn1.Name = "DataGridViewButtonColumn1"
        Me.DataGridViewButtonColumn1.ReadOnly = True
        Me.DataGridViewButtonColumn1.Width = 50
        '
        'DataGridViewTextBoxColumn23
        '
        Me.DataGridViewTextBoxColumn23.HeaderText = "الربح"
        Me.DataGridViewTextBoxColumn23.Name = "DataGridViewTextBoxColumn23"
        Me.DataGridViewTextBoxColumn23.ReadOnly = True
        Me.DataGridViewTextBoxColumn23.Visible = False
        '
        'DataGridViewTextBoxColumn24
        '
        Me.DataGridViewTextBoxColumn24.HeaderText = "التصنيف"
        Me.DataGridViewTextBoxColumn24.Name = "DataGridViewTextBoxColumn24"
        Me.DataGridViewTextBoxColumn24.ReadOnly = True
        Me.DataGridViewTextBoxColumn24.Visible = False
        '
        'DataGridViewTextBoxColumn25
        '
        Me.DataGridViewTextBoxColumn25.HeaderText = "الشركة"
        Me.DataGridViewTextBoxColumn25.Name = "DataGridViewTextBoxColumn25"
        Me.DataGridViewTextBoxColumn25.ReadOnly = True
        Me.DataGridViewTextBoxColumn25.Visible = False
        '
        'DataGridViewTextBoxColumn26
        '
        Me.DataGridViewTextBoxColumn26.HeaderText = "البونص"
        Me.DataGridViewTextBoxColumn26.Name = "DataGridViewTextBoxColumn26"
        Me.DataGridViewTextBoxColumn26.ReadOnly = True
        Me.DataGridViewTextBoxColumn26.Visible = False
        '
        'DataGridViewTextBoxColumn27
        '
        Me.DataGridViewTextBoxColumn27.HeaderText = "ضريبة الصنف"
        Me.DataGridViewTextBoxColumn27.Name = "DataGridViewTextBoxColumn27"
        Me.DataGridViewTextBoxColumn27.ReadOnly = True
        Me.DataGridViewTextBoxColumn27.Visible = False
        '
        'DataGridViewTextBoxColumn28
        '
        Me.DataGridViewTextBoxColumn28.HeaderText = "مصنعية التركيب"
        Me.DataGridViewTextBoxColumn28.Name = "DataGridViewTextBoxColumn28"
        Me.DataGridViewTextBoxColumn28.ReadOnly = True
        Me.DataGridViewTextBoxColumn28.Visible = False
        '
        'DataGridViewTextBoxColumn29
        '
        Me.DataGridViewTextBoxColumn29.HeaderText = "الكمية الحاليى"
        Me.DataGridViewTextBoxColumn29.Name = "DataGridViewTextBoxColumn29"
        Me.DataGridViewTextBoxColumn29.ReadOnly = True
        Me.DataGridViewTextBoxColumn29.Visible = False
        '
        'DataGridViewTextBoxColumn30
        '
        Me.DataGridViewTextBoxColumn30.HeaderText = "المجموعة"
        Me.DataGridViewTextBoxColumn30.Name = "DataGridViewTextBoxColumn30"
        Me.DataGridViewTextBoxColumn30.ReadOnly = True
        Me.DataGridViewTextBoxColumn30.Visible = False
        '
        'DataGridViewTextBoxColumn31
        '
        Me.DataGridViewTextBoxColumn31.HeaderText = "الرصيد"
        Me.DataGridViewTextBoxColumn31.Name = "DataGridViewTextBoxColumn31"
        Me.DataGridViewTextBoxColumn31.ReadOnly = True
        Me.DataGridViewTextBoxColumn31.Visible = False
        '
        'Column22
        '
        Me.Column22.HeaderText = "كود الصنف"
        Me.Column22.Name = "Column22"
        Me.Column22.ReadOnly = True
        '
        'Button5
        '
        Me.Button5.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button5.BackColor = System.Drawing.Color.SeaGreen
        Me.Button5.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button5.FlatAppearance.BorderSize = 0
        Me.Button5.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Gold
        Me.Button5.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button5.ForeColor = System.Drawing.Color.White
        Me.Button5.Image = CType(resources.GetObject("Button5.Image"), System.Drawing.Image)
        Me.Button5.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button5.Location = New System.Drawing.Point(3, 265)
        Me.Button5.Name = "Button5"
        Me.Button5.Size = New System.Drawing.Size(197, 50)
        Me.Button5.TabIndex = 1258
        Me.Button5.Text = "طلبية للصنف"
        Me.Button5.UseVisualStyleBackColor = False
        '
        'code3
        '
        Me.code3.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.code3.Location = New System.Drawing.Point(61, 18)
        Me.code3.Name = "code3"
        Me.code3.Size = New System.Drawing.Size(10, 22)
        Me.code3.TabIndex = 280
        Me.code3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.code3.Visible = False
        '
        'code2
        '
        Me.code2.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.code2.Location = New System.Drawing.Point(89, 18)
        Me.code2.Name = "code2"
        Me.code2.Size = New System.Drawing.Size(19, 22)
        Me.code2.TabIndex = 279
        Me.code2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.code2.Visible = False
        '
        'Button4
        '
        Me.Button4.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button4.BackColor = System.Drawing.Color.DarkCyan
        Me.Button4.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button4.FlatAppearance.BorderSize = 0
        Me.Button4.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Gold
        Me.Button4.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button4.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button4.ForeColor = System.Drawing.Color.White
        Me.Button4.Image = CType(resources.GetObject("Button4.Image"), System.Drawing.Image)
        Me.Button4.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button4.Location = New System.Drawing.Point(3, 153)
        Me.Button4.Name = "Button4"
        Me.Button4.Size = New System.Drawing.Size(197, 50)
        Me.Button4.TabIndex = 1257
        Me.Button4.Text = "تعديل اسعار الصنف"
        Me.Button4.UseVisualStyleBackColor = False
        '
        'code
        '
        Me.code.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.code.Location = New System.Drawing.Point(138, 17)
        Me.code.Name = "code"
        Me.code.Size = New System.Drawing.Size(14, 22)
        Me.code.TabIndex = 278
        Me.code.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.code.Visible = False
        '
        'dgv_total
        '
        Me.dgv_total.AllowUserToAddRows = False
        DataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dgv_total.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle6
        Me.dgv_total.BackgroundColor = System.Drawing.Color.WhiteSmoke
        Me.dgv_total.BorderStyle = System.Windows.Forms.BorderStyle.None
        DataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle7.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle7.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle7.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_total.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle7
        Me.dgv_total.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_total.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewComboBoxColumn1, Me.DataGridViewTextBoxColumn14, Me.DataGridViewTextBoxColumn15})
        DataGridViewCellStyle11.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle11.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle11.Font = New System.Drawing.Font("Tahoma", 8.25!)
        DataGridViewCellStyle11.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle11.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle11.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle11.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_total.DefaultCellStyle = DataGridViewCellStyle11
        Me.dgv_total.Location = New System.Drawing.Point(42, 34)
        Me.dgv_total.Name = "dgv_total"
        Me.dgv_total.RowHeadersVisible = False
        Me.dgv_total.RowHeadersWidth = 49
        Me.dgv_total.Size = New System.Drawing.Size(29, 23)
        Me.dgv_total.TabIndex = 1179
        Me.dgv_total.Visible = False
        '
        'DataGridViewComboBoxColumn1
        '
        Me.DataGridViewComboBoxColumn1.DataPropertyName = "item_code"
        DataGridViewCellStyle8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewComboBoxColumn1.DefaultCellStyle = DataGridViewCellStyle8
        Me.DataGridViewComboBoxColumn1.HeaderText = "الكود"
        Me.DataGridViewComboBoxColumn1.Name = "DataGridViewComboBoxColumn1"
        Me.DataGridViewComboBoxColumn1.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewComboBoxColumn1.Width = 30
        '
        'DataGridViewTextBoxColumn14
        '
        Me.DataGridViewTextBoxColumn14.DataPropertyName = "item_name"
        DataGridViewCellStyle9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        DataGridViewCellStyle9.NullValue = Nothing
        Me.DataGridViewTextBoxColumn14.DefaultCellStyle = DataGridViewCellStyle9
        Me.DataGridViewTextBoxColumn14.HeaderText = "اسم الصنف"
        Me.DataGridViewTextBoxColumn14.Name = "DataGridViewTextBoxColumn14"
        Me.DataGridViewTextBoxColumn14.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        '
        'DataGridViewTextBoxColumn15
        '
        Me.DataGridViewTextBoxColumn15.DataPropertyName = "item_count"
        DataGridViewCellStyle10.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewTextBoxColumn15.DefaultCellStyle = DataGridViewCellStyle10
        Me.DataGridViewTextBoxColumn15.HeaderText = "الكمية"
        Me.DataGridViewTextBoxColumn15.Name = "DataGridViewTextBoxColumn15"
        Me.DataGridViewTextBoxColumn15.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn15.Width = 30
        '
        'Button2
        '
        Me.Button2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button2.BackColor = System.Drawing.Color.Gray
        Me.Button2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button2.FlatAppearance.BorderSize = 0
        Me.Button2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Gold
        Me.Button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button2.ForeColor = System.Drawing.Color.White
        Me.Button2.Image = CType(resources.GetObject("Button2.Image"), System.Drawing.Image)
        Me.Button2.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button2.Location = New System.Drawing.Point(3, 209)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(197, 50)
        Me.Button2.TabIndex = 1256
        Me.Button2.Text = "طباعة باركود الصنف"
        Me.Button2.UseVisualStyleBackColor = False
        '
        'Button3
        '
        Me.Button3.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button3.BackColor = System.Drawing.Color.RoyalBlue
        Me.Button3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button3.FlatAppearance.BorderSize = 0
        Me.Button3.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Gold
        Me.Button3.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button3.ForeColor = System.Drawing.Color.White
        Me.Button3.Image = CType(resources.GetObject("Button3.Image"), System.Drawing.Image)
        Me.Button3.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button3.Location = New System.Drawing.Point(3, 97)
        Me.Button3.Name = "Button3"
        Me.Button3.Size = New System.Drawing.Size(197, 50)
        Me.Button3.TabIndex = 1181
        Me.Button3.Text = "اضافة صنف جديد"
        Me.Button3.UseVisualStyleBackColor = False
        '
        'XtraTabPage6
        '
        Me.XtraTabPage6.Controls.Add(Me.GroupBox2)
        Me.XtraTabPage6.Controls.Add(Me.show_itemcus)
        Me.XtraTabPage6.Controls.Add(Me.show_itembuy)
        Me.XtraTabPage6.Controls.Add(Me.block_sale)
        Me.XtraTabPage6.Controls.Add(Me.itemcouta)
        Me.XtraTabPage6.Controls.Add(Me.item_tax_add)
        Me.XtraTabPage6.Controls.Add(Me.tax_add)
        Me.XtraTabPage6.Controls.Add(Me.CusCreditlimit)
        Me.XtraTabPage6.Controls.Add(Me.Cuscusgroup)
        Me.XtraTabPage6.Controls.Add(Me.cusGovernorate)
        Me.XtraTabPage6.Controls.Add(Me.cuscity)
        Me.XtraTabPage6.Controls.Add(Me.cus_price_private)
        Me.XtraTabPage6.Controls.Add(Me.item_group)
        Me.XtraTabPage6.Controls.Add(Me.item_catorgey)
        Me.XtraTabPage6.Controls.Add(Me.item_company)
        Me.XtraTabPage6.Controls.Add(Me.rebh)
        Me.XtraTabPage6.ImageOptions.Image = CType(resources.GetObject("XtraTabPage6.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage6.Name = "XtraTabPage6"
        Me.XtraTabPage6.Size = New System.Drawing.Size(204, 542)
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.SimpleButton1)
        Me.GroupBox2.Controls.Add(Me.item_from)
        Me.GroupBox2.Controls.Add(Me.Label44)
        Me.GroupBox2.Controls.Add(Me.Label35)
        Me.GroupBox2.Controls.Add(Me.item_to)
        Me.GroupBox2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox2.Location = New System.Drawing.Point(10, 127)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(171, 113)
        Me.GroupBox2.TabIndex = 1232
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "بحث بين تاريخين"
        Me.GroupBox2.Visible = False
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseTextOptions = True
        Me.SimpleButton1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton1.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton1.Location = New System.Drawing.Point(12, 78)
        Me.SimpleButton1.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(133, 29)
        Me.SimpleButton1.TabIndex = 1232
        Me.SimpleButton1.Text = "بحث"
        '
        'item_from
        '
        Me.item_from.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_from.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.item_from.Location = New System.Drawing.Point(13, 19)
        Me.item_from.Name = "item_from"
        Me.item_from.Size = New System.Drawing.Size(98, 25)
        Me.item_from.TabIndex = 1230
        Me.item_from.Value = New Date(2019, 4, 30, 0, 0, 0, 0)
        '
        'Label44
        '
        Me.Label44.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label44.Location = New System.Drawing.Point(115, 47)
        Me.Label44.Name = "Label44"
        Me.Label44.Size = New System.Drawing.Size(43, 25)
        Me.Label44.TabIndex = 1160
        Me.Label44.Text = "الي :"
        '
        'Label35
        '
        Me.Label35.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label35.Location = New System.Drawing.Point(112, 21)
        Me.Label35.Name = "Label35"
        Me.Label35.Size = New System.Drawing.Size(43, 25)
        Me.Label35.TabIndex = 1159
        Me.Label35.Text = "من :"
        '
        'item_to
        '
        Me.item_to.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_to.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.item_to.Location = New System.Drawing.Point(13, 50)
        Me.item_to.Name = "item_to"
        Me.item_to.Size = New System.Drawing.Size(98, 25)
        Me.item_to.TabIndex = 1231
        Me.item_to.Value = New Date(2019, 4, 30, 0, 0, 0, 0)
        '
        'show_itemcus
        '
        Me.show_itemcus.AutoSize = True
        Me.show_itemcus.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_itemcus.Location = New System.Drawing.Point(83, 70)
        Me.show_itemcus.Name = "show_itemcus"
        Me.show_itemcus.Size = New System.Drawing.Size(90, 20)
        Me.show_itemcus.TabIndex = 1233
        Me.show_itemcus.Text = "اصناف العميل"
        Me.show_itemcus.UseVisualStyleBackColor = True
        '
        'show_itembuy
        '
        Me.show_itembuy.AutoSize = True
        Me.show_itembuy.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_itembuy.Location = New System.Drawing.Point(88, 46)
        Me.show_itembuy.Name = "show_itembuy"
        Me.show_itembuy.Size = New System.Drawing.Size(87, 20)
        Me.show_itembuy.TabIndex = 289
        Me.show_itembuy.Text = "اصناف حديثة"
        Me.show_itembuy.UseVisualStyleBackColor = True
        '
        'block_sale
        '
        Me.block_sale.BackColor = System.Drawing.Color.White
        Me.block_sale.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.block_sale.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.block_sale.Location = New System.Drawing.Point(119, 264)
        Me.block_sale.Name = "block_sale"
        Me.block_sale.Size = New System.Drawing.Size(18, 18)
        Me.block_sale.TabIndex = 288
        Me.block_sale.Text = "0"
        Me.block_sale.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.block_sale.Visible = False
        '
        'itemcouta
        '
        Me.itemcouta.BackColor = System.Drawing.Color.White
        Me.itemcouta.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.itemcouta.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.itemcouta.Location = New System.Drawing.Point(134, 264)
        Me.itemcouta.Name = "itemcouta"
        Me.itemcouta.Size = New System.Drawing.Size(18, 18)
        Me.itemcouta.TabIndex = 287
        Me.itemcouta.Text = "0"
        Me.itemcouta.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.itemcouta.Visible = False
        '
        'item_tax_add
        '
        Me.item_tax_add.BackColor = System.Drawing.Color.White
        Me.item_tax_add.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.item_tax_add.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_tax_add.Location = New System.Drawing.Point(182, 264)
        Me.item_tax_add.Name = "item_tax_add"
        Me.item_tax_add.Size = New System.Drawing.Size(11, 18)
        Me.item_tax_add.TabIndex = 286
        Me.item_tax_add.Text = "0"
        Me.item_tax_add.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.item_tax_add.Visible = False
        '
        'tax_add
        '
        Me.tax_add.BackColor = System.Drawing.Color.White
        Me.tax_add.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.tax_add.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tax_add.Location = New System.Drawing.Point(157, 264)
        Me.tax_add.Name = "tax_add"
        Me.tax_add.Size = New System.Drawing.Size(22, 18)
        Me.tax_add.TabIndex = 285
        Me.tax_add.Text = "0"
        Me.tax_add.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.tax_add.Visible = False
        '
        'CusCreditlimit
        '
        Me.CusCreditlimit.BackColor = System.Drawing.Color.White
        Me.CusCreditlimit.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.CusCreditlimit.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CusCreditlimit.Location = New System.Drawing.Point(176, 231)
        Me.CusCreditlimit.Name = "CusCreditlimit"
        Me.CusCreditlimit.Size = New System.Drawing.Size(13, 18)
        Me.CusCreditlimit.TabIndex = 273
        Me.CusCreditlimit.Text = "0"
        Me.CusCreditlimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.CusCreditlimit.Visible = False
        '
        'Cuscusgroup
        '
        Me.Cuscusgroup.BackColor = System.Drawing.Color.White
        Me.Cuscusgroup.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Cuscusgroup.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Cuscusgroup.Location = New System.Drawing.Point(157, 231)
        Me.Cuscusgroup.Name = "Cuscusgroup"
        Me.Cuscusgroup.Size = New System.Drawing.Size(13, 18)
        Me.Cuscusgroup.TabIndex = 272
        Me.Cuscusgroup.Text = "0"
        Me.Cuscusgroup.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Cuscusgroup.Visible = False
        '
        'cusGovernorate
        '
        Me.cusGovernorate.BackColor = System.Drawing.Color.White
        Me.cusGovernorate.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.cusGovernorate.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cusGovernorate.Location = New System.Drawing.Point(142, 231)
        Me.cusGovernorate.Name = "cusGovernorate"
        Me.cusGovernorate.Size = New System.Drawing.Size(11, 18)
        Me.cusGovernorate.TabIndex = 271
        Me.cusGovernorate.Text = "0"
        Me.cusGovernorate.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.cusGovernorate.Visible = False
        '
        'cuscity
        '
        Me.cuscity.BackColor = System.Drawing.Color.White
        Me.cuscity.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.cuscity.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cuscity.Location = New System.Drawing.Point(127, 231)
        Me.cuscity.Name = "cuscity"
        Me.cuscity.Size = New System.Drawing.Size(13, 18)
        Me.cuscity.TabIndex = 270
        Me.cuscity.Text = "0"
        Me.cuscity.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.cuscity.Visible = False
        '
        'cus_price_private
        '
        Me.cus_price_private.BackColor = System.Drawing.Color.White
        Me.cus_price_private.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.cus_price_private.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cus_price_private.Location = New System.Drawing.Point(109, 231)
        Me.cus_price_private.Name = "cus_price_private"
        Me.cus_price_private.Size = New System.Drawing.Size(15, 18)
        Me.cus_price_private.TabIndex = 269
        Me.cus_price_private.Text = "0"
        Me.cus_price_private.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.cus_price_private.Visible = False
        '
        'item_group
        '
        Me.item_group.BackColor = System.Drawing.Color.White
        Me.item_group.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.item_group.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_group.Location = New System.Drawing.Point(63, 264)
        Me.item_group.Name = "item_group"
        Me.item_group.Size = New System.Drawing.Size(11, 18)
        Me.item_group.TabIndex = 264
        Me.item_group.Text = "0"
        Me.item_group.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.item_group.Visible = False
        '
        'item_catorgey
        '
        Me.item_catorgey.BackColor = System.Drawing.Color.White
        Me.item_catorgey.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.item_catorgey.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_catorgey.Location = New System.Drawing.Point(38, 264)
        Me.item_catorgey.Name = "item_catorgey"
        Me.item_catorgey.Size = New System.Drawing.Size(22, 18)
        Me.item_catorgey.TabIndex = 263
        Me.item_catorgey.Text = "0"
        Me.item_catorgey.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.item_catorgey.Visible = False
        '
        'item_company
        '
        Me.item_company.BackColor = System.Drawing.Color.White
        Me.item_company.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.item_company.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_company.Location = New System.Drawing.Point(3, 459)
        Me.item_company.Name = "item_company"
        Me.item_company.Size = New System.Drawing.Size(18, 18)
        Me.item_company.TabIndex = 262
        Me.item_company.Text = "0"
        Me.item_company.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.item_company.Visible = False
        '
        'rebh
        '
        Me.rebh.AutoSize = True
        Me.rebh.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rebh.Location = New System.Drawing.Point(37, 21)
        Me.rebh.Name = "rebh"
        Me.rebh.Size = New System.Drawing.Size(130, 20)
        Me.rebh.TabIndex = 261
        Me.rebh.Text = "ربح الفاتورة         f11"
        Me.rebh.UseVisualStyleBackColor = True
        '
        'XtraTabPage7
        '
        Me.XtraTabPage7.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabPage7.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage7.Appearance.HeaderActive.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.XtraTabPage7.Appearance.HeaderActive.Options.UseFont = True
        Me.XtraTabPage7.Appearance.HeaderDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.XtraTabPage7.Appearance.HeaderDisabled.Options.UseFont = True
        Me.XtraTabPage7.Appearance.HeaderHotTracked.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.XtraTabPage7.Appearance.HeaderHotTracked.Options.UseFont = True
        Me.XtraTabPage7.Appearance.PageClient.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.XtraTabPage7.Appearance.PageClient.Options.UseFont = True
        Me.XtraTabPage7.Controls.Add(Me.dgv_2)
        Me.XtraTabPage7.Controls.Add(Me.TextBox23)
        Me.XtraTabPage7.Controls.Add(Me.count_text)
        Me.XtraTabPage7.Controls.Add(Me.Alternativeitem)
        Me.XtraTabPage7.Controls.Add(Me.code_invoice)
        Me.XtraTabPage7.Controls.Add(Me.code_pharche)
        Me.XtraTabPage7.Controls.Add(Me.item_notes)
        Me.XtraTabPage7.Controls.Add(Me.TextBox9)
        Me.XtraTabPage7.Controls.Add(Me.show_count)
        Me.XtraTabPage7.Controls.Add(Me.show_buy)
        Me.XtraTabPage7.Controls.Add(Me.show_datebuy)
        Me.XtraTabPage7.Controls.Add(Me.show_datesale)
        Me.XtraTabPage7.Controls.Add(Me.TextBox17)
        Me.XtraTabPage7.Controls.Add(Me.Label11)
        Me.XtraTabPage7.Controls.Add(Me.TextBox11)
        Me.XtraTabPage7.Controls.Add(Me.TextBox12)
        Me.XtraTabPage7.Controls.Add(Me.TextBox21)
        Me.XtraTabPage7.Controls.Add(Me.DataGridView1)
        Me.XtraTabPage7.ImageOptions.Image = CType(resources.GetObject("XtraTabPage7.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage7.Name = "XtraTabPage7"
        Me.XtraTabPage7.Size = New System.Drawing.Size(204, 542)
        '
        'dgv_2
        '
        Me.dgv_2.AllowUserToAddRows = False
        Me.dgv_2.AllowUserToDeleteRows = False
        DataGridViewCellStyle12.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        DataGridViewCellStyle12.SelectionBackColor = System.Drawing.Color.SeaGreen
        Me.dgv_2.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle12
        Me.dgv_2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.dgv_2.BackgroundColor = System.Drawing.Color.White
        Me.dgv_2.BorderStyle = System.Windows.Forms.BorderStyle.None
        DataGridViewCellStyle13.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle13.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        DataGridViewCellStyle13.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle13.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle13.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle13.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_2.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle13
        Me.dgv_2.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_2.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn1})
        DataGridViewCellStyle14.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle14.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        DataGridViewCellStyle14.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle14.SelectionBackColor = System.Drawing.Color.SeaGreen
        DataGridViewCellStyle14.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle14.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_2.DefaultCellStyle = DataGridViewCellStyle14
        Me.dgv_2.Location = New System.Drawing.Point(2, 330)
        Me.dgv_2.Name = "dgv_2"
        Me.dgv_2.ReadOnly = True
        Me.dgv_2.RowHeadersVisible = False
        Me.dgv_2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_2.Size = New System.Drawing.Size(204, 148)
        Me.dgv_2.TabIndex = 247
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.DataPropertyName = "Alternativeitem"
        Me.DataGridViewTextBoxColumn1.HeaderText = "اسم النصف"
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        Me.DataGridViewTextBoxColumn1.Width = 200
        '
        'TextBox23
        '
        Me.TextBox23.BackColor = System.Drawing.Color.White
        Me.TextBox23.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox23.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox23.ForeColor = System.Drawing.Color.Black
        Me.TextBox23.Location = New System.Drawing.Point(154, 8)
        Me.TextBox23.Name = "TextBox23"
        Me.TextBox23.Size = New System.Drawing.Size(46, 14)
        Me.TextBox23.TabIndex = 248
        Me.TextBox23.Text = "الكمية :"
        '
        'count_text
        '
        Me.count_text.BackColor = System.Drawing.Color.White
        Me.count_text.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.count_text.Cursor = System.Windows.Forms.Cursors.Default
        Me.count_text.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.count_text.ForeColor = System.Drawing.Color.Blue
        Me.count_text.Location = New System.Drawing.Point(-1, 10)
        Me.count_text.Name = "count_text"
        Me.count_text.ReadOnly = True
        Me.count_text.Size = New System.Drawing.Size(164, 15)
        Me.count_text.TabIndex = 1250
        '
        'Alternativeitem
        '
        Me.Alternativeitem.AutoSize = True
        Me.Alternativeitem.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Alternativeitem.Location = New System.Drawing.Point(-2, 306)
        Me.Alternativeitem.Name = "Alternativeitem"
        Me.Alternativeitem.Size = New System.Drawing.Size(91, 17)
        Me.Alternativeitem.TabIndex = 271
        Me.Alternativeitem.Text = "الاصناف البديلة"
        Me.Alternativeitem.UseVisualStyleBackColor = True
        '
        'code_invoice
        '
        Me.code_invoice.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.code_invoice.AutoSize = True
        Me.code_invoice.BackColor = System.Drawing.Color.WhiteSmoke
        Me.code_invoice.Font = New System.Drawing.Font("Arial", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(178, Byte))
        Me.code_invoice.ForeColor = System.Drawing.Color.Black
        Me.code_invoice.Location = New System.Drawing.Point(55, 349)
        Me.code_invoice.Name = "code_invoice"
        Me.code_invoice.Size = New System.Drawing.Size(13, 14)
        Me.code_invoice.TabIndex = 273
        Me.code_invoice.Text = "0"
        Me.code_invoice.Visible = False
        '
        'code_pharche
        '
        Me.code_pharche.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.code_pharche.AutoSize = True
        Me.code_pharche.BackColor = System.Drawing.Color.WhiteSmoke
        Me.code_pharche.Font = New System.Drawing.Font("Arial", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(178, Byte))
        Me.code_pharche.ForeColor = System.Drawing.Color.Black
        Me.code_pharche.Location = New System.Drawing.Point(36, 349)
        Me.code_pharche.Name = "code_pharche"
        Me.code_pharche.Size = New System.Drawing.Size(13, 14)
        Me.code_pharche.TabIndex = 272
        Me.code_pharche.Text = "0"
        Me.code_pharche.Visible = False
        '
        'item_notes
        '
        Me.item_notes.BackColor = System.Drawing.Color.White
        Me.item_notes.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.item_notes.Cursor = System.Windows.Forms.Cursors.Default
        Me.item_notes.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_notes.Location = New System.Drawing.Point(7, 118)
        Me.item_notes.Multiline = True
        Me.item_notes.Name = "item_notes"
        Me.item_notes.ReadOnly = True
        Me.item_notes.Size = New System.Drawing.Size(142, 42)
        Me.item_notes.TabIndex = 270
        '
        'TextBox9
        '
        Me.TextBox9.BackColor = System.Drawing.Color.White
        Me.TextBox9.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox9.Cursor = System.Windows.Forms.Cursors.Default
        Me.TextBox9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox9.ForeColor = System.Drawing.Color.Black
        Me.TextBox9.Location = New System.Drawing.Point(110, 111)
        Me.TextBox9.Name = "TextBox9"
        Me.TextBox9.ReadOnly = True
        Me.TextBox9.Size = New System.Drawing.Size(89, 14)
        Me.TextBox9.TabIndex = 269
        Me.TextBox9.Text = "الوصف :"
        '
        'show_count
        '
        Me.show_count.BackColor = System.Drawing.Color.White
        Me.show_count.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.show_count.Cursor = System.Windows.Forms.Cursors.Default
        Me.show_count.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_count.Location = New System.Drawing.Point(10, 15)
        Me.show_count.Name = "show_count"
        Me.show_count.ReadOnly = True
        Me.show_count.Size = New System.Drawing.Size(77, 18)
        Me.show_count.TabIndex = 263
        Me.show_count.Text = "0"
        Me.show_count.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'show_buy
        '
        Me.show_buy.BackColor = System.Drawing.Color.White
        Me.show_buy.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.show_buy.Cursor = System.Windows.Forms.Cursors.Default
        Me.show_buy.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_buy.Location = New System.Drawing.Point(7, 40)
        Me.show_buy.Name = "show_buy"
        Me.show_buy.ReadOnly = True
        Me.show_buy.Size = New System.Drawing.Size(122, 18)
        Me.show_buy.TabIndex = 262
        Me.show_buy.Text = "0"
        Me.show_buy.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'show_datebuy
        '
        Me.show_datebuy.BackColor = System.Drawing.Color.White
        Me.show_datebuy.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.show_datebuy.Cursor = System.Windows.Forms.Cursors.Default
        Me.show_datebuy.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_datebuy.Location = New System.Drawing.Point(7, 66)
        Me.show_datebuy.Name = "show_datebuy"
        Me.show_datebuy.ReadOnly = True
        Me.show_datebuy.Size = New System.Drawing.Size(116, 18)
        Me.show_datebuy.TabIndex = 258
        Me.show_datebuy.Text = "0"
        Me.show_datebuy.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'show_datesale
        '
        Me.show_datesale.BackColor = System.Drawing.Color.White
        Me.show_datesale.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.show_datesale.Cursor = System.Windows.Forms.Cursors.Default
        Me.show_datesale.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_datesale.Location = New System.Drawing.Point(7, 89)
        Me.show_datesale.Name = "show_datesale"
        Me.show_datesale.ReadOnly = True
        Me.show_datesale.Size = New System.Drawing.Size(116, 18)
        Me.show_datesale.TabIndex = 249
        Me.show_datesale.Text = "0"
        Me.show_datesale.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'TextBox17
        '
        Me.TextBox17.BackColor = System.Drawing.Color.White
        Me.TextBox17.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox17.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox17.ForeColor = System.Drawing.Color.Maroon
        Me.TextBox17.Location = New System.Drawing.Point(95, 306)
        Me.TextBox17.Name = "TextBox17"
        Me.TextBox17.Size = New System.Drawing.Size(119, 15)
        Me.TextBox17.TabIndex = 257
        Me.TextBox17.Text = "الاصناف البديلة"
        Me.TextBox17.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label11
        '
        Me.Label11.BackColor = System.Drawing.Color.Black
        Me.Label11.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.ForeColor = System.Drawing.Color.White
        Me.Label11.Location = New System.Drawing.Point(2, 302)
        Me.Label11.Margin = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(200, 1)
        Me.Label11.TabIndex = 256
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TextBox11
        '
        Me.TextBox11.BackColor = System.Drawing.Color.White
        Me.TextBox11.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox11.Cursor = System.Windows.Forms.Cursors.Default
        Me.TextBox11.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox11.ForeColor = System.Drawing.Color.Black
        Me.TextBox11.Location = New System.Drawing.Point(110, 86)
        Me.TextBox11.Name = "TextBox11"
        Me.TextBox11.ReadOnly = True
        Me.TextBox11.Size = New System.Drawing.Size(89, 14)
        Me.TextBox11.TabIndex = 255
        Me.TextBox11.Text = "أخر تاريخ بيع :"
        '
        'TextBox12
        '
        Me.TextBox12.BackColor = System.Drawing.Color.White
        Me.TextBox12.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox12.Cursor = System.Windows.Forms.Cursors.Default
        Me.TextBox12.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox12.ForeColor = System.Drawing.Color.Black
        Me.TextBox12.Location = New System.Drawing.Point(110, 63)
        Me.TextBox12.Name = "TextBox12"
        Me.TextBox12.ReadOnly = True
        Me.TextBox12.Size = New System.Drawing.Size(89, 14)
        Me.TextBox12.TabIndex = 254
        Me.TextBox12.Text = "أخر تاريخ شراء :"
        '
        'TextBox21
        '
        Me.TextBox21.BackColor = System.Drawing.Color.White
        Me.TextBox21.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox21.Cursor = System.Windows.Forms.Cursors.Default
        Me.TextBox21.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox21.ForeColor = System.Drawing.Color.Black
        Me.TextBox21.Location = New System.Drawing.Point(130, 39)
        Me.TextBox21.Name = "TextBox21"
        Me.TextBox21.ReadOnly = True
        Me.TextBox21.Size = New System.Drawing.Size(70, 14)
        Me.TextBox21.TabIndex = 250
        Me.TextBox21.Text = "سعر الشراء :"
        '
        'DataGridView1
        '
        Me.DataGridView1.AllowUserToAddRows = False
        Me.DataGridView1.AllowUserToDeleteRows = False
        DataGridViewCellStyle15.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle15.SelectionBackColor = System.Drawing.Color.SeaGreen
        Me.DataGridView1.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle15
        Me.DataGridView1.BackgroundColor = System.Drawing.Color.White
        Me.DataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None
        DataGridViewCellStyle16.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle16.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        DataGridViewCellStyle16.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle16.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle16.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle16.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridView1.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle16
        Me.DataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.DataGridView1.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn11, Me.DataGridViewTextBoxColumn12, Me.DataGridViewTextBoxColumn13})
        DataGridViewCellStyle17.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle17.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle17.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        DataGridViewCellStyle17.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle17.SelectionBackColor = System.Drawing.Color.SeaGreen
        DataGridViewCellStyle17.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle17.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.DataGridView1.DefaultCellStyle = DataGridViewCellStyle17
        Me.DataGridView1.Location = New System.Drawing.Point(3, 166)
        Me.DataGridView1.Name = "DataGridView1"
        Me.DataGridView1.ReadOnly = True
        Me.DataGridView1.RowHeadersVisible = False
        Me.DataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DataGridView1.Size = New System.Drawing.Size(202, 121)
        Me.DataGridView1.TabIndex = 1249
        '
        'DataGridViewTextBoxColumn11
        '
        Me.DataGridViewTextBoxColumn11.DataPropertyName = "unit"
        Me.DataGridViewTextBoxColumn11.HeaderText = "الوحدة"
        Me.DataGridViewTextBoxColumn11.Name = "DataGridViewTextBoxColumn11"
        Me.DataGridViewTextBoxColumn11.ReadOnly = True
        Me.DataGridViewTextBoxColumn11.Width = 135
        '
        'DataGridViewTextBoxColumn12
        '
        Me.DataGridViewTextBoxColumn12.DataPropertyName = "count"
        Me.DataGridViewTextBoxColumn12.HeaderText = "الكمية"
        Me.DataGridViewTextBoxColumn12.Name = "DataGridViewTextBoxColumn12"
        Me.DataGridViewTextBoxColumn12.ReadOnly = True
        Me.DataGridViewTextBoxColumn12.Width = 65
        '
        'DataGridViewTextBoxColumn13
        '
        Me.DataGridViewTextBoxColumn13.HeaderText = "العدد"
        Me.DataGridViewTextBoxColumn13.Name = "DataGridViewTextBoxColumn13"
        Me.DataGridViewTextBoxColumn13.ReadOnly = True
        Me.DataGridViewTextBoxColumn13.Visible = False
        '
        'XtraTabPage8
        '
        Me.XtraTabPage8.Controls.Add(Me.dgv_3)
        Me.XtraTabPage8.Controls.Add(Me.Label40)
        Me.XtraTabPage8.Controls.Add(Me.report_imp)
        Me.XtraTabPage8.Controls.Add(Me.Label50)
        Me.XtraTabPage8.Controls.Add(Me.Accounts_phone2)
        Me.XtraTabPage8.Controls.Add(Me.day_sale)
        Me.XtraTabPage8.Controls.Add(Me.TextBox28)
        Me.XtraTabPage8.Controls.Add(Me.Accounts_phone1)
        Me.XtraTabPage8.Controls.Add(Me.Label43)
        Me.XtraTabPage8.Controls.Add(Me.Accounts_adress)
        Me.XtraTabPage8.Controls.Add(Me.Label42)
        Me.XtraTabPage8.Controls.Add(Me.Label38)
        Me.XtraTabPage8.Controls.Add(Me.balace_show)
        Me.XtraTabPage8.Controls.Add(Me.Label41)
        Me.XtraTabPage8.ImageOptions.Image = CType(resources.GetObject("XtraTabPage8.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage8.Name = "XtraTabPage8"
        Me.XtraTabPage8.Size = New System.Drawing.Size(204, 542)
        '
        'dgv_3
        '
        Me.dgv_3.AllowUserToAddRows = False
        Me.dgv_3.AllowUserToDeleteRows = False
        DataGridViewCellStyle18.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle18.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle18.Font = New System.Drawing.Font("Arial", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle18.NullValue = "30/04"
        DataGridViewCellStyle18.SelectionBackColor = System.Drawing.Color.SeaGreen
        Me.dgv_3.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle18
        Me.dgv_3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.dgv_3.BackgroundColor = System.Drawing.Color.White
        Me.dgv_3.BorderStyle = System.Windows.Forms.BorderStyle.None
        DataGridViewCellStyle19.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle19.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle19.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle19.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle19.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle19.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle19.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_3.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle19
        Me.dgv_3.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_3.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.Column9, Me.Column10, Me.Column11})
        DataGridViewCellStyle21.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle21.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle21.Font = New System.Drawing.Font("Tahoma", 8.25!)
        DataGridViewCellStyle21.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle21.SelectionBackColor = System.Drawing.Color.SeaGreen
        DataGridViewCellStyle21.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle21.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_3.DefaultCellStyle = DataGridViewCellStyle21
        Me.dgv_3.Location = New System.Drawing.Point(4, 254)
        Me.dgv_3.Name = "dgv_3"
        Me.dgv_3.ReadOnly = True
        Me.dgv_3.RowHeadersVisible = False
        Me.dgv_3.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_3.Size = New System.Drawing.Size(201, 224)
        Me.dgv_3.TabIndex = 249
        '
        'Column9
        '
        Me.Column9.DataPropertyName = "custrans_type"
        Me.Column9.HeaderText = "التعامل"
        Me.Column9.Name = "Column9"
        Me.Column9.ReadOnly = True
        '
        'Column10
        '
        Me.Column10.DataPropertyName = "custrans_date"
        DataGridViewCellStyle20.Format = "d"
        DataGridViewCellStyle20.NullValue = "0"
        Me.Column10.DefaultCellStyle = DataGridViewCellStyle20
        Me.Column10.HeaderText = "التاريخ"
        Me.Column10.Name = "Column10"
        Me.Column10.ReadOnly = True
        Me.Column10.Width = 55
        '
        'Column11
        '
        Me.Column11.DataPropertyName = "customer_pay"
        Me.Column11.HeaderText = "القيمة"
        Me.Column11.Name = "Column11"
        Me.Column11.ReadOnly = True
        Me.Column11.Width = 45
        '
        'Label40
        '
        Me.Label40.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label40.BackColor = System.Drawing.SystemColors.Window
        Me.Label40.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label40.ForeColor = System.Drawing.Color.Black
        Me.Label40.Location = New System.Drawing.Point(23, 205)
        Me.Label40.Name = "Label40"
        Me.Label40.Size = New System.Drawing.Size(29, 22)
        Me.Label40.TabIndex = 260
        Me.Label40.Text = "يوم"
        '
        'report_imp
        '
        Me.report_imp.AutoSize = True
        Me.report_imp.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.report_imp.Location = New System.Drawing.Point(-1, 231)
        Me.report_imp.Name = "report_imp"
        Me.report_imp.Size = New System.Drawing.Size(78, 17)
        Me.report_imp.TabIndex = 259
        Me.report_imp.Text = "تعامل عميل"
        Me.report_imp.UseVisualStyleBackColor = True
        '
        'Label50
        '
        Me.Label50.BackColor = System.Drawing.Color.Black
        Me.Label50.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label50.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label50.ForeColor = System.Drawing.Color.White
        Me.Label50.Location = New System.Drawing.Point(2, 201)
        Me.Label50.Margin = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.Label50.Name = "Label50"
        Me.Label50.Size = New System.Drawing.Size(200, 1)
        Me.Label50.TabIndex = 258
        Me.Label50.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Accounts_phone2
        '
        Me.Accounts_phone2.BackColor = System.Drawing.Color.White
        Me.Accounts_phone2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Accounts_phone2.Cursor = System.Windows.Forms.Cursors.Default
        Me.Accounts_phone2.Enabled = False
        Me.Accounts_phone2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Accounts_phone2.ForeColor = System.Drawing.Color.Black
        Me.Accounts_phone2.Location = New System.Drawing.Point(12, 149)
        Me.Accounts_phone2.Name = "Accounts_phone2"
        Me.Accounts_phone2.ReadOnly = True
        Me.Accounts_phone2.Size = New System.Drawing.Size(107, 14)
        Me.Accounts_phone2.TabIndex = 257
        Me.Accounts_phone2.Text = "0"
        Me.Accounts_phone2.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'day_sale
        '
        Me.day_sale.BackColor = System.Drawing.Color.White
        Me.day_sale.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.day_sale.Cursor = System.Windows.Forms.Cursors.Default
        Me.day_sale.Enabled = False
        Me.day_sale.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.day_sale.ForeColor = System.Drawing.Color.Black
        Me.day_sale.Location = New System.Drawing.Point(51, 208)
        Me.day_sale.Name = "day_sale"
        Me.day_sale.ReadOnly = True
        Me.day_sale.Size = New System.Drawing.Size(68, 19)
        Me.day_sale.TabIndex = 251
        Me.day_sale.Text = "0"
        Me.day_sale.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'TextBox28
        '
        Me.TextBox28.BackColor = System.Drawing.Color.White
        Me.TextBox28.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox28.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox28.ForeColor = System.Drawing.Color.Black
        Me.TextBox28.Location = New System.Drawing.Point(116, 206)
        Me.TextBox28.Name = "TextBox28"
        Me.TextBox28.Size = New System.Drawing.Size(91, 14)
        Me.TextBox28.TabIndex = 250
        Me.TextBox28.Text = "أخر تعامل منذ"
        Me.TextBox28.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Accounts_phone1
        '
        Me.Accounts_phone1.BackColor = System.Drawing.Color.White
        Me.Accounts_phone1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Accounts_phone1.Cursor = System.Windows.Forms.Cursors.Default
        Me.Accounts_phone1.Enabled = False
        Me.Accounts_phone1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Accounts_phone1.ForeColor = System.Drawing.Color.Black
        Me.Accounts_phone1.Location = New System.Drawing.Point(12, 112)
        Me.Accounts_phone1.Name = "Accounts_phone1"
        Me.Accounts_phone1.ReadOnly = True
        Me.Accounts_phone1.Size = New System.Drawing.Size(107, 14)
        Me.Accounts_phone1.TabIndex = 255
        Me.Accounts_phone1.Text = "0"
        Me.Accounts_phone1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Label43
        '
        Me.Label43.BackColor = System.Drawing.SystemColors.Window
        Me.Label43.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label43.ForeColor = System.Drawing.Color.Black
        Me.Label43.Location = New System.Drawing.Point(146, 149)
        Me.Label43.Name = "Label43"
        Me.Label43.Size = New System.Drawing.Size(59, 22)
        Me.Label43.TabIndex = 256
        Me.Label43.Text = "هاتف 2 :"
        '
        'Accounts_adress
        '
        Me.Accounts_adress.BackColor = System.Drawing.SystemColors.Window
        Me.Accounts_adress.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Accounts_adress.Cursor = System.Windows.Forms.Cursors.Default
        Me.Accounts_adress.Enabled = False
        Me.Accounts_adress.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Accounts_adress.ForeColor = System.Drawing.Color.Black
        Me.Accounts_adress.Location = New System.Drawing.Point(12, 67)
        Me.Accounts_adress.Multiline = True
        Me.Accounts_adress.Name = "Accounts_adress"
        Me.Accounts_adress.ReadOnly = True
        Me.Accounts_adress.Size = New System.Drawing.Size(195, 42)
        Me.Accounts_adress.TabIndex = 253
        Me.Accounts_adress.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Label42
        '
        Me.Label42.BackColor = System.Drawing.SystemColors.Window
        Me.Label42.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label42.ForeColor = System.Drawing.Color.Black
        Me.Label42.Location = New System.Drawing.Point(146, 112)
        Me.Label42.Name = "Label42"
        Me.Label42.Size = New System.Drawing.Size(59, 22)
        Me.Label42.TabIndex = 254
        Me.Label42.Text = "هاتف 1 :"
        '
        'Label38
        '
        Me.Label38.BackColor = System.Drawing.SystemColors.Window
        Me.Label38.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label38.ForeColor = System.Drawing.Color.Maroon
        Me.Label38.Location = New System.Drawing.Point(155, 7)
        Me.Label38.Name = "Label38"
        Me.Label38.Size = New System.Drawing.Size(50, 22)
        Me.Label38.TabIndex = 247
        Me.Label38.Text = "الرصيد :"
        '
        'balace_show
        '
        Me.balace_show.BackColor = System.Drawing.Color.White
        Me.balace_show.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.balace_show.Cursor = System.Windows.Forms.Cursors.Default
        Me.balace_show.Enabled = False
        Me.balace_show.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.balace_show.ForeColor = System.Drawing.Color.Maroon
        Me.balace_show.Location = New System.Drawing.Point(12, 9)
        Me.balace_show.Name = "balace_show"
        Me.balace_show.ReadOnly = True
        Me.balace_show.Size = New System.Drawing.Size(127, 14)
        Me.balace_show.TabIndex = 248
        Me.balace_show.Text = "0"
        Me.balace_show.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Label41
        '
        Me.Label41.BackColor = System.Drawing.SystemColors.Window
        Me.Label41.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label41.ForeColor = System.Drawing.Color.Black
        Me.Label41.Location = New System.Drawing.Point(159, 45)
        Me.Label41.Name = "Label41"
        Me.Label41.Size = New System.Drawing.Size(46, 22)
        Me.Label41.TabIndex = 252
        Me.Label41.Text = "العنوان"
        '
        'TextBox3
        '
        Me.TextBox3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TextBox3.BackColor = System.Drawing.Color.White
        Me.TextBox3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox3.Cursor = System.Windows.Forms.Cursors.Default
        Me.TextBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox3.ForeColor = System.Drawing.Color.Black
        Me.TextBox3.Location = New System.Drawing.Point(227, 439)
        Me.TextBox3.Name = "TextBox3"
        Me.TextBox3.ReadOnly = True
        Me.TextBox3.Size = New System.Drawing.Size(40, 15)
        Me.TextBox3.TabIndex = 1352
        Me.TextBox3.Text = "الربح :"
        Me.TextBox3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.TextBox3.Visible = False
        '
        'earn_invoice
        '
        Me.earn_invoice.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.earn_invoice.BackColor = System.Drawing.Color.White
        Me.earn_invoice.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.earn_invoice.Cursor = System.Windows.Forms.Cursors.Default
        Me.earn_invoice.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.earn_invoice.ForeColor = System.Drawing.Color.Black
        Me.earn_invoice.Location = New System.Drawing.Point(139, 443)
        Me.earn_invoice.Name = "earn_invoice"
        Me.earn_invoice.ReadOnly = True
        Me.earn_invoice.Size = New System.Drawing.Size(87, 18)
        Me.earn_invoice.TabIndex = 1351
        Me.earn_invoice.Text = "0"
        Me.earn_invoice.Visible = False
        '
        'ContextMenuStrip1
        '
        Me.ContextMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.A4ToolStripMenuItem, Me.A5ToolStripMenuItem, Me.PosToolStripMenuItem})
        Me.ContextMenuStrip1.Name = "ContextMenuStrip1"
        Me.ContextMenuStrip1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.ContextMenuStrip1.Size = New System.Drawing.Size(94, 70)
        '
        'A4ToolStripMenuItem
        '
        Me.A4ToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.شكل1ToolStripMenuItem, Me.شكل2ToolStripMenuItem})
        Me.A4ToolStripMenuItem.Name = "A4ToolStripMenuItem"
        Me.A4ToolStripMenuItem.Size = New System.Drawing.Size(93, 22)
        Me.A4ToolStripMenuItem.Text = "a4"
        '
        'شكل1ToolStripMenuItem
        '
        Me.شكل1ToolStripMenuItem.Name = "شكل1ToolStripMenuItem"
        Me.شكل1ToolStripMenuItem.Size = New System.Drawing.Size(107, 22)
        Me.شكل1ToolStripMenuItem.Text = "شكل 1"
        '
        'شكل2ToolStripMenuItem
        '
        Me.شكل2ToolStripMenuItem.Name = "شكل2ToolStripMenuItem"
        Me.شكل2ToolStripMenuItem.Size = New System.Drawing.Size(107, 22)
        Me.شكل2ToolStripMenuItem.Text = "شكل 2"
        '
        'A5ToolStripMenuItem
        '
        Me.A5ToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.شكل1ToolStripMenuItem1, Me.شكل2ToolStripMenuItem1})
        Me.A5ToolStripMenuItem.Name = "A5ToolStripMenuItem"
        Me.A5ToolStripMenuItem.Size = New System.Drawing.Size(93, 22)
        Me.A5ToolStripMenuItem.Text = "a5"
        '
        'شكل1ToolStripMenuItem1
        '
        Me.شكل1ToolStripMenuItem1.Name = "شكل1ToolStripMenuItem1"
        Me.شكل1ToolStripMenuItem1.Size = New System.Drawing.Size(107, 22)
        Me.شكل1ToolStripMenuItem1.Text = "شكل 1"
        '
        'شكل2ToolStripMenuItem1
        '
        Me.شكل2ToolStripMenuItem1.Name = "شكل2ToolStripMenuItem1"
        Me.شكل2ToolStripMenuItem1.Size = New System.Drawing.Size(107, 22)
        Me.شكل2ToolStripMenuItem1.Text = "شكل 2"
        '
        'PosToolStripMenuItem
        '
        Me.PosToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.شكل1ToolStripMenuItem2, Me.شكل2ToolStripMenuItem2})
        Me.PosToolStripMenuItem.Name = "PosToolStripMenuItem"
        Me.PosToolStripMenuItem.Size = New System.Drawing.Size(93, 22)
        Me.PosToolStripMenuItem.Text = "pos"
        '
        'شكل1ToolStripMenuItem2
        '
        Me.شكل1ToolStripMenuItem2.Name = "شكل1ToolStripMenuItem2"
        Me.شكل1ToolStripMenuItem2.Size = New System.Drawing.Size(107, 22)
        Me.شكل1ToolStripMenuItem2.Text = "شكل 1"
        '
        'شكل2ToolStripMenuItem2
        '
        Me.شكل2ToolStripMenuItem2.Name = "شكل2ToolStripMenuItem2"
        Me.شكل2ToolStripMenuItem2.Size = New System.Drawing.Size(107, 22)
        Me.شكل2ToolStripMenuItem2.Text = "شكل 2"
        '
        'lastpay
        '
        Me.lastpay.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lastpay.Location = New System.Drawing.Point(216, 586)
        Me.lastpay.Name = "lastpay"
        Me.lastpay.Size = New System.Drawing.Size(32, 22)
        Me.lastpay.TabIndex = 1180
        Me.lastpay.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.lastpay.Visible = False
        '
        'Label33
        '
        Me.Label33.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label33.AutoSize = True
        Me.Label33.BackColor = System.Drawing.Color.Black
        Me.Label33.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label33.ForeColor = System.Drawing.Color.White
        Me.Label33.Location = New System.Drawing.Point(865, 620)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(68, 16)
        Me.Label33.TabIndex = 1231
        Me.Label33.Text = "نوع التعامل :"
        '
        'Label22
        '
        Me.Label22.AutoSize = True
        Me.Label22.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.Location = New System.Drawing.Point(1033, 46)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(19, 13)
        Me.Label22.TabIndex = 1230
        Me.Label22.Text = "00"
        Me.Label22.Visible = False
        '
        'Label21
        '
        Me.Label21.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label21.AutoSize = True
        Me.Label21.BackColor = System.Drawing.Color.Black
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.ForeColor = System.Drawing.Color.White
        Me.Label21.Location = New System.Drawing.Point(1142, 620)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(71, 16)
        Me.Label21.TabIndex = 1201
        Me.Label21.Text = "أسم المندوب :"
        '
        'Label20
        '
        Me.Label20.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label20.BackColor = System.Drawing.Color.Black
        Me.Label20.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.White
        Me.Label20.Location = New System.Drawing.Point(2, 613)
        Me.Label20.Margin = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(1235, 43)
        Me.Label20.TabIndex = 1200
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label10
        '
        Me.Label10.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(178, Byte))
        Me.Label10.ForeColor = System.Drawing.Color.Gold
        Me.Label10.Image = CType(resources.GetObject("Label10.Image"), System.Drawing.Image)
        Me.Label10.Location = New System.Drawing.Point(576, -3)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(119, 29)
        Me.Label10.TabIndex = 1252
        Me.Label10.Text = "فاتورة مبيعات"
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.Image = CType(resources.GetObject("PictureBox1.Image"), System.Drawing.Image)
        Me.PictureBox1.Location = New System.Drawing.Point(-1, -6)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(1240, 46)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox1.TabIndex = 1253
        Me.PictureBox1.TabStop = False
        '
        'new_btn
        '
        Me.new_btn.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.new_btn.BackColor = System.Drawing.Color.Gainsboro
        Me.new_btn.BackgroundImage = CType(resources.GetObject("new_btn.BackgroundImage"), System.Drawing.Image)
        Me.new_btn.Cursor = System.Windows.Forms.Cursors.Hand
        Me.new_btn.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.new_btn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.new_btn.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.new_btn.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.new_btn.Image = CType(resources.GetObject("new_btn.Image"), System.Drawing.Image)
        Me.new_btn.ImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.new_btn.Location = New System.Drawing.Point(1157, 41)
        Me.new_btn.Name = "new_btn"
        Me.new_btn.Size = New System.Drawing.Size(70, 27)
        Me.new_btn.TabIndex = 1256
        Me.new_btn.Text = "جديد"
        Me.new_btn.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.new_btn.UseVisualStyleBackColor = False
        '
        'save_btn
        '
        Me.save_btn.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.save_btn.BackColor = System.Drawing.Color.Gainsboro
        Me.save_btn.BackgroundImage = CType(resources.GetObject("save_btn.BackgroundImage"), System.Drawing.Image)
        Me.save_btn.Cursor = System.Windows.Forms.Cursors.Hand
        Me.save_btn.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.save_btn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.save_btn.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.save_btn.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.save_btn.Image = CType(resources.GetObject("save_btn.Image"), System.Drawing.Image)
        Me.save_btn.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.save_btn.Location = New System.Drawing.Point(1080, 41)
        Me.save_btn.Name = "save_btn"
        Me.save_btn.Size = New System.Drawing.Size(75, 27)
        Me.save_btn.TabIndex = 1257
        Me.save_btn.Text = "حفظ"
        Me.save_btn.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.save_btn.UseVisualStyleBackColor = False
        '
        'edit_btn
        '
        Me.edit_btn.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.edit_btn.BackColor = System.Drawing.Color.Gainsboro
        Me.edit_btn.BackgroundImage = CType(resources.GetObject("edit_btn.BackgroundImage"), System.Drawing.Image)
        Me.edit_btn.Cursor = System.Windows.Forms.Cursors.Hand
        Me.edit_btn.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.edit_btn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.edit_btn.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.edit_btn.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.edit_btn.Image = CType(resources.GetObject("edit_btn.Image"), System.Drawing.Image)
        Me.edit_btn.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.edit_btn.Location = New System.Drawing.Point(1003, 41)
        Me.edit_btn.Name = "edit_btn"
        Me.edit_btn.Size = New System.Drawing.Size(75, 27)
        Me.edit_btn.TabIndex = 1258
        Me.edit_btn.Text = "تعديل"
        Me.edit_btn.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.edit_btn.UseVisualStyleBackColor = False
        '
        'delet_btn
        '
        Me.delet_btn.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.delet_btn.BackColor = System.Drawing.Color.Gainsboro
        Me.delet_btn.BackgroundImage = CType(resources.GetObject("delet_btn.BackgroundImage"), System.Drawing.Image)
        Me.delet_btn.Cursor = System.Windows.Forms.Cursors.Hand
        Me.delet_btn.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.delet_btn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.delet_btn.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.delet_btn.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.delet_btn.Image = CType(resources.GetObject("delet_btn.Image"), System.Drawing.Image)
        Me.delet_btn.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.delet_btn.Location = New System.Drawing.Point(926, 41)
        Me.delet_btn.Name = "delet_btn"
        Me.delet_btn.Size = New System.Drawing.Size(75, 27)
        Me.delet_btn.TabIndex = 1259
        Me.delet_btn.Text = "حذف"
        Me.delet_btn.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.delet_btn.UseVisualStyleBackColor = False
        '
        'print_btn
        '
        Me.print_btn.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.print_btn.BackColor = System.Drawing.Color.Gainsboro
        Me.print_btn.BackgroundImage = CType(resources.GetObject("print_btn.BackgroundImage"), System.Drawing.Image)
        Me.print_btn.ContextMenuStrip = Me.ContextMenuStrip1
        Me.print_btn.Cursor = System.Windows.Forms.Cursors.Hand
        Me.print_btn.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.print_btn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.print_btn.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.print_btn.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.print_btn.Image = CType(resources.GetObject("print_btn.Image"), System.Drawing.Image)
        Me.print_btn.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.print_btn.Location = New System.Drawing.Point(858, 41)
        Me.print_btn.Name = "print_btn"
        Me.print_btn.Size = New System.Drawing.Size(66, 27)
        Me.print_btn.TabIndex = 1260
        Me.print_btn.Text = "طباعة"
        Me.print_btn.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.print_btn.UseVisualStyleBackColor = False
        '
        'PictureBox3
        '
        Me.PictureBox3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox3.Image = CType(resources.GetObject("PictureBox3.Image"), System.Drawing.Image)
        Me.PictureBox3.Location = New System.Drawing.Point(216, 39)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(1023, 33)
        Me.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox3.TabIndex = 1269
        Me.PictureBox3.TabStop = False
        '
        'Label26
        '
        Me.Label26.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Black
        Me.Label26.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(346, 620)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(93, 16)
        Me.Label26.TabIndex = 1284
        Me.Label26.Text = "تاريخ الاستحقاق :"
        '
        'Button8
        '
        Me.Button8.BackColor = System.Drawing.Color.Gainsboro
        Me.Button8.BackgroundImage = CType(resources.GetObject("Button8.BackgroundImage"), System.Drawing.Image)
        Me.Button8.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button8.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.Button8.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.Button8.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Button8.Image = CType(resources.GetObject("Button8.Image"), System.Drawing.Image)
        Me.Button8.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button8.Location = New System.Drawing.Point(219, 41)
        Me.Button8.Name = "Button8"
        Me.Button8.Size = New System.Drawing.Size(100, 27)
        Me.Button8.TabIndex = 1285
        Me.Button8.Text = "طباعة الاصناف"
        Me.Button8.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button8.UseVisualStyleBackColor = False
        '
        'Button9
        '
        Me.Button9.BackColor = System.Drawing.Color.Gainsboro
        Me.Button9.BackgroundImage = CType(resources.GetObject("Button9.BackgroundImage"), System.Drawing.Image)
        Me.Button9.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button9.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.Button9.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.Button9.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Button9.Image = CType(resources.GetObject("Button9.Image"), System.Drawing.Image)
        Me.Button9.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button9.Location = New System.Drawing.Point(321, 41)
        Me.Button9.Name = "Button9"
        Me.Button9.Size = New System.Drawing.Size(96, 27)
        Me.Button9.TabIndex = 1286
        Me.Button9.Text = "حفظ الحقول"
        Me.Button9.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button9.UseVisualStyleBackColor = False
        '
        'Button11
        '
        Me.Button11.BackColor = System.Drawing.Color.Gainsboro
        Me.Button11.BackgroundImage = CType(resources.GetObject("Button11.BackgroundImage"), System.Drawing.Image)
        Me.Button11.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button11.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.Button11.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.Button11.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button11.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Button11.Image = CType(resources.GetObject("Button11.Image"), System.Drawing.Image)
        Me.Button11.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button11.Location = New System.Drawing.Point(420, 41)
        Me.Button11.Name = "Button11"
        Me.Button11.Size = New System.Drawing.Size(95, 27)
        Me.Button11.TabIndex = 1288
        Me.Button11.Text = "اختيار الحقول"
        Me.Button11.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button11.UseVisualStyleBackColor = False
        '
        'DefaultLookAndFeel1
        '
        Me.DefaultLookAndFeel1.LookAndFeel.SkinName = "VS2010"
        '
        'delegate_name
        '
        Me.delegate_name.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.delegate_name.Location = New System.Drawing.Point(955, 617)
        Me.delegate_name.Name = "delegate_name"
        Me.delegate_name.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.delegate_name.Properties.Appearance.Options.UseFont = True
        Me.delegate_name.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.delegate_name.Size = New System.Drawing.Size(188, 22)
        Me.delegate_name.TabIndex = 1306
        '
        'Dealing
        '
        Me.Dealing.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Dealing.Location = New System.Drawing.Point(751, 617)
        Me.Dealing.Name = "Dealing"
        Me.Dealing.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Dealing.Properties.Appearance.Options.UseFont = True
        Me.Dealing.Properties.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Dealing.Properties.AppearanceDisabled.Options.UseFont = True
        Me.Dealing.Properties.AppearanceDropDown.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.Dealing.Properties.AppearanceDropDown.Options.UseFont = True
        Me.Dealing.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Dealing.Properties.Items.AddRange(New Object() {"أخر تعامل", "سعر الخاص"})
        Me.Dealing.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.Dealing.Size = New System.Drawing.Size(111, 22)
        Me.Dealing.TabIndex = 1310
        '
        'Date_pay
        '
        Me.Date_pay.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Date_pay.EditValue = Nothing
        Me.Date_pay.Location = New System.Drawing.Point(211, 618)
        Me.Date_pay.Name = "Date_pay"
        Me.Date_pay.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.Date_pay.Properties.Appearance.Options.UseFont = True
        Me.Date_pay.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_pay.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Date_pay.Properties.CalendarView = DevExpress.XtraEditors.Repository.CalendarView.TouchUI
        Me.Date_pay.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.[False]
        Me.Date_pay.Size = New System.Drawing.Size(132, 24)
        Me.Date_pay.TabIndex = 1333
        '
        'SplitContainerControl1
        '
        Me.SplitContainerControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SplitContainerControl1.Location = New System.Drawing.Point(212, 74)
        Me.SplitContainerControl1.Name = "SplitContainerControl1"
        '
        'SplitContainerControl1.Panel1
        '
        Me.SplitContainerControl1.Panel1.Controls.Add(Me.XtraTabControl1)
        Me.SplitContainerControl1.Panel1.Text = "Panel1"
        '
        'SplitContainerControl1.Panel2
        '
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GroupBox4)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GroupBox3)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GroupBox6)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.amount_string)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.CostCenterCombo)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl7)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelCostCenter)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Yup)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.monetary)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl16)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl19)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Accounts_name)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GroupBox5)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_date)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.ComboBoxEdit2)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.ComboBoxEdit1)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.store)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_number)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl5)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl4)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl3)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Accounts_balace)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label19)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl2)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.LabelControl1)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label25)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label23)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label24)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.TextBox8)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.pay_money)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label3)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label1)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.PictureBox5)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.new_balace)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label16)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label9)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label46)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label45)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Accounts_code)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_tax)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label4)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Label2)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_descound)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.PictureBox4)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.check_order)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.check_suplier)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GridControl1)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_count)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.total_invoice)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.after_total)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_note)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.invoice_pound)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.dgv_5)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.dgv_final_expire)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.dgv_serial_no)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.descound_Values)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.descound_Rate)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.show_total)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.curency_item)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.dgv_4)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.data_sale)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.item_barcode)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.tax_Rate)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.TextBox3)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.earn_invoice)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.tax_Values)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.inserte)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.Button1)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.item_descound)
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GroupBox1)
        Me.SplitContainerControl1.Panel2.Text = "Panel2"
        Me.SplitContainerControl1.Size = New System.Drawing.Size(1021, 538)
        Me.SplitContainerControl1.SplitterPosition = 333
        Me.SplitContainerControl1.TabIndex = 1334
        Me.SplitContainerControl1.Text = "SplitContainerControl1"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.Appearance.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(6, 4)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Seven Classic"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.MultiLine = DevExpress.Utils.DefaultBoolean.[True]
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.XtraTabPage1
        Me.XtraTabControl1.Size = New System.Drawing.Size(324, 536)
        Me.XtraTabControl1.TabIndex = 1286
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.XtraTabPage2})
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Controls.Add(Me.GridControl2)
        Me.XtraTabPage1.Controls.Add(Me.LabelControl14)
        Me.XtraTabPage1.Controls.Add(Me.TextEdit1)
        Me.XtraTabPage1.Controls.Add(Me.LabelControl26)
        Me.XtraTabPage1.Controls.Add(Me.item_name)
        Me.XtraTabPage1.ImageOptions.Image = CType(resources.GetObject("XtraTabPage1.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(321, 508)
        '
        'GridControl2
        '
        Me.GridControl2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.GridControl2.EmbeddedNavigator.Appearance.BackColor = System.Drawing.Color.White
        Me.GridControl2.EmbeddedNavigator.Appearance.Options.UseBackColor = True
        GridLevelNode1.RelationName = "Level1"
        Me.GridControl2.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode1})
        Me.GridControl2.Location = New System.Drawing.Point(3, 55)
        Me.GridControl2.MainView = Me.GridView1
        Me.GridControl2.Name = "GridControl2"
        Me.GridControl2.Size = New System.Drawing.Size(313, 452)
        Me.GridControl2.TabIndex = 1399
        Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.RoyalBlue
        Me.GridView1.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White
        Me.GridView1.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView1.Appearance.FocusedRow.Options.UseForeColor = True
        Me.GridView1.Appearance.FooterPanel.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.Appearance.FooterPanel.Options.UseFont = True
        Me.GridView1.Appearance.FooterPanel.Options.UseTextOptions = True
        Me.GridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView1.AppearancePrint.EvenRow.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridView1.AppearancePrint.EvenRow.Options.UseBackColor = True
        Me.GridView1.AppearancePrint.FooterPanel.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.AppearancePrint.FooterPanel.Options.UseFont = True
        Me.GridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = True
        Me.GridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView1.AppearancePrint.HeaderPanel.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView1.AppearancePrint.HeaderPanel.Options.UseFont = True
        Me.GridView1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn27, Me.GridColumn26, Me.GridColumn28, Me.GridColumn29})
        Me.GridView1.CustomizationFormBounds = New System.Drawing.Rectangle(1064, 304, 216, 232)
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        GridFormatRule1.Name = "Format0"
        FormatConditionIconSet1.CategoryName = "Ratings"
        FormatConditionIconSetIcon1.PredefinedName = "Stars3_1.png"
        FormatConditionIconSetIcon1.Value = New Decimal(New Integer() {67, 0, 0, 0})
        FormatConditionIconSetIcon1.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon2.PredefinedName = "Stars3_2.png"
        FormatConditionIconSetIcon2.Value = New Decimal(New Integer() {33, 0, 0, 0})
        FormatConditionIconSetIcon2.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon3.PredefinedName = "Stars3_3.png"
        FormatConditionIconSetIcon3.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSet1.Icons.Add(FormatConditionIconSetIcon1)
        FormatConditionIconSet1.Icons.Add(FormatConditionIconSetIcon2)
        FormatConditionIconSet1.Icons.Add(FormatConditionIconSetIcon3)
        FormatConditionIconSet1.Name = "Stars3"
        FormatConditionIconSet1.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent
        FormatConditionRuleIconSet1.IconSet = FormatConditionIconSet1
        GridFormatRule1.Rule = FormatConditionRuleIconSet1
        Me.GridView1.FormatRules.Add(GridFormatRule1)
        Me.GridView1.GridControl = Me.GridControl2
        Me.GridView1.GroupPanelText = "قم بسحب الحقل هنا لتقسيمه"
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsBehavior.AllowFixedGroups = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsBehavior.AllowGroupExpandAnimation = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsPrint.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsPrint.PrintHorzLines = False
        Me.GridView1.OptionsPrint.PrintPreview = True
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView1.OptionsView.AllowHtmlDrawHeaders = True
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.RowAutoHeight = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.PaintStyleName = "Skin"
        Me.GridView1.RowHeight = 30
        '
        'GridColumn27
        '
        Me.GridColumn27.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn27.AppearanceCell.Options.UseFont = True
        Me.GridColumn27.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn27.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn27.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn27.AppearanceHeader.Options.UseFont = True
        Me.GridColumn27.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn27.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn27.Caption = "كود"
        Me.GridColumn27.FieldName = "itemcode"
        Me.GridColumn27.Name = "GridColumn27"
        Me.GridColumn27.OptionsColumn.AllowEdit = False
        Me.GridColumn27.OptionsColumn.ReadOnly = True
        Me.GridColumn27.Visible = True
        Me.GridColumn27.VisibleIndex = 0
        Me.GridColumn27.Width = 42
        '
        'GridColumn26
        '
        Me.GridColumn26.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn26.AppearanceCell.Options.UseFont = True
        Me.GridColumn26.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn26.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn26.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.GridColumn26.AppearanceHeader.Options.UseFont = True
        Me.GridColumn26.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn26.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn26.Caption = "أسم الصنف"
        Me.GridColumn26.FieldName = "itemnamearabic"
        Me.GridColumn26.Name = "GridColumn26"
        Me.GridColumn26.OptionsColumn.AllowEdit = False
        Me.GridColumn26.OptionsColumn.ReadOnly = True
        Me.GridColumn26.Visible = True
        Me.GridColumn26.VisibleIndex = 1
        Me.GridColumn26.Width = 154
        '
        'GridColumn28
        '
        Me.GridColumn28.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn28.AppearanceCell.Options.UseFont = True
        Me.GridColumn28.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn28.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn28.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn28.AppearanceHeader.Options.UseFont = True
        Me.GridColumn28.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn28.Caption = "الكمية"
        Me.GridColumn28.FieldName = "itemcount"
        Me.GridColumn28.Name = "GridColumn28"
        Me.GridColumn28.OptionsColumn.AllowEdit = False
        Me.GridColumn28.Visible = True
        Me.GridColumn28.VisibleIndex = 2
        Me.GridColumn28.Width = 49
        '
        'GridColumn29
        '
        Me.GridColumn29.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn29.AppearanceCell.Options.UseFont = True
        Me.GridColumn29.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn29.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn29.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn29.AppearanceHeader.Options.UseFont = True
        Me.GridColumn29.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn29.Caption = "السعر"
        Me.GridColumn29.FieldName = "itemprice1"
        Me.GridColumn29.Name = "GridColumn29"
        Me.GridColumn29.OptionsColumn.AllowEdit = False
        Me.GridColumn29.Visible = True
        Me.GridColumn29.VisibleIndex = 3
        Me.GridColumn29.Width = 50
        '
        'LabelControl14
        '
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.LabelControl14.Appearance.Options.UseFont = True
        Me.LabelControl14.Appearance.Options.UseForeColor = True
        Me.LabelControl14.Location = New System.Drawing.Point(9, 23)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(15, 13)
        Me.LabelControl14.TabIndex = 1401
        Me.LabelControl14.Text = "f12"
        '
        'TextEdit1
        '
        Me.TextEdit1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEdit1.Location = New System.Drawing.Point(4, 16)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.Appearance.BorderColor = System.Drawing.Color.Gainsboro
        Me.TextEdit1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit1.Properties.Appearance.Options.UseBorderColor = True
        Me.TextEdit1.Properties.Appearance.Options.UseFont = True
        Me.TextEdit1.Properties.LookAndFeel.SkinName = "Liquid Sky"
        Me.TextEdit1.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.TextEdit1.Size = New System.Drawing.Size(312, 26)
        Me.TextEdit1.TabIndex = 1400
        '
        'LabelControl26
        '
        Me.LabelControl26.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl26.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.LabelControl26.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.LabelControl26.Appearance.Options.UseFont = True
        Me.LabelControl26.Appearance.Options.UseForeColor = True
        Me.LabelControl26.Location = New System.Drawing.Point(190, -3)
        Me.LabelControl26.Name = "LabelControl26"
        Me.LabelControl26.Size = New System.Drawing.Size(93, 13)
        Me.LabelControl26.TabIndex = 1402
        Me.LabelControl26.Text = "ابحث باسم الصنف او الباركود"
        '
        'item_name
        '
        Me.item_name.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.item_name.Location = New System.Drawing.Point(91, 198)
        Me.item_name.Name = "item_name"
        Me.item_name.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_name.Properties.Appearance.Options.UseFont = True
        Me.item_name.Properties.Appearance.Options.UseTextOptions = True
        Me.item_name.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.item_name.Properties.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.item_name.Properties.AppearanceDisabled.Options.UseFont = True
        Me.item_name.Properties.AppearanceDropDown.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.item_name.Properties.AppearanceDropDown.Options.UseFont = True
        Me.item_name.Properties.AppearanceDropDownHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.item_name.Properties.AppearanceDropDownHeader.Options.UseFont = True
        Me.item_name.Properties.AppearanceFocused.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.item_name.Properties.AppearanceFocused.Options.UseFont = True
        Me.item_name.Properties.AppearanceReadOnly.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.item_name.Properties.AppearanceReadOnly.Options.UseFont = True
        Me.item_name.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.item_name.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("itemcode", "كود", 10, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("itemnamearabic", "اسم الصنف", 80, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.item_name.Properties.ImmediatePopup = True
        Me.item_name.Properties.NullText = ""
        Me.item_name.Properties.PopupWidthMode = DevExpress.XtraEditors.PopupWidthMode.UseEditorWidth
        Me.item_name.Properties.SearchMode = DevExpress.XtraEditors.Controls.SearchMode.OnlyInPopup
        Me.item_name.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.item_name.Size = New System.Drawing.Size(216, 22)
        Me.item_name.TabIndex = 1329
        Me.item_name.TabStop = False
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Appearance.PageClient.BackColor = System.Drawing.Color.LightGray
        Me.XtraTabPage2.Appearance.PageClient.Options.UseBackColor = True
        Me.XtraTabPage2.Controls.Add(Me.dgv_catarogy)
        Me.XtraTabPage2.Controls.Add(Me.dgv_touch)
        Me.XtraTabPage2.ImageOptions.Image = CType(resources.GetObject("XtraTabPage2.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(321, 508)
        '
        'dgv_catarogy
        '
        Me.dgv_catarogy.AllowUserToAddRows = False
        Me.dgv_catarogy.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgv_catarogy.BackgroundColor = System.Drawing.Color.White
        Me.dgv_catarogy.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.dgv_catarogy.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.None
        Me.dgv_catarogy.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None
        Me.dgv_catarogy.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_catarogy.ColumnHeadersVisible = False
        Me.dgv_catarogy.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn39})
        Me.dgv_catarogy.Location = New System.Drawing.Point(206, 3)
        Me.dgv_catarogy.Name = "dgv_catarogy"
        Me.dgv_catarogy.RowHeadersVisible = False
        Me.dgv_catarogy.RowHeadersWidth = 100
        Me.dgv_catarogy.RowTemplate.Height = 70
        Me.dgv_catarogy.Size = New System.Drawing.Size(112, 467)
        Me.dgv_catarogy.TabIndex = 1283
        '
        'DataGridViewTextBoxColumn39
        '
        Me.DataGridViewTextBoxColumn39.DataPropertyName = "Category_name"
        DataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle22.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewTextBoxColumn39.DefaultCellStyle = DataGridViewCellStyle22
        Me.DataGridViewTextBoxColumn39.HeaderText = "Column1"
        Me.DataGridViewTextBoxColumn39.Name = "DataGridViewTextBoxColumn39"
        Me.DataGridViewTextBoxColumn39.ReadOnly = True
        Me.DataGridViewTextBoxColumn39.Width = 110
        '
        'dgv_touch
        '
        Me.dgv_touch.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        GridLevelNode2.RelationName = "Level1"
        Me.dgv_touch.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode2})
        Me.dgv_touch.Location = New System.Drawing.Point(5, 6)
        Me.dgv_touch.LookAndFeel.UseDefaultLookAndFeel = False
        Me.dgv_touch.MainView = Me.CardView2
        Me.dgv_touch.Name = "dgv_touch"
        Me.dgv_touch.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.dgv_touch.Size = New System.Drawing.Size(195, 465)
        Me.dgv_touch.TabIndex = 1282
        Me.dgv_touch.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.CardView2})
        '
        'CardView2
        '
        Me.CardView2.ActiveFilterEnabled = False
        Me.CardView2.Appearance.Card.BorderColor = System.Drawing.Color.White
        Me.CardView2.Appearance.Card.Image = CType(resources.GetObject("CardView2.Appearance.Card.Image"), System.Drawing.Image)
        Me.CardView2.Appearance.Card.Options.UseBorderColor = True
        Me.CardView2.Appearance.Card.Options.UseImage = True
        Me.CardView2.Appearance.CardButton.BorderColor = System.Drawing.Color.White
        Me.CardView2.Appearance.CardButton.Options.UseBorderColor = True
        Me.CardView2.Appearance.CardCaption.BorderColor = System.Drawing.Color.White
        Me.CardView2.Appearance.CardCaption.Options.UseBorderColor = True
        Me.CardView2.Appearance.SeparatorLine.BackColor = System.Drawing.Color.White
        Me.CardView2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.CardView2.CardInterval = 4
        Me.CardView2.CardScrollButtonBorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.CardView2.CardWidth = 150
        Me.CardView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn24, Me.itemnamearabic, Me.itemcount, Me.itemcode})
        Me.CardView2.DetailHeight = 100
        Me.CardView2.DetailTabHeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Left
        GridFormatRule2.Name = "Format0"
        FormatConditionIconSet2.CategoryName = "Ratings"
        FormatConditionIconSetIcon4.PredefinedName = "Stars3_1.png"
        FormatConditionIconSetIcon4.Value = New Decimal(New Integer() {67, 0, 0, 0})
        FormatConditionIconSetIcon4.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon5.PredefinedName = "Stars3_2.png"
        FormatConditionIconSetIcon5.Value = New Decimal(New Integer() {33, 0, 0, 0})
        FormatConditionIconSetIcon5.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon6.PredefinedName = "Stars3_3.png"
        FormatConditionIconSetIcon6.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSet2.Icons.Add(FormatConditionIconSetIcon4)
        FormatConditionIconSet2.Icons.Add(FormatConditionIconSetIcon5)
        FormatConditionIconSet2.Icons.Add(FormatConditionIconSetIcon6)
        FormatConditionIconSet2.Name = "Stars3"
        FormatConditionIconSet2.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent
        FormatConditionRuleIconSet2.IconSet = FormatConditionIconSet2
        GridFormatRule2.Rule = FormatConditionRuleIconSet2
        Me.CardView2.FormatRules.Add(GridFormatRule2)
        Me.CardView2.GridControl = Me.dgv_touch
        Me.CardView2.Name = "CardView2"
        Me.CardView2.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.CardView2.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.CardView2.OptionsBehavior.FieldAutoHeight = True
        Me.CardView2.OptionsPrint.PrintEmptyFields = False
        Me.CardView2.OptionsView.ShowCardCaption = False
        Me.CardView2.OptionsView.ShowFieldCaptions = False
        Me.CardView2.OptionsView.ShowQuickCustomizeButton = False
        '
        'GridColumn24
        '
        Me.GridColumn24.AppearanceCell.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.GridColumn24.AppearanceCell.Options.UseFont = True
        Me.GridColumn24.AppearanceCell.Options.UseImage = True
        Me.GridColumn24.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn24.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn24.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.GridColumn24.AppearanceHeader.Options.UseFont = True
        Me.GridColumn24.AppearanceHeader.Options.UseImage = True
        Me.GridColumn24.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn24.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn24.FieldName = "item_pic"
        Me.GridColumn24.ImageOptions.Alignment = System.Drawing.StringAlignment.Center
        Me.GridColumn24.Name = "GridColumn24"
        Me.GridColumn24.OptionsColumn.AllowEdit = False
        Me.GridColumn24.OptionsColumn.ReadOnly = True
        Me.GridColumn24.OptionsColumn.ShowCaption = False
        Me.GridColumn24.Visible = True
        Me.GridColumn24.VisibleIndex = 0
        Me.GridColumn24.Width = 85
        '
        'itemnamearabic
        '
        Me.itemnamearabic.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.itemnamearabic.AppearanceCell.ForeColor = System.Drawing.Color.Blue
        Me.itemnamearabic.AppearanceCell.Options.UseFont = True
        Me.itemnamearabic.AppearanceCell.Options.UseForeColor = True
        Me.itemnamearabic.AppearanceCell.Options.UseTextOptions = True
        Me.itemnamearabic.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.itemnamearabic.Caption = "itemnamearabic"
        Me.itemnamearabic.FieldName = "itemnamearabic"
        Me.itemnamearabic.Name = "itemnamearabic"
        Me.itemnamearabic.OptionsColumn.AllowEdit = False
        Me.itemnamearabic.OptionsColumn.ReadOnly = True
        Me.itemnamearabic.Visible = True
        Me.itemnamearabic.VisibleIndex = 1
        '
        'itemcount
        '
        Me.itemcount.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.itemcount.AppearanceCell.ForeColor = System.Drawing.Color.Blue
        Me.itemcount.AppearanceCell.Options.UseFont = True
        Me.itemcount.AppearanceCell.Options.UseForeColor = True
        Me.itemcount.AppearanceCell.Options.UseTextOptions = True
        Me.itemcount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.itemcount.Caption = "itemcount"
        Me.itemcount.FieldName = "itemcount"
        Me.itemcount.Name = "itemcount"
        Me.itemcount.OptionsColumn.AllowEdit = False
        Me.itemcount.OptionsColumn.ReadOnly = True
        Me.itemcount.Visible = True
        Me.itemcount.VisibleIndex = 2
        '
        'itemcode
        '
        Me.itemcode.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.itemcode.AppearanceCell.ForeColor = System.Drawing.Color.Blue
        Me.itemcode.AppearanceCell.Options.UseFont = True
        Me.itemcode.AppearanceCell.Options.UseForeColor = True
        Me.itemcode.Caption = "itemcode"
        Me.itemcode.FieldName = "itemcode"
        Me.itemcode.Name = "itemcode"
        '
        'GroupBox4
        '
        Me.GroupBox4.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.GroupBox4.Controls.Add(Me.LabelControl8)
        Me.GroupBox4.Controls.Add(Me.serial_search)
        Me.GroupBox4.Controls.Add(Me.SimpleButton6)
        Me.GroupBox4.Controls.Add(Me.SimpleButton7)
        Me.GroupBox4.Controls.Add(Me.dgv_serial)
        Me.GroupBox4.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox4.Location = New System.Drawing.Point(110, 101)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(394, 349)
        Me.GroupBox4.TabIndex = 1393
        Me.GroupBox4.TabStop = False
        '
        'LabelControl8
        '
        Me.LabelControl8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl8.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl8.Appearance.Options.UseFont = True
        Me.LabelControl8.Location = New System.Drawing.Point(326, 63)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(31, 15)
        Me.LabelControl8.TabIndex = 1322
        Me.LabelControl8.Text = "البحث :"
        '
        'serial_search
        '
        Me.serial_search.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.serial_search.Location = New System.Drawing.Point(13, 62)
        Me.serial_search.Name = "serial_search"
        Me.serial_search.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.serial_search.Properties.Appearance.Options.UseFont = True
        Me.serial_search.Size = New System.Drawing.Size(305, 24)
        Me.serial_search.TabIndex = 1321
        '
        'SimpleButton6
        '
        Me.SimpleButton6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton6.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton6.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton6.Appearance.Options.UseBackColor = True
        Me.SimpleButton6.Appearance.Options.UseFont = True
        Me.SimpleButton6.Appearance.Options.UseTextOptions = True
        Me.SimpleButton6.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton6.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton6.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton6.Location = New System.Drawing.Point(47, 304)
        Me.SimpleButton6.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton6.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton6.Name = "SimpleButton6"
        Me.SimpleButton6.Size = New System.Drawing.Size(148, 39)
        Me.SimpleButton6.TabIndex = 1320
        Me.SimpleButton6.Text = "الغاء"
        '
        'SimpleButton7
        '
        Me.SimpleButton7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton7.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton7.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton7.Appearance.Options.UseBackColor = True
        Me.SimpleButton7.Appearance.Options.UseFont = True
        Me.SimpleButton7.Appearance.Options.UseTextOptions = True
        Me.SimpleButton7.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton7.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton7.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton7.Location = New System.Drawing.Point(206, 304)
        Me.SimpleButton7.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton7.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton7.Name = "SimpleButton7"
        Me.SimpleButton7.Size = New System.Drawing.Size(155, 39)
        Me.SimpleButton7.TabIndex = 1319
        Me.SimpleButton7.Text = "موافق"
        '
        'LabelControl7
        '
        Me.LabelControl7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl7.Appearance.BackColor = System.Drawing.Color.RoyalBlue
        Me.LabelControl7.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.LabelControl7.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelControl7.Appearance.Options.UseBackColor = True
        Me.LabelControl7.Appearance.Options.UseFont = True
        Me.LabelControl7.Appearance.Options.UseForeColor = True
        Me.LabelControl7.Appearance.Options.UseTextOptions = True
        Me.LabelControl7.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl7.Location = New System.Drawing.Point(35, 50)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(394, 44)
        Me.LabelControl7.TabIndex = 1318
        Me.LabelControl7.Text = "اختار سيريل الصنف"
        '
        'dgv_serial
        '
        Me.dgv_serial.AllowUserToAddRows = False
        DataGridViewCellStyle23.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle23.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle23.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.dgv_serial.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle23
        Me.dgv_serial.BackgroundColor = System.Drawing.Color.White
        Me.dgv_serial.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        DataGridViewCellStyle24.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle24.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle24.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle24.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle24.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle24.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle24.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_serial.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle24
        Me.dgv_serial.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_serial.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn35, Me.DataGridViewTextBoxColumn38, Me.Column3})
        DataGridViewCellStyle27.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle27.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle27.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle27.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle27.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle27.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle27.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_serial.DefaultCellStyle = DataGridViewCellStyle27
        Me.dgv_serial.GridColor = System.Drawing.Color.AliceBlue
        Me.dgv_serial.Location = New System.Drawing.Point(13, 92)
        Me.dgv_serial.Name = "dgv_serial"
        Me.dgv_serial.RowHeadersVisible = False
        Me.dgv_serial.RowHeadersWidth = 49
        Me.dgv_serial.Size = New System.Drawing.Size(364, 198)
        Me.dgv_serial.TabIndex = 1317
        '
        'DataGridViewTextBoxColumn35
        '
        Me.DataGridViewTextBoxColumn35.DataPropertyName = "item_code"
        DataGridViewCellStyle25.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewTextBoxColumn35.DefaultCellStyle = DataGridViewCellStyle25
        Me.DataGridViewTextBoxColumn35.HeaderText = "الكود"
        Me.DataGridViewTextBoxColumn35.Name = "DataGridViewTextBoxColumn35"
        Me.DataGridViewTextBoxColumn35.ReadOnly = True
        Me.DataGridViewTextBoxColumn35.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn35.Width = 70
        '
        'DataGridViewTextBoxColumn38
        '
        Me.DataGridViewTextBoxColumn38.DataPropertyName = "item_serial"
        DataGridViewCellStyle26.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle26.Format = "N0"
        DataGridViewCellStyle26.NullValue = "0"
        Me.DataGridViewTextBoxColumn38.DefaultCellStyle = DataGridViewCellStyle26
        Me.DataGridViewTextBoxColumn38.HeaderText = "السيريل"
        Me.DataGridViewTextBoxColumn38.Name = "DataGridViewTextBoxColumn38"
        Me.DataGridViewTextBoxColumn38.ReadOnly = True
        Me.DataGridViewTextBoxColumn38.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn38.Width = 240
        '
        'Column3
        '
        Me.Column3.FlatStyle = System.Windows.Forms.FlatStyle.Popup
        Me.Column3.HeaderText = ""
        Me.Column3.Name = "Column3"
        Me.Column3.Width = 50
        '
        'GroupBox3
        '
        Me.GroupBox3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.GroupBox3.Controls.Add(Me.SimpleButton5)
        Me.GroupBox3.Controls.Add(Me.SimpleButton4)
        Me.GroupBox3.Controls.Add(Me.LabelControl6)
        Me.GroupBox3.Controls.Add(Me.dgv_expire)
        Me.GroupBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox3.Location = New System.Drawing.Point(110, 137)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(394, 349)
        Me.GroupBox3.TabIndex = 1389
        Me.GroupBox3.TabStop = False
        '
        'SimpleButton5
        '
        Me.SimpleButton5.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton5.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseFont = True
        Me.SimpleButton5.Appearance.Options.UseTextOptions = True
        Me.SimpleButton5.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton5.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton5.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton5.Location = New System.Drawing.Point(47, 304)
        Me.SimpleButton5.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton5.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(148, 39)
        Me.SimpleButton5.TabIndex = 1320
        Me.SimpleButton5.Text = "الغاء"
        '
        'SimpleButton4
        '
        Me.SimpleButton4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton4.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton4.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton4.Appearance.Options.UseBackColor = True
        Me.SimpleButton4.Appearance.Options.UseFont = True
        Me.SimpleButton4.Appearance.Options.UseTextOptions = True
        Me.SimpleButton4.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton4.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton4.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton4.Location = New System.Drawing.Point(206, 304)
        Me.SimpleButton4.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton4.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton4.Name = "SimpleButton4"
        Me.SimpleButton4.Size = New System.Drawing.Size(155, 39)
        Me.SimpleButton4.TabIndex = 1319
        Me.SimpleButton4.Text = "موافق"
        '
        'LabelControl6
        '
        Me.LabelControl6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl6.Appearance.BackColor = System.Drawing.Color.RoyalBlue
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.LabelControl6.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelControl6.Appearance.Options.UseBackColor = True
        Me.LabelControl6.Appearance.Options.UseFont = True
        Me.LabelControl6.Appearance.Options.UseForeColor = True
        Me.LabelControl6.Appearance.Options.UseTextOptions = True
        Me.LabelControl6.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl6.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl6.Location = New System.Drawing.Point(0, 14)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(394, 44)
        Me.LabelControl6.TabIndex = 1318
        Me.LabelControl6.Text = "اختار تواريخ الصلاحية"
        '
        'dgv_expire
        '
        Me.dgv_expire.AllowUserToAddRows = False
        DataGridViewCellStyle28.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle28.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle28.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.dgv_expire.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle28
        Me.dgv_expire.BackgroundColor = System.Drawing.Color.White
        Me.dgv_expire.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        DataGridViewCellStyle29.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle29.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle29.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle29.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle29.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle29.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle29.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_expire.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle29
        Me.dgv_expire.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_expire.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn32, Me.DataGridViewTextBoxColumn34, Me.Column1, Me.Column2})
        DataGridViewCellStyle34.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle34.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle34.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle34.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle34.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle34.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle34.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_expire.DefaultCellStyle = DataGridViewCellStyle34
        Me.dgv_expire.GridColor = System.Drawing.Color.AliceBlue
        Me.dgv_expire.Location = New System.Drawing.Point(13, 65)
        Me.dgv_expire.Name = "dgv_expire"
        Me.dgv_expire.RowHeadersVisible = False
        Me.dgv_expire.RowHeadersWidth = 49
        Me.dgv_expire.Size = New System.Drawing.Size(364, 225)
        Me.dgv_expire.TabIndex = 1317
        '
        'DataGridViewTextBoxColumn32
        '
        Me.DataGridViewTextBoxColumn32.DataPropertyName = "item_code"
        DataGridViewCellStyle30.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewTextBoxColumn32.DefaultCellStyle = DataGridViewCellStyle30
        Me.DataGridViewTextBoxColumn32.HeaderText = "الكود"
        Me.DataGridViewTextBoxColumn32.Name = "DataGridViewTextBoxColumn32"
        Me.DataGridViewTextBoxColumn32.ReadOnly = True
        Me.DataGridViewTextBoxColumn32.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn32.Width = 70
        '
        'DataGridViewTextBoxColumn34
        '
        Me.DataGridViewTextBoxColumn34.DataPropertyName = "item_count"
        DataGridViewCellStyle31.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle31.Format = "N0"
        DataGridViewCellStyle31.NullValue = "0"
        Me.DataGridViewTextBoxColumn34.DefaultCellStyle = DataGridViewCellStyle31
        Me.DataGridViewTextBoxColumn34.HeaderText = "الكمية"
        Me.DataGridViewTextBoxColumn34.Name = "DataGridViewTextBoxColumn34"
        Me.DataGridViewTextBoxColumn34.ReadOnly = True
        Me.DataGridViewTextBoxColumn34.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn34.Width = 90
        '
        'Column1
        '
        Me.Column1.DataPropertyName = "item_date"
        DataGridViewCellStyle32.Format = "d"
        DataGridViewCellStyle32.NullValue = Nothing
        Me.Column1.DefaultCellStyle = DataGridViewCellStyle32
        Me.Column1.HeaderText = "التاريخ"
        Me.Column1.Name = "Column1"
        Me.Column1.ReadOnly = True
        '
        'Column2
        '
        DataGridViewCellStyle33.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle33.Format = "N2"
        DataGridViewCellStyle33.NullValue = "0"
        Me.Column2.DefaultCellStyle = DataGridViewCellStyle33
        Me.Column2.HeaderText = "الكمية"
        Me.Column2.Name = "Column2"
        '
        'GroupBox6
        '
        Me.GroupBox6.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.GroupBox6.Controls.Add(Me.SimpleButton10)
        Me.GroupBox6.Controls.Add(Me.Label6)
        Me.GroupBox6.Controls.Add(Me.GridControl3)
        Me.GroupBox6.Location = New System.Drawing.Point(215, 143)
        Me.GroupBox6.Name = "GroupBox6"
        Me.GroupBox6.Size = New System.Drawing.Size(253, 253)
        Me.GroupBox6.TabIndex = 1410
        Me.GroupBox6.TabStop = False
        Me.GroupBox6.Visible = False
        '
        'SimpleButton10
        '
        Me.SimpleButton10.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton10.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton10.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton10.Appearance.Options.UseBackColor = True
        Me.SimpleButton10.Appearance.Options.UseFont = True
        Me.SimpleButton10.Appearance.Options.UseTextOptions = True
        Me.SimpleButton10.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton10.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton10.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton10.Location = New System.Drawing.Point(16, 210)
        Me.SimpleButton10.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton10.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton10.Name = "SimpleButton10"
        Me.SimpleButton10.Size = New System.Drawing.Size(221, 34)
        Me.SimpleButton10.TabIndex = 1321
        Me.SimpleButton10.Text = "اخفاء"
        '
        'Label6
        '
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.Maroon
        Me.Label6.Location = New System.Drawing.Point(7, 16)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(236, 25)
        Me.Label6.TabIndex = 1401
        Me.Label6.Text = "سعر الشراء"
        '
        'GridControl3
        '
        Me.GridControl3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl3.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.GridControl3.EmbeddedNavigator.Appearance.BackColor = System.Drawing.Color.White
        Me.GridControl3.EmbeddedNavigator.Appearance.Options.UseBackColor = True
        GridLevelNode3.RelationName = "Level1"
        Me.GridControl3.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode3})
        Me.GridControl3.Location = New System.Drawing.Point(15, 43)
        Me.GridControl3.MainView = Me.GridView3
        Me.GridControl3.Name = "GridControl3"
        Me.GridControl3.Size = New System.Drawing.Size(220, 156)
        Me.GridControl3.TabIndex = 1404
        Me.GridControl3.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView3})
        '
        'GridView3
        '
        Me.GridView3.Appearance.FocusedRow.BackColor = System.Drawing.Color.RoyalBlue
        Me.GridView3.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White
        Me.GridView3.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView3.Appearance.FocusedRow.Options.UseForeColor = True
        Me.GridView3.Appearance.FooterPanel.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView3.Appearance.FooterPanel.Options.UseFont = True
        Me.GridView3.Appearance.FooterPanel.Options.UseTextOptions = True
        Me.GridView3.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView3.AppearancePrint.EvenRow.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridView3.AppearancePrint.EvenRow.Options.UseBackColor = True
        Me.GridView3.AppearancePrint.FooterPanel.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView3.AppearancePrint.FooterPanel.Options.UseFont = True
        Me.GridView3.AppearancePrint.FooterPanel.Options.UseTextOptions = True
        Me.GridView3.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView3.AppearancePrint.HeaderPanel.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView3.AppearancePrint.HeaderPanel.Options.UseFont = True
        Me.GridView3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D
        Me.GridView3.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn25, Me.GridColumn30, Me.GridColumn31})
        Me.GridView3.CustomizationFormBounds = New System.Drawing.Rectangle(1064, 304, 216, 232)
        Me.GridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        GridFormatRule3.Name = "Format0"
        FormatConditionIconSet3.CategoryName = "Ratings"
        FormatConditionIconSetIcon7.PredefinedName = "Stars3_1.png"
        FormatConditionIconSetIcon7.Value = New Decimal(New Integer() {67, 0, 0, 0})
        FormatConditionIconSetIcon7.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon8.PredefinedName = "Stars3_2.png"
        FormatConditionIconSetIcon8.Value = New Decimal(New Integer() {33, 0, 0, 0})
        FormatConditionIconSetIcon8.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon9.PredefinedName = "Stars3_3.png"
        FormatConditionIconSetIcon9.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSet3.Icons.Add(FormatConditionIconSetIcon7)
        FormatConditionIconSet3.Icons.Add(FormatConditionIconSetIcon8)
        FormatConditionIconSet3.Icons.Add(FormatConditionIconSetIcon9)
        FormatConditionIconSet3.Name = "Stars3"
        FormatConditionIconSet3.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent
        FormatConditionRuleIconSet3.IconSet = FormatConditionIconSet3
        GridFormatRule3.Rule = FormatConditionRuleIconSet3
        Me.GridView3.FormatRules.Add(GridFormatRule3)
        Me.GridView3.GridControl = Me.GridControl3
        Me.GridView3.GroupPanelText = "قم بسحب الحقل هنا لتقسيمه"
        Me.GridView3.Name = "GridView3"
        Me.GridView3.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView3.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView3.OptionsBehavior.AllowFixedGroups = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView3.OptionsBehavior.AllowGroupExpandAnimation = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView3.OptionsPrint.EnableAppearanceEvenRow = True
        Me.GridView3.OptionsPrint.PrintHorzLines = False
        Me.GridView3.OptionsPrint.PrintPreview = True
        Me.GridView3.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView3.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView3.OptionsView.AllowHtmlDrawHeaders = True
        Me.GridView3.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView3.OptionsView.RowAutoHeight = True
        Me.GridView3.OptionsView.ShowGroupPanel = False
        Me.GridView3.PaintStyleName = "Skin"
        Me.GridView3.RowHeight = 25
        '
        'GridColumn25
        '
        Me.GridColumn25.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn25.AppearanceCell.Options.UseFont = True
        Me.GridColumn25.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn25.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn25.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn25.AppearanceHeader.Options.UseFont = True
        Me.GridColumn25.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn25.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn25.Caption = "الوحدة"
        Me.GridColumn25.FieldName = "unit"
        Me.GridColumn25.Name = "GridColumn25"
        Me.GridColumn25.OptionsColumn.AllowEdit = False
        Me.GridColumn25.OptionsColumn.ReadOnly = True
        Me.GridColumn25.Visible = True
        Me.GridColumn25.VisibleIndex = 0
        Me.GridColumn25.Width = 71
        '
        'GridColumn30
        '
        Me.GridColumn30.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn30.AppearanceCell.Options.UseFont = True
        Me.GridColumn30.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn30.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn30.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn30.AppearanceHeader.Options.UseFont = True
        Me.GridColumn30.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn30.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn30.Caption = "العدد"
        Me.GridColumn30.FieldName = "count"
        Me.GridColumn30.Name = "GridColumn30"
        Me.GridColumn30.OptionsColumn.AllowEdit = False
        Me.GridColumn30.OptionsColumn.ReadOnly = True
        Me.GridColumn30.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Count, "أسم الصنف", "{0}", "0")})
        Me.GridColumn30.Visible = True
        Me.GridColumn30.VisibleIndex = 1
        Me.GridColumn30.Width = 74
        '
        'GridColumn31
        '
        Me.GridColumn31.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn31.AppearanceCell.Options.UseFont = True
        Me.GridColumn31.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn31.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.0!)
        Me.GridColumn31.AppearanceHeader.Options.UseFont = True
        Me.GridColumn31.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn31.Caption = "السعر"
        Me.GridColumn31.FieldName = "price1"
        Me.GridColumn31.Name = "GridColumn31"
        Me.GridColumn31.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Count, "الكمية", "{0:0.##}", "0")})
        Me.GridColumn31.Visible = True
        Me.GridColumn31.VisibleIndex = 2
        Me.GridColumn31.Width = 90
        '
        'amount_string
        '
        Me.amount_string.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.amount_string.BackColor = System.Drawing.Color.White
        Me.amount_string.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.amount_string.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.amount_string.Location = New System.Drawing.Point(134, 496)
        Me.amount_string.Margin = New System.Windows.Forms.Padding(4)
        Me.amount_string.Name = "amount_string"
        Me.amount_string.Size = New System.Drawing.Size(188, 14)
        Me.amount_string.TabIndex = 1334
        Me.amount_string.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Yup
        '
        Me.Yup.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Yup.AutoSize = True
        Me.Yup.BackColor = System.Drawing.Color.Gainsboro
        Me.Yup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Yup.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Yup.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Yup.Location = New System.Drawing.Point(233, 25)
        Me.Yup.Name = "Yup"
        Me.Yup.Size = New System.Drawing.Size(41, 19)
        Me.Yup.TabIndex = 1344
        Me.Yup.Text = "أجل"
        Me.Yup.UseVisualStyleBackColor = False
        '
        'monetary
        '
        Me.monetary.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.monetary.BackColor = System.Drawing.Color.Gainsboro
        Me.monetary.Checked = True
        Me.monetary.Cursor = System.Windows.Forms.Cursors.Hand
        Me.monetary.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.monetary.ForeColor = System.Drawing.SystemColors.ControlText
        Me.monetary.Location = New System.Drawing.Point(264, 25)
        Me.monetary.Name = "monetary"
        Me.monetary.Size = New System.Drawing.Size(59, 23)
        Me.monetary.TabIndex = 1342
        Me.monetary.TabStop = True
        Me.monetary.Text = "نقدي"
        Me.monetary.UseVisualStyleBackColor = False
        '
        'LabelControl16
        '
        Me.LabelControl16.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl16.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.LabelControl16.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.LabelControl16.Appearance.Options.UseFont = True
        Me.LabelControl16.Appearance.Options.UseForeColor = True
        Me.LabelControl16.Location = New System.Drawing.Point(470, 511)
        Me.LabelControl16.Name = "LabelControl16"
        Me.LabelControl16.Size = New System.Drawing.Size(9, 13)
        Me.LabelControl16.TabIndex = 1406
        Me.LabelControl16.Text = "f8"
        '
        'LabelControl19
        '
        Me.LabelControl19.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl19.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.LabelControl19.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.LabelControl19.Appearance.Options.UseFont = True
        Me.LabelControl19.Appearance.Options.UseForeColor = True
        Me.LabelControl19.Location = New System.Drawing.Point(344, 27)
        Me.LabelControl19.Name = "LabelControl19"
        Me.LabelControl19.Size = New System.Drawing.Size(9, 13)
        Me.LabelControl19.TabIndex = 1404
        Me.LabelControl19.Text = "f6"
        '
        'Accounts_name
        '
        Me.Accounts_name.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Accounts_name.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.Accounts_name.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.Accounts_name.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Accounts_name.FormattingEnabled = True
        Me.Accounts_name.Location = New System.Drawing.Point(328, 23)
        Me.Accounts_name.Name = "Accounts_name"
        Me.Accounts_name.Size = New System.Drawing.Size(260, 24)
        Me.Accounts_name.TabIndex = 1390
        '
        'GroupBox5
        '
        Me.GroupBox5.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.GroupBox5.Controls.Add(Me.LabelControl13)
        Me.GroupBox5.Controls.Add(Me.amount_rate)
        Me.GroupBox5.Controls.Add(Me.LabelControl12)
        Me.GroupBox5.Controls.Add(Me.end_date)
        Me.GroupBox5.Controls.Add(Me.LabelControl11)
        Me.GroupBox5.Controls.Add(Me.start_date)
        Me.GroupBox5.Controls.Add(Me.LabelControl10)
        Me.GroupBox5.Controls.Add(Me.cobon_code)
        Me.GroupBox5.Controls.Add(Me.SimpleButton8)
        Me.GroupBox5.Controls.Add(Me.SimpleButton9)
        Me.GroupBox5.Controls.Add(Me.LabelControl9)
        Me.GroupBox5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox5.Location = New System.Drawing.Point(110, 137)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(394, 253)
        Me.GroupBox5.TabIndex = 1392
        Me.GroupBox5.TabStop = False
        '
        'LabelControl13
        '
        Me.LabelControl13.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl13.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl13.Appearance.Options.UseFont = True
        Me.LabelControl13.Location = New System.Drawing.Point(302, 165)
        Me.LabelControl13.Name = "LabelControl13"
        Me.LabelControl13.Size = New System.Drawing.Size(54, 15)
        Me.LabelControl13.TabIndex = 1328
        Me.LabelControl13.Text = "نسبة الخصم :"
        '
        'amount_rate
        '
        Me.amount_rate.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.amount_rate.EditValue = "0"
        Me.amount_rate.Location = New System.Drawing.Point(185, 163)
        Me.amount_rate.Name = "amount_rate"
        Me.amount_rate.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.amount_rate.Properties.Appearance.Options.UseFont = True
        Me.amount_rate.Properties.Appearance.Options.UseTextOptions = True
        Me.amount_rate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.amount_rate.Size = New System.Drawing.Size(111, 24)
        Me.amount_rate.TabIndex = 1327
        '
        'LabelControl12
        '
        Me.LabelControl12.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl12.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl12.Appearance.Options.UseFont = True
        Me.LabelControl12.Location = New System.Drawing.Point(302, 135)
        Me.LabelControl12.Name = "LabelControl12"
        Me.LabelControl12.Size = New System.Drawing.Size(60, 15)
        Me.LabelControl12.TabIndex = 1326
        Me.LabelControl12.Text = "تاريخ الانتهاء :"
        '
        'end_date
        '
        Me.end_date.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.end_date.Enabled = False
        Me.end_date.Location = New System.Drawing.Point(185, 133)
        Me.end_date.Name = "end_date"
        Me.end_date.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.end_date.Properties.Appearance.Options.UseFont = True
        Me.end_date.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.end_date.Size = New System.Drawing.Size(111, 22)
        Me.end_date.TabIndex = 1325
        '
        'LabelControl11
        '
        Me.LabelControl11.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.Options.UseFont = True
        Me.LabelControl11.Location = New System.Drawing.Point(302, 103)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(64, 15)
        Me.LabelControl11.TabIndex = 1324
        Me.LabelControl11.Text = "تاريخ الاصدار :"
        '
        'start_date
        '
        Me.start_date.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.start_date.Enabled = False
        Me.start_date.Location = New System.Drawing.Point(185, 103)
        Me.start_date.Name = "start_date"
        Me.start_date.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.start_date.Properties.Appearance.Options.UseFont = True
        Me.start_date.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.start_date.Size = New System.Drawing.Size(111, 22)
        Me.start_date.TabIndex = 1323
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.Options.UseFont = True
        Me.LabelControl10.Location = New System.Drawing.Point(322, 73)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(26, 15)
        Me.LabelControl10.TabIndex = 1322
        Me.LabelControl10.Text = "الكود :"
        '
        'cobon_code
        '
        Me.cobon_code.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cobon_code.EditValue = ""
        Me.cobon_code.Location = New System.Drawing.Point(16, 73)
        Me.cobon_code.Name = "cobon_code"
        Me.cobon_code.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.cobon_code.Properties.Appearance.Options.UseFont = True
        Me.cobon_code.Properties.Appearance.Options.UseTextOptions = True
        Me.cobon_code.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.cobon_code.Size = New System.Drawing.Size(280, 24)
        Me.cobon_code.TabIndex = 1321
        '
        'SimpleButton8
        '
        Me.SimpleButton8.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton8.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton8.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton8.Appearance.Options.UseBackColor = True
        Me.SimpleButton8.Appearance.Options.UseFont = True
        Me.SimpleButton8.Appearance.Options.UseTextOptions = True
        Me.SimpleButton8.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton8.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton8.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton8.Location = New System.Drawing.Point(48, 205)
        Me.SimpleButton8.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton8.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton8.Name = "SimpleButton8"
        Me.SimpleButton8.Size = New System.Drawing.Size(148, 39)
        Me.SimpleButton8.TabIndex = 1320
        Me.SimpleButton8.Text = "الغاء"
        '
        'SimpleButton9
        '
        Me.SimpleButton9.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton9.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton9.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton9.Appearance.Options.UseBackColor = True
        Me.SimpleButton9.Appearance.Options.UseFont = True
        Me.SimpleButton9.Appearance.Options.UseTextOptions = True
        Me.SimpleButton9.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton9.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton9.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton9.Location = New System.Drawing.Point(207, 205)
        Me.SimpleButton9.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton9.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton9.Name = "SimpleButton9"
        Me.SimpleButton9.Size = New System.Drawing.Size(155, 39)
        Me.SimpleButton9.TabIndex = 1319
        Me.SimpleButton9.Text = "موافق"
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.BackColor = System.Drawing.Color.RoyalBlue
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelControl9.Appearance.Options.UseBackColor = True
        Me.LabelControl9.Appearance.Options.UseFont = True
        Me.LabelControl9.Appearance.Options.UseForeColor = True
        Me.LabelControl9.Appearance.Options.UseTextOptions = True
        Me.LabelControl9.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.Location = New System.Drawing.Point(0, 14)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(394, 44)
        Me.LabelControl9.TabIndex = 1318
        Me.LabelControl9.Text = "قم بادخال الكبون"
        '
        'invoice_date
        '
        Me.invoice_date.EditValue = Nothing
        Me.invoice_date.Location = New System.Drawing.Point(6, 71)
        Me.invoice_date.Name = "invoice_date"
        Me.invoice_date.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.invoice_date.Properties.Appearance.Options.UseFont = True
        Me.invoice_date.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.invoice_date.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.invoice_date.Properties.CalendarView = DevExpress.XtraEditors.Repository.CalendarView.TouchUI
        Me.invoice_date.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.[False]
        Me.invoice_date.Size = New System.Drawing.Size(128, 24)
        Me.invoice_date.TabIndex = 1395
        '
        'ComboBoxEdit2
        '
        Me.ComboBoxEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ComboBoxEdit2.Location = New System.Drawing.Point(562, 476)
        Me.ComboBoxEdit2.Name = "ComboBoxEdit2"
        Me.ComboBoxEdit2.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ComboBoxEdit2.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxEdit2.Properties.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.ComboBoxEdit2.Properties.AppearanceDisabled.Options.UseFont = True
        Me.ComboBoxEdit2.Properties.AppearanceDropDown.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.ComboBoxEdit2.Properties.AppearanceDropDown.Options.UseFont = True
        Me.ComboBoxEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit2.Properties.Items.AddRange(New Object() {"نسبة", "قيمة"})
        Me.ComboBoxEdit2.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit2.Size = New System.Drawing.Size(55, 20)
        Me.ComboBoxEdit2.TabIndex = 1385
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(562, 443)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxEdit1.Properties.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.ComboBoxEdit1.Properties.AppearanceDisabled.Options.UseFont = True
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.Options.UseFont = True
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.Items.AddRange(New Object() {"نسبة", "قيمة"})
        Me.ComboBoxEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(55, 20)
        Me.ComboBoxEdit1.TabIndex = 1384
        '
        'store
        '
        Me.store.Location = New System.Drawing.Point(6, 3)
        Me.store.Name = "store"
        Me.store.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.store.Properties.Appearance.Options.UseFont = True
        Me.store.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.store.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.store.Size = New System.Drawing.Size(128, 22)
        Me.store.TabIndex = 1382
        '
        'invoice_number
        '
        Me.invoice_number.Location = New System.Drawing.Point(6, 37)
        Me.invoice_number.Name = "invoice_number"
        Me.invoice_number.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.invoice_number.Properties.Appearance.Options.UseFont = True
        Me.invoice_number.Properties.Appearance.Options.UseTextOptions = True
        Me.invoice_number.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.invoice_number.Properties.AppearanceDisabled.Options.UseTextOptions = True
        Me.invoice_number.Properties.AppearanceDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.invoice_number.Properties.AppearanceFocused.Options.UseTextOptions = True
        Me.invoice_number.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.invoice_number.Properties.AppearanceReadOnly.Options.UseTextOptions = True
        Me.invoice_number.Properties.AppearanceReadOnly.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.invoice_number.Size = New System.Drawing.Size(129, 22)
        Me.invoice_number.TabIndex = 1381
        '
        'LabelControl5
        '
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.Options.UseFont = True
        Me.LabelControl5.Location = New System.Drawing.Point(147, 6)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(36, 15)
        Me.LabelControl5.TabIndex = 1380
        Me.LabelControl5.Text = "المخزن :"
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.Options.UseFont = True
        Me.LabelControl4.Location = New System.Drawing.Point(150, 75)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(34, 15)
        Me.LabelControl4.TabIndex = 1379
        Me.LabelControl4.Text = "التاريخ :"
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.Options.UseFont = True
        Me.LabelControl3.Location = New System.Drawing.Point(140, 40)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(37, 15)
        Me.LabelControl3.TabIndex = 1378
        Me.LabelControl3.Text = "الفاتورة :"
        '
        'Accounts_balace
        '
        Me.Accounts_balace.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Accounts_balace.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.Accounts_balace.Appearance.Options.UseFont = True
        Me.Accounts_balace.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.Accounts_balace.Location = New System.Drawing.Point(266, 0)
        Me.Accounts_balace.Name = "Accounts_balace"
        Me.Accounts_balace.Size = New System.Drawing.Size(95, 25)
        Me.Accounts_balace.TabIndex = 1377
        Me.Accounts_balace.Text = "0.00"
        '
        'Label19
        '
        Me.Label19.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label19.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.Label19.Appearance.Options.UseFont = True
        Me.Label19.Location = New System.Drawing.Point(365, 0)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(44, 18)
        Me.Label19.TabIndex = 1376
        Me.Label19.Text = "الرصيد :"
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.Options.UseFont = True
        Me.LabelControl2.Location = New System.Drawing.Point(595, 69)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(44, 15)
        Me.LabelControl2.TabIndex = 1375
        Me.LabelControl2.Text = "ملاحظات :"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Location = New System.Drawing.Point(595, 27)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(48, 15)
        Me.LabelControl1.TabIndex = 1374
        Me.LabelControl1.Text = "أسم العميل :"
        '
        'Label25
        '
        Me.Label25.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label25.AutoSize = True
        Me.Label25.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label25.Location = New System.Drawing.Point(618, 506)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(35, 15)
        Me.Label25.TabIndex = 1371
        Me.Label25.Text = "المبلغ :"
        '
        'Label23
        '
        Me.Label23.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label23.Location = New System.Drawing.Point(618, 475)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(46, 15)
        Me.Label23.TabIndex = 1370
        Me.Label23.Text = "الضريبة :"
        '
        'Label24
        '
        Me.Label24.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label24.Location = New System.Drawing.Point(618, 442)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(34, 15)
        Me.Label24.TabIndex = 1369
        Me.Label24.Text = "خصم :"
        '
        'TextBox8
        '
        Me.TextBox8.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TextBox8.BackColor = System.Drawing.Color.White
        Me.TextBox8.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBox8.Cursor = System.Windows.Forms.Cursors.Default
        Me.TextBox8.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox8.ForeColor = System.Drawing.Color.Maroon
        Me.TextBox8.Location = New System.Drawing.Point(0, 492)
        Me.TextBox8.Name = "TextBox8"
        Me.TextBox8.ReadOnly = True
        Me.TextBox8.Size = New System.Drawing.Size(87, 18)
        Me.TextBox8.TabIndex = 1368
        Me.TextBox8.Text = "0"
        Me.TextBox8.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'pay_money
        '
        Me.pay_money.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pay_money.Location = New System.Drawing.Point(456, 509)
        Me.pay_money.Name = "pay_money"
        Me.pay_money.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.pay_money.Properties.Appearance.Options.UseFont = True
        Me.pay_money.Properties.Appearance.Options.UseTextOptions = True
        Me.pay_money.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.pay_money.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.pay_money.Properties.Mask.BeepOnError = True
        Me.pay_money.Properties.Mask.EditMask = "f"
        Me.pay_money.Size = New System.Drawing.Size(161, 24)
        Me.pay_money.TabIndex = 1367
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Label3.ForeColor = System.Drawing.Color.Maroon
        Me.Label3.Location = New System.Drawing.Point(87, 489)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(50, 16)
        Me.Label3.TabIndex = 1366
        Me.Label3.Text = "المدفوع :"
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Label1.ForeColor = System.Drawing.Color.Navy
        Me.Label1.Location = New System.Drawing.Point(87, 511)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(44, 16)
        Me.Label1.TabIndex = 1365
        Me.Label1.Text = "الباقي :"
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = CType(resources.GetObject("PictureBox5.Image"), System.Drawing.Image)
        Me.PictureBox5.Location = New System.Drawing.Point(-6, 105)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(264, 3)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox5.TabIndex = 1364
        Me.PictureBox5.TabStop = False
        '
        'new_balace
        '
        Me.new_balace.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.new_balace.BackColor = System.Drawing.Color.White
        Me.new_balace.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.new_balace.Cursor = System.Windows.Forms.Cursors.Default
        Me.new_balace.Enabled = False
        Me.new_balace.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.new_balace.ForeColor = System.Drawing.Color.Navy
        Me.new_balace.Location = New System.Drawing.Point(0, 514)
        Me.new_balace.Name = "new_balace"
        Me.new_balace.ReadOnly = True
        Me.new_balace.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.new_balace.Size = New System.Drawing.Size(87, 18)
        Me.new_balace.TabIndex = 1362
        Me.new_balace.Text = "0"
        Me.new_balace.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label16
        '
        Me.Label16.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Label16.Location = New System.Drawing.Point(87, 466)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(48, 16)
        Me.Label16.TabIndex = 1361
        Me.Label16.Text = "الضريبة :"
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Label9.Location = New System.Drawing.Point(87, 443)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(36, 16)
        Me.Label9.TabIndex = 1360
        Me.Label9.Text = "خصم :"
        '
        'Label46
        '
        Me.Label46.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label46.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Label46.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label46.Image = CType(resources.GetObject("Label46.Image"), System.Drawing.Image)
        Me.Label46.Location = New System.Drawing.Point(662, 143)
        Me.Label46.Name = "Label46"
        Me.Label46.Size = New System.Drawing.Size(17, 19)
        Me.Label46.TabIndex = 1348
        '
        'Label45
        '
        Me.Label45.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label45.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Label45.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label45.Image = CType(resources.GetObject("Label45.Image"), System.Drawing.Image)
        Me.Label45.Location = New System.Drawing.Point(662, 120)
        Me.Label45.Name = "Label45"
        Me.Label45.Size = New System.Drawing.Size(17, 19)
        Me.Label45.TabIndex = 1347
        '
        'Accounts_code
        '
        Me.Accounts_code.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Accounts_code.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Accounts_code.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Accounts_code.Cursor = System.Windows.Forms.Cursors.Default
        Me.Accounts_code.Enabled = False
        Me.Accounts_code.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Accounts_code.Location = New System.Drawing.Point(512, -2)
        Me.Accounts_code.Name = "Accounts_code"
        Me.Accounts_code.Size = New System.Drawing.Size(76, 15)
        Me.Accounts_code.TabIndex = 1346
        Me.Accounts_code.Text = "0"
        Me.Accounts_code.Visible = False
        '
        'invoice_tax
        '
        Me.invoice_tax.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.invoice_tax.BackColor = System.Drawing.Color.White
        Me.invoice_tax.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.invoice_tax.Cursor = System.Windows.Forms.Cursors.Default
        Me.invoice_tax.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.invoice_tax.Location = New System.Drawing.Point(0, 469)
        Me.invoice_tax.Name = "invoice_tax"
        Me.invoice_tax.ReadOnly = True
        Me.invoice_tax.Size = New System.Drawing.Size(87, 18)
        Me.invoice_tax.TabIndex = 1338
        Me.invoice_tax.Text = "0"
        Me.invoice_tax.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label4
        '
        Me.Label4.BackColor = System.Drawing.Color.DodgerBlue
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.White
        Me.Label4.Image = CType(resources.GetObject("Label4.Image"), System.Drawing.Image)
        Me.Label4.Location = New System.Drawing.Point(197, 53)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(56, 23)
        Me.Label4.TabIndex = 1335
        Me.Label4.Text = "الفاتورة"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.BackColor = System.Drawing.Color.DodgerBlue
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.White
        Me.Label2.Image = CType(resources.GetObject("Label2.Image"), System.Drawing.Image)
        Me.Label2.Location = New System.Drawing.Point(207, 28)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(39, 18)
        Me.Label2.TabIndex = 1333
        Me.Label2.Text = "بيانات"
        '
        'invoice_descound
        '
        Me.invoice_descound.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.invoice_descound.BackColor = System.Drawing.Color.White
        Me.invoice_descound.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.invoice_descound.Cursor = System.Windows.Forms.Cursors.Default
        Me.invoice_descound.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.invoice_descound.Location = New System.Drawing.Point(0, 446)
        Me.invoice_descound.Name = "invoice_descound"
        Me.invoice_descound.ReadOnly = True
        Me.invoice_descound.Size = New System.Drawing.Size(87, 18)
        Me.invoice_descound.TabIndex = 1341
        Me.invoice_descound.Text = "0"
        Me.invoice_descound.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = CType(resources.GetObject("PictureBox4.Image"), System.Drawing.Image)
        Me.PictureBox4.Location = New System.Drawing.Point(195, -3)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(63, 114)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox4.TabIndex = 1363
        Me.PictureBox4.TabStop = False
        '
        'check_order
        '
        Me.check_order.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.check_order.Appearance.Options.UseFont = True
        Me.check_order.Cursor = System.Windows.Forms.Cursors.Hand
        Me.check_order.Location = New System.Drawing.Point(225, 9)
        Me.check_order.Name = "check_order"
        Me.check_order.Size = New System.Drawing.Size(7, 16)
        Me.check_order.TabIndex = 1387
        Me.check_order.Text = "0"
        Me.check_order.Visible = False
        '
        'check_suplier
        '
        Me.check_suplier.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.check_suplier.Appearance.Options.UseFont = True
        Me.check_suplier.Cursor = System.Windows.Forms.Cursors.Hand
        Me.check_suplier.Location = New System.Drawing.Point(207, 6)
        Me.check_suplier.Name = "check_suplier"
        Me.check_suplier.Size = New System.Drawing.Size(7, 16)
        Me.check_suplier.TabIndex = 1388
        Me.check_suplier.Text = "0"
        Me.check_suplier.Visible = False
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        GridLevelNode4.RelationName = "Level1"
        Me.GridControl1.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode4})
        Me.GridControl1.Location = New System.Drawing.Point(-3, 179)
        Me.GridControl1.MainView = Me.GridView2
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemCalcEdit1, Me.RepositoryItemCalcEdit2, Me.RepositoryItemCalcEdit3, Me.RepositoryItemCalcEdit4, Me.RepositoryItemButtonEdit2, Me.RepositoryItemButtonEdit3, Me.RepositoryItemButtonEdit4, Me.RepositoryItemButtonEdit5, Me.RepositoryItemComboBox1})
        Me.GridControl1.Size = New System.Drawing.Size(679, 261)
        Me.GridControl1.TabIndex = 1355
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
        '
        'GridView2
        '
        Me.GridView2.Appearance.EvenRow.BackColor = System.Drawing.Color.Gainsboro
        Me.GridView2.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView2.Appearance.FocusedRow.BackColor = System.Drawing.Color.RoyalBlue
        Me.GridView2.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView2.Appearance.FocusedRow.Options.UseForeColor = True
        Me.GridView2.Appearance.FooterPanel.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridView2.Appearance.FooterPanel.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView2.Appearance.FooterPanel.Options.UseBackColor = True
        Me.GridView2.Appearance.FooterPanel.Options.UseFont = True
        Me.GridView2.Appearance.FooterPanel.Options.UseTextOptions = True
        Me.GridView2.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView2.Appearance.HorzLine.BackColor = System.Drawing.Color.White
        Me.GridView2.Appearance.HorzLine.Options.UseBackColor = True
        Me.GridView2.AppearancePrint.EvenRow.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridView2.AppearancePrint.EvenRow.Options.UseBackColor = True
        Me.GridView2.AppearancePrint.FooterPanel.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView2.AppearancePrint.FooterPanel.Options.UseFont = True
        Me.GridView2.AppearancePrint.FooterPanel.Options.UseTextOptions = True
        Me.GridView2.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView2.AppearancePrint.HeaderPanel.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridView2.AppearancePrint.HeaderPanel.Options.UseFont = True
        Me.GridView2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn7, Me.GridColumn8, Me.GridColumn9, Me.GridColumn10, Me.GridColumn13, Me.GridColumn14, Me.GridColumn11, Me.GridColumn15, Me.GridColumn16, Me.GridColumn17, Me.GridColumn18, Me.GridColumn19, Me.GridColumn20, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6, Me.GridColumn21, Me.GridColumn22, Me.GridColumn23, Me.GridColumn12})
        Me.GridView2.CustomizationFormBounds = New System.Drawing.Rectangle(1064, 304, 216, 232)
        Me.GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        GridFormatRule4.Name = "Format0"
        FormatConditionIconSet4.CategoryName = "Ratings"
        FormatConditionIconSetIcon10.PredefinedName = "Stars3_1.png"
        FormatConditionIconSetIcon10.Value = New Decimal(New Integer() {67, 0, 0, 0})
        FormatConditionIconSetIcon10.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon11.PredefinedName = "Stars3_2.png"
        FormatConditionIconSetIcon11.Value = New Decimal(New Integer() {33, 0, 0, 0})
        FormatConditionIconSetIcon11.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSetIcon12.PredefinedName = "Stars3_3.png"
        FormatConditionIconSetIcon12.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual
        FormatConditionIconSet4.Icons.Add(FormatConditionIconSetIcon10)
        FormatConditionIconSet4.Icons.Add(FormatConditionIconSetIcon11)
        FormatConditionIconSet4.Icons.Add(FormatConditionIconSetIcon12)
        FormatConditionIconSet4.Name = "Stars3"
        FormatConditionIconSet4.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent
        FormatConditionRuleIconSet4.IconSet = FormatConditionIconSet4
        GridFormatRule4.Rule = FormatConditionRuleIconSet4
        Me.GridView2.FormatRules.Add(GridFormatRule4)
        Me.GridView2.GridControl = Me.GridControl1
        Me.GridView2.GroupPanelText = "قم بسحب الحقل هنا لتقسيمه"
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsBehavior.AllowFixedGroups = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsBehavior.AllowGroupExpandAnimation = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsPrint.EnableAppearanceEvenRow = True
        Me.GridView2.OptionsPrint.PrintHorzLines = False
        Me.GridView2.OptionsPrint.PrintPreview = True
        Me.GridView2.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView2.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridView2.OptionsView.AllowHtmlDrawHeaders = True
        Me.GridView2.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView2.OptionsView.RowAutoHeight = True
        Me.GridView2.OptionsView.ShowAutoFilterRow = True
        Me.GridView2.OptionsView.ShowFooter = True
        Me.GridView2.OptionsView.ShowGroupPanel = False
        Me.GridView2.PaintStyleName = "Skin"
        Me.GridView2.RowHeight = 25
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn1.AppearanceCell.Options.UseFont = True
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn1.AppearanceHeader.Options.UseFont = True
        Me.GridColumn1.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.Caption = "كود"
        Me.GridColumn1.FieldName = "كود"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        '
        'GridColumn2
        '
        Me.GridColumn2.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn2.AppearanceCell.Options.UseFont = True
        Me.GridColumn2.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn2.AppearanceHeader.Options.UseFont = True
        Me.GridColumn2.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.Caption = "أسم الصنف"
        Me.GridColumn2.FieldName = "أسم الصنف"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.AllowEdit = False
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Count, "أسم الصنف", "{0}", "0")})
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 0
        Me.GridColumn2.Width = 418
        '
        'GridColumn7
        '
        Me.GridColumn7.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn7.AppearanceCell.Options.UseFont = True
        Me.GridColumn7.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn7.AppearanceHeader.Options.UseFont = True
        Me.GridColumn7.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.Caption = "الوحدة"
        Me.GridColumn7.ColumnEdit = Me.RepositoryItemComboBox1
        Me.GridColumn7.FieldName = "الوحدة"
        Me.GridColumn7.Name = "GridColumn7"
        Me.GridColumn7.OptionsColumn.AllowEdit = False
        Me.GridColumn7.OptionsColumn.ReadOnly = True
        Me.GridColumn7.Visible = True
        Me.GridColumn7.VisibleIndex = 1
        Me.GridColumn7.Width = 88
        '
        'RepositoryItemComboBox1
        '
        Me.RepositoryItemComboBox1.AutoHeight = False
        Me.RepositoryItemComboBox1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemComboBox1.Name = "RepositoryItemComboBox1"
        '
        'GridColumn8
        '
        Me.GridColumn8.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn8.AppearanceCell.Options.UseFont = True
        Me.GridColumn8.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn8.AppearanceHeader.Options.UseFont = True
        Me.GridColumn8.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.Caption = "الكمية"
        Me.GridColumn8.ColumnEdit = Me.RepositoryItemCalcEdit1
        Me.GridColumn8.FieldName = "الكمية"
        Me.GridColumn8.Name = "GridColumn8"
        Me.GridColumn8.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Count, "الكمية", "{0:0.##}", "0")})
        Me.GridColumn8.Visible = True
        Me.GridColumn8.VisibleIndex = 2
        Me.GridColumn8.Width = 88
        '
        'RepositoryItemCalcEdit1
        '
        Me.RepositoryItemCalcEdit1.AutoHeight = False
        Me.RepositoryItemCalcEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemCalcEdit1.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.RepositoryItemCalcEdit1.Name = "RepositoryItemCalcEdit1"
        '
        'GridColumn9
        '
        Me.GridColumn9.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn9.AppearanceCell.Options.UseFont = True
        Me.GridColumn9.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn9.AppearanceHeader.Options.UseFont = True
        Me.GridColumn9.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.Caption = "السعر"
        Me.GridColumn9.ColumnEdit = Me.RepositoryItemCalcEdit2
        Me.GridColumn9.FieldName = "السعر"
        Me.GridColumn9.Name = "GridColumn9"
        Me.GridColumn9.Visible = True
        Me.GridColumn9.VisibleIndex = 3
        Me.GridColumn9.Width = 88
        '
        'RepositoryItemCalcEdit2
        '
        Me.RepositoryItemCalcEdit2.AutoHeight = False
        Me.RepositoryItemCalcEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemCalcEdit2.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.RepositoryItemCalcEdit2.Name = "RepositoryItemCalcEdit2"
        '
        'GridColumn10
        '
        Me.GridColumn10.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn10.AppearanceCell.Options.UseFont = True
        Me.GridColumn10.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn10.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn10.AppearanceHeader.Options.UseFont = True
        Me.GridColumn10.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn10.Caption = "الخصم"
        Me.GridColumn10.ColumnEdit = Me.RepositoryItemCalcEdit3
        Me.GridColumn10.FieldName = "الخصم"
        Me.GridColumn10.Name = "GridColumn10"
        Me.GridColumn10.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "الخصم", "{0:0.##}", "0")})
        Me.GridColumn10.Visible = True
        Me.GridColumn10.VisibleIndex = 4
        Me.GridColumn10.Width = 88
        '
        'RepositoryItemCalcEdit3
        '
        Me.RepositoryItemCalcEdit3.AutoHeight = False
        Me.RepositoryItemCalcEdit3.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemCalcEdit3.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.RepositoryItemCalcEdit3.Name = "RepositoryItemCalcEdit3"
        '
        'GridColumn13
        '
        Me.GridColumn13.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn13.AppearanceCell.Options.UseFont = True
        Me.GridColumn13.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn13.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn13.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn13.AppearanceHeader.Options.UseFont = True
        Me.GridColumn13.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn13.Caption = "الأجمالي"
        Me.GridColumn13.ColumnEdit = Me.RepositoryItemCalcEdit4
        Me.GridColumn13.FieldName = "الأجمالي"
        Me.GridColumn13.Name = "GridColumn13"
        Me.GridColumn13.OptionsColumn.AllowEdit = False
        Me.GridColumn13.OptionsColumn.ReadOnly = True
        Me.GridColumn13.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "الأجمالي", "{0:0.##}", CType(0, Short))})
        Me.GridColumn13.Visible = True
        Me.GridColumn13.VisibleIndex = 5
        Me.GridColumn13.Width = 88
        '
        'RepositoryItemCalcEdit4
        '
        Me.RepositoryItemCalcEdit4.AutoHeight = False
        Me.RepositoryItemCalcEdit4.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemCalcEdit4.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.RepositoryItemCalcEdit4.Name = "RepositoryItemCalcEdit4"
        '
        'GridColumn14
        '
        Me.GridColumn14.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn14.AppearanceCell.Options.UseFont = True
        Me.GridColumn14.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn14.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn14.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn14.AppearanceHeader.Options.UseFont = True
        Me.GridColumn14.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn14.Caption = "الربح"
        Me.GridColumn14.FieldName = "الربح"
        Me.GridColumn14.Name = "GridColumn14"
        Me.GridColumn14.OptionsColumn.AllowEdit = False
        Me.GridColumn14.OptionsColumn.ReadOnly = True
        Me.GridColumn14.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "الربح", "{0:0.##}", "0")})
        '
        'GridColumn11
        '
        Me.GridColumn11.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn11.AppearanceCell.Options.UseFont = True
        Me.GridColumn11.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn11.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn11.AppearanceHeader.Options.UseFont = True
        Me.GridColumn11.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn11.Caption = "التصنيف"
        Me.GridColumn11.FieldName = "التصنيف"
        Me.GridColumn11.Name = "GridColumn11"
        Me.GridColumn11.OptionsColumn.AllowEdit = False
        Me.GridColumn11.OptionsColumn.ReadOnly = True
        '
        'GridColumn15
        '
        Me.GridColumn15.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn15.AppearanceCell.Options.UseFont = True
        Me.GridColumn15.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn15.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn15.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn15.AppearanceHeader.Options.UseFont = True
        Me.GridColumn15.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn15.Caption = "الشركة"
        Me.GridColumn15.FieldName = "الشركة"
        Me.GridColumn15.Name = "GridColumn15"
        Me.GridColumn15.OptionsColumn.AllowEdit = False
        Me.GridColumn15.OptionsColumn.ReadOnly = True
        '
        'GridColumn16
        '
        Me.GridColumn16.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn16.AppearanceCell.Options.UseFont = True
        Me.GridColumn16.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn16.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn16.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn16.AppearanceHeader.Options.UseFont = True
        Me.GridColumn16.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn16.Caption = "ضريبة الصنف"
        Me.GridColumn16.FieldName = "ضريبة الصنف"
        Me.GridColumn16.Name = "GridColumn16"
        Me.GridColumn16.OptionsColumn.AllowEdit = False
        Me.GridColumn16.OptionsColumn.ReadOnly = True
        Me.GridColumn16.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "ضريبة الصنف", "{0:0.##}", "0")})
        '
        'GridColumn17
        '
        Me.GridColumn17.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn17.AppearanceCell.Options.UseFont = True
        Me.GridColumn17.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn17.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn17.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn17.AppearanceHeader.Options.UseFont = True
        Me.GridColumn17.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn17.Caption = "مصنعية التركيب"
        Me.GridColumn17.FieldName = "مصنعية التركيب"
        Me.GridColumn17.Name = "GridColumn17"
        Me.GridColumn17.OptionsColumn.AllowEdit = False
        Me.GridColumn17.OptionsColumn.ReadOnly = True
        Me.GridColumn17.Summary.AddRange(New DevExpress.XtraGrid.GridSummaryItem() {New DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "مصنعية التركيب", "{0:0.##}", "0")})
        '
        'GridColumn18
        '
        Me.GridColumn18.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn18.AppearanceCell.Options.UseFont = True
        Me.GridColumn18.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn18.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn18.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn18.AppearanceHeader.Options.UseFont = True
        Me.GridColumn18.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn18.Caption = "الكمية الحالية"
        Me.GridColumn18.FieldName = "الكمية الحالية"
        Me.GridColumn18.Name = "GridColumn18"
        Me.GridColumn18.OptionsColumn.AllowEdit = False
        Me.GridColumn18.OptionsColumn.ReadOnly = True
        '
        'GridColumn19
        '
        Me.GridColumn19.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn19.AppearanceCell.Options.UseFont = True
        Me.GridColumn19.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn19.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn19.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn19.AppearanceHeader.Options.UseFont = True
        Me.GridColumn19.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn19.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn19.Caption = "المجموعة"
        Me.GridColumn19.FieldName = "المجموعة"
        Me.GridColumn19.Name = "GridColumn19"
        Me.GridColumn19.OptionsColumn.AllowEdit = False
        Me.GridColumn19.OptionsColumn.ReadOnly = True
        '
        'GridColumn20
        '
        Me.GridColumn20.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn20.AppearanceCell.Options.UseFont = True
        Me.GridColumn20.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn20.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn20.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn20.AppearanceHeader.Options.UseFont = True
        Me.GridColumn20.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn20.Caption = "الرصيد"
        Me.GridColumn20.FieldName = "الرصيد"
        Me.GridColumn20.Name = "GridColumn20"
        Me.GridColumn20.OptionsColumn.AllowEdit = False
        Me.GridColumn20.OptionsColumn.ReadOnly = True
        '
        'GridColumn3
        '
        Me.GridColumn3.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn3.AppearanceCell.Options.UseFont = True
        Me.GridColumn3.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn3.AppearanceHeader.Options.UseFont = True
        Me.GridColumn3.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.Caption = "سعر الشراء"
        Me.GridColumn3.FieldName = "سعر الشراء"
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.AllowEdit = False
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Width = 54
        '
        'GridColumn4
        '
        Me.GridColumn4.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn4.AppearanceCell.Options.UseFont = True
        Me.GridColumn4.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn4.AppearanceHeader.Options.UseFont = True
        Me.GridColumn4.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.Caption = "نوع الصنف"
        Me.GridColumn4.FieldName = "نوع الصنف"
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.AllowEdit = False
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Width = 54
        '
        'GridColumn5
        '
        Me.GridColumn5.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn5.AppearanceCell.Options.UseFont = True
        Me.GridColumn5.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn5.AppearanceHeader.Options.UseFont = True
        Me.GridColumn5.ColumnEdit = Me.RepositoryItemButtonEdit2
        Me.GridColumn5.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.OptionsColumn.AllowSize = False
        Me.GridColumn5.OptionsColumn.Printable = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridColumn5.OptionsFilter.AllowAutoFilter = False
        Me.GridColumn5.OptionsFilter.AllowFilter = False
        Me.GridColumn5.OptionsFilter.ImmediateUpdateAutoFilter = False
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 7
        Me.GridColumn5.Width = 20
        '
        'RepositoryItemButtonEdit2
        '
        Me.RepositoryItemButtonEdit2.AutoHeight = False
        EditorButtonImageOptions1.Image = CType(resources.GetObject("EditorButtonImageOptions1.Image"), System.Drawing.Image)
        SerializableAppearanceObject1.Options.UseImage = True
        Me.RepositoryItemButtonEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions1, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject1, SerializableAppearanceObject2, SerializableAppearanceObject3, SerializableAppearanceObject4, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.RepositoryItemButtonEdit2.Name = "RepositoryItemButtonEdit2"
        Me.RepositoryItemButtonEdit2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn6
        '
        Me.GridColumn6.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn6.AppearanceCell.Options.UseFont = True
        Me.GridColumn6.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn6.AppearanceHeader.Options.UseFont = True
        Me.GridColumn6.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.Caption = "بعد الخصم"
        Me.GridColumn6.FieldName = "بعد الخصم"
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.OptionsColumn.AllowEdit = False
        Me.GridColumn6.OptionsColumn.ReadOnly = True
        Me.GridColumn6.Width = 65
        '
        'GridColumn21
        '
        Me.GridColumn21.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn21.AppearanceCell.Options.UseFont = True
        Me.GridColumn21.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn21.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn21.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn21.AppearanceHeader.Options.UseFont = True
        Me.GridColumn21.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn21.Caption = "خصم نقدي"
        Me.GridColumn21.FieldName = "خصم نقدي"
        Me.GridColumn21.Name = "GridColumn21"
        Me.GridColumn21.OptionsColumn.AllowEdit = False
        Me.GridColumn21.OptionsColumn.ReadOnly = True
        '
        'GridColumn22
        '
        Me.GridColumn22.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn22.AppearanceCell.Options.UseFont = True
        Me.GridColumn22.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn22.AppearanceHeader.Options.UseFont = True
        Me.GridColumn22.ColumnEdit = Me.RepositoryItemButtonEdit3
        Me.GridColumn22.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.GridColumn22.Name = "GridColumn22"
        Me.GridColumn22.OptionsColumn.AllowSize = False
        Me.GridColumn22.OptionsColumn.Printable = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridColumn22.OptionsFilter.AllowAutoFilter = False
        Me.GridColumn22.OptionsFilter.AllowFilter = False
        Me.GridColumn22.OptionsFilter.ImmediateUpdateAutoFilter = False
        Me.GridColumn22.Visible = True
        Me.GridColumn22.VisibleIndex = 8
        Me.GridColumn22.Width = 20
        '
        'RepositoryItemButtonEdit3
        '
        Me.RepositoryItemButtonEdit3.AutoHeight = False
        EditorButtonImageOptions2.Image = CType(resources.GetObject("EditorButtonImageOptions2.Image"), System.Drawing.Image)
        Me.RepositoryItemButtonEdit3.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions2, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject5, SerializableAppearanceObject6, SerializableAppearanceObject7, SerializableAppearanceObject8, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.RepositoryItemButtonEdit3.Name = "RepositoryItemButtonEdit3"
        Me.RepositoryItemButtonEdit3.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn23
        '
        Me.GridColumn23.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn23.AppearanceCell.Options.UseFont = True
        Me.GridColumn23.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn23.AppearanceHeader.Options.UseFont = True
        Me.GridColumn23.ColumnEdit = Me.RepositoryItemButtonEdit4
        Me.GridColumn23.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText
        Me.GridColumn23.Name = "GridColumn23"
        Me.GridColumn23.OptionsColumn.AllowSize = False
        Me.GridColumn23.OptionsColumn.Printable = DevExpress.Utils.DefaultBoolean.[False]
        Me.GridColumn23.OptionsFilter.AllowAutoFilter = False
        Me.GridColumn23.OptionsFilter.AllowFilter = False
        Me.GridColumn23.OptionsFilter.ImmediateUpdateAutoFilter = False
        Me.GridColumn23.Visible = True
        Me.GridColumn23.VisibleIndex = 9
        Me.GridColumn23.Width = 20
        '
        'RepositoryItemButtonEdit4
        '
        Me.RepositoryItemButtonEdit4.AutoHeight = False
        EditorButtonImageOptions3.Image = CType(resources.GetObject("EditorButtonImageOptions3.Image"), System.Drawing.Image)
        Me.RepositoryItemButtonEdit4.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions3, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject9, SerializableAppearanceObject10, SerializableAppearanceObject11, SerializableAppearanceObject12, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.RepositoryItemButtonEdit4.Name = "RepositoryItemButtonEdit4"
        Me.RepositoryItemButtonEdit4.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn12
        '
        Me.GridColumn12.ColumnEdit = Me.RepositoryItemButtonEdit5
        Me.GridColumn12.Name = "GridColumn12"
        Me.GridColumn12.OptionsColumn.AllowSize = False
        Me.GridColumn12.OptionsFilter.AllowAutoFilter = False
        Me.GridColumn12.OptionsFilter.AllowFilter = False
        Me.GridColumn12.Visible = True
        Me.GridColumn12.VisibleIndex = 6
        Me.GridColumn12.Width = 21
        '
        'RepositoryItemButtonEdit5
        '
        Me.RepositoryItemButtonEdit5.AutoHeight = False
        EditorButtonImageOptions4.Image = CType(resources.GetObject("EditorButtonImageOptions4.Image"), System.Drawing.Image)
        Me.RepositoryItemButtonEdit5.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions4, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject13, SerializableAppearanceObject14, SerializableAppearanceObject15, SerializableAppearanceObject16, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.RepositoryItemButtonEdit5.Name = "RepositoryItemButtonEdit5"
        Me.RepositoryItemButtonEdit5.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'invoice_count
        '
        Me.invoice_count.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.invoice_count.BackColor = System.Drawing.Color.WhiteSmoke
        Me.invoice_count.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.invoice_count.Cursor = System.Windows.Forms.Cursors.Default
        Me.invoice_count.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.invoice_count.Location = New System.Drawing.Point(60, 368)
        Me.invoice_count.Name = "invoice_count"
        Me.invoice_count.ReadOnly = True
        Me.invoice_count.Size = New System.Drawing.Size(87, 25)
        Me.invoice_count.TabIndex = 1349
        Me.invoice_count.Text = "0"
        Me.invoice_count.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'total_invoice
        '
        Me.total_invoice.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.total_invoice.BackColor = System.Drawing.Color.WhiteSmoke
        Me.total_invoice.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.total_invoice.Cursor = System.Windows.Forms.Cursors.Default
        Me.total_invoice.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.total_invoice.Location = New System.Drawing.Point(173, 365)
        Me.total_invoice.Name = "total_invoice"
        Me.total_invoice.ReadOnly = True
        Me.total_invoice.Size = New System.Drawing.Size(87, 18)
        Me.total_invoice.TabIndex = 1339
        Me.total_invoice.Text = "0"
        Me.total_invoice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'after_total
        '
        Me.after_total.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.after_total.BackColor = System.Drawing.Color.WhiteSmoke
        Me.after_total.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.after_total.Cursor = System.Windows.Forms.Cursors.Default
        Me.after_total.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.after_total.Location = New System.Drawing.Point(136, 370)
        Me.after_total.Name = "after_total"
        Me.after_total.ReadOnly = True
        Me.after_total.Size = New System.Drawing.Size(87, 18)
        Me.after_total.TabIndex = 1340
        Me.after_total.Text = "0"
        Me.after_total.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'invoice_note
        '
        Me.invoice_note.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.invoice_note.Location = New System.Drawing.Point(328, 67)
        Me.invoice_note.Name = "invoice_note"
        Me.invoice_note.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.invoice_note.Properties.Appearance.Options.UseFont = True
        Me.invoice_note.Size = New System.Drawing.Size(262, 24)
        Me.invoice_note.TabIndex = 1383
        '
        'invoice_pound
        '
        Me.invoice_pound.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.invoice_pound.BackColor = System.Drawing.Color.WhiteSmoke
        Me.invoice_pound.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.invoice_pound.Cursor = System.Windows.Forms.Cursors.Default
        Me.invoice_pound.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.invoice_pound.Location = New System.Drawing.Point(229, 363)
        Me.invoice_pound.Name = "invoice_pound"
        Me.invoice_pound.ReadOnly = True
        Me.invoice_pound.Size = New System.Drawing.Size(87, 25)
        Me.invoice_pound.TabIndex = 1345
        Me.invoice_pound.Text = "0"
        Me.invoice_pound.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'dgv_5
        '
        Me.dgv_5.AllowUserToAddRows = False
        Me.dgv_5.AllowUserToDeleteRows = False
        DataGridViewCellStyle35.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle35.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle35.Font = New System.Drawing.Font("Tahoma", 8.25!)
        DataGridViewCellStyle35.NullValue = "30/04"
        DataGridViewCellStyle35.SelectionBackColor = System.Drawing.Color.SeaGreen
        Me.dgv_5.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle35
        Me.dgv_5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgv_5.BackgroundColor = System.Drawing.Color.White
        Me.dgv_5.BorderStyle = System.Windows.Forms.BorderStyle.None
        DataGridViewCellStyle36.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle36.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle36.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle36.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle36.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle36.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle36.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_5.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle36
        Me.dgv_5.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_5.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn6, Me.DataGridViewTextBoxColumn7, Me.DataGridViewTextBoxColumn8, Me.DataGridViewTextBoxColumn9, Me.Column4, Me.DataGridViewTextBoxColumn10})
        DataGridViewCellStyle38.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle38.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle38.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle38.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle38.SelectionBackColor = System.Drawing.Color.SeaGreen
        DataGridViewCellStyle38.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle38.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_5.DefaultCellStyle = DataGridViewCellStyle38
        Me.dgv_5.Location = New System.Drawing.Point(258, -2)
        Me.dgv_5.Name = "dgv_5"
        Me.dgv_5.ReadOnly = True
        Me.dgv_5.RowHeadersVisible = False
        Me.dgv_5.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_5.Size = New System.Drawing.Size(421, 92)
        Me.dgv_5.TabIndex = 1354
        Me.dgv_5.Visible = False
        '
        'DataGridViewTextBoxColumn6
        '
        Me.DataGridViewTextBoxColumn6.DataPropertyName = "invoice_number"
        Me.DataGridViewTextBoxColumn6.HeaderText = "رقم الفاتورة"
        Me.DataGridViewTextBoxColumn6.Name = "DataGridViewTextBoxColumn6"
        Me.DataGridViewTextBoxColumn6.ReadOnly = True
        Me.DataGridViewTextBoxColumn6.Width = 111
        '
        'DataGridViewTextBoxColumn7
        '
        Me.DataGridViewTextBoxColumn7.DataPropertyName = "item_date"
        DataGridViewCellStyle37.Format = "d"
        DataGridViewCellStyle37.NullValue = Nothing
        Me.DataGridViewTextBoxColumn7.DefaultCellStyle = DataGridViewCellStyle37
        Me.DataGridViewTextBoxColumn7.HeaderText = "التاريخ"
        Me.DataGridViewTextBoxColumn7.Name = "DataGridViewTextBoxColumn7"
        Me.DataGridViewTextBoxColumn7.ReadOnly = True
        Me.DataGridViewTextBoxColumn7.Width = 103
        '
        'DataGridViewTextBoxColumn8
        '
        Me.DataGridViewTextBoxColumn8.DataPropertyName = "item_name"
        Me.DataGridViewTextBoxColumn8.HeaderText = "اسم الصنف"
        Me.DataGridViewTextBoxColumn8.Name = "DataGridViewTextBoxColumn8"
        Me.DataGridViewTextBoxColumn8.ReadOnly = True
        Me.DataGridViewTextBoxColumn8.Width = 250
        '
        'DataGridViewTextBoxColumn9
        '
        Me.DataGridViewTextBoxColumn9.DataPropertyName = "item_price"
        Me.DataGridViewTextBoxColumn9.HeaderText = "السعر"
        Me.DataGridViewTextBoxColumn9.Name = "DataGridViewTextBoxColumn9"
        Me.DataGridViewTextBoxColumn9.ReadOnly = True
        Me.DataGridViewTextBoxColumn9.Width = 78
        '
        'Column4
        '
        Me.Column4.DataPropertyName = "item_count"
        Me.Column4.HeaderText = "الكمية"
        Me.Column4.Name = "Column4"
        Me.Column4.ReadOnly = True
        '
        'DataGridViewTextBoxColumn10
        '
        Me.DataGridViewTextBoxColumn10.DataPropertyName = "item_descound"
        Me.DataGridViewTextBoxColumn10.HeaderText = "الخصم"
        Me.DataGridViewTextBoxColumn10.Name = "DataGridViewTextBoxColumn10"
        Me.DataGridViewTextBoxColumn10.ReadOnly = True
        Me.DataGridViewTextBoxColumn10.Width = 70
        '
        'dgv_final_expire
        '
        Me.dgv_final_expire.AllowUserToAddRows = False
        DataGridViewCellStyle39.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle39.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle39.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.dgv_final_expire.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle39
        Me.dgv_final_expire.BackgroundColor = System.Drawing.Color.White
        Me.dgv_final_expire.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        DataGridViewCellStyle40.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle40.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle40.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle40.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle40.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle40.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle40.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_final_expire.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle40
        Me.dgv_final_expire.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_final_expire.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn33, Me.DataGridViewTextBoxColumn36, Me.DataGridViewTextBoxColumn37})
        DataGridViewCellStyle43.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle43.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle43.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle43.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle43.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle43.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle43.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_final_expire.DefaultCellStyle = DataGridViewCellStyle43
        Me.dgv_final_expire.GridColor = System.Drawing.Color.AliceBlue
        Me.dgv_final_expire.Location = New System.Drawing.Point(-219, -6)
        Me.dgv_final_expire.Name = "dgv_final_expire"
        Me.dgv_final_expire.RowHeadersVisible = False
        Me.dgv_final_expire.RowHeadersWidth = 49
        Me.dgv_final_expire.Size = New System.Drawing.Size(389, 112)
        Me.dgv_final_expire.TabIndex = 1391
        Me.dgv_final_expire.Visible = False
        '
        'DataGridViewTextBoxColumn33
        '
        Me.DataGridViewTextBoxColumn33.DataPropertyName = "item_code"
        DataGridViewCellStyle41.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewTextBoxColumn33.DefaultCellStyle = DataGridViewCellStyle41
        Me.DataGridViewTextBoxColumn33.HeaderText = "الكود"
        Me.DataGridViewTextBoxColumn33.Name = "DataGridViewTextBoxColumn33"
        Me.DataGridViewTextBoxColumn33.ReadOnly = True
        Me.DataGridViewTextBoxColumn33.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn33.Width = 70
        '
        'DataGridViewTextBoxColumn36
        '
        Me.DataGridViewTextBoxColumn36.DataPropertyName = "item_date"
        DataGridViewCellStyle42.Format = "d"
        DataGridViewCellStyle42.NullValue = Nothing
        Me.DataGridViewTextBoxColumn36.DefaultCellStyle = DataGridViewCellStyle42
        Me.DataGridViewTextBoxColumn36.HeaderText = "التاريخ"
        Me.DataGridViewTextBoxColumn36.Name = "DataGridViewTextBoxColumn36"
        Me.DataGridViewTextBoxColumn36.ReadOnly = True
        '
        'DataGridViewTextBoxColumn37
        '
        Me.DataGridViewTextBoxColumn37.HeaderText = "الكمية"
        Me.DataGridViewTextBoxColumn37.Name = "DataGridViewTextBoxColumn37"
        '
        'dgv_serial_no
        '
        Me.dgv_serial_no.AllowUserToAddRows = False
        DataGridViewCellStyle44.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle44.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle44.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.dgv_serial_no.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle44
        Me.dgv_serial_no.BackgroundColor = System.Drawing.Color.White
        Me.dgv_serial_no.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        DataGridViewCellStyle45.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle45.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle45.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle45.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle45.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle45.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle45.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_serial_no.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle45
        Me.dgv_serial_no.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_serial_no.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn41, Me.DataGridViewTextBoxColumn43})
        DataGridViewCellStyle47.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle47.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle47.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle47.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle47.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle47.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle47.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_serial_no.DefaultCellStyle = DataGridViewCellStyle47
        Me.dgv_serial_no.GridColor = System.Drawing.Color.AliceBlue
        Me.dgv_serial_no.Location = New System.Drawing.Point(-219, -78)
        Me.dgv_serial_no.Name = "dgv_serial_no"
        Me.dgv_serial_no.RowHeadersVisible = False
        Me.dgv_serial_no.RowHeadersWidth = 49
        Me.dgv_serial_no.Size = New System.Drawing.Size(340, 118)
        Me.dgv_serial_no.TabIndex = 1394
        Me.dgv_serial_no.Visible = False
        '
        'DataGridViewTextBoxColumn41
        '
        Me.DataGridViewTextBoxColumn41.DataPropertyName = "item_code"
        DataGridViewCellStyle46.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DataGridViewTextBoxColumn41.DefaultCellStyle = DataGridViewCellStyle46
        Me.DataGridViewTextBoxColumn41.HeaderText = "الكود"
        Me.DataGridViewTextBoxColumn41.Name = "DataGridViewTextBoxColumn41"
        Me.DataGridViewTextBoxColumn41.ReadOnly = True
        Me.DataGridViewTextBoxColumn41.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewTextBoxColumn41.Width = 70
        '
        'DataGridViewTextBoxColumn43
        '
        Me.DataGridViewTextBoxColumn43.HeaderText = "السيريل"
        Me.DataGridViewTextBoxColumn43.Name = "DataGridViewTextBoxColumn43"
        Me.DataGridViewTextBoxColumn43.Width = 250
        '
        'descound_Values
        '
        Me.descound_Values.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.descound_Values.Location = New System.Drawing.Point(456, 444)
        Me.descound_Values.Name = "descound_Values"
        Me.descound_Values.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.descound_Values.Properties.Appearance.Options.UseFont = True
        Me.descound_Values.Properties.Appearance.Options.UseTextOptions = True
        Me.descound_Values.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.descound_Values.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.descound_Values.Properties.Mask.BeepOnError = True
        Me.descound_Values.Properties.Mask.EditMask = "f"
        Me.descound_Values.Size = New System.Drawing.Size(100, 24)
        Me.descound_Values.TabIndex = 1357
        '
        'descound_Rate
        '
        Me.descound_Rate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.descound_Rate.Location = New System.Drawing.Point(456, 444)
        Me.descound_Rate.Name = "descound_Rate"
        Me.descound_Rate.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.descound_Rate.Properties.Appearance.Options.UseFont = True
        Me.descound_Rate.Properties.Appearance.Options.UseTextOptions = True
        Me.descound_Rate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.descound_Rate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.descound_Rate.Properties.Mask.BeepOnError = True
        Me.descound_Rate.Properties.Mask.EditMask = "f"
        Me.descound_Rate.Size = New System.Drawing.Size(100, 24)
        Me.descound_Rate.TabIndex = 1356
        '
        'show_total
        '
        Me.show_total.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.show_total.BackColor = System.Drawing.Color.White
        Me.show_total.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.show_total.Cursor = System.Windows.Forms.Cursors.Default
        Me.show_total.Font = New System.Drawing.Font("Microsoft Sans Serif", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.show_total.ForeColor = System.Drawing.Color.Black
        Me.show_total.Location = New System.Drawing.Point(139, 458)
        Me.show_total.Name = "show_total"
        Me.show_total.ReadOnly = True
        Me.show_total.Size = New System.Drawing.Size(192, 42)
        Me.show_total.TabIndex = 1398
        Me.show_total.Text = "0"
        Me.show_total.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'curency_item
        '
        Me.curency_item.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.curency_item.Location = New System.Drawing.Point(135, 317)
        Me.curency_item.Name = "curency_item"
        Me.curency_item.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.curency_item.Properties.Appearance.Options.UseFont = True
        Me.curency_item.Properties.Appearance.Options.UseTextOptions = True
        Me.curency_item.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.curency_item.Properties.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.curency_item.Properties.AppearanceDisabled.Options.UseFont = True
        Me.curency_item.Properties.AppearanceDropDown.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.curency_item.Properties.AppearanceDropDown.Options.UseFont = True
        Me.curency_item.Properties.AppearanceDropDownHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.curency_item.Properties.AppearanceDropDownHeader.Options.UseFont = True
        Me.curency_item.Properties.AppearanceFocused.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.curency_item.Properties.AppearanceFocused.Options.UseFont = True
        Me.curency_item.Properties.AppearanceReadOnly.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!)
        Me.curency_item.Properties.AppearanceReadOnly.Options.UseFont = True
        Me.curency_item.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.curency_item.Properties.CaseSensitiveSearch = True
        Me.curency_item.Properties.Columns.AddRange(New DevExpress.XtraEditors.Controls.LookUpColumnInfo() {New DevExpress.XtraEditors.Controls.LookUpColumnInfo("c1", "الاسم", 10, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default]), New DevExpress.XtraEditors.Controls.LookUpColumnInfo("c2", "السعر", 80, DevExpress.Utils.FormatType.None, "", True, DevExpress.Utils.HorzAlignment.[Default], DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.[Default])})
        Me.curency_item.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains
        Me.curency_item.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard
        Me.curency_item.Size = New System.Drawing.Size(4, 22)
        Me.curency_item.TabIndex = 1403
        '
        'dgv_4
        '
        Me.dgv_4.AllowUserToAddRows = False
        Me.dgv_4.AllowUserToDeleteRows = False
        DataGridViewCellStyle48.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle48.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle48.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        DataGridViewCellStyle48.NullValue = "30/04"
        DataGridViewCellStyle48.SelectionBackColor = System.Drawing.Color.SeaGreen
        Me.dgv_4.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle48
        Me.dgv_4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgv_4.BackgroundColor = System.Drawing.Color.White
        Me.dgv_4.BorderStyle = System.Windows.Forms.BorderStyle.None
        DataGridViewCellStyle49.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle49.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle49.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle49.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle49.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle49.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle49.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_4.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle49
        Me.dgv_4.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgv_4.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn2, Me.DataGridViewTextBoxColumn3, Me.DataGridViewTextBoxColumn4, Me.DataGridViewTextBoxColumn5, Me.Column20})
        DataGridViewCellStyle51.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle51.BackColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle51.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle51.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle51.SelectionBackColor = System.Drawing.Color.SeaGreen
        DataGridViewCellStyle51.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle51.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_4.DefaultCellStyle = DataGridViewCellStyle51
        Me.dgv_4.Location = New System.Drawing.Point(262, -1)
        Me.dgv_4.Name = "dgv_4"
        Me.dgv_4.ReadOnly = True
        Me.dgv_4.RowHeadersVisible = False
        Me.dgv_4.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_4.Size = New System.Drawing.Size(417, 94)
        Me.dgv_4.TabIndex = 1353
        Me.dgv_4.Visible = False
        '
        'DataGridViewTextBoxColumn2
        '
        Me.DataGridViewTextBoxColumn2.DataPropertyName = "Purchases_number"
        Me.DataGridViewTextBoxColumn2.HeaderText = "رقم الفاتورة"
        Me.DataGridViewTextBoxColumn2.Name = "DataGridViewTextBoxColumn2"
        Me.DataGridViewTextBoxColumn2.ReadOnly = True
        Me.DataGridViewTextBoxColumn2.Width = 111
        '
        'DataGridViewTextBoxColumn3
        '
        Me.DataGridViewTextBoxColumn3.DataPropertyName = "item_date"
        DataGridViewCellStyle50.Format = "d"
        DataGridViewCellStyle50.NullValue = Nothing
        Me.DataGridViewTextBoxColumn3.DefaultCellStyle = DataGridViewCellStyle50
        Me.DataGridViewTextBoxColumn3.HeaderText = "التاريخ"
        Me.DataGridViewTextBoxColumn3.Name = "DataGridViewTextBoxColumn3"
        Me.DataGridViewTextBoxColumn3.ReadOnly = True
        Me.DataGridViewTextBoxColumn3.Width = 103
        '
        'DataGridViewTextBoxColumn4
        '
        Me.DataGridViewTextBoxColumn4.DataPropertyName = "item_name"
        Me.DataGridViewTextBoxColumn4.HeaderText = "اسم الصنف"
        Me.DataGridViewTextBoxColumn4.Name = "DataGridViewTextBoxColumn4"
        Me.DataGridViewTextBoxColumn4.ReadOnly = True
        Me.DataGridViewTextBoxColumn4.Width = 250
        '
        'DataGridViewTextBoxColumn5
        '
        Me.DataGridViewTextBoxColumn5.DataPropertyName = "item_price"
        Me.DataGridViewTextBoxColumn5.HeaderText = "السعر"
        Me.DataGridViewTextBoxColumn5.Name = "DataGridViewTextBoxColumn5"
        Me.DataGridViewTextBoxColumn5.ReadOnly = True
        Me.DataGridViewTextBoxColumn5.Width = 78
        '
        'Column20
        '
        Me.Column20.DataPropertyName = "item_descound"
        Me.Column20.HeaderText = "الخصم"
        Me.Column20.Name = "Column20"
        Me.Column20.ReadOnly = True
        Me.Column20.Width = 70
        '
        'data_sale
        '
        Me.data_sale.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.data_sale.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.data_sale.Location = New System.Drawing.Point(494, -12)
        Me.data_sale.Name = "data_sale"
        Me.data_sale.Size = New System.Drawing.Size(98, 25)
        Me.data_sale.TabIndex = 1350
        Me.data_sale.Value = New Date(2019, 4, 30, 0, 0, 0, 0)
        Me.data_sale.Visible = False
        '
        'item_barcode
        '
        Me.item_barcode.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.item_barcode.Font = New System.Drawing.Font("Arial", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_barcode.Location = New System.Drawing.Point(509, 303)
        Me.item_barcode.Name = "item_barcode"
        Me.item_barcode.Size = New System.Drawing.Size(51, 32)
        Me.item_barcode.TabIndex = 1158
        Me.item_barcode.Visible = False
        '
        'tax_Rate
        '
        Me.tax_Rate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tax_Rate.Location = New System.Drawing.Point(456, 476)
        Me.tax_Rate.Name = "tax_Rate"
        Me.tax_Rate.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.tax_Rate.Properties.Appearance.Options.UseFont = True
        Me.tax_Rate.Properties.Appearance.Options.UseTextOptions = True
        Me.tax_Rate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.tax_Rate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.tax_Rate.Properties.Mask.BeepOnError = True
        Me.tax_Rate.Properties.Mask.EditMask = "f"
        Me.tax_Rate.Size = New System.Drawing.Size(100, 24)
        Me.tax_Rate.TabIndex = 1358
        '
        'tax_Values
        '
        Me.tax_Values.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tax_Values.Location = New System.Drawing.Point(456, 476)
        Me.tax_Values.Name = "tax_Values"
        Me.tax_Values.Properties.Appearance.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold)
        Me.tax_Values.Properties.Appearance.Options.UseFont = True
        Me.tax_Values.Properties.Appearance.Options.UseTextOptions = True
        Me.tax_Values.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.tax_Values.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.tax_Values.Properties.Mask.BeepOnError = True
        Me.tax_Values.Properties.Mask.EditMask = "f"
        Me.tax_Values.Size = New System.Drawing.Size(100, 24)
        Me.tax_Values.TabIndex = 1359
        '
        'inserte
        '
        Me.inserte.BackColor = System.Drawing.Color.Gold
        Me.inserte.Cursor = System.Windows.Forms.Cursors.Hand
        Me.inserte.FlatAppearance.BorderSize = 0
        Me.inserte.FlatAppearance.MouseOverBackColor = System.Drawing.Color.MediumSeaGreen
        Me.inserte.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.inserte.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.inserte.Image = CType(resources.GetObject("inserte.Image"), System.Drawing.Image)
        Me.inserte.Location = New System.Drawing.Point(24, 300)
        Me.inserte.Name = "inserte"
        Me.inserte.Size = New System.Drawing.Size(63, 25)
        Me.inserte.TabIndex = 159
        Me.inserte.UseVisualStyleBackColor = False
        '
        'Button1
        '
        Me.Button1.BackColor = System.Drawing.Color.Gold
        Me.Button1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button1.FlatAppearance.BorderSize = 0
        Me.Button1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.MediumSeaGreen
        Me.Button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button1.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button1.Image = CType(resources.GetObject("Button1.Image"), System.Drawing.Image)
        Me.Button1.Location = New System.Drawing.Point(44, 300)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(63, 25)
        Me.Button1.TabIndex = 1254
        Me.Button1.UseVisualStyleBackColor = False
        '
        'item_descound
        '
        Me.item_descound.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_descound.Location = New System.Drawing.Point(16, 303)
        Me.item_descound.Name = "item_descound"
        Me.item_descound.Size = New System.Drawing.Size(71, 25)
        Me.item_descound.TabIndex = 171
        Me.item_descound.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.item_descound.Visible = False
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.item_total)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Controls.Add(Me.item_unit)
        Me.GroupBox1.Controls.Add(Me.number_unit)
        Me.GroupBox1.Controls.Add(Me.Label47)
        Me.GroupBox1.Controls.Add(Me.item_count)
        Me.GroupBox1.Controls.Add(Me.item_price)
        Me.GroupBox1.Controls.Add(Me.Label13)
        Me.GroupBox1.Controls.Add(Me.Label14)
        Me.GroupBox1.Controls.Add(Me.item_type)
        Me.GroupBox1.Controls.Add(Me.disc_rate)
        Me.GroupBox1.Controls.Add(Me.item_total2)
        Me.GroupBox1.Controls.Add(Me.Label17)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox1.ForeColor = System.Drawing.Color.Black
        Me.GroupBox1.Location = New System.Drawing.Point(197, 102)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(459, 71)
        Me.GroupBox1.TabIndex = 1336
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "بيانات الصنف"
        '
        'item_total
        '
        Me.item_total.Location = New System.Drawing.Point(13, 36)
        Me.item_total.Name = "item_total"
        Me.item_total.Properties.Appearance.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_total.Properties.Appearance.Options.UseFont = True
        Me.item_total.Properties.Appearance.Options.UseTextOptions = True
        Me.item_total.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.item_total.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.item_total.Properties.Mask.BeepOnError = True
        Me.item_total.Properties.Mask.EditMask = "f"
        Me.item_total.Size = New System.Drawing.Size(81, 26)
        Me.item_total.TabIndex = 1413
        '
        'Label5
        '
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label5.Location = New System.Drawing.Point(29, 13)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(47, 25)
        Me.Label5.TabIndex = 1412
        Me.Label5.Text = "الاجمالي"
        '
        'item_unit
        '
        Me.item_unit.Location = New System.Drawing.Point(345, 34)
        Me.item_unit.Name = "item_unit"
        Me.item_unit.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.item_unit.Properties.Appearance.Options.UseFont = True
        Me.item_unit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.item_unit.Size = New System.Drawing.Size(96, 22)
        Me.item_unit.TabIndex = 1305
        '
        'number_unit
        '
        Me.number_unit.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.number_unit.Location = New System.Drawing.Point(341, 12)
        Me.number_unit.Name = "number_unit"
        Me.number_unit.Size = New System.Drawing.Size(26, 21)
        Me.number_unit.TabIndex = 1160
        Me.number_unit.Text = "0"
        Me.number_unit.Visible = False
        '
        'Label47
        '
        Me.Label47.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label47.Location = New System.Drawing.Point(360, 13)
        Me.Label47.Name = "Label47"
        Me.Label47.Size = New System.Drawing.Size(59, 21)
        Me.Label47.TabIndex = 1159
        Me.Label47.Text = "الوحدة"
        '
        'item_count
        '
        Me.item_count.Location = New System.Drawing.Point(261, 36)
        Me.item_count.Name = "item_count"
        Me.item_count.Properties.Appearance.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Bold)
        Me.item_count.Properties.Appearance.Options.UseFont = True
        Me.item_count.Properties.Appearance.Options.UseTextOptions = True
        Me.item_count.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.item_count.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.item_count.Properties.Mask.BeepOnError = True
        Me.item_count.Properties.Mask.EditMask = "f2"
        Me.item_count.Size = New System.Drawing.Size(81, 26)
        Me.item_count.TabIndex = 1252
        '
        'item_price
        '
        Me.item_price.Location = New System.Drawing.Point(177, 36)
        Me.item_price.Name = "item_price"
        Me.item_price.Properties.Appearance.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Bold)
        Me.item_price.Properties.Appearance.Options.UseFont = True
        Me.item_price.Properties.Appearance.Options.UseTextOptions = True
        Me.item_price.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.item_price.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.item_price.Properties.Mask.BeepOnError = True
        Me.item_price.Properties.Mask.EditMask = "f2"
        Me.item_price.Size = New System.Drawing.Size(81, 26)
        Me.item_price.TabIndex = 1253
        '
        'Label13
        '
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label13.Location = New System.Drawing.Point(276, 13)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(49, 25)
        Me.Label13.TabIndex = 161
        Me.Label13.Text = "الكمية"
        '
        'Label14
        '
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label14.Location = New System.Drawing.Point(183, 13)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(65, 25)
        Me.Label14.TabIndex = 162
        Me.Label14.Text = "سعر البيع"
        '
        'item_type
        '
        Me.item_type.BackColor = System.Drawing.Color.White
        Me.item_type.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.item_type.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_type.Location = New System.Drawing.Point(30, 39)
        Me.item_type.Name = "item_type"
        Me.item_type.Size = New System.Drawing.Size(63, 18)
        Me.item_type.TabIndex = 1234
        Me.item_type.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.item_type.Visible = False
        '
        'disc_rate
        '
        Me.disc_rate.Location = New System.Drawing.Point(99, 36)
        Me.disc_rate.Name = "disc_rate"
        Me.disc_rate.Properties.Appearance.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.disc_rate.Properties.Appearance.Options.UseFont = True
        Me.disc_rate.Properties.Appearance.Options.UseTextOptions = True
        Me.disc_rate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.disc_rate.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.disc_rate.Properties.Mask.BeepOnError = True
        Me.disc_rate.Properties.Mask.EditMask = "f2"
        Me.disc_rate.Size = New System.Drawing.Size(74, 26)
        Me.disc_rate.TabIndex = 1255
        '
        'item_total2
        '
        Me.item_total2.Location = New System.Drawing.Point(100, 36)
        Me.item_total2.Name = "item_total2"
        Me.item_total2.Properties.Appearance.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.item_total2.Properties.Appearance.Options.UseFont = True
        Me.item_total2.Properties.Appearance.Options.UseTextOptions = True
        Me.item_total2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.item_total2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.item_total2.Properties.Mask.BeepOnError = True
        Me.item_total2.Properties.Mask.EditMask = "f"
        Me.item_total2.Size = New System.Drawing.Size(74, 26)
        Me.item_total2.TabIndex = 1415
        '
        'Label17
        '
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label17.Location = New System.Drawing.Point(114, 13)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(47, 25)
        Me.Label17.TabIndex = 172
        Me.Label17.Text = "الخصم"
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.Label7.Location = New System.Drawing.Point(116, 13)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(47, 25)
        Me.Label7.TabIndex = 1414
        Me.Label7.Text = "الاجمالي"
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        '
        'Button13
        '
        Me.Button13.BackColor = System.Drawing.Color.Gainsboro
        Me.Button13.BackgroundImage = CType(resources.GetObject("Button13.BackgroundImage"), System.Drawing.Image)
        Me.Button13.ContextMenuStrip = Me.ContextMenuStrip1
        Me.Button13.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button13.FlatAppearance.BorderColor = System.Drawing.Color.Silver
        Me.Button13.FlatAppearance.MouseDownBackColor = System.Drawing.Color.BurlyWood
        Me.Button13.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button13.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.Button13.Image = CType(resources.GetObject("Button13.Image"), System.Drawing.Image)
        Me.Button13.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button13.Location = New System.Drawing.Point(518, 40)
        Me.Button13.Name = "Button13"
        Me.Button13.Size = New System.Drawing.Size(66, 27)
        Me.Button13.TabIndex = 1337
        Me.Button13.Text = "تراجع"
        Me.Button13.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button13.UseVisualStyleBackColor = False
        '
        'treasury_balace
        '
        Me.treasury_balace.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.treasury_balace.BackColor = System.Drawing.Color.Black
        Me.treasury_balace.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.treasury_balace.Cursor = System.Windows.Forms.Cursors.Default
        Me.treasury_balace.Font = New System.Drawing.Font("Arial", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.treasury_balace.ForeColor = System.Drawing.Color.White
        Me.treasury_balace.Location = New System.Drawing.Point(495, 623)
        Me.treasury_balace.Name = "treasury_balace"
        Me.treasury_balace.ReadOnly = True
        Me.treasury_balace.Size = New System.Drawing.Size(76, 15)
        Me.treasury_balace.TabIndex = 1340
        Me.treasury_balace.Text = "0"
        '
        'Label18
        '
        Me.Label18.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label18.AutoSize = True
        Me.Label18.BackColor = System.Drawing.Color.Black
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.ForeColor = System.Drawing.Color.White
        Me.Label18.Location = New System.Drawing.Point(703, 621)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(46, 16)
        Me.Label18.TabIndex = 1339
        Me.Label18.Text = "الخزينة :"
        '
        'type_pay
        '
        Me.type_pay.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.type_pay.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.type_pay.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.type_pay.FormattingEnabled = True
        Me.type_pay.Location = New System.Drawing.Point(576, 617)
        Me.type_pay.Name = "type_pay"
        Me.type_pay.Size = New System.Drawing.Size(123, 24)
        Me.type_pay.TabIndex = 1338
        '
        'CostCenterCombo
        '
        Me.CostCenterCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterCombo.Location = New System.Drawing.Point(193, 512)
        Me.CostCenterCombo.Name = "CostCenterCombo"
        Me.CostCenterCombo.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CostCenterCombo.Properties.Appearance.Options.UseFont = True
        Me.CostCenterCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CostCenterCombo.Properties.DisplayMember = "display_name"
        Me.CostCenterCombo.Properties.ValueMember = "cost_center_id"
        Me.CostCenterCombo.Size = New System.Drawing.Size(123, 22)
        Me.CostCenterCombo.TabIndex = 1340
        '
        'LabelCostCenter
        '
        Me.LabelCostCenter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelCostCenter.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCostCenter.Appearance.Options.UseFont = True
        Me.LabelCostCenter.Location = New System.Drawing.Point(342, 519)
        Me.LabelCostCenter.Name = "LabelCostCenter"
        Me.LabelCostCenter.Size = New System.Drawing.Size(63, 16)
        Me.LabelCostCenter.TabIndex = 1341
        Me.LabelCostCenter.Text = "مركز التكلفة:"
        '
        'invoice_add
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1233, 650)
        Me.Controls.Add(Me.treasury_balace)
        Me.Controls.Add(Me.Label18)
        Me.Controls.Add(Me.type_pay)
        Me.Controls.Add(Me.XtraTabControl2)
        Me.Controls.Add(Me.Button13)
        Me.Controls.Add(Me.SplitContainerControl1)
        Me.Controls.Add(Me.Date_pay)
        Me.Controls.Add(Me.Dealing)
        Me.Controls.Add(Me.delegate_name)
        Me.Controls.Add(Me.Button11)
        Me.Controls.Add(Me.Button9)
        Me.Controls.Add(Me.Button8)
        Me.Controls.Add(Me.Label26)
        Me.Controls.Add(Me.print_btn)
        Me.Controls.Add(Me.delet_btn)
        Me.Controls.Add(Me.edit_btn)
        Me.Controls.Add(Me.save_btn)
        Me.Controls.Add(Me.new_btn)
        Me.Controls.Add(Me.Label10)
        Me.Controls.Add(Me.lastpay)
        Me.Controls.Add(Me.Label33)
        Me.Controls.Add(Me.Label22)
        Me.Controls.Add(Me.Label21)
        Me.Controls.Add(Me.PictureBox3)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.Label20)
        Me.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!)
        Me.KeyPreview = True
        Me.Margin = New System.Windows.Forms.Padding(6, 9, 6, 9)
        Me.Name = "invoice_add"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.RepositoryItemCalcEdit5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCalcEdit6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl2.ResumeLayout(False)
        Me.XtraTabPage5.ResumeLayout(False)
        Me.XtraTabPage5.PerformLayout()
        CType(Me.dgv_10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_total, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage6.ResumeLayout(False)
        Me.XtraTabPage6.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.XtraTabPage7.ResumeLayout(False)
        Me.XtraTabPage7.PerformLayout()
        CType(Me.dgv_2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DataGridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage8.ResumeLayout(False)
        Me.XtraTabPage8.PerformLayout()
        CType(Me.dgv_3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ContextMenuStrip1.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.delegate_name.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Dealing.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_pay.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Date_pay.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitContainerControl1.Panel1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SplitContainerControl1.Panel1.ResumeLayout(False)
        CType(Me.SplitContainerControl1.Panel2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SplitContainerControl1.Panel2.ResumeLayout(False)
        Me.SplitContainerControl1.Panel2.PerformLayout()
        CType(Me.SplitContainerControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SplitContainerControl1.ResumeLayout(False)
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        Me.XtraTabPage1.PerformLayout()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item_name.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        CType(Me.dgv_catarogy, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_touch, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CardView2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox4.PerformLayout()
        CType(Me.serial_search.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_serial, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox3.ResumeLayout(False)
        CType(Me.dgv_expire, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox6.ResumeLayout(False)
        CType(Me.GridControl3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        CType(Me.amount_rate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.end_date.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.start_date.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cobon_code.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.invoice_date.Properties.CalendarTimeProperties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.invoice_date.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.store.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.invoice_number.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pay_money.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemComboBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCalcEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCalcEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCalcEdit3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCalcEdit4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.invoice_note.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_final_expire, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_serial_no, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.descound_Values.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.descound_Rate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.curency_item.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.dgv_4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tax_Rate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.tax_Values.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.item_total.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item_unit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item_count.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item_price.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.disc_rate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.item_total2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents XtraTabControl2 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage5 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents money_plus As System.Windows.Forms.Label
    Friend WithEvents printcode As System.Windows.Forms.TextBox
    Friend WithEvents code3 As System.Windows.Forms.TextBox
    Friend WithEvents code2 As System.Windows.Forms.TextBox
    Friend WithEvents code As System.Windows.Forms.TextBox
    Friend WithEvents XtraTabPage6 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents block_sale As System.Windows.Forms.TextBox
    Friend WithEvents itemcouta As System.Windows.Forms.TextBox
    Friend WithEvents item_tax_add As System.Windows.Forms.TextBox
    Friend WithEvents tax_add As System.Windows.Forms.TextBox
    Friend WithEvents CusCreditlimit As System.Windows.Forms.TextBox
    Friend WithEvents Cuscusgroup As System.Windows.Forms.TextBox
    Friend WithEvents cusGovernorate As System.Windows.Forms.TextBox
    Friend WithEvents cuscity As System.Windows.Forms.TextBox
    Friend WithEvents cus_price_private As System.Windows.Forms.TextBox
    Friend WithEvents item_group As System.Windows.Forms.TextBox
    Friend WithEvents item_catorgey As System.Windows.Forms.TextBox
    Friend WithEvents item_company As System.Windows.Forms.TextBox
    Friend WithEvents rebh As System.Windows.Forms.CheckBox
    Friend WithEvents XtraTabPage8 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Label40 As System.Windows.Forms.Label
    Friend WithEvents report_imp As System.Windows.Forms.CheckBox
    Friend WithEvents Label50 As System.Windows.Forms.Label
    Friend WithEvents Accounts_phone2 As System.Windows.Forms.TextBox
    Friend WithEvents day_sale As System.Windows.Forms.TextBox
    Friend WithEvents TextBox28 As System.Windows.Forms.TextBox
    Friend WithEvents Accounts_phone1 As System.Windows.Forms.TextBox
    Friend WithEvents dgv_3 As System.Windows.Forms.DataGridView
    Friend WithEvents Label43 As System.Windows.Forms.Label
    Friend WithEvents Accounts_adress As System.Windows.Forms.TextBox
    Friend WithEvents Label42 As System.Windows.Forms.Label
    Friend WithEvents Label38 As System.Windows.Forms.Label
    Friend WithEvents balace_show As System.Windows.Forms.TextBox
    Friend WithEvents Label41 As System.Windows.Forms.Label
    Friend WithEvents Label33 As System.Windows.Forms.Label
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents ContextMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents A4ToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents شكل1ToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents شكل2ToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents A5ToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents شكل1ToolStripMenuItem1 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents شكل2ToolStripMenuItem1 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents PosToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents شكل1ToolStripMenuItem2 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents شكل2ToolStripMenuItem2 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lastpay As System.Windows.Forms.TextBox
    Friend WithEvents show_itembuy As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents item_from As System.Windows.Forms.DateTimePicker
    Friend WithEvents Label44 As System.Windows.Forms.Label
    Friend WithEvents Label35 As System.Windows.Forms.Label
    Friend WithEvents item_to As System.Windows.Forms.DateTimePicker
    Friend WithEvents show_itemcus As System.Windows.Forms.CheckBox
    Friend WithEvents XtraTabPage7 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents TextBox23 As System.Windows.Forms.TextBox
    Friend WithEvents count_text As System.Windows.Forms.TextBox
    Friend WithEvents DataGridView1 As System.Windows.Forms.DataGridView
    Friend WithEvents Alternativeitem As System.Windows.Forms.CheckBox
    Friend WithEvents code_invoice As System.Windows.Forms.Label
    Friend WithEvents code_pharche As System.Windows.Forms.Label
    Friend WithEvents item_notes As System.Windows.Forms.TextBox
    Friend WithEvents TextBox9 As System.Windows.Forms.TextBox
    Friend WithEvents show_count As System.Windows.Forms.TextBox
    Friend WithEvents show_buy As System.Windows.Forms.TextBox
    Friend WithEvents show_datebuy As System.Windows.Forms.TextBox
    Friend WithEvents show_datesale As System.Windows.Forms.TextBox
    Friend WithEvents TextBox17 As System.Windows.Forms.TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents dgv_2 As System.Windows.Forms.DataGridView
    Friend WithEvents TextBox11 As System.Windows.Forms.TextBox
    Friend WithEvents TextBox12 As System.Windows.Forms.TextBox
    Friend WithEvents TextBox21 As System.Windows.Forms.TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents dgv_total As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewComboBoxColumn1 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn14 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn15 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents dgv_10 As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn16 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn17 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn18 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn19 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn20 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn21 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn22 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewButtonColumn1 As System.Windows.Forms.DataGridViewButtonColumn
    Friend WithEvents DataGridViewTextBoxColumn23 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn24 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn25 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn26 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn27 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn28 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn29 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn30 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn31 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column22 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents new_btn As System.Windows.Forms.Button
    Friend WithEvents save_btn As System.Windows.Forms.Button
    Friend WithEvents edit_btn As System.Windows.Forms.Button
    Friend WithEvents delet_btn As System.Windows.Forms.Button
    Friend WithEvents print_btn As System.Windows.Forms.Button
    Friend WithEvents Button3 As System.Windows.Forms.Button
    Friend WithEvents PictureBox3 As System.Windows.Forms.PictureBox
    Friend WithEvents Button7 As System.Windows.Forms.Button
    Friend WithEvents Button6 As System.Windows.Forms.Button
    Friend WithEvents Button5 As System.Windows.Forms.Button
    Friend WithEvents Button4 As System.Windows.Forms.Button
    Friend WithEvents Button2 As System.Windows.Forms.Button
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents Button8 As System.Windows.Forms.Button
    Friend WithEvents Button9 As System.Windows.Forms.Button
    Friend WithEvents Button11 As System.Windows.Forms.Button
    Friend WithEvents DefaultLookAndFeel1 As DevExpress.LookAndFeel.DefaultLookAndFeel
    Friend WithEvents delegate_name As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Dealing As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents DataGridViewTextBoxColumn1 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn11 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn12 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn13 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column9 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column10 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column11 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Date_pay As DevExpress.XtraEditors.DateEdit
    Friend WithEvents SplitContainerControl1 As DevExpress.XtraEditors.SplitContainerControl
    Friend WithEvents Accounts_name As System.Windows.Forms.ComboBox
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents LabelControl13 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents amount_rate As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl12 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents end_date As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents start_date As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cobon_code As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SimpleButton8 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton9 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents dgv_expire As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn32 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn34 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column1 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column2 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents invoice_date As DevExpress.XtraEditors.DateEdit
    Friend WithEvents ComboBoxEdit2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents store As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents invoice_number As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Accounts_balace As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Label19 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents TextBox8 As System.Windows.Forms.TextBox
    Friend WithEvents pay_money As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents PictureBox5 As System.Windows.Forms.PictureBox
    Friend WithEvents new_balace As System.Windows.Forms.TextBox
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents monetary As System.Windows.Forms.RadioButton
    Friend WithEvents Yup As System.Windows.Forms.RadioButton
    Friend WithEvents data_sale As System.Windows.Forms.DateTimePicker
    Friend WithEvents Label46 As System.Windows.Forms.Label
    Friend WithEvents Label45 As System.Windows.Forms.Label
    Friend WithEvents Accounts_code As System.Windows.Forms.TextBox
    Friend WithEvents invoice_tax As System.Windows.Forms.TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents invoice_descound As System.Windows.Forms.TextBox
    Friend WithEvents PictureBox4 As System.Windows.Forms.PictureBox
    Friend WithEvents check_order As DevExpress.XtraEditors.LabelControl
    Friend WithEvents check_suplier As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents serial_search As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SimpleButton6 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton7 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents dgv_serial As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn35 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn38 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column3 As System.Windows.Forms.DataGridViewButtonColumn
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn7 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn8 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemCalcEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit
    Friend WithEvents GridColumn9 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemCalcEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit
    Friend WithEvents GridColumn10 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemCalcEdit3 As DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit
    Friend WithEvents GridColumn13 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemCalcEdit4 As DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit
    Friend WithEvents GridColumn14 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn11 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn15 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn16 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn17 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn18 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn19 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn20 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemButtonEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn21 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn22 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemButtonEdit3 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn23 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemButtonEdit4 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn12 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemButtonEdit5 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents invoice_count As System.Windows.Forms.TextBox
    Friend WithEvents total_invoice As System.Windows.Forms.TextBox
    Friend WithEvents after_total As System.Windows.Forms.TextBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents item_unit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents item_type As System.Windows.Forms.TextBox
    Friend WithEvents number_unit As System.Windows.Forms.Label
    Friend WithEvents Label47 As System.Windows.Forms.Label
    Friend WithEvents inserte As System.Windows.Forms.Button
    Friend WithEvents item_count As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents item_price As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents disc_rate As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents item_descound As System.Windows.Forms.TextBox
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents item_barcode As System.Windows.Forms.TextBox
    Friend WithEvents item_name As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents invoice_note As DevExpress.XtraEditors.TextEdit
    Friend WithEvents invoice_pound As System.Windows.Forms.TextBox
    Friend WithEvents TextBox3 As System.Windows.Forms.TextBox
    Friend WithEvents earn_invoice As System.Windows.Forms.TextBox
    Friend WithEvents dgv_4 As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn2 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn3 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn4 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn5 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column20 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents dgv_5 As System.Windows.Forms.DataGridView
    Friend WithEvents dgv_final_expire As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn33 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn36 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn37 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents dgv_serial_no As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn41 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn43 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents amount_string As System.Windows.Forms.TextBox
    Friend WithEvents tax_Rate As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents tax_Values As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents descound_Values As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents descound_Rate As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents show_total As System.Windows.Forms.TextBox
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn27 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn26 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn28 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn29 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents dgv_catarogy As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn39 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents dgv_touch As DevExpress.XtraGrid.GridControl
    Private WithEvents CardView2 As DevExpress.XtraGrid.Views.Card.CardView
    Friend WithEvents GridColumn24 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents itemnamearabic As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents itemcount As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents itemcode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents curency_item As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents LabelControl16 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl19 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl26 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents DataGridViewTextBoxColumn6 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn7 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn8 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn9 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Column4 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn10 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents Timer1 As System.Windows.Forms.Timer
    Friend WithEvents RepositoryItemCalcEdit5 As DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit
    Friend WithEvents RepositoryItemCalcEdit6 As DevExpress.XtraEditors.Repository.RepositoryItemCalcEdit
    Friend WithEvents Button13 As System.Windows.Forms.Button
    Friend WithEvents RepositoryItemComboBox1 As DevExpress.XtraEditors.Repository.RepositoryItemComboBox
    Friend WithEvents GroupBox6 As System.Windows.Forms.GroupBox
    Friend WithEvents SimpleButton10 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents GridControl3 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView3 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn25 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn30 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn31 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents item_total2 As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents item_total As DevExpress.XtraEditors.CalcEdit
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents treasury_balace As System.Windows.Forms.TextBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents type_pay As System.Windows.Forms.ComboBox
    Friend WithEvents CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents LabelCostCenter As DevExpress.XtraEditors.LabelControl
End Class
