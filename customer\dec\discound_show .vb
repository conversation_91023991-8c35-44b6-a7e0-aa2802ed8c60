﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI
Public Class discound_show


    Private Sub day_option_SelectedIndexChanged(sender As Object, e As EventArgs) Handles day_option.SelectedIndexChanged
        If day_option.SelectedIndex = 0 Then
            Datefrom.Value = Now.Date
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 1 Then
            Datefrom.Value = DateAdd("d", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 2 Then
            Datefrom.Value = DateAdd("ww", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 3 Then
            Datefrom.Value = DateAdd("ww", -2, Now.Date)
            Dateto.Value = DateAdd("ww", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 4 Then
            Datefrom.Value = DateAdd("m", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 5 Then
            Datefrom.Value = DateAdd("m", -2, Now.Date)
            Dateto.Value = DateAdd("m", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 6 Then
            Datefrom.Value = DateAdd("m", -3, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 7 Then
            Datefrom.Value = DateAdd("m", -6, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 8 Then
            Datefrom.Value = DateAdd("yyyy", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 9 Then
            Datefrom.Value = DateAdd("yyyy", -2, Now.Date)
            Dateto.Value = DateAdd("yyyy", -1, Now.Date)
        End If
    End Sub
    Private Sub searsh_btn_Click(sender As Object, e As EventArgs) Handles searsh_btn.Click
        Dim adp As New SqlDataAdapter("select * from discound_cashing where descdate>='" & Format(Datefrom.Value, "yyy/MM/dd") & "'  and descdate<='" & Format(Dateto.Value, "yyy/MM/dd") & "' order by descdate", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt
    End Sub
    Private Sub discound_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        day_option.SelectedIndex = 6
        searsh_btn_Click(Nothing, Nothing)

    End Sub
  
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub

    Private Sub delete_btn_Click(sender As Object, e As EventArgs) Handles delete_btn.Click
        If XtraForm1.m25.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد سند لحذفة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If XtraMessageBox.Show("هل تريد حذف هذا السند ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
            Dim sql = "select * from discound_cashing where desccode=N'" & GridView2.GetFocusedRowCellValue("desccode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                delet_custrans()

                searsh_btn_Click(Nothing, Nothing)

                XtraMessageBox.Show("تم حذف السند بنجاح")

            End If
        End If
    End Sub
    Sub delet_custrans()
        Dim sql = "select * from customer_trans where custrans_type= 'سند خصم' and custrans_code=N'" & GridView2.GetFocusedRowCellValue("desccode") & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("فشل في جلب البيانات")
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            adp = New SqlDataAdapter("select * from customer where cusname=N'" & (GridView2.GetFocusedRowCellValue("customername")) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr5 = dt.Rows(0)
            dr5!Accounts_balace = Format(Val(dr5!Accounts_balace) + Val(GridView2.GetFocusedRowCellValue("Amount")), "0.0")
            Dim cmd5 As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
    End Sub
    
    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If XtraForm1.m24.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد سند للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As descound_customer = New descound_customer()
        f.Text = "سند خصم"
        f.MdiParent = XtraForm1
        f.Show()
        f.show_data(GridView2.GetFocusedRowCellValue("desccode"))

        Dim sql = "select * from discound_cashing where desccode=N'" & GridView2.GetFocusedRowCellValue("desccode") & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("فشل في جلب البيانات")
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            delet_custrans()
        End If
        f.refresh_cash()
    End Sub
    Private Sub تعديلToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تعديلToolStripMenuItem.Click
        edit_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub حذفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حذفToolStripMenuItem.Click
        delete_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub طباعةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles طباعةToolStripMenuItem.Click
        print_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        searsh_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجEscToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجEscToolStripMenuItem.Click
        Me.Dispose()
    End Sub

    Private Sub discound_show_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            edit_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            delete_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            print_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            searsh_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click

        Dim adp As New SqlDataAdapter("select * from desc_cus_print where desccode=N'" & GridView2.GetFocusedRowCellValue("desccode") & "'", sqlconn)
        Dim ds As New DataSet

        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "desc_cus_s4vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub
End Class