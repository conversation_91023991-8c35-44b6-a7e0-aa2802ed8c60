﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="spaxet.invoice_pos, spaxet store, Version=*******, Culture=neutral, PublicKeyToken=null" Name="invoice_pos" RollPaper="true" Margins="0, 0, 0, 0" PaperKind="Custom" PageWidth="245" PageHeight="1100" ScriptLanguage="VisualBasic" Version="17.2" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="123.0416">
      <Controls>
        <Item1 Ref="6" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="116.2499,62.12497" LocationFloat="58.12505, 0">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="8" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="243.6874,31.33333" LocationFloat="0, 62.12497" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="9" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="10" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="11" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="245,29.58336" LocationFloat="0, 93.45829" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="12" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="14" ControlType="PageHeaderBand" Name="PageHeader" HeightF="156.8119">
      <Controls>
        <Item1 Ref="15" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleCenter" SizeF="84.82989,23.41665" LocationFloat="1.759999, 7.916674" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="16" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="17" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="18" ControlType="XRLabel" Name="XrLabel19" Text="فاتورة بيع" TextAlignment="MiddleCenter" SizeF="64.16669,31.33332" LocationFloat="88.55727, 0" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="19" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="20" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="64.16669,31.24999" LocationFloat="88.55727, 31.33332" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="21" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="22" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="23" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="174.2292,22.91663" LocationFloat="0.2239532, 62.58332" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="24" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="25" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="26" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="70.32288,22.91664" LocationFloat="174.4532, 62.58332" Font="Droid Arabic Kufi, 8pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="27" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="28" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="174.2292,23.95833" LocationFloat="0.2239532, 85.49996" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="29" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="30" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="31" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="69.45821,23.95833" LocationFloat="174.4532, 85.49995" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="32" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="33" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="هاتف" TextAlignment="TopCenter" SizeF="69.45821,23.95832" LocationFloat="174.4532, 109.4583" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="34" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="35" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="174.2292,23.95831" LocationFloat="0.2239532, 109.4583" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="36" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="37" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="38" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="91.18738,23.41665" LocationFloat="152.724, 7.916677" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="39" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="40" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="41" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="243.6874,17.0573" LocationFloat="0.2239532, 139.7546" Font="Droid Arabic Kufi, 7pt" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="42" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
              <Cells>
                <Item1 Ref="43" ControlType="XRTableCell" Name="XrTableCell17" Weight="0.34842684818111214" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="44" UsePadding="false" />
                </Item1>
                <Item2 Ref="45" ControlType="XRTableCell" Name="XrTableCell33" Weight="0.30313313203298747" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="46" ControlType="XRTableCell" Name="XrTableCell34" Weight="0.28168509471873737" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="47" UsePadding="false" />
                </Item3>
                <Item4 Ref="48" ControlType="XRTableCell" Name="XrTableCell36" Weight="0.91442565892173233" Text="اسم الصنف" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100" BorderColor="White">
                  <StylePriority Ref="49" UseFont="false" />
                </Item4>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item5>
    <Item6 Ref="51" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="188.0475">
      <Controls>
        <Item1 Ref="52" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" TextAlignment="TopLeft" SizeF="240.4755,15.78847" LocationFloat="1.759999, 171.8767" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="53" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="54" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="55" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="156.3242,13.03909" LocationFloat="1.327644, 158.8376" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="56" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="58" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="156.3242,13.03906" LocationFloat="1.327644, 145.7986" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="59" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="60" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="61" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="86.25954,13.03905" LocationFloat="157.6518, 145.7986" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="63" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="84.58365,13.03909" LocationFloat="157.6518, 158.8376" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="65" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="195.6255,14.45311" LocationFloat="46.60996, 128.2786" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="66" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="68" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="195.6257,14.45314" LocationFloat="46.60996, 113.8254" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="69" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="71" ControlType="XRTable" Name="XrTable7" AnchorVertical="Bottom" TextAlignment="MiddleCenter" SizeF="135.0396,21.09375" LocationFloat="1.327644, 0" Font="Droid Arabic Kufi, 7pt" Borders="None">
          <Rows>
            <Item1 Ref="72" ControlType="XRTableRow" Name="XrTableRow9" Weight="1">
              <Cells>
                <Item1 Ref="73" ControlType="XRTableCell" Name="XrTableCell6" Weight="1" CanGrow="false" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="74" Expression="[invoice_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="75" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="76" ControlType="XRTableCell" Name="XrTableCell7" Weight="1" CanGrow="false" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="77" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="78" ControlType="XRTableRow" Name="XrTableRow13" Weight="1">
              <Cells>
                <Item1 Ref="79" ControlType="XRTableCell" Name="XrTableCell10" Weight="1" CanGrow="false" TextFormatString="{0:#,#}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="80" Expression="[invoice_pound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="81" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="82" ControlType="XRTableCell" Name="XrTableCell15" Weight="1" CanGrow="false" Text="الاحمالي" TextAlignment="MiddleRight" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="83" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="84" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="85" ControlType="XRTable" Name="XrTable6" AnchorVertical="Bottom" TextAlignment="MiddleCenter" SizeF="135.0396,10.54688" LocationFloat="1.327635, 91.79689" Font="Droid Arabic Kufi, 7pt" BackColor="White" Borders="None">
          <Rows>
            <Item1 Ref="86" ControlType="XRTableRow" Name="XrTableRow12" Weight="1">
              <Cells>
                <Item1 Ref="87" ControlType="XRTableCell" Name="XrTableCell8" Weight="1" CanGrow="false" TextFormatString="{0:#,#}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="88" Expression="[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="89" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="90" ControlType="XRTableCell" Name="XrTableCell9" Weight="1" CanGrow="false" Text="الباقي" TextAlignment="MiddleRight" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="91" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="92" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="93" ControlType="XRTable" Name="XrTable5" AnchorVertical="Bottom" TextAlignment="MiddleCenter" SizeF="135.0396,24.60938" LocationFloat="0.671339, 67.18752" Font="Droid Arabic Kufi, 7pt" BackColor="White" Borders="None">
          <Rows>
            <Item1 Ref="94" ControlType="XRTableRow" Name="XrTableRow7" Weight="1">
              <Cells>
                <Item1 Ref="95" ControlType="XRTableCell" Name="XrTableCell21" Weight="1" CanGrow="false" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="96" Expression="[money_plus]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="97" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="98" ControlType="XRTableCell" Name="XrTableCell22" Weight="1" CanGrow="false" Text="المطلوب" TextAlignment="MiddleRight" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="99" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="100" ControlType="XRTableRow" Name="XrTableRow8" Weight="1">
              <Cells>
                <Item1 Ref="101" ControlType="XRTableCell" Name="XrTableCell23" Weight="1" CanGrow="false" TextFormatString="{0:#,#}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="102" Expression="[pay]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="103" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="104" ControlType="XRTableCell" Name="XrTableCell24" Weight="1" CanGrow="false" Text="المدفوع" TextAlignment="MiddleRight" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="105" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="106" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="107" ControlType="XRTable" Name="XrTable2" AnchorVertical="Bottom" TextAlignment="MiddleCenter" SizeF="135.7109,46.09377" LocationFloat="0, 21.09375" Font="Droid Arabic Kufi, 7pt" Borders="None">
          <Rows>
            <Item1 Ref="108" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
              <Cells>
                <Item1 Ref="109" ControlType="XRTableCell" Name="XrTableCell5" Weight="1" CanGrow="false" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="110" Expression="[invoice_descound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="111" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="112" ControlType="XRTableCell" Name="XrTableCell18" Weight="1" CanGrow="false" Text="الخصم" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="113" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="114" ControlType="XRTableRow" Name="XrTableRow6" Weight="1">
              <Cells>
                <Item1 Ref="115" ControlType="XRTableCell" Name="XrTableCell19" Weight="1" CanGrow="false" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="116" Expression="[invoice_tax]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="117" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="118" ControlType="XRTableCell" Name="XrTableCell20" Weight="1" CanGrow="false" Text="الضريبة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="119" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
            <Item3 Ref="120" ControlType="XRTableRow" Name="XrTableRow10" Weight="1">
              <Cells>
                <Item1 Ref="121" ControlType="XRTableCell" Name="XrTableCell27" Weight="1" CanGrow="false" Text="XrTableCell27" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="122" Expression="[total_invoice]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="123" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="124" ControlType="XRTableCell" Name="XrTableCell28" Weight="1" CanGrow="false" Text="صافي الفاتورة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="125" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item3>
            <Item4 Ref="126" ControlType="XRTableRow" Name="XrTableRow11" Weight="1">
              <Cells>
                <Item1 Ref="127" ControlType="XRTableCell" Name="XrTableCell29" Weight="1" CanGrow="false" Text="XrTableCell29" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="128" Expression="[past_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="129" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="130" ControlType="XRTableCell" Name="XrTableCell30" Weight="1" CanGrow="false" Text="السابق" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="131" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item4>
          </Rows>
          <StylePriority Ref="132" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item6>
    <Item7 Ref="133" ControlType="PageFooterBand" Name="PageFooter" HeightF="16.07827">
      <Controls>
        <Item1 Ref="134" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleCenter" SizeF="192.6042,15.58078" LocationFloat="26.1979, 0.4974952" Font="Times New Roman, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="135" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item7>
    <Item8 Ref="136" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="137" ControlType="DetailBand" Name="Detail1" HeightF="17.0573">
          <Controls>
            <Item1 Ref="138" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="243.6874,17.0573" LocationFloat="0.6563034, 0" Font="Droid Arabic Kufi, 7pt" ForeColor="Black" BackColor="White" BorderColor="White">
              <Rows>
                <Item1 Ref="139" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
                  <Cells>
                    <Item1 Ref="140" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.34842684818111214" TextFormatString="{0:#,#}" Text="الاجمالي" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="141" Expression="[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="142" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="143" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.30313313203298747" TextFormatString="{0:#.00}" Text="السعر" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="144" Expression="[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item2>
                    <Item3 Ref="145" ControlType="XRTableCell" Name="XrTableCell3" Weight="0.28168509471873737" Text="الكمية" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="146" Expression="[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="147" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="148" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.91442565892173233" Text="اسم الصنف" TextAlignment="MiddleRight" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100" BorderColor="White">
                      <ExpressionBindings>
                        <Item1 Ref="149" Expression="[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="150" UseFont="false" UseTextAlignment="false" />
                    </Item4>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="151" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>