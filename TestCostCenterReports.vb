Imports System.Data.SqlClient
Imports DevExpress.XtraEditors

Public Class TestCostCenterReports
    Inherits XtraForm

    Private btnTestReports As SimpleButton
    Private btnCreateSampleData As SimpleButton
    Private btnOpenReports As SimpleButton
    Private memoResults As DevExpress.XtraEditors.MemoEdit

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "اختبار تقارير مراكز التكلفة"
        Me.Size = New Size(700, 500)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes

        ' زر اختبار التقارير
        btnTestReports = New SimpleButton()
        btnTestReports.Text = "اختبار قاعدة البيانات"
        btnTestReports.Location = New Point(20, 20)
        btnTestReports.Size = New Size(150, 30)
        AddHandler btnTestReports.Click, AddressOf btnTestReports_Click
        Me.Controls.Add(btnTestReports)

        ' زر إنشاء بيانات تجريبية
        btnCreateSampleData = New SimpleButton()
        btnCreateSampleData.Text = "إنشاء بيانات تجريبية"
        btnCreateSampleData.Location = New Point(190, 20)
        btnCreateSampleData.Size = New Size(150, 30)
        AddHandler btnCreateSampleData.Click, AddressOf btnCreateSampleData_Click
        Me.Controls.Add(btnCreateSampleData)

        ' زر فتح التقارير
        btnOpenReports = New SimpleButton()
        btnOpenReports.Text = "فتح التقارير المتقدمة"
        btnOpenReports.Location = New Point(360, 20)
        btnOpenReports.Size = New Size(150, 30)
        AddHandler btnOpenReports.Click, AddressOf btnOpenReports_Click
        Me.Controls.Add(btnOpenReports)

        ' منطقة النتائج
        memoResults = New DevExpress.XtraEditors.MemoEdit()
        memoResults.Location = New Point(20, 70)
        memoResults.Size = New Size(640, 400)
        memoResults.Properties.ReadOnly = True
        memoResults.Properties.ScrollBars = ScrollBars.Both
        Me.Controls.Add(memoResults)
    End Sub

    Private Sub btnTestReports_Click(sender As Object, e As EventArgs)
        Try
            memoResults.Text = "🔍 جاري اختبار قاعدة البيانات..." & vbCrLf & vbCrLf

            ' اختبار الاتصال بقاعدة البيانات
            TestDatabaseConnection()

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في اختبار قاعدة البيانات: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub TestDatabaseConnection()
        Try
            memoResults.Text += "📊 اختبار الجداول المطلوبة:" & vbCrLf

            ' اختبار جدول مراكز التكلفة
            Dim costCentersCount As Integer = GetTableCount("cost_centers")
            memoResults.Text += $"   📋 مراكز التكلفة: {costCentersCount} مركز" & vbCrLf

            ' اختبار جدول الفواتير
            Dim invoicesCount As Integer = GetTableCount("invoice_add")
            memoResults.Text += $"   🧾 فواتير المبيعات: {invoicesCount} فاتورة" & vbCrLf

            ' اختبار جدول المصروفات
            Dim expensesCount As Integer = GetTableCount("Expenses_add")
            memoResults.Text += $"   💰 المصروفات: {expensesCount} مصروف" & vbCrLf

            ' اختبار جدول الموازنة
            Dim budgetCount As Integer = GetTableCount("cost_center_budget")
            memoResults.Text += $"   📊 بنود الموازنة: {budgetCount} بند" & vbCrLf & vbCrLf

            ' اختبار الأعمدة المطلوبة
            TestRequiredColumns()

            If costCentersCount = 0 Then
                memoResults.Text += "⚠️ تحذير: لا توجد مراكز تكلفة. استخدم زر 'إنشاء بيانات تجريبية'" & vbCrLf
            Else
                memoResults.Text += "✅ قاعدة البيانات جاهزة للاستخدام!" & vbCrLf
                memoResults.Text += "📊 يمكنك الآن فتح التقارير المتقدمة" & vbCrLf
            End If

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في اختبار قاعدة البيانات: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub TestRequiredColumns()
        Try
            memoResults.Text += "🔍 اختبار الأعمدة المطلوبة:" & vbCrLf

            ' اختبار عمود cost_center_id في جدول الفواتير
            Dim invoiceColumnExists As Boolean = CheckColumnExists("invoice_add", "cost_center_id")
            memoResults.Text += $"   📋 عمود cost_center_id في الفواتير: {If(invoiceColumnExists, "✅ موجود", "❌ غير موجود")}" & vbCrLf

            ' اختبار عمود cost_center_id في جدول المصروفات
            Dim expenseColumnExists As Boolean = CheckColumnExists("Expenses_add", "cost_center_id")
            memoResults.Text += $"   💰 عمود cost_center_id في المصروفات: {If(expenseColumnExists, "✅ موجود", "❌ غير موجود")}" & vbCrLf

            If Not invoiceColumnExists OrElse Not expenseColumnExists Then
                memoResults.Text += "⚠️ ملاحظة: بعض الأعمدة المطلوبة غير موجودة. سيتم إنشاؤها تلقائياً." & vbCrLf
            End If

        Catch ex As Exception
            memoResults.Text += "⚠️ تحذير في اختبار الأعمدة: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Function GetTableCount(tableName As String) As Integer
        Try
            Dim sql As String = $"SELECT COUNT(*) FROM {tableName}"
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                Return Convert.ToInt32(cmd.ExecuteScalar())
            End Using
        Catch
            Return 0
        End Try
    End Function

    Private Function CheckColumnExists(tableName As String, columnName As String) As Boolean
        Try
            Dim sql As String = $"SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{tableName}' AND COLUMN_NAME = '{columnName}'"
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                Return Convert.ToInt32(cmd.ExecuteScalar()) > 0
            End Using
        Catch
            Return False
        End Try
    End Function

    Private Sub btnCreateSampleData_Click(sender As Object, e As EventArgs)
        Try
            memoResults.Text = "🚀 جاري إنشاء البيانات التجريبية..." & vbCrLf & vbCrLf

            ' إنشاء مراكز التكلفة التجريبية
            CreateSampleCostCenters()

            ' إنشاء بيانات الموازنة
            CreateSampleBudget()

            ' تحديث الجداول الموجودة
            UpdateExistingTables()

            memoResults.Text += vbCrLf & "✅ تم إنشاء البيانات التجريبية بنجاح!" & vbCrLf
            memoResults.Text += "📊 يمكنك الآن اختبار التقارير المتقدمة" & vbCrLf
            memoResults.Text += "🎯 البيانات المنشأة:" & vbCrLf
            memoResults.Text += "   • 5 مراكز تكلفة" & vbCrLf
            memoResults.Text += "   • موازنات متنوعة لكل مركز" & vbCrLf
            memoResults.Text += "   • أعمدة مراكز التكلفة في الجداول الموجودة" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في إنشاء البيانات التجريبية: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub CreateSampleCostCenters()
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            Dim createTableSql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_centers (
                        cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_code NVARCHAR(20) NOT NULL,
                        cost_center_name NVARCHAR(100) NOT NULL,
                        description NVARCHAR(255),
                        is_active BIT DEFAULT 1,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                END"

            Using cmd As New SqlCommand(createTableSql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using

            ' إدراج مراكز تكلفة تجريبية
            Dim insertSql As String = "
                IF NOT EXISTS (SELECT * FROM cost_centers WHERE cost_center_code = 'CC001')
                BEGIN
                    INSERT INTO cost_centers (cost_center_code, cost_center_name, description) VALUES
                    ('CC001', 'فرع الرئيسي', 'المكتب الرئيسي للشركة'),
                    ('CC002', 'فرع الشمال', 'فرع المنطقة الشمالية'),
                    ('CC003', 'فرع الجنوب', 'فرع المنطقة الجنوبية'),
                    ('CC004', 'قسم التسويق', 'قسم التسويق والمبيعات'),
                    ('CC005', 'قسم الإنتاج', 'قسم الإنتاج والتصنيع');
                END"

            Using cmd As New SqlCommand(insertSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم إنشاء مراكز التكلفة التجريبية" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في إنشاء مراكز التكلفة: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub CreateSampleBudget()
        Try
            ' إنشاء جدول الموازنة
            Dim createBudgetSql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_center_budget' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_center_budget (
                        budget_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_id INT NOT NULL,
                        budget_year INT NOT NULL,
                        budget_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                END"

            Using cmd As New SqlCommand(createBudgetSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            ' إدراج بيانات الموازنة
            Dim insertBudgetSql As String = "
                DELETE FROM cost_center_budget WHERE budget_year = YEAR(GETDATE());
                
                INSERT INTO cost_center_budget (cost_center_id, budget_year, budget_amount)
                SELECT cost_center_id, YEAR(GETDATE()), 
                    CASE cost_center_id 
                        WHEN 1 THEN 100000
                        WHEN 2 THEN 75000
                        WHEN 3 THEN 80000
                        WHEN 4 THEN 50000
                        WHEN 5 THEN 120000
                        ELSE 60000
                    END
                FROM cost_centers WHERE is_active = 1;"

            Using cmd As New SqlCommand(insertBudgetSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم إنشاء بيانات الموازنة التجريبية" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في إنشاء الموازنة: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub UpdateExistingTables()
        Try
            ' إضافة عمود cost_center_id للفواتير إذا لم يكن موجوداً
            Dim updateInvoicesSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE invoice_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(updateInvoicesSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            ' إضافة عمود cost_center_id للمصروفات إذا لم يكن موجوداً
            Dim updateExpensesSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = 'Expenses_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE Expenses_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(updateExpensesSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم تحديث الجداول الموجودة" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "⚠️ تحذير في تحديث الجداول: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub btnOpenReports_Click(sender As Object, e As EventArgs)
        Try
            main_page.OpenAdvancedCostCenterReports()
            memoResults.Text += "✅ تم فتح التقارير المتقدمة!" & vbCrLf
        Catch ex As Exception
            memoResults.Text += "❌ خطأ في فتح التقارير: " & ex.Message & vbCrLf
        End Try
    End Sub
End Class
