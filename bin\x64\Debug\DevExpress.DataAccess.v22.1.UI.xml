<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <assembly>
    <name>DevExpress.DataAccess.v22.1.UI</name>
  </assembly>
  <members>
    <member name="N:DevExpress.DataAccess.UI.Design">
      <summary>
        <para>Contains classes that define the UI settings of applications providing Data Access functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Design.DataSourceOptionsContainer">
      <summary>
        <para>Provides settings that define the data source options of an end-user application.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Design.DataSourceOptionsContainer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Design.DataSourceOptionsContainer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Design.DataSourceOptionsContainer.ObjectDataSourceLoadingBehavior">
      <summary>
        <para>Specifies whether to trust the object data sources available in the end-user application.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.DocumentLoadingBehavior"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Design.DataSourceOptionsContainer.Reset">
      <summary>
        <para>Restore the default data source settings.</para>
      </summary>
    </member>
    <member name="N:DevExpress.DataAccess.UI.EntityFramework">
      <summary>
        <para>This namespace is no longer used.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext">
      <summary>
        <para>Provides settings required to edit filter settings of an Entity Framework data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext.ConnectionStorageService">
      <summary>
        <para>Specifies an object that provides a service functionality to store the data connection settings.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext.ConnectionStringsProvider">
      <summary>
        <para>Specifies a storage of data connection settings.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext.Options">
      <summary>
        <para>Specifies the options to customize the Entity Framework Data Source Wizard.</para>
      </summary>
      <value>One or more <see cref="T:DevExpress.DataAccess.Wizard.EFWizardOptions"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext.ParameterService">
      <summary>
        <para>Provides access to the service for managing report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.EntityFramework.EditFiltersContext.SolutionTypesProvider">
      <summary>
        <para>Specifies the object that provides access to the known types in the current solution.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</value>
    </member>
    <member name="N:DevExpress.DataAccess.UI.Excel">
      <summary>
        <para>Provides an application programming interface (API) to access the graphical user interface (GUI) related to configuring an Excel data source connection.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Excel.EditDataSourceContext">
      <summary>
        <para>Provides settings required to edit an Excel data source connection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.EditDataSourceContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Excel.EditDataSourceContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Excel.EditDataSourceContext.DataDirectoryPatchingService">
      <summary>
        <para>Patches a path to an Excel file.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Data.Utils.IDataDirectoryPatchingService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Excel.EditDataSourceContext.ExcelSchemaProvider">
      <summary>
        <para>Specifies a provider of an Excel data source schema.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper">
      <summary>
        <para>Provides functionality to configure the connection to an <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> in code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.Excel.ExcelDataSource)">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.DataAccess.UI.Excel.EditDataSourceContext)">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Excel.EditDataSourceContext"/> object.</param>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Excel.IExcelSchemaProvider)">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface, specifying the wizard settings.</param>
      <param name="excelSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</param>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Excel.IExcelSchemaProvider)">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="excelSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</param>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditDataSource``1(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.DataAccess.UI.Excel.EditDataSourceContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Excel Data Source Editor with the specified settings.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Excel.EditDataSourceContext"/> object.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the wizard was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditSchema(DevExpress.DataAccess.Excel.ExcelDataSource)">
      <summary />
      <param name="dataSource"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditSchema(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.DataAccess.UI.Excel.EditSchemaContext)">
      <summary />
      <param name="dataSource"></param>
      <param name="context"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.EditSchema``1(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.DataAccess.UI.Excel.EditSchemaContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary />
      <param name="dataSource"></param>
      <param name="context"></param>
      <param name="customizeWizard"></param>
      <typeparam name="TModel"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.UpdateSchema(DevExpress.DataAccess.Excel.ExcelDataSource)">
      <summary>
        <para>Updates the data source schema available on the client in accordance with the current data source structure.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <returns>true, if the data source schema has been rebuilt; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.UpdateSchema(DevExpress.DataAccess.Excel.ExcelDataSource,DevExpress.DataAccess.UI.Excel.UpdateSchemaContext)">
      <summary>
        <para>Updates the data source schema available on the client in accordance with the current data source structure.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Excel.UpdateSchemaContext"/> object.</param>
      <returns>true, if the data source schema has been rebuilt; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.ExcelDataSourceUIHelper.UpdateSchema(DevExpress.DataAccess.Excel.ExcelDataSource,System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.DataAccess.Excel.IExcelSchemaProvider)">
      <summary>
        <para>Updates the data source schema available on the client in accordance with the current data source structure.</para>
      </summary>
      <param name="dataSource">An <see cref="T:DevExpress.DataAccess.Excel.ExcelDataSource"/> object.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="schemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</param>
      <returns>true, if the data source schema has been rebuilt; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Excel.UpdateSchemaContext">
      <summary>
        <para>Provides settings required to update an Excel data source schema.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Excel.UpdateSchemaContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Excel.UpdateSchemaContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Excel.UpdateSchemaContext.DataDirectoryPatchingService">
      <summary>
        <para>Patches a path to an Excel file.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Data.Utils.IDataDirectoryPatchingService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Excel.UpdateSchemaContext.ExcelSchemaProvider">
      <summary>
        <para>Specifies a provider of an Excel data source schema.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</value>
    </member>
    <member name="N:DevExpress.DataAccess.UI.MongoDB">
      <summary>
        <para>Contains classes that allow you to invoke and customize the Connection Editor and the Manage Queries dialog for the MongoDB Data Source.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.MongoDB.IMongoDBEditorsCustomizationService">
      <summary>
        <para>If implemented, allows you to display a custom Connection Editor or Manage Queries dialog.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.IMongoDBEditorsCustomizationService.CustomizeEditor(DevExpress.DataAccess.UI.MongoDB.MongoDBEditorId,DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.DataAccess.Wizard.Model.MongoDBDataSourceModel})">
      <summary>
        <para>Replaces the standard Connection Editor or Manage Queries dialog with a custom one.</para>
      </summary>
      <param name="editor">An editor to be customized.</param>
      <param name="tool">An object that contains customization settings.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext">
      <summary>
        <para>Contains settings used to display the Manage Queries dialog for the MongoDB Data Source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext.ParameterService">
      <summary>
        <para>Stores a service that manages external parameters of MongoDB queries in the Manage Queries dialog.</para>
      </summary>
      <value>An object that manages external parameters of MongoDB queries.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext.PropertyGridServices">
      <summary>
        <para>Stores a service that displays the Property Grid in the Manage Queries dialog.</para>
      </summary>
      <value>A service that displays the Property Grid in the Manage Queries dialog.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext.RepositoryItemsProvider">
      <summary>
        <para>Stores an object that contains repository items. These items allow you to edit parameters of MongoDB queries in the Manage Queries dialog.</para>
      </summary>
      <value>An object that contains repository items.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper">
      <summary>
        <para>Contains methods that allow you to invoke the Connection Editor and the Manage Queries dialog for the MongoDB Data Source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.MongoDB.MongoDBDataSourceBase)">
      <summary>
        <para>Invokes the Connection Editor for the MongoDB Data Source.</para>
      </summary>
      <param name="dataSource">A MongoDB data source.</param>
      <returns>true if a user clicked the Finish button to close the editor; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.MongoDB.MongoDBDataSourceBase,DevExpress.DataAccess.UI.MongoDB.ConfigureMongoDBConnectionContext)">
      <summary>
        <para>Invokes the Connection Editor with the specified settings for the MongoDB Data Source.</para>
      </summary>
      <param name="dataSource">A MongoDB data source.</param>
      <param name="context">An object that contains settings for the editor.</param>
      <returns>true if a user clicked the Finish button to close the editor; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper.ConfigureConnection``1(DevExpress.DataAccess.MongoDB.MongoDBDataSourceBase,DevExpress.DataAccess.UI.MongoDB.ConfigureMongoDBConnectionContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Connection Editor with the specified settings and customization parameters for the MongoDB Data Source.</para>
      </summary>
      <param name="dataSource">A MongoDB data source.</param>
      <param name="context">An object that contains settings for the editor.</param>
      <param name="customizeWizard">A method that customizes the editor.</param>
      <typeparam name="TModel">A class that implements the IMongoDBDataSourceModel interface.</typeparam>
      <returns>true if a user clicked the Finish button to close the editor; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.MongoDB.MongoDBDataSourceBase)">
      <summary>
        <para>Invokes the Manage Queries dialog for the MongoDB Data Source.</para>
      </summary>
      <param name="dataSource">A MongoDB data source.</param>
      <returns>true if a user clicked the OK button to close the dialog; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.MongoDB.MongoDBDataSourceBase,DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext)">
      <summary>
        <para>Invokes the Manage Queries dialog with the specified settings for the MongoDB Data Source.</para>
      </summary>
      <param name="dataSource">A MongoDB data source.</param>
      <param name="context">An object that contains settings for the dialog.</param>
      <returns>true if a user clicked the OK button to close the dialog; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.MongoDB.MongoDBDataSourceUIHelper.ManageQueries``1(DevExpress.DataAccess.MongoDB.MongoDBDataSourceBase,DevExpress.DataAccess.UI.MongoDB.ManageQueriesContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Manage Queries dialog with the specified settings and customization parameters for the MongoDB Data Source.</para>
      </summary>
      <param name="dataSource">A MongoDB data source.</param>
      <param name="context">An object that contains settings for the dialog.</param>
      <param name="customizeWizard">A method that customizes the dialog.</param>
      <typeparam name="TModel">A class that implements the IMongoDBDataSourceModel interface.</typeparam>
      <returns>true if a user clicked the OK button to close the dialog; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.DataAccess.UI.MongoDB.MongoDBEditorId">
      <summary>
        <para>Lists MongoDB editors.</para>
      </summary>
    </member>
    <member name="F:DevExpress.DataAccess.UI.MongoDB.MongoDBEditorId.Connection">
      <summary>
        <para>The Connection Editor.</para>
      </summary>
    </member>
    <member name="F:DevExpress.DataAccess.UI.MongoDB.MongoDBEditorId.Query">
      <summary>
        <para>The Manage Queries dialog.</para>
      </summary>
    </member>
    <member name="N:DevExpress.DataAccess.UI.ObjectBinding">
      <summary>
        <para>Provides an application programming interface (API) to access the graphical user interface (GUI) related to configuring an object data source connection.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext">
      <summary>
        <para>Provides settings required to edit the constructors of an object data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext.OperationMode">
      <summary>
        <para>Specifies the availability of an object data source’s data, schema or both on the client.</para>
      </summary>
      <value>An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext.ParameterService">
      <summary>
        <para>Specifies an object that provides a service functionality to manage report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext.PropertyGridServices">
      <summary>
        <para>Specifies services that provide functionality to custom parameter editors displayed in the Property Grid that is invoked from the Data Source wizard.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.IServiceProvider"/> interface that is used to obtain the service object of a specific type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext.RepositoryItemsProvider">
      <summary>
        <para>Specifies an object that provides repository items for editing query parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext.SolutionTypesProvider">
      <summary>
        <para>Specifies an object that provides access to the known types in the current solution.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext">
      <summary>
        <para>Provides settings required to edit the data members of an object data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext.ParameterService">
      <summary>
        <para>Specifies an object that provides a service functionality to manage report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext.PropertyGridServices">
      <summary>
        <para>Specifies services that provide functionality to custom parameter editors displayed in the Property Grid that is invoked from the Data Source wizard.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.IServiceProvider"/> interface that is used to obtain the service object of a specific type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext.RepositoryItemsProvider">
      <summary>
        <para>Specifies an object that provides repository items for editing query parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext.SolutionTypesProvider">
      <summary>
        <para>Specifies an object that provides access to the known types in the current solution.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext">
      <summary>
        <para>Provides settings required to edit an object data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext.OperationMode">
      <summary>
        <para>Specifies the modes of operation available for an object data source.</para>
      </summary>
      <value>An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext.ParameterService">
      <summary>
        <para>Specifies an object that provides a service functionality to manage report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext.PropertyGridServices">
      <summary>
        <para>Specifies services that provide functionality to custom parameter editors displayed in the Property Grid that is invoked from the Data Source wizard.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.IServiceProvider"/> interface that is used to obtain the service object of a specific type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext.RepositoryItemsProvider">
      <summary>
        <para>Specifies an object that provides repository items for editing query parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext.SolutionTypesProvider">
      <summary>
        <para>Specifies an object that provides access to the known types in the current solution.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext">
      <summary>
        <para>Provides settings required to edit the parameters of an object data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext.ParameterService">
      <summary>
        <para>Specifies an object that provides a service functionality to manage report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext.PropertyGridServices">
      <summary>
        <para>Specifies services that provide functionality to custom parameter editors displayed in the Property Grid that is invoked from the Data Source wizard.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.IServiceProvider"/> interface that is used to obtain the service object of a specific type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext.RepositoryItemsProvider">
      <summary>
        <para>Specifies an object that provides repository items for editing query parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext.SolutionTypesProvider">
      <summary>
        <para>Specifies an object that provides access to the known types in the current solution.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper">
      <summary>
        <para>Provides functionality to configure the connection to an <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/> in code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor(DevExpress.DataAccess.ObjectBinding.ObjectDataSource)">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext)">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext"/> object.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditConstructor``1(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Configure Constructor Settings dialog with the specified settings.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditConstructorContext"/> object.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataMember(DevExpress.DataAccess.ObjectBinding.ObjectDataSource)">
      <summary>
        <para>Invokes a dialog that enables you to edit the data members of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataMember(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext)">
      <summary>
        <para>Invokes a dialog that enables you to edit the data members of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext"/> object.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataMember(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Invokes a dialog that enables you to edit the data members of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataMember(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes a dialog that enables you to edit the data members of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataMember``1(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes a dialog that enables you to edit the data members of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditDataMemberContext"/> object.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.ObjectBinding.ObjectDataSource)">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext)">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Excel.EditDataSourceContext"/> object.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource``1(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext,DevExpress.DataAccess.Wizard.Native.IObjectTypeInfoProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary />
      <param name="objectDataSource"></param>
      <param name="context"></param>
      <param name="typeInfoProvider"></param>
      <param name="customizeWizard"></param>
      <typeparam name="TModel"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditDataSource``1(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditDataSourceContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes a dialog that enables you to edit the settings of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Excel.EditDataSourceContext"/> object.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditParameters(DevExpress.DataAccess.ObjectBinding.ObjectDataSource)">
      <summary>
        <para>Invokes a dialog that enables you to edit the parameters of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditParameters(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext)">
      <summary>
        <para>Invokes a dialog that enables you to edit the parameters of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext"/> object.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditParameters(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Invokes a dialog that enables you to edit the parameters of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditParameters(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes a dialog that enables you to edit the parameters of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditParameters``1(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext,DevExpress.DataAccess.Wizard.Native.IObjectTypeInfoProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary />
      <param name="objectDataSource"></param>
      <param name="context"></param>
      <param name="typeInfoProvider"></param>
      <param name="customizeWizard"></param>
      <typeparam name="TModel"></typeparam>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.ObjectBinding.ObjectDataSourceUIHelper.EditParameters``1(DevExpress.DataAccess.ObjectBinding.ObjectDataSource,DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes a dialog that enables you to edit the parameters of an object data source.</para>
      </summary>
      <param name="objectDataSource">An <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectDataSource"/>.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.ObjectBinding.EditParametersContext"/> object.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was closed by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="N:DevExpress.DataAccess.UI.Sql">
      <summary>
        <para>Provides an application programming interface (API) to access the graphical user interface (GUI) related to configuring a SQL data source connection.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext">
      <summary>
        <para>Contains settings used to display the Connection Editor dialog when the SqlDataSourceUIHelper.ConfigureConnection method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext.ConnectionProviderService">
      <summary>
        <para>Specifies an object that enables the data connection’s serialization.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionProviderService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext.ConnectionStorageService">
      <summary>
        <para>Specifies an object that provides a service functionality to store the data connection settings.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext.DatabaseCredentialsSavingBehavior">
      <summary>
        <para>Specifies whether or not to serialize database credentials along with document layouts (e.g., report or dashboards). Optionally, you can enable your end-users to select the required behavior.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Wizard.SensitiveInfoSavingBehavior"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext.DataProviders">
      <summary>
        <para>Specifies the list of data providers available for a data connection.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.DataAccess.UI.Wizard.ProviderLookupItem"/> objects.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext.Options">
      <summary>
        <para>Specifies the settings of the Connection Editor wizard options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.EditQueryContext">
      <summary>
        <para>Contains settings used to display the Query Builder dialog when the SqlDataSourceUIHelper.EditQuery or SqlDataSourceUIHelper.ManageQueries method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.EditQueryContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.ConnectionProviderService">
      <summary>
        <para>Specifies an object that enables the data connection’s serialization.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionProviderService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.DBSchemaProviderEx">
      <summary>
        <para>Specifies a database schema provider for the Query Builder.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.DisplayNameProvider">
      <summary>
        <para>Specifies a provider of display names to data fields in the Query Builder dialog.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.Options">
      <summary>
        <para>Specifies the Edit Query dialog options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.ParameterService">
      <summary>
        <para>Specifies an object that provides a service functionality to manage report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.PropertyGridServices">
      <summary>
        <para>Specifies an object that provides a service functionality to the Property Grid.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.IServiceProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.QueryValidator">
      <summary>
        <para>Specifies an object that is used to validate SQL strings.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.RepositoryItemsProvider">
      <summary>
        <para>Specifies an object that provides repository items for editing query parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.EditQueryContext.ResultSchemaProvider">
      <summary>
        <para>Specifies the provider of a data source result schema.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IResultSchemaProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.ISqlEditorsCustomizationService">
      <summary>
        <para>If implemented, enables you to display a custom Query Editor and Data Connections Editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.ISqlEditorsCustomizationService.CustomizeEditor(DevExpress.DataAccess.UI.Sql.SqlEditorId,DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.DataAccess.Wizard.Model.SqlDataSourceModel})">
      <summary>
        <para>Replaces the standard Query Editor or Data Connections Editor with a custom one.</para>
      </summary>
      <param name="editor">A <see cref="T:DevExpress.DataAccess.UI.Sql.SqlEditorId"/> enumeration value, specifying the editor to be customized.</param>
      <param name="tool">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface with the specified <see cref="T:DevExpress.DataAccess.Wizard.Model.SqlDataSourceModel"/> type parameter.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.ManageRelationsContext">
      <summary>
        <para>Contains settings used to display the Master-Detail Relations Editor dialog when the SqlDataSourceUIHelper.ManageRelations method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.ManageRelationsContext.#ctor">
      <summary>
        <para>Initialize a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.ManageRelationsContext"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ManageRelationsContext.ConnectionProviderService">
      <summary>
        <para>Specifies an object that enables the data connection’s serialization.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionProviderService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ManageRelationsContext.DBSchemaProviderEx">
      <summary>
        <para>Specifies a database schema provider for the Master-Detail Relations Editor.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ManageRelationsContext.LegacyEditor">
      <summary>
        <para>Specifies the kind of Master-Detail Relation Editor to use in the application.</para>
      </summary>
      <value>true, to use an older editor version; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ManageRelationsContext.LookAndFeel">
      <summary>
        <para>Specifies the look and feel settings for the Master-Detail Relations Editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look and feel settings for editors.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.ManageRelationsContext.Owner">
      <summary>
        <para>Specifying the owner window of the Master-Detail Relations Editor.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface, specifying the owner window.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext">
      <summary>
        <para>Specifies the Query Builder options when using it in a standalone mode.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.ConnectionProviderService">
      <summary>
        <para>Specifies an object that enables the data connection’s serialization.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionProviderService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.CustomQueryValidator">
      <summary>
        <para>Provides access to a custom SQL query validator.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.DBSchemaProvider">
      <summary>
        <para>Specifies a database schema provider for the Query Builder.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.DisplayNameProvider">
      <summary>
        <para>Specifies a provider of display names to data fields in the Query Builder dialog.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.EnableCustomSql">
      <summary>
        <para>Specifies whether or not the Query Builder allows end-users to create and execute custom SQL queries.</para>
      </summary>
      <value>true, to allow end-users to use custom SQL queries; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.ExpressionEditorContext">
      <summary>
        <para>Specifies the object enabling customization of the Expression Editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Controls.ExpressionEditor.ExpressionEditorContext"/> object.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.LegacyExpressionEditor">
      <summary>
        <para>Enables a user to switch to an older version of the Expression Editor that does not support intelligent code completion.</para>
      </summary>
      <value>true, to use the older Expression Editor version; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.ParameterService">
      <summary>
        <para>Provides access to the service for managing report parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.PropertyGridServices">
      <summary>
        <para>Specifies an object that provides a service functionality to the Property Grid.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.IServiceProvider"/> interface that is used to obtain the service object of a specific type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.QueryBuilderLight">
      <summary>
        <para>Specifies whether or not the Query Builder enables end-users to specify custom SQL queries, column aliases and expressions.</para>
      </summary>
      <value>true, to disallow end-users from specifying custom SQL queries, column aliases and expressions; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.QueryBuilderTreeListView">
      <summary>
        <para>Enables a table-like interface for selecting fields in the Query Builder (instead of a diagram-based control).</para>
      </summary>
      <value>true, to enable a table-like interface in the Query Builder; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.RepositoryItemsProvider">
      <summary>
        <para>Specifies an object that provides repository items for editing query parameters.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext.ResultSchemaProvider">
      <summary>
        <para>Specifies the provider of a data source result schema.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IResultSchemaProvider"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner">
      <summary>
        <para>Enables you to integrate the Query Builder into your application.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner.#ctor(DevExpress.DataAccess.Sql.DBSchema,DevExpress.DataAccess.Sql.SqlDataConnection,DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner"/> class with the specified settings.</para>
      </summary>
      <param name="dbSchema">A <see cref="T:DevExpress.DataAccess.Sql.DBSchema"/> object that is the data source schema.</param>
      <param name="sqlDataConnection">An <see cref="T:DevExpress.DataAccess.Sql.SqlDataConnection"/> object that specifies a connection to a data provider.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext"/> object.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner.#ctor(DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.DBSchema,DevExpress.DataAccess.Sql.SqlDataConnection,System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel,System.Boolean,System.Boolean,System.Boolean,System.Boolean,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>This constructor has become obsolete. Use another constructor instead.</para>
      </summary>
      <param name="schemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema.</param>
      <param name="dbSchema">A <see cref="T:DevExpress.DataAccess.Sql.DBSchema"/> object that is the data source schema.</param>
      <param name="connection">An <see cref="T:DevExpress.DataAccess.Sql.SqlDataConnection"/> object that specifies a connection to a data provider.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.</param>
      <param name="noCustomSql">true, to disable custom SQL editing; otherwise, false.</param>
      <param name="light">true, to disallow end-users from specifying custom SQL queries, column aliases and expressions; otherwise, false.</param>
      <param name="noDiagramControl">true, to use a table-based user interface; false, to use a diagram-based user interface.</param>
      <param name="legacyExpressionEditor">true, to use the older Expression Editor version; otherwise, false.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface that provides custom validation logic for SQL queries.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner.#ctor(DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.DBSchema,DevExpress.DataAccess.Sql.SqlDataConnection,System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel,System.Boolean,System.Boolean,System.Boolean,System.Boolean,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.Services.IExceptionHandler)">
      <summary>
        <para>This constructor has become obsolete. Use another constructor instead.</para>
      </summary>
      <param name="schemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema.</param>
      <param name="dbSchema">A <see cref="T:DevExpress.DataAccess.Sql.DBSchema"/> object that is the data source schema.</param>
      <param name="connection">An <see cref="T:DevExpress.DataAccess.Sql.SqlDataConnection"/> object that specifies a connection to a data provider.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.</param>
      <param name="noCustomSql">true, to disable custom SQL editing; otherwise, false.</param>
      <param name="light">true, to disallow end-users from specifying custom SQL queries, column aliases and expressions; otherwise, false.</param>
      <param name="noDiagramControl">true, to use a table-based user interface; false, to use a diagram-based user interface.</param>
      <param name="legacyExpressionEditor">true, to use the older Expression Editor version; otherwise, false.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface that provides custom validation logic for SQL queries.</param>
      <param name="loaderExceptionHandler">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IExceptionHandler"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner.#ctor(DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.DBSchema,DevExpress.DataAccess.Sql.SqlDataConnection,System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel,System.Boolean,System.Boolean,System.Boolean,System.Boolean,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.Services.IExceptionHandler,DevExpress.Data.Controls.ExpressionEditor.ExpressionEditorContext,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunner"/> class with the specified settings.</para>
      </summary>
      <param name="schemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema.</param>
      <param name="dbSchema">A <see cref="T:DevExpress.DataAccess.Sql.DBSchema"/> object that is the data source schema.</param>
      <param name="connection">An <see cref="T:DevExpress.DataAccess.Sql.SqlDataConnection"/> object that specifies a connection to a data provider.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.</param>
      <param name="noCustomSql">true, to disable custom SQL editing; otherwise, false.</param>
      <param name="light">true, to disallow end-users from specifying custom SQL queries, column aliases and expressions; otherwise, false.</param>
      <param name="noDiagramControl">true, to use a table-based user interface; false, to use a diagram-based user interface.</param>
      <param name="legacyExpressionEditor">true, to use the older Expression Editor version; otherwise, false.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface that provides custom validation logic for SQL queries.</param>
      <param name="loaderExceptionHandler">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IExceptionHandler"/> interface.</param>
      <param name="context">An <see cref="T:DevExpress.Data.Controls.ExpressionEditor.ExpressionEditorContext"/> object.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunnerDelegate">
      <summary />
      <param name="dbSchema"></param>
      <param name="connection"></param>
      <param name="context"></param>
      <value></value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext">
      <summary>
        <para>Contains settings used to update a data source schema when calling the SqlDataSourceUIHelper.RebuildResultSchema method.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.#ctor">
      <summary>
        <para>Initialize a new instance of the <see cref="T:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.ConnectionProviderService">
      <summary>
        <para>Specifies an object that enables the data connection’s serialization.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionProviderService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.LookAndFeel">
      <summary>
        <para>Specifies the look and feel settings for the Rebuild Schema dialog.</para>
      </summary>
      <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look and feel settings for editors.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.Owner">
      <summary>
        <para>Specifying the owner window of the Rebuild Schema dialog.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface, specifying the owner window.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.ParameterService">
      <summary>
        <para>Specifies an object that provides a service functionality to manage report parameters created in the Report Wizard.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.ResultSchemaProvider">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext.ShowSuccessMessage">
      <summary>
        <para>Specifies whether or not to show a message notifying that the resulting schema has been rebuilt successfully.</para>
      </summary>
      <value>true to show the message; otherwise false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper">
      <summary>
        <para>Provides functionality to configure the connection to a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> in code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Invokes the Query Editor dialog.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <returns>true, if the wizard page was switched by clicking the Next or Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.EditQueryContext)">
      <summary>
        <para>Invokes the Query Editor dialog.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> object, specifying the Query Designer settings.</param>
      <returns>true, if the wizard page was switched by clicking the Next or Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An IDBSchemaProvider, providing access to the schema of the SQL data base.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An IDBSchemaProvider, providing access to the schema of the SQL data base.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.EditQueryContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Query Editor dialog.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> object, specifying the Query Designer settings.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the wizard page was switched by clicking the Next or Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery&lt;TModel&gt; overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}},System.IServiceProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery&lt;TModel&gt; overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="propertyGridServices">An object implementing the System.IServiceProvider interface that provides access to services managing property grid functionality.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}},System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery&lt;TModel&gt; overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="propertyGridServices">An object implementing the System.IServiceProvider interface that provides access to services managing property grid functionality.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface, which provides custom query validation logic.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery&lt;TModel&gt; overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQuery``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}},System.IServiceProvider)">
      <summary>
        <para>Use another SqlDataSourceUIHelper.AddQuery&lt;TModel&gt; overload that accepts a <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> and <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> as parameters.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface that provides access to services managing property grid functionality.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQueryWithQueryBuilder(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Invokes the Query Builder.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <returns>true, if the Query Builder was closed by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQueryWithQueryBuilder(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext)">
      <summary>
        <para>Invokes the Query Builder.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunnerDelegate"/> object.</param>
      <returns>true, if the Query Builder was closed by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.AddQueryWithQueryBuilder(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext,DevExpress.DataAccess.UI.Sql.QueryBuilderRunnerDelegate)">
      <summary>
        <para>Invokes the Query Builder.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> to which the resulting query will be added.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext"/> object.</param>
      <param name="createQueryBuilderRunner">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunnerDelegate"/> object.</param>
      <returns>true, if the Query Builder was closed by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Invokes the Connection Editor with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext)">
      <summary>
        <para>Invokes the Connection Editor with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext"/> object, specifying the editor settings.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Use an appropriate overload of the SqlDataSourceUIHelper.ConfigureConnection method instead.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="wizardRunnerContext">An object implementing the IWizardRunnerContext interface.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the Connection Editor with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Connection Editor with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Invokes the Connection Editor with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Connection Editor with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext"/> object, specifying the editor settings.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnection``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Use an appropriate overload of the SqlDataSourceUIHelper.ConfigureConnection method instead.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="wizardRunnerContext">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Invokes the Connection Editor to edit the parameters used to establish a connection to a database.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext)">
      <summary>
        <para>Invokes the Connection Editor to edit the parameters used to establish a connection to a data base.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext"/> object, specifying the editor settings.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Use an appropriate overload of the SqlDataSourceUIHelper.ConfigureConnection method instead.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">An object implementing the IWizardRunnerContext interface.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the Connection Editor to edit the parameters used to establish a connection to a data base.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Connection Editor to edit the parameters used to establish a connection to a data base.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Invokes the Connection Editor to edit the parameters used to establish a connection to a database.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Connection Editor to edit the parameters used to establish a connection to a data base.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.ConfigureConnectionContext"/> object, specifying the editor settings.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ConfigureConnectionParameters``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Use an appropriate overload of the SqlDataSourceUIHelper.ConfigureConnection method instead.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.DataAccess.UI.Sql.EditQueryContext)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> object.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> object, specifying the Query Builder settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An IDBSchemaProvider, providing access to the schema of the SQL data base</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> object.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> object.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> object.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery``1(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.DataAccess.UI.Sql.EditQueryContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> object.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> object, specifying the Query Builder settings.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery``1(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery``1(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}},System.IServiceProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="propertyGridServices">An object implementing the System.IServiceProvider interface that provides access to services managing property grid functionality.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery``1(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}},System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items for editing query parameters.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="propertyGridServices">An object implementing the System.IServiceProvider interface that provides access to services managing property grid functionality.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface, which provides custom query validation logic.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery``1(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQuery``1(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}},System.IServiceProvider)">
      <summary>
        <para>Invokes the Query Builder to edit the specified query.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="propertyGridServices">An object implementing the System.IServiceProvider interface that provides access to services managing property grid functionality.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQueryWithQueryBuilder(DevExpress.DataAccess.Sql.SqlQuery)">
      <summary>
        <para>Invokes the Query Builder.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <returns>true, if the Query Builder was closed by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQueryWithQueryBuilder(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext)">
      <summary>
        <para>Invokes the Query Builder.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext"/> object.</param>
      <returns>true, if the Query Builder was closed by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.EditQueryWithQueryBuilder(DevExpress.DataAccess.Sql.SqlQuery,DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext,DevExpress.DataAccess.UI.Sql.QueryBuilderRunnerDelegate)">
      <summary>
        <para>Invokes the Query Builder.</para>
      </summary>
      <param name="query">A <see cref="T:DevExpress.DataAccess.Sql.SqlQuery"/> to be edited.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderEditQueryContext"/> object.</param>
      <param name="createQueryBuilderRunner">A <see cref="T:DevExpress.DataAccess.UI.Sql.QueryBuilderRunnerDelegate"/> object.</param>
      <returns>true, if the Query Builder was closed by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.EditQueryContext)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> object, specifying the Manage Queries dialog settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.DataAccess.Wizard.Model.SqlDataSourceModel}})">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.DataAccess.Wizard.Model.SqlDataSourceModel}},DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>Invokes the Manage Queries dialog window with the specified settings.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface, which provides custom query validation logic.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageQueries``1(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.EditQueryContext,System.Action{DevExpress.DataAccess.UI.Wizard.IWizardCustomization{``0}})">
      <summary>
        <para>Invokes the Query Builder to manage the specified queries.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">An <see cref="T:DevExpress.DataAccess.UI.Sql.EditQueryContext"/> object, specifying the Query Designer settings.</param>
      <param name="customizeWizard">A <see cref="T:System.Action"/> delegate of an object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, if the dialog was finished by clicking the Finish button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageRelations(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Invokes the Master-Detail Relations Editor to specify the key data fields by which a pair of queries is related.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageRelations(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.ManageRelationsContext)">
      <summary>
        <para>Invokes the Master-Detail Relations Editor to specify the key data fields by which a pair of queries is related.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.ManageRelationsContext"/> object, specifying the Master-Detail Relations Editor settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageRelations(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the Master-Detail Relations Editor to specify the key data fields by which a pair of queries is related.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageRelations(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Master-Detail Relations Editor to specify the key data fields by which a pair of queries is related.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ManageRelations(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Sql.IDBSchemaProvider)">
      <summary>
        <para>Invokes the Master-Detail Relations Editor to specify the key data fields by which a pair of queries is related.</para>
      </summary>
      <param name="sqlDataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="dbSchemaProvider">An object implementing the IDBSchemaProvider interface.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.OpenConnectionAsync(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.Wizard.Services.IConnectionProviderService,DevExpress.DataAccess.UI.Sql.IEditContext,System.Threading.CancellationTokenSource)">
      <summary />
      <param name="dataSource"></param>
      <param name="connectionService"></param>
      <param name="context"></param>
      <param name="tokenSource"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.RebuildResultSchema(DevExpress.DataAccess.Sql.SqlDataSource)">
      <summary>
        <para>Updates the data source schema available on the client in accordance to the current data source structure.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.RebuildResultSchema(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext)">
      <summary>
        <para>Updates the data source schema available on the client in accordance to the current data source structure.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="context">A <see cref="T:DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext"/> object that contains settings used to update a data source schema.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.RebuildResultSchema(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,DevExpress.DataAccess.Wizard.Services.IParameterService,System.Boolean)">
      <summary>
        <para>Updates the data source schema available on the client in accordance to the current data source structure.</para>
      </summary>
      <param name="dataSource">A <see cref="T:DevExpress.DataAccess.Sql.SqlDataSource"/> object, specifying the database connection.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the application’s look and feel settings.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface that is the owner of the current dialog window.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="showSuccessMessage">true to acknowledge the task completion upon successfully updating the schema; otherwise false.</param>
      <returns>true, if the dialog was finished by clicking the OK button; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.RebuildResultSchemaAsync(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.UI.Sql.RebuildResultSchemaContext,System.Threading.CancellationTokenSource)">
      <summary />
      <param name="dataSource"></param>
      <param name="context"></param>
      <param name="tokenSource"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Sql.SqlDataSourceUIHelper.ValidateConnection(DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.Wizard.Services.IConnectionProviderService,DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window,System.Boolean)">
      <summary />
      <param name="sqlDataSource"></param>
      <param name="connectionProviderService"></param>
      <param name="lookAndFeel"></param>
      <param name="owner"></param>
      <param name="showMessage"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Sql.SqlEditorId">
      <summary>
        <para>Lists the editors that can be customized by implementing the <see cref="T:DevExpress.DataAccess.UI.Sql.ISqlEditorsCustomizationService"/> interface.</para>
      </summary>
    </member>
    <member name="F:DevExpress.DataAccess.UI.Sql.SqlEditorId.Connection">
      <summary>
        <para>Identifies the Data Connections Editor.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.DataAccess.UI.Sql.SqlEditorId.Query">
      <summary>
        <para>Identifies the Query Editor.</para>
        <para></para>
      </summary>
    </member>
    <member name="N:DevExpress.DataAccess.UI.Wizard">
      <summary>
        <para>Contains classes used to manage UI settings of the Data Source wizard and Query Builder.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1">
      <summary>
        <para>Provides functionality to customize the Data Source Wizard.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.Model">
      <summary>
        <para>Provides access to a document model associated with a custom wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/> or <see cref="T:DevExpress.DashboardCommon.DashboardDataSourceModel"/> object.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.RegisterInstance``1(``0)">
      <summary>
        <para>Registers a specified service instance.</para>
      </summary>
      <param name="instance">A server instance.</param>
      <typeparam name="TServiceType"></typeparam>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.RegisterPage``2">
      <summary>
        <para>Registers a custom wizard page.</para>
      </summary>
      <typeparam name="TPageType"></typeparam>
      <typeparam name="TPageInstance"></typeparam>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.RegisterPageView``2">
      <summary>
        <para>Registers a custom wizard page view.</para>
      </summary>
      <typeparam name="TViewType"></typeparam>
      <typeparam name="TViewInstance"></typeparam>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.RegisterType``2">
      <summary>
        <para>Registers a service type.</para>
      </summary>
      <typeparam name="TServiceType"></typeparam>
      <typeparam name="TConcreteType"></typeparam>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.Resolve(System.Type)">
      <summary>
        <para>Returns the list of available data providers from the internal container.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> object.</param>
      <returns>A <see cref="T:System.Object"/> value.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.Resolve``1">
      <summary />
      <typeparam name="TServiceType"></typeparam>
      <returns></returns>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.StartPage">
      <summary>
        <para>Specifies the starting page of a wizard.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.WizardSize">
      <summary>
        <para>Specifies the dimensions of the wizard window.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1.WizardTitle">
      <summary>
        <para>Specifies the title of the wizard window.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="N:DevExpress.DataAccess.UI.Wizard.Services">
      <summary>
        <para>Contains interfaces that provide additional functionality to the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider">
      <summary>
        <para>When implemented by a class, provides repository items for editing query parameters.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider.GetRepositoryItem(System.Type)">
      <summary>
        <para>Gets a repository item for editing a parameter of the specified type.</para>
      </summary>
      <param name="type">The type of a parameter to edit.</param>
      <returns>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object.</returns>
    </member>
    <member name="N:DevExpress.DataAccess.UI.Wizard.Views">
      <summary>
        <para>Contains classes that provide the user interface functionality to the Data Source Wizard views.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView">
      <summary>
        <para>Provides a view for the Select a Data Connection page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.#ctor(DevExpress.DataAccess.Wizard.SqlWizardOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView"/> class with the specified options.</para>
      </summary>
      <param name="options">An object that specifies the wizard options.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available connections is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.ExistingConnectionName">
      <summary>
        <para>Gets the name of the connection selected from the list of available connections.</para>
      </summary>
      <value>A string specifying the name of the selected connection.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.SetConnections(System.Collections.Generic.IEnumerable{System.String})">
      <summary>
        <para>Changes the list of available connections on a wizard page.</para>
      </summary>
      <param name="connectionNames">A collection of strings specifying connection names.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.SetSelectedConnection(System.String)">
      <summary>
        <para>Changes the selected item from the list of available connections.</para>
      </summary>
      <param name="connectionName">A string that specifies the name of the connection to be selected.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseConnectionPageView.ShouldCreateNewConnection">
      <summary>
        <para>Gets whether to create a new data connection or use an existing one from the list.</para>
      </summary>
      <value>true, to create a new connection; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceNamePageView">
      <summary>
        <para>Provides a view for the Enter the Data Source Name page of the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceNamePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceNamePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceNamePageView.DataSourceName">
      <summary>
        <para>Specifies the data source name.</para>
      </summary>
      <value>A string that specifies the data source name.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceNamePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceNamePageView.ShowErrorMessage">
      <summary>
        <para>Displays an error message when a data source with the specified name already exists.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView">
      <summary>
        <para>Provides a view for the Select the Data Source Type page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView.#ctor(DevExpress.DataAccess.Wizard.Services.DataSourceTypes)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView"/> class with the specified data source types.</para>
      </summary>
      <param name="dataSourceTypes">A <see cref="T:DevExpress.DataAccess.Wizard.Services.DataSourceTypes"/> object.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView.DataSourceType">
      <summary>
        <para>Specifies the selected data source type.</para>
      </summary>
      <value>A value that specifies the data source type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseDataSourceTypePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView">
      <summary>
        <para>Provides a view for the Select the Connection String page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available connections is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.ExistingConnectionName">
      <summary>
        <para>Gets the name of the connection selected from the list of available connections.</para>
      </summary>
      <value>A string specifying the name of the selected connection.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.Initialize">
      <summary>
        <para>Initializes a wizard page with the list of available connections.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.SetConnections(System.Collections.Generic.IEnumerable{System.String})">
      <summary>
        <para>Changes the list of existing connections on a wizard page.</para>
      </summary>
      <param name="connectionNames">A collection of strings specifying connection names.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.SetSelectedConnection(System.String)">
      <summary>
        <para>Changes the selected item from the list of available connections.</para>
      </summary>
      <param name="connectionName">A string that specifies the name of the connection to be selected.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFConnectionStringPageView.ShouldCreateNewConnection">
      <summary>
        <para>Gets whether to create a new data connection or use an existing one from the list.</para>
      </summary>
      <value>true, to create a new connection; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView">
      <summary>
        <para>Provides a view for the Select the Data Context page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.#ctor(DevExpress.DataAccess.Wizard.EFWizardOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView"/> class with the specified settings.</para>
      </summary>
      <param name="options">An <see cref="T:DevExpress.DataAccess.Wizard.EFWizardOptions"/> enumeration value.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.BrowseForAssembly">
      <summary>
        <para>Occurs after the Browse button is clicked on a wizard page and an assembly is selected.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.ContextName">
      <summary>
        <para>Specifies the item selected in the list of available data contexts.</para>
      </summary>
      <value>A string that specifies the selected data context.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.ContextNameChanged">
      <summary>
        <para>Occurs when the selected item in the list of available data contexts is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.Initialize">
      <summary>
        <para>Initializes a wizard page with a list of available data contexts.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFContextPageView.RefreshContextList(System.Collections.Generic.IEnumerable{DevExpress.Entity.Model.IContainerInfo})">
      <summary>
        <para>Changes the list of available data contexts.</para>
      </summary>
      <param name="containers">A collection of <see cref="T:DevExpress.Entity.Model.IContainerInfo"/> objects containing settings of data contexts.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView">
      <summary>
        <para>Provides a view for the Select a Data Member page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView.DataMember">
      <summary>
        <para>Specifies the name of the selected data member.</para>
      </summary>
      <value>A string that specifies the name of the selected data member.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView.DataMemberChanged">
      <summary>
        <para>Occurs when the selected item in the list of available members is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.Xpo.DB.DBTable},System.Collections.Generic.IEnumerable{DevExpress.Xpo.DB.DBStoredProcedure},System.String)">
      <summary>
        <para>Initializes a wizard page with the list of available data members.</para>
      </summary>
      <param name="tables">A collection of the <see cref="T:DevExpress.Xpo.DB.DBTable"/> objects specifying tables from an Entity Framework data source.</param>
      <param name="procedures">A collection of the <see cref="T:DevExpress.Xpo.DB.DBStoredProcedure"/> objects specifying stored procedures from an Entity Framework data source.</param>
      <param name="dataMember">A string that specifies the name of the selected data member.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseEFDataMemberPageView.StoredProcChosen">
      <summary>
        <para>Specifies whether the selected data member is a stored procedure.</para>
      </summary>
      <value>true, if the selected data member is a stored procedure; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView">
      <summary>
        <para>Provides a view for the Select a Worksheet, Table or Named Range page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available worksheets, tables and named regions is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView.Initialize(DevExpress.DataAccess.Wizard.Presenters.ListBoxItem[])">
      <summary>
        <para>Initializes a wizard page with the list of available worksheets, tables and named regions.</para>
      </summary>
      <param name="items">A collection of <see cref="T:DevExpress.DataAccess.Wizard.Presenters.ListBoxItem"/> objects containing the settings of file data ranges.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseExcelFileDataRangePageView.SelectedItem">
      <summary>
        <para>Specifies the item selected in the list of available worksheets, tables and named regions.</para>
      </summary>
      <value>An object containing settings of the selected item.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView">
      <summary>
        <para>Provides a view for the Specify Import Settings page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.DetectEncoding">
      <summary>
        <para>Occurs when the state of the Detect automatically check box for the Encoding option is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.DetectNewlineType">
      <summary>
        <para>Occurs when the state of the Detect automatically check box for the Newline type option is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.DetectValueSeparator">
      <summary>
        <para>Occurs when the state of the Detect automatically check box for the Value separator option is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.DocumentFormat">
      <summary>
        <para>Specifies the format of the selected document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Excel.ExcelDocumentFormat"/> enumeration value that specifies the format of the selected document.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.Initialize(DevExpress.DataAccess.Excel.ExcelSourceOptionsBase)">
      <summary>
        <para>Initializes a wizard page with the available options depending on the type of the selected file.</para>
      </summary>
      <param name="options">An <see cref="T:DevExpress.DataAccess.Excel.ExcelSourceOptionsBase"/> descendant containing options used to extract data from the selected file.  This value is assigned to the <see cref="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.SourceOptions"/> property.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.SetEncoding(System.Text.Encoding)">
      <summary>
        <para>Changes the entry selected in the Encoding editor.</para>
      </summary>
      <param name="encoding">An <see cref="T:System.Text.Encoding"/> value that specifies character encoding in the selected CSV file.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.SetNewlineType(DevExpress.DataAccess.Excel.CsvNewlineType)">
      <summary>
        <para>Changes the entry selected in the Newline type editor.</para>
      </summary>
      <param name="newlineType">An <see cref="T:DevExpress.XtraExport.Csv.CsvNewlineType"/> enumeration value that specifies the line break type in the source CSV file.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.SetValueSeparator(System.Char)">
      <summary>
        <para>Changes the entry selected in the Value separator editor.</para>
      </summary>
      <param name="separator">A character used to separate values in the selected CSV file.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.SourceOptions">
      <summary>
        <para>Gets options used to extract data form the selected file (Microsoft Excel workbook or CSV file).</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Excel.ExcelSourceOptionsBase"/> descendant containing options used to extract data from the selected file.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator">
      <summary />
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.#ctor">
      <summary />
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.#ctor(System.Char)">
      <summary />
      <param name="value"></param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.#ctor(System.Char,System.String)">
      <summary />
      <param name="value"></param>
      <param name="displayName"></param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.DisplayName">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.Equals(System.Object)">
      <summary />
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.GetHashCode">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.ToString">
      <summary />
      <returns></returns>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFileOptionsPageView.ValueSeparator.Value">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView">
      <summary>
        <para>Provides a view for the Select an Excel Workbook or CSV File page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView.Changed">
      <summary>
        <para>Occurs when the selected file is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView.FileName">
      <summary>
        <para>Specifies the name of the selected file.</para>
      </summary>
      <value>A string specifying the file name.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseFilePageView.ShowPasswordForm(System.String,System.String,DevExpress.DataAccess.Wizard.Presenters.FileInfo@)">
      <summary>
        <para>Shows the form for specifying a password, if the selected file is password protected.</para>
      </summary>
      <param name="caption">The caption of the password form.</param>
      <param name="fileName">The name of the selected file.</param>
      <param name="fileInfo">An object containing settings of the selected file.</param>
      <returns>true, if the password was successfully specified, otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView">
      <summary>
        <para>Provides a view for the Select Data Fields page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.#ctor(DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView"/> class with the specified settings.</para>
      </summary>
      <param name="repositoryItemsProvider">An object implementing the DevExpress.DataAccess.Wizard.Views.IRepositoryItemsProvider interface that provides repository items for editing query parameters.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.AnalyzeAllRecordsRequested">
      <summary />
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the Select Data Fields wizard page.</para>
      </summary>
      <value>The page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.Initialize(DevExpress.DataAccess.Json.JsonSchemaNode,System.String,System.String[],System.Int32,System.Boolean)">
      <summary />
      <param name="schema"></param>
      <param name="rootElement"></param>
      <param name="roots"></param>
      <param name="schemaDiscoveryMaxItemCount"></param>
      <param name="maxItemCountApplied"></param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.RootElement">
      <summary>
        <para>Gets the name of the selected root element.</para>
      </summary>
      <value>The name of the selected root element.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSchemaPageView.Schema">
      <summary>
        <para>Gets the schema of the retrieved JSON data source.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.DataAccess.Json.JsonSchemaNode"/> objects that specifies the schema of the retrieved JSON data source.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView">
      <summary>
        <para>Provides a view for the Specify JSON Data Location page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.#ctor(DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,System.IServiceProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView"/> class with specified settings.</para>
      </summary>
      <param name="repositoryItemsProvider"></param>
      <param name="parameterService"></param>
      <param name="propertyGridServices"></param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.Changed">
      <summary>
        <para>Occurs after settings on the Specify JSON Data Location wizard page have changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.ConnectionType">
      <summary>
        <para>Specifies the type of the connection to a JSON data source.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Wizard.Views.JsonConnectionType"/> enumeration value that specifies the type of the connection to a JSON data source.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.FilePath">
      <summary>
        <para>Specifies the path to a JSON file.</para>
      </summary>
      <value>The path to a JSON file.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the Specify JSON Data Location wizard page.</para>
      </summary>
      <value>The page description.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.Json">
      <summary>
        <para>Specifies the string with JSON content.</para>
      </summary>
      <value>A string that specifies a JSON data source.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.PathParameters">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.QueryParameters">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.SetResultUri(System.String)">
      <summary />
      <param name="uri"></param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseJsonSourcePageView.Uri">
      <summary>
        <para>Specifies the path to a JSON data source.</para>
      </summary>
      <value>The path to an JSON data source.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView">
      <summary>
        <para>Provides a view for the Select an Assembly page of the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available assemblies is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.Initialize">
      <summary>
        <para>Initializes a wizard page with the list of available assemblies.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.SelectedItem">
      <summary>
        <para>Specifies the item selected in the list of available assemblies.</para>
      </summary>
      <value>An object containing settings of the selected assembly.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.SetData(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Views.AssemblyViewInfo},System.Boolean)">
      <summary>
        <para>Changes the list of available assemblies.</para>
      </summary>
      <param name="items">A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.AssemblyViewInfo"/> objects containing settings of assemblies.</param>
      <param name="showAll">The state of the Show only highlighted assemblies check box.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectAssemblyPageView.ShowAll">
      <summary>
        <para>Specifies the state of the Show only highlighted assemblies check box.</para>
      </summary>
      <value>true, to disable the check box; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectBindingModePageView">
      <summary>
        <para>Provides a view for the Select the Data Binding Mode page of the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectBindingModePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectBindingModePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectBindingModePageView.Changed">
      <summary>
        <para>Occurs when the object binding mode selected on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectBindingModePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectBindingModePageView.SchemaOnly">
      <summary>
        <para>Specifies whether the mode obtaining the data source schema or the mode retrieving actual data is selected on a wizard page.</para>
      </summary>
      <value>true, to select the mode obtaining only the data source schema; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView">
      <summary>
        <para>Provides a view for the Select a Data Source Constructor page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available constructors is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Views.ConstructorViewInfo},System.Boolean)">
      <summary>
        <para>Initializes a wizard page with the list of available constructors.</para>
      </summary>
      <param name="items">A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.ConstructorViewInfo"/> objects containing the settings of constructors.</param>
      <param name="showAll">The state of the Show only highlighted constructors check box.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView.Result">
      <summary>
        <para>Specifies the item selected in the list of available data source constructors.</para>
      </summary>
      <value>An object containing settings of the selected data source constructor.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectConstructorPageView.ShowAll">
      <summary>
        <para>Specifies the state of the Show only highlighted constructors check box.</para>
      </summary>
      <value>true, to disable the check box; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView">
      <summary>
        <para>Provides a view for the Select a Data Source Member page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available members is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.ObjectBinding.ObjectMember},System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a wizard page with the list of available members.</para>
      </summary>
      <param name="items">A collection of <see cref="T:DevExpress.DataAccess.ObjectBinding.ObjectMember"/> objects containing settings of object members.</param>
      <param name="staticType">A value that specifies whether binding to an object member (not to the entire object) is selected.</param>
      <param name="showAll">The state of the Show only highlighted members check box.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Item">
      <summary />
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Item.#ctor(DevExpress.DataAccess.ObjectBinding.ObjectMember,System.String)">
      <summary />
      <param name="data"></param>
      <param name="description"></param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Item.Data">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Item.Description">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Item.Highlighted">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Item.MemberType">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.MemberType">
      <summary />
    </member>
    <member name="F:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.MemberType.Method">
      <summary />
    </member>
    <member name="F:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.MemberType.Property">
      <summary />
    </member>
    <member name="F:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.MemberType.StaticMethod">
      <summary />
    </member>
    <member name="F:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.MemberType.StaticProperty">
      <summary />
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.Result">
      <summary>
        <para>Specifies the item selected in the list of available members.</para>
      </summary>
      <value>An object containing settings of the selected member.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectMemberPageView.ShowAll">
      <summary>
        <para>Specifies the state of the Show only highlighted members check box.</para>
      </summary>
      <value>true, to disable the check box; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView">
      <summary>
        <para>Provides a view for the Select a Data Source Type page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView.Changed">
      <summary>
        <para>Occurs when the selected item in the list of available types is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Views.TypeViewInfo},System.Boolean)">
      <summary>
        <para>Initializes a wizard page with the list of available types.</para>
      </summary>
      <param name="items">A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.TypeViewInfo"/> objects containing settings of object types.</param>
      <param name="showAll">The state of the Show only highlighted types check box.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView.SelectedItem">
      <summary>
        <para>Specifies the item selected in the list of available types.</para>
      </summary>
      <value>An object containing settings of the selected type.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseObjectTypePageView.ShowAll">
      <summary>
        <para>Specifies the state of the Show only highlighted types check box.</para>
      </summary>
      <value>true, to disable the check box; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseXPObjectSourceEntityTypePageView">
      <summary>
        <para>Provides a view for the Choose an Entity Type page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseXPObjectSourceEntityTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ChooseXPObjectSourceEntityTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseXPObjectSourceEntityTypePageView.EntityType">
      <summary>
        <para>Specifies the selected persistent object class.</para>
      </summary>
      <value>A value that specifies the selected persistent object class.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ChooseXPObjectSourceEntityTypePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the Choose an Entity Type wizard page.</para>
      </summary>
      <value>The page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ChooseXPObjectSourceEntityTypePageView.Initialize(System.Collections.IEnumerable)">
      <summary>
        <para>Initializes the Choose an Entity Type wizard page with the list of available entity types.</para>
      </summary>
      <param name="entityTypes">A collection of available persistent object classes.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView">
      <summary>
        <para>Provides a view for the Specify a Connection String page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.ConnectionName">
      <summary>
        <para>Specifies the name of the created connection.</para>
      </summary>
      <value>A string specifying the name of the created connection.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.ConnectionParametersChanged">
      <summary>
        <para>Occurs when connection information on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.ConnectionString">
      <summary>
        <para>Specifies the connection string created on a wizard page.</para>
      </summary>
      <value>The connection string.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.SetCanSaveToStorage(System.Boolean)">
      <summary>
        <para>Sets whether the connection string can be saved to the dedicated storage.</para>
      </summary>
      <param name="value">true, if the connection string can be saved to the storage; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.ShouldSaveConnectionString">
      <summary>
        <para>Specifies whether to save the connection string to the dedicated storage.</para>
      </summary>
      <value>true, to save the connection string; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFConnectionStringPageView.UseDefaultConnectionString">
      <summary>
        <para>Specifies whether to use the default connection string.</para>
      </summary>
      <value>true, to use the default connection string; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFFiltersPageView">
      <summary>
        <para>Provides a view for the Configure Filters page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFFiltersPageView.#ctor(DevExpress.DataAccess.Wizard.Services.IParameterService,System.IServiceProvider,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFFiltersPageView"/> class with the specified settings.</para>
      </summary>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFFiltersPageView.Filters">
      <summary>
        <para>Provides access to the collection of data filters.</para>
      </summary>
      <value>An array of <see cref="T:DevExpress.DataAccess.EntityFramework.DBSetFilter"/> objects.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFFiltersPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of a wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFFiltersPageView.Initialize(DevExpress.DataAccess.EntityFramework.DBSetFilter[],System.Collections.Generic.IDictionary{System.String,DevExpress.Xpo.DB.DBTable})">
      <summary>
        <para>Initializes a wizard page with the list of available tables and filters.</para>
      </summary>
      <param name="filters">An array of <see cref="T:DevExpress.DataAccess.EntityFramework.DBSetFilter"/> objects.</param>
      <param name="dbTables">Initializes a wizard page with the list of available tables and filters.
A dictionary, specifying a value pair that includes the DBSet‘s name and the DBTable.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView">
      <summary>
        <para>Provides a view for the Bind to a Stored Procedure page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.#ctor(System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider,DevExpress.DataAccess.Wizard.IWizardRunnerContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView"/> class with the specified settings.</para>
      </summary>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface that provides the service for the property grid.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</param>
      <param name="context">An object that specifies the wizard options.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.AddClick">
      <summary>
        <para>Occurs when the Add button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.AddToList(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo})">
      <summary>
        <para>Adds the specified stored procedures to the list to be used.</para>
      </summary>
      <param name="procedures">A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo"/> objects, which provide the settings of stored procedures.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.ChooseProceduresToAdd(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo})">
      <summary>
        <para>Shows the dialog for selecting stored procedures to be added to the list.</para>
      </summary>
      <param name="available">A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo"/> objects containing the settings of available stored procedures.</param>
      <returns>A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo"/> objects containing the settings of the selected stored procedures.</returns>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo},System.Func{DevExpress.DataAccess.Wizard.PreviewData})">
      <summary />
      <param name="procedures"></param>
      <param name="getPreviewData"></param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.Procedures">
      <summary>
        <para>Gets the stored procedures with the specified parameters from the list of procedures to be used.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo"/> objects, which provide the settings of stored procedures.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.RemoveClick">
      <summary>
        <para>Occurs when the Remove button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.RemoveFromList(DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo)">
      <summary>
        <para>Removes the specified stored procedure from the list of procedures to be used.</para>
      </summary>
      <param name="procedure">A <see cref="T:DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo"/> object that provides the stored procedure settings.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.SelectedItem">
      <summary>
        <para>Gets the selected item in the list of stored procedures to be used.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Wizard.Views.StoredProcedureViewInfo"/> object containing the settings of the selected stored procedure.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureEFStoredProceduresPageView.SetAddEnabled(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Add button on a wizard page.</para>
      </summary>
      <param name="value">true, to activate the button; false, to deactivate the button.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView">
      <summary>
        <para>Provides a view for the Choose Columns page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView.Changed">
      <summary>
        <para>Occurs when a collection of selected columns from an Excel file is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView.Initialize(DevExpress.DataAccess.Excel.FieldInfo[],System.Func{DevExpress.DataAccess.Excel.FieldInfo[],DevExpress.DataAccess.Native.ColumnarData})">
      <summary>
        <para>Initializes a wizard page with the list of available columns.</para>
      </summary>
      <param name="schema">A collection of <see cref="T:DevExpress.DataAccess.Excel.FieldInfo"/> objects containing settings of Excel data fields.</param>
      <param name="loadPreviewData">A function that previews the result data.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView.ResetSchema">
      <summary />
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureExcelFileColumnsPageView.Schema">
      <summary>
        <para>Gets a collection of columns selected on a wizard page.</para>
      </summary>
      <value>An array of <see cref="T:DevExpress.DataAccess.Excel.FieldInfo"/> objects containing the settings of Excel data fields.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView">
      <summary>
        <para>Provides a view for the Configure Query Parameters page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView.#ctor(System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView"/> class with the specified settings.</para>
      </summary>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface that provides the service for the property grid.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView.ConfirmQueryExecution">
      <summary>
        <para>Displays the dialog to confirm query execution.</para>
      </summary>
      <returns>true, if the query execution is confirmed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView.ParameterRenamed">
      <summary />
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureParametersPageView.ShowDuplicatingColumnNameError(System.String)">
      <summary>
        <para>Displays the error message about duplicating the specified column.</para>
      </summary>
      <param name="columnName">The name of the column that is duplicated.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView">
      <summary>
        <para>Provides a view for the Create a Query or Select a Stored Procedure page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.#ctor(DevExpress.Data.IDisplayNameProvider,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView"/> class with the specified settings.</para>
      </summary>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface that provides methods to specify custom names for data items.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface that provides the service for the property grid.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface that provides custom validation logic for SQL queries.</param>
      <param name="options">An object that specifies the wizard options.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.#ctor(DevExpress.Data.IDisplayNameProvider,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Wizard.IWizardRunnerContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView"/> class with the specified settings.</para>
      </summary>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="options">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.#ctor(DevExpress.Data.IDisplayNameProvider,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView"/> class with the specified settings.</para>
      </summary>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="options">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.CreateQueryBuilderRunner(DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.DBSchema,DevExpress.DataAccess.Sql.SqlDataConnection,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Creates an object that enables running the Query Builder.</para>
      </summary>
      <param name="schemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema (by calling the <see cref="M:DevExpress.DataAccess.Sql.IDBSchemaProviderEx.LoadColumns(DevExpress.DataAccess.Sql.SqlDataConnection,DevExpress.Xpo.DB.DBTable[])"/> method).</param>
      <param name="dbSchema">A <see cref="T:DevExpress.DataAccess.Sql.DBSchema"/> object that is used to display database objects in the grid control.</param>
      <param name="connection">An object specifying a connection to an SQL data source.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides the capability to manage report parameters.</param>
      <returns>A <see cref="T:DevExpress.DataAccess.UI.Wizard.QueryBuilderRunnerBase"/> object.</returns>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.Initialize(System.Boolean,System.Boolean,DevExpress.DataAccess.Native.Sql.ConnectionProviders.IAliasFormatter)">
      <summary>
        <para>For internal use. Initializes a wizard page with the specified options.</para>
      </summary>
      <param name="allowCustomSql">Specifies whether custom SQL editing is enabled on a wizard page.</param>
      <param name="storedProceduresSupported">Specifies whether a data source contains stored procedures.</param>
      <param name="aliasFormatter">An object specifying an SQL query formatter.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.InitializeStoredProcedures(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Wizard.Presenters.DBStoredProcedureViewInfo})">
      <summary />
      <param name="items"></param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.QueryType">
      <summary>
        <para>Specifies the type of the query selected on a wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.Wizard.Presenters.QueryType"/> enumerator value.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.QueryTypeChanged">
      <summary>
        <para>Occurs when the query type selected on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.RunQueryBuilder">
      <summary>
        <para>Occurs when the Run Query Builder button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.SelectedStoredProcedureIndex">
      <summary>
        <para>Specifies the index of the selected item in the list of available stored procedures.</para>
      </summary>
      <value>An index of the selected stored procedure.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.SqlString">
      <summary>
        <para>Specifies the SQL string created on a wizard page.</para>
      </summary>
      <value>The SQL string.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.SqlStringChanged">
      <summary>
        <para>Occurs when the SQL string created on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConfigureQueryPageView.StoredProcedureChanged">
      <summary>
        <para>Occurs when the selected item in the list of available stored procedures is changed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView">
      <summary>
        <para>Provides a view for the Specify a Connection String page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.#ctor(System.Collections.Generic.List{DevExpress.DataAccess.UI.Wizard.ProviderLookupItem})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView"/> class with the specified data providers.</para>
      </summary>
      <param name="dataProviders">A collection of <see cref="T:DevExpress.DataAccess.UI.Wizard.ProviderLookupItem"/> objects.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.#ctor(System.Collections.Generic.List{DevExpress.DataAccess.UI.Wizard.ProviderLookupItem},DevExpress.DataAccess.Wizard.IWizardRunnerContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView"/> class with the specified settings.</para>
      </summary>
      <param name="dataProviders">A collection of <see cref="T:DevExpress.DataAccess.UI.Wizard.ProviderLookupItem"/> objects.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.Changed">
      <summary>
        <para>Occurs when the item selected in the Provider drop-down list is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.ConnectionName">
      <summary>
        <para>Specifies the name of the connection created on a wizard page.</para>
      </summary>
      <value>A string specifying the connection name.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.DataConnectionParameters">
      <summary>
        <para>Specifies the parameters of the current data connection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase"/> descendant containing connection parameters.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.InitControls">
      <summary>
        <para>Initializes controls of the current wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.SelectProvider(System.String)">
      <summary>
        <para>Changes the item selected in the Provider drop-down list.</para>
      </summary>
      <param name="provider">A string specifying the name of the provider to be selected.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ConnectionPropertiesPageView.SetConnections(System.Collections.Generic.IEnumerable{System.String})">
      <summary>
        <para>Changes the list of existing connections on a wizard page.</para>
      </summary>
      <param name="connectionNames">A collection of strings specifying connection names.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView">
      <summary>
        <para>Provides a view for the Create a Query or Select a Stored Procedure (Multi-Query Version) page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.#ctor(DevExpress.Data.IDisplayNameProvider,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView"/> class with the specified settings.</para>
      </summary>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="options">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.#ctor(DevExpress.Data.IDisplayNameProvider,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Wizard.IWizardRunnerContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView"/> class with the specified settings.</para>
      </summary>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="options">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.#ctor(DevExpress.Data.IDisplayNameProvider,System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView"/> class with the specified settings.</para>
      </summary>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="options">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.ConfirmQueryExecution(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Sql.SqlQuery})">
      <summary />
      <param name="queries"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.CreateMasterDetailRunner">
      <summary>
        <para>For internal use. Creates an object that enables running the Master-Detail Relation Editor.</para>
      </summary>
      <returns>A DevExpress.DataAccess.Native.Sql.MasterDetail.MasterDetailRunnerBase object that enables running the Master-Detail Relation Editor.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.CreateQueryBuilderRunner(DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.DBSchema,DevExpress.DataAccess.Sql.SqlDataConnection,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Creates an object that enables running the Query Builder.</para>
      </summary>
      <param name="schemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema (by calling the <see cref="M:DevExpress.DataAccess.Sql.IDBSchemaProviderEx.LoadColumns(DevExpress.DataAccess.Sql.SqlDataConnection,DevExpress.Xpo.DB.DBTable[])"/> method).</param>
      <param name="dbSchema">A <see cref="T:DevExpress.DataAccess.Sql.DBSchema"/> object that is used to display database objects in the grid control.</param>
      <param name="connection">An object specifying a connection to an SQL data source.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides the capability to manage report parameters.</param>
      <returns>A <see cref="T:DevExpress.DataAccess.UI.Wizard.QueryBuilderRunnerBase"/> object.</returns>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.ExpandNode">
      <summary>
        <para>Occurs on expanding a tree list node.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.Items">
      <summary>
        <para>Specifies the collection of tree list elements.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.DataAccess.Wizard.Presenters.MultiQueryItem"/> objects.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigurePageView.SetEditRelationEnabled(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Edit Relations button is enabled on the current wizard page.</para>
      </summary>
      <param name="enabled">true, to enable the Edit Relations button; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView">
      <summary>
        <para>Provides a view for the Configure Query Parameters page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView.#ctor(System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView"/> class with the specified settings.</para>
      </summary>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView.ConfirmQueryExecution(System.Collections.Generic.IEnumerable{DevExpress.DataAccess.Sql.SqlQuery})">
      <summary />
      <param name="queries"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView.ParameterRenamed">
      <summary />
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView.SelectedQueryName">
      <summary>
        <para>Returns the name of a query to which the currently edited parameter belongs.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the query name.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.MultiQueryConfigureParametersPageView.ShowDuplicatingColumnNameError(System.String)">
      <summary />
      <param name="columnName"></param>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ObjectConstructorParametersPageView">
      <summary>
        <para>Provides a view for the Specify the Constructor Parameters page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ObjectConstructorParametersPageView.#ctor(System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ObjectConstructorParametersPageView"/> class with the specified settings.</para>
      </summary>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface that provides the service for the property grid.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ObjectConstructorParametersPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ObjectMemberParametersPageView">
      <summary>
        <para>Provides a view for the Specify the Member Parameters page of the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ObjectMemberParametersPageView.#ctor(System.IServiceProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.ObjectMemberParametersPageView"/> class with the specified settings.</para>
      </summary>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface that provides the service for the property grid.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="repositoryItemsProvider">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.Services.IRepositoryItemsProvider"/> interface that provides repository items to manage query parameters.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.ObjectMemberParametersPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.ParametersPageViewBase">
      <summary>
        <para>The base for Data Source Wizard page views providing functionality to specify parameters.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.ParametersPageViewBase.GetParameters">
      <summary>
        <para>Returns a collection of parameters.</para>
      </summary>
      <returns>A collection of objects implementing the <see cref="T:DevExpress.Data.IParameter"/> interface.</returns>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView">
      <summary>
        <para>Provides a view for the Save the Connection String page of the Data Source Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.ConnectionName">
      <summary>
        <para>Specifies the name of the created connection.</para>
      </summary>
      <value>A string specifying the name of the created connection.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.SetCanSaveToStorage(System.Boolean)">
      <summary>
        <para>Sets whether the connection string can be saved to the dedicated storage.</para>
      </summary>
      <param name="value">true, if the connection string can be saved to the storage; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.SetConnectionUsesServerAuth(System.Boolean)">
      <summary>
        <para>Sets whether the data connection uses server authentication.</para>
      </summary>
      <param name="value">true, if the data connection uses server authentication; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.ShouldSaveConnectionString">
      <summary>
        <para>Specifies whether to save the connection string to the dedicated storage.</para>
      </summary>
      <value>true, to save the connection string; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.SaveConnectionPageView.ShouldSaveCredentials">
      <summary>
        <para>Gets whether to save the user credentials along with the connection string.</para>
      </summary>
      <value>true, to save the user credentials; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase">
      <summary>
        <para>The base for classes providing functionality to Data Source Wizard page views.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.ActiveButton">
      <summary>
        <para>Returns the button that closes the current wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.SimpleButton"/> object, specifying the “Next” or “Finish” button.</value>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.EnableFinish(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Finish button is enabled on a wizard page.</para>
      </summary>
      <param name="enable">true if the Finish button is enabled; otherwise false.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.EnableNext(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Next button is enabled on a wizard page.</para>
      </summary>
      <param name="enable">true if the Next button is enabled; otherwise false.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.EnablePrevious(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Previous button is enabled on a wizard page.</para>
      </summary>
      <param name="enable">true if the Previous button is enabled; otherwise false.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.Finish">
      <summary>
        <para>Occurs after clicking the Finish button on a wizard page.</para>
      </summary>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.HeaderDescription">
      <summary>
        <para>Specifies the description of a wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the page description.</value>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.Next">
      <summary>
        <para>Occurs after clicking the Next button on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.Paint">
      <summary>
        <para>Occurs when the content of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> and <see cref="T:DevExpress.XtraEditors.PanelControl"/> residing on a wizard page is being redrawn.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.Views.WizardViewBase.Previous">
      <summary>
        <para>Occurs after clicking the Previous button on a wizard page.</para>
      </summary>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext">
      <summary>
        <para>The default implementation of the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface, enabling you to run the Data Source Wizard in a WinForms application.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.#ctor(DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext"/> class with the specified owner and appearance.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.Confirm(System.String)">
      <summary>
        <para>Displays a confirmation window before the execution of a wizard.</para>
      </summary>
      <param name="message">A <see cref="T:System.String"/> value.</param>
      <returns>true, if a user clicks OK; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.CreateWizardView(System.String,System.Drawing.Size)">
      <summary>
        <para>Creates a Data Source Wizard view.</para>
      </summary>
      <param name="wizardTitle">A <see cref="T:System.String"/> value.</param>
      <param name="wizardSize">A <see cref="T:System.Drawing.Size"/> structure.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Data.WizardFramework.IWizardView"/> interface.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.Dispose">
      <summary>
        <para>Releases all resources used by <see cref="T:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.Run``1(DevExpress.Data.WizardFramework.Wizard{``0})">
      <summary>
        <para>Runs the wizard with a specified model.</para>
      </summary>
      <param name="wizard">A <see cref="T:DevExpress.Data.WizardFramework.Wizard`1"/> object.</param>
      <typeparam name="TModel"></typeparam>
      <returns>true, to save the changes to the wizard model; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.ShowMessage(System.String)">
      <summary>
        <para>Displays a message with the specified text before the execution of a wizard.</para>
      </summary>
      <param name="message">A <see cref="T:System.String"/> value.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.ShowMessage(System.String,System.String)">
      <summary>
        <para>Displays a message with the specified text and caption before the execution of a wizard.</para>
      </summary>
      <param name="message">A <see cref="T:System.String"/> value.</param>
      <param name="caption">A <see cref="T:System.String"/> value.</param>
    </member>
    <member name="P:DevExpress.DataAccess.UI.Wizard.WizardRunnerContext.WaitFormActivator">
      <summary>
        <para>Returns an object that displays a form while the wizard is loading information about a data source.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IWaitFormActivator"/> interface.</value>
    </member>
    <member name="T:DevExpress.DataAccess.UI.Wizard.WizardView">
      <summary>
        <para>The default implementation of the <see cref="T:DevExpress.Data.WizardFramework.IWizardView"/> interface, providing an empty form containing the Data Source Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.DataAccess.UI.Wizard.WizardView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.WizardView.Cancel">
      <summary>
        <para>Occurs after clicking the Cancel button in a wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardView.EnableFinish(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Finish button is available in a wizard.</para>
      </summary>
      <param name="enable">true, if the Finish button is available; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardView.EnableNext(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Next button is available in a wizard.</para>
      </summary>
      <param name="enable">true, if the Next button is available; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardView.EnablePrevious(System.Boolean)">
      <summary>
        <para>Specifies whether or not the Previous button is available in a wizard.</para>
      </summary>
      <param name="enable">true, if the Previous button is available; otherwise, false.</param>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.WizardView.Finish">
      <summary>
        <para>Occurs after clicking the Finish button in a wizard.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.WizardView.Next">
      <summary>
        <para>Occurs after clicking the Next button in a wizard.</para>
      </summary>
    </member>
    <member name="E:DevExpress.DataAccess.UI.Wizard.WizardView.Previous">
      <summary>
        <para>Occurs after clicking the Previous button in a wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardView.SetPageContent(System.Object)">
      <summary>
        <para>Specifies the content of a wizard page.</para>
      </summary>
      <param name="pageView">A <see cref="T:System.Object"/> value.</param>
    </member>
    <member name="M:DevExpress.DataAccess.UI.Wizard.WizardView.ShowError(System.String)">
      <summary>
        <para>Displays an error message with a specified text.</para>
      </summary>
      <param name="error">A <see cref="T:System.String"/> value.</param>
    </member>
    <member name="F:DevExpress.DataAccess.UI.Wizard.WizardView.TouchUIFormScalingAware">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
  </members>
</doc>