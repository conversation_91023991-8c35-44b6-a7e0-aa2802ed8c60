﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class Treasury
    Sub fill_treasury_name()

        type_pay.Items.Clear()
        Dim adp As New SqlDataAdapter("select treasury_name from treasury_name", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            type_pay.Items.Add(dt.Rows(i).Item("treasury_name"))
        Next
    End Sub
    Function gettreasury(subname) As String

        Dim sql = "select balace from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function
    Private Sub Treasury_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        fill_treasury_name()
        type_pay.SelectedIndex = 0
        day_option.SelectedIndex = 0
        inserte_Click(Nothing, Nothing)

    End Sub
    Sub cus_tru()
        Dim str = "select sum(income), sum(Expenses) from treasury_pay where Treasury_name=N'" & type_pay.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("treasury_pay")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        Dim sumcredit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
        treasury_balace.Text = Format(Val(sumdebit), "#,0.00") - Format(Val(sumcredit), "#,0.00")
        save_try()
    End Sub
    Sub save_try()
        Try
            Dim sql = "select * from treasury_name where Treasury_name=N'" & type_pay.Text() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then

            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
            
                dr!balace = treasury_balace.Text
            
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

            End If
        Catch ex As Exception

        End Try
    End Sub
 

    Private Sub inserte_Click(sender As Object, e As EventArgs) Handles inserte.Click
        Dim adp As New SqlDataAdapter("select * from treasury_pay where Treasury_name=N'" & (type_pay.Text) & "' and Treasury_date>='" & Format(datefrom.Value, "yyy/MM/dd") & "'  and Treasury_date<='" & Format(dateto.Value, "yyy/MM/dd") & "' order by Treasury_date", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt
        TextBox1.Text = GridView2.Columns("income").SummaryItem.SummaryValue - GridView2.Columns("Expenses").SummaryItem.SummaryValue
        cus_tru()
    End Sub
    Private Sub day_option_SelectedIndexChanged(sender As Object, e As EventArgs) Handles day_option.SelectedIndexChanged
        If day_option.SelectedIndex = 0 Then
            datefrom.Value = Now.Date
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 1 Then
            datefrom.Value = DateAdd("d", -1, Now.Date)
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 2 Then
            datefrom.Value = DateAdd("ww", -1, Now.Date)
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 3 Then
            datefrom.Value = DateAdd("ww", -2, Now.Date)
            dateto.Value = DateAdd("ww", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 4 Then
            datefrom.Value = DateAdd("m", -1, Now.Date)
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 5 Then
            datefrom.Value = DateAdd("m", -2, Now.Date)
            dateto.Value = DateAdd("m", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 6 Then
            datefrom.Value = DateAdd("m", -3, Now.Date)
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 7 Then
            datefrom.Value = DateAdd("m", -6, Now.Date)
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 8 Then
            datefrom.Value = DateAdd("yyyy", -1, Now.Date)
            dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 9 Then
            datefrom.Value = DateAdd("yyyy", -2, Now.Date)
            dateto.Value = DateAdd("yyyy", -1, Now.Date)
        End If
    End Sub

    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged
        treasury_balace.Text = gettreasury(type_pay.Text)
        inserte_Click(Nothing, Nothing)
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If XtraForm1.m81.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Deposit.Show()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        If XtraForm1.m83.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        trans_money.Show()
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        If XtraForm1.m82.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Withdrawal.Show()
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub
End Class