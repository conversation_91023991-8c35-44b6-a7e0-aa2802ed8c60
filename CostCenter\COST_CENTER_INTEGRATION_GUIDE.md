# دليل التكامل - نظام مراكز التكلفة

## نظرة عامة
تم دمج نظام مراكز التكلفة بنجاح في النظام الموجود ليوفر تتبعاً شاملاً للتكاليف والإيرادات حسب مراكز التكلفة المختلفة.

## المكونات المضافة

### 1. الملفات الجديدة
- `CostCenter/CostCenterHelper.vb` - مكتبة مساعدة لجميع عمليات مراكز التكلفة
- `CostCenter/CostCenterManagement.vb` - شاشة إدارة مراكز التكلفة
- `Reports/CostCenterReport.vb` - تقرير شامل لمراكز التكلفة

### 2. التعديلات على الملفات الموجودة

#### أ. شاشة فواتير المبيعات (`invoice/invoice_add.vb`)
- إضافة حقل اختيار مركز التكلفة
- ربط الفاتورة بمركز التكلفة عند الحفظ
- تحميل مركز التكلفة عند عرض فاتورة موجودة

#### ب. شاشة المصروفات (`Expenses/Expenses_add.vb`)
- إضافة حقل اختيار مركز التكلفة
- ربط المصروف بمركز التكلفة عند الحفظ
- تحميل مركز التكلفة عند عرض مصروف موجود

#### ج. شاشة التقارير (`report/report_show.vb`)
- إضافة زر "تقرير مراكز التكلفة" في تبويب الحسابات
- إضافة معالج الحدث لفتح التقرير

## طريقة الوصول للنظام

### 1. إدارة مراكز التكلفة
```
القائمة الرئيسية → الإعدادات → إدارة مراكز التكلفة
```
أو يمكن إضافتها إلى القائمة الرئيسية حسب الحاجة.

### 2. تقرير مراكز التكلفة
```
القائمة الرئيسية → التقارير → تبويب الحسابات → تقرير مراكز التكلفة
```

### 3. استخدام مراكز التكلفة في الفواتير
- في شاشة فواتير المبيعات: ستجد حقل "مركز التكلفة" في أسفل الشاشة
- في شاشة المصروفات: ستجد حقل "مركز التكلفة" في أسفل الشاشة

## هيكل قاعدة البيانات

### جدول مراكز التكلفة الجديد
```sql
CREATE TABLE cost_centers (
    cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
    cost_center_code NVARCHAR(50) NOT NULL UNIQUE,
    cost_center_name NVARCHAR(255) NOT NULL,
    description NVARCHAR(500),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE()
)
```

### التعديلات على الجداول الموجودة
```sql
-- إضافة عمود مركز التكلفة إلى جدول الفواتير
ALTER TABLE invoice_add ADD cost_center_id INT;

-- إضافة عمود مركز التكلفة إلى جدول المصروفات
ALTER TABLE Expenses_add ADD cost_center_id INT;
```

## الميزات الرئيسية

### 1. إدارة مراكز التكلفة
- إضافة مراكز تكلفة جديدة
- تعديل مراكز التكلفة الموجودة
- حذف مراكز التكلفة غير المستخدمة
- تفعيل/إلغاء تفعيل مراكز التكلفة

### 2. ربط العمليات المالية
- ربط فواتير المبيعات بمراكز التكلفة
- ربط المصروفات بمراكز التكلفة
- حفظ واسترجاع البيانات تلقائياً

### 3. التقارير الشاملة
- تقرير تفصيلي لجميع العمليات حسب مركز التكلفة
- ملخص إجمالي للأداء المالي
- إمكانية الفلترة حسب التاريخ ومركز التكلفة
- تصدير التقارير إلى Excel
- طباعة التقارير

## كيفية الاستخدام

### الخطوة 1: إعداد مراكز التكلفة
1. افتح شاشة "إدارة مراكز التكلفة"
2. أضف مراكز التكلفة المطلوبة (مثل: قسم المبيعات، قسم الإنتاج، إلخ)
3. تأكد من تفعيل مراكز التكلفة المطلوبة

### الخطوة 2: استخدام مراكز التكلفة في العمليات
1. عند إنشاء فاتورة مبيعات جديدة، اختر مركز التكلفة المناسب
2. عند إدخال مصروف جديد، اختر مركز التكلفة المناسب
3. احفظ العملية كالمعتاد

### الخطوة 3: مراجعة التقارير
1. افتح "تقرير مراكز التكلفة"
2. حدد الفترة الزمنية المطلوبة
3. اختر مركز التكلفة (أو اتركه فارغاً لعرض الكل)
4. اضغط "عرض التقرير"

## المعلومات المعروضة في التقرير

### التقرير التفصيلي
- مركز التكلفة
- نوع العملية (فاتورة مبيعات / مصروفات)
- رقم الفاتورة والتاريخ
- العميل أو نوع المصروف
- المبلغ والربح
- المستخدم ونوع الدفع
- الملاحظات

### الملخص الإجمالي
- عدد فواتير المبيعات لكل مركز تكلفة
- إجمالي المبيعات لكل مركز تكلفة
- عدد المصروفات لكل مركز تكلفة
- إجمالي المصروفات لكل مركز تكلفة
- صافي الربح لكل مركز تكلفة

## الأمان والاستقرار

### التعامل مع الأخطاء
- النظام يتعامل بأمان مع عدم وجود جدول مراكز التكلفة
- في حالة عدم وجود الجدول، يتم إخفاء حقول مراكز التكلفة
- رسائل خطأ واضحة ومفيدة للمستخدم

### التوافق مع النظام الموجود
- لا يؤثر على العمليات الموجودة
- يعمل بشكل اختياري (يمكن ترك مركز التكلفة فارغاً)
- لا يتطلب تعديل البيانات الموجودة

## نصائح للاستخدام الأمثل

1. **التخطيط المسبق**: حدد مراكز التكلفة المطلوبة قبل البدء
2. **التسمية الواضحة**: استخدم أسماء واضحة ومفهومة لمراكز التكلفة
3. **المراجعة الدورية**: راجع تقارير مراكز التكلفة بانتظام
4. **التدريب**: تأكد من تدريب المستخدمين على النظام الجديد
5. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية قبل التطبيق

## الدعم الفني

في حالة وجود أي مشاكل أو استفسارات:
1. تحقق من ملف README.md للحصول على معلومات تفصيلية
2. راجع رسائل الخطأ للحصول على معلومات مفيدة
3. تأكد من صحة الاتصال بقاعدة البيانات
4. تحقق من صلاحيات المستخدم في قاعدة البيانات

## الخلاصة

نظام مراكز التكلفة يوفر:
- تتبع دقيق للتكاليف والإيرادات
- تقارير شاملة ومفصلة
- سهولة في الاستخدام والإدارة
- تكامل سلس مع النظام الموجود
- مرونة في التطبيق والاستخدام
