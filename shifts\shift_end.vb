﻿﻿Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox
Imports DevExpress.XtraReports.UI

Public Class shift_end
    
    Public current_shift_id As Integer = 0
    Private shift_data As DataRow = Nothing
    
    Private Sub shift_end_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تهيئة النموذج
            InitializeForm()
            
            ' تحميل بيانات الشيفت الحالي
            LoadShiftData()
            
            ' حساب إجماليات الشيفت
            CalculateShiftTotals()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل النموذج: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub InitializeForm()
        ' إعداد النموذج
        Me.Text = "إقفال الشيفت"
        Me.RightToLeft = RightToLeft.Yes
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        
        ' تعيين التاريخ والوقت الحالي
        end_date.EditValue = Now.Date
        end_time.EditValue = Now
        
        ' تعيين مبلغ النهاية الافتراضي
        end_amount.Value = 0
        
        ' تفعيل التركيز على مبلغ النهاية
        end_amount.Focus()
    End Sub
    
    Private Sub LoadShiftData()
        Try
            If current_shift_id = 0 Then
                ' البحث عن الشيفت النشط للمستخدم الحالي
                current_shift_id = GetActiveShiftId()
            End If
            
            If current_shift_id = 0 Then
                XtraMessageBox.Show("لا يوجد شيفت نشط للإقفال", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Me.Close()
                Return
            End If
            
            ' تحميل بيانات الشيفت
            Dim sql As String = "SELECT * FROM shifts WHERE shift_id = " & current_shift_id
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                Using adapter As New SqlDataAdapter(cmd)
                    Dim dt As New DataTable()
                    adapter.Fill(dt)
                    
                    If dt.Rows.Count > 0 Then
                        shift_data = dt.Rows(0)
                        
                        ' عرض بيانات الشيفت
                        shift_code.Text = shift_data("shift_code").ToString()
                        cashier_name.Text = shift_data("cashier_name").ToString()
                        store_name.Text = shift_data("store_name").ToString()
                        start_date.EditValue = Convert.ToDateTime(shift_data("start_date"))
                        start_amount.Value = Convert.ToDecimal(shift_data("start_amount"))
                        
                        ' عرض ملاحظات البداية
                        start_notes.Text = shift_data("notes").ToString()
                    Else
                        XtraMessageBox.Show("لم يتم العثور على بيانات الشيفت", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                        Me.Close()
                    End If
                End Using
            End Using
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل بيانات الشيفت: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function GetActiveShiftId() As Integer
        Try
            Dim cashier As String = ""
            If XtraForm1 IsNot Nothing Then
                cashier = XtraForm1.user_name.Text
            End If
            
            Dim sql As String = "SELECT shift_id FROM shifts WHERE cashier_name = N'" & cashier & "' AND shift_status = N'مفتوح'"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                Dim result = cmd.ExecuteScalar()
                Return If(result IsNot Nothing, Convert.ToInt32(result), 0)
            End Using
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    Private Sub CalculateShiftTotals()
        Try
            If current_shift_id = 0 Then Return
            
            ' حساب إجماليات الشيفت من الفواتير
            Dim sql As String = "SELECT " &
                               "COUNT(*) as total_invoices, " &
                               "ISNULL(SUM(total_invoice), 0) as total_sales, " &
                               "ISNULL(SUM(CASE WHEN type_money = N'نقدي' THEN total_invoice ELSE 0 END), 0) as total_cash, " &
                               "ISNULL(SUM(CASE WHEN type_money != N'نقدي' THEN total_invoice ELSE 0 END), 0) as total_credit, " &
                               "ISNULL(SUM(earn_invoice), 0) as total_profit, " &
                               "ISNULL(SUM(invoice_count), 0) as total_items " &
                               "FROM invoice_add WHERE shift_id = " & current_shift_id
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then
                        total_invoices.Value = Convert.ToInt32(reader("total_invoices"))
                        total_sales.Value = Convert.ToDecimal(reader("total_sales"))
                        total_cash.Value = Convert.ToDecimal(reader("total_cash"))
                        total_credit.Value = Convert.ToDecimal(reader("total_credit"))
                        total_profit.Value = Convert.ToDecimal(reader("total_profit"))
                        total_items.Value = Convert.ToInt32(reader("total_items"))
                    End If
                End Using
            End Using
            
            ' حساب الفرق في الخزينة
            CalculateCashDifference()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في حساب إجماليات الشيفت: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub CalculateCashDifference()
        Try
            ' حساب المبلغ المتوقع في الخزينة
            Dim expected_amount As Decimal = start_amount.Value + total_cash.Value
            expected_cash.Value = expected_amount
            
            ' حساب الفرق
            Dim difference As Decimal = end_amount.Value - expected_amount
            cash_difference.Value = difference
            
            ' تحديد لون الفرق
            If difference > 0 Then
                cash_difference.Properties.Appearance.ForeColor = Color.Green
                difference_label.Text = "زيادة في الخزينة:"
            ElseIf difference < 0 Then
                cash_difference.Properties.Appearance.ForeColor = Color.Red
                difference_label.Text = "نقص في الخزينة:"
            Else
                cash_difference.Properties.Appearance.ForeColor = Color.Black
                difference_label.Text = "الفرق في الخزينة:"
            End If
            
        Catch ex As Exception
            ' تجاهل الأخطاء في الحساب
        End Try
    End Sub
    
    Private Sub end_amount_EditValueChanged(sender As Object, e As EventArgs) Handles end_amount.EditValueChanged
        ' إعادة حساب الفرق عند تغيير مبلغ النهاية
        CalculateCashDifference()
    End Sub
    
    Private Sub end_shift_btn_Click(sender As Object, e As EventArgs) Handles end_shift_btn.Click
        Try
            ' التحقق من صحة البيانات
            If Not ValidateData() Then
                Return
            End If
            
            ' تأكيد إقفال الشيفت
            Dim message As String = "هل أنت متأكد من إقفال الشيفت؟" & vbCrLf & vbCrLf &
                                   "كود الشيفت: " & shift_code.Text & vbCrLf &
                                   "إجمالي المبيعات: " & Format(total_sales.Value, "#,##0.00") & vbCrLf &
                                   "إجمالي الأرباح: " & Format(total_profit.Value, "#,##0.00") & vbCrLf &
                                   "عدد الفواتير: " & total_invoices.Value.ToString()
            
            Dim result As DialogResult = XtraMessageBox.Show(message, "تأكيد إقفال الشيفت", 
                                                            MessageBoxButtons.YesNo, 
                                                            MessageBoxIcon.Question)
            
            If result = DialogResult.Yes Then
                ' إقفال الشيفت
                If CloseShift() Then
                    XtraMessageBox.Show("تم إقفال الشيفت بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    
                    ' طباعة تقرير الشيفت
                    If XtraMessageBox.Show("هل تريد طباعة تقرير الشيفت؟", "طباعة التقرير", 
                                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                        PrintShiftReport()
                    End If
                    
                    Me.Close()
                End If
            End If
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في إقفال الشيفت: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function ValidateData() As Boolean
        ' التحقق من مبلغ النهاية
        If end_amount.Value < 0 Then
            XtraMessageBox.Show("مبلغ النهاية لا يمكن أن يكون سالباً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            end_amount.Focus()
            Return False
        End If
        
        ' تحذير في حالة وجود فرق كبير في الخزينة
        If Math.Abs(cash_difference.Value) > 100 Then
            Dim message As String = "يوجد فرق كبير في الخزينة: " & Format(cash_difference.Value, "#,##0.00") & vbCrLf &
                                   "هل تريد المتابعة؟"
            
            Dim result As DialogResult = XtraMessageBox.Show(message, "تحذير", 
                                                            MessageBoxButtons.YesNo, 
                                                            MessageBoxIcon.Warning)
            
            If result = DialogResult.No Then
                end_amount.Focus()
                Return False
            End If
        End If
        
        Return True
    End Function
    
    Private Function CloseShift() As Boolean
        Try
            ' تحديث بيانات الشيفت
            Dim sql As String = "UPDATE shifts SET " &
                               "end_date = @end_date, " &
                               "end_amount = @end_amount, " &
                               "total_sales = @total_sales, " &
                               "total_cash = @total_cash, " &
                               "total_credit = @total_credit, " &
                               "total_profit = @total_profit, " &
                               "total_invoices = @total_invoices, " &
                               "total_items = @total_items, " &
                               "shift_status = N'مغلق', " &
                               "closed_by = @closed_by, " &
                               "closed_date = @closed_date, " &
                               "notes = @notes " &
                               "WHERE shift_id = @shift_id"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                cmd.Parameters.AddWithValue("@end_date", Now)
                cmd.Parameters.AddWithValue("@end_amount", end_amount.Value)
                cmd.Parameters.AddWithValue("@total_sales", total_sales.Value)
                cmd.Parameters.AddWithValue("@total_cash", total_cash.Value)
                cmd.Parameters.AddWithValue("@total_credit", total_credit.Value)
                cmd.Parameters.AddWithValue("@total_profit", total_profit.Value)
                cmd.Parameters.AddWithValue("@total_invoices", total_invoices.Value)
                cmd.Parameters.AddWithValue("@total_items", total_items.Value)
                cmd.Parameters.AddWithValue("@closed_by", cashier_name.Text)
                cmd.Parameters.AddWithValue("@closed_date", Now)
                cmd.Parameters.AddWithValue("@notes", end_notes.Text)
                cmd.Parameters.AddWithValue("@shift_id", current_shift_id)
                
                Dim result As Integer = cmd.ExecuteNonQuery()
                
                If result > 0 Then
                    ' مسح المتغير العام للشيفت النشط
                    If main_page IsNot Nothing Then
                        main_page.current_active_shift_id = 0
                    End If
                    
                    Return True
                Else
                    Return False
                End If
            End Using
            
        Catch ex As Exception
            Throw New Exception("خطأ في إقفال الشيفت: " & ex.Message)
        End Try
    End Function
    
    Private Sub PrintShiftReport()
        Try
            ' إنشاء تقرير الشيفت
            ' يمكن استخدام DevExpress Reports أو Crystal Reports
            ' هنا مثال بسيط لعرض البيانات
            
            Dim reportText As String = "تقرير الشيفت" & vbCrLf &
                                      "=============" & vbCrLf & vbCrLf &
                                      "كود الشيفت: " & shift_code.Text & vbCrLf &
                                      "الكاشير: " & cashier_name.Text & vbCrLf &
                                      "المخزن: " & store_name.Text & vbCrLf &
                                      "تاريخ البداية: " & start_date.Text & vbCrLf &
                                      "تاريخ النهاية: " & end_date.Text & vbCrLf & vbCrLf &
                                      "مبلغ البداية: " & Format(start_amount.Value, "#,##0.00") & vbCrLf &
                                      "مبلغ النهاية: " & Format(end_amount.Value, "#,##0.00") & vbCrLf &
                                      "الفرق: " & Format(cash_difference.Value, "#,##0.00") & vbCrLf & vbCrLf &
                                      "إجمالي المبيعات: " & Format(total_sales.Value, "#,##0.00") & vbCrLf &
                                      "المبيعات النقدية: " & Format(total_cash.Value, "#,##0.00") & vbCrLf &
                                      "المبيعات الآجلة: " & Format(total_credit.Value, "#,##0.00") & vbCrLf &
                                      "إجمالي الأرباح: " & Format(total_profit.Value, "#,##0.00") & vbCrLf &
                                      "عدد الفواتير: " & total_invoices.Value.ToString() & vbCrLf &
                                      "عدد الأصناف: " & total_items.Value.ToString()
            
            ' عرض التقرير في نافذة منفصلة أو طباعة مباشرة
            Dim reportForm As New Form()
            reportForm.Text = "تقرير الشيفت - " & shift_code.Text
            reportForm.Size = New Size(400, 600)
            reportForm.StartPosition = FormStartPosition.CenterScreen
            
            Dim textBox As New TextBox()
            textBox.Multiline = True
            textBox.ScrollBars = ScrollBars.Vertical
            textBox.Dock = DockStyle.Fill
            textBox.Text = reportText
            textBox.Font = New Font("Tahoma", 10)
            textBox.RightToLeft = RightToLeft.Yes
            
            reportForm.Controls.Add(textBox)
            reportForm.ShowDialog()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في طباعة التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub cancel_btn_Click(sender As Object, e As EventArgs) Handles cancel_btn.Click
        Me.Close()
    End Sub
    
    Private Sub refresh_btn_Click(sender As Object, e As EventArgs) Handles refresh_btn.Click
        ' إعادة حساب الإجماليات
        CalculateShiftTotals()
    End Sub
    
End Class
