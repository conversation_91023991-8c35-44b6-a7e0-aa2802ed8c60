﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class descound_customer

    Private Sub new_cash()
        openconnection()
        fillcom()
        descdate.Text = Now.Date
        Amount.Text = 0
        desccode.Text = getlastcode("discound_cashing", "desccode") + 1
        customername.Text = ""
        Accounts_balace.Text = 0
        balace_now.Text = 0
        bill_number.Text = ""
        cash_type.Text = ""
        cuscity.Text = ""
        cusGovernorate.Text = ""

        Cuscusgroup.Text = ""
        customername.Focus()
    End Sub
    Private Sub fillcom()
        customername.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from customer order by cusname", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            customername.Properties.Items.Add(dt.Rows(i).Item("cusname"))
        Next
    End Sub

    Private Sub cashing_customer_Load_1(sender As Object, e As EventArgs) Handles MyBase.Load

        new_cash()

    End Sub

    Sub refresh_cash()
        Try
            customername.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(customername.Text)
            Cuscusgroup.Text = getCuscusgroup(customername.Text)
            cusGovernorate.Text = getcusGovernorate(customername.Text)
            cuscity.Text = getcuscity(customername.Text)
            If customername.Text.Trim() <> Nothing Then
                Label4.Visible = True
                Accounts_balace.Visible = True
            Else
                Label4.Visible = False
                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_TextChanged(Nothing, Nothing)
        Catch ex As Exception

        End Try
    End Sub
    Public Sub show_data(x)
        Dim sql = "select * from discound_cashing where desccode=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("لم يتم العثور علي بيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim dr = dt.Rows(0)
            desccode.Text = dr!desccode
            descdate.Text = dr!descdate
            customername.Text = dr!customername
            Amount.Text = dr!Amount
            bill_number.Text = dr!bill_number
            cash_type.Text = dr!cash_type
            cuscity.Text = dr!cuscity
            cusGovernorate.Text = dr!cusGovernorate
            Cuscusgroup.Text = dr!Cuscusgroup
        End If
    End Sub
    Function getbalace(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (customername.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Accounts_balace") Else Return ""
    End Function
    Function getCuscusgroup(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (customername.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Cuscusgroup") Else Return ""
    End Function
    Function getcusGovernorate(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (customername.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("cusGovernorate") Else Return ""
    End Function
    Function getcuscity(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (customername.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("cuscity") Else Return ""
    End Function

    Private Sub Amount_TextChanged(sender As Object, e As EventArgs) Handles Amount.TextChanged
        balace_now.Text = Format(Val(Accounts_balace.Text) - Val(Amount.Text), "0.0")
        If Amount.Text.Trim() <> Nothing Then
            Label13.Visible = True
            balace_now.Visible = True
        Else
            Label13.Visible = False
            balace_now.Visible = False
        End If
        amount_string.Text = NoToTxt(Amount.Text, "جنية", "قرش") & " " & "فقط لاغير"
    End Sub
   
    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click

        If customername.Text = "" Then
            XtraMessageBox.Show("اسم العميل فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If Amount.Text = 0 Then
            XtraMessageBox.Show("المبلغ = 0", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If Amount.Text = "" Then
            XtraMessageBox.Show("المبلغ فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        desccode.Text = getlastcode("discound_cashing", "desccode") + 1
        printcode.Text = getlastcode("discound_cashing", "desccode") + 1
        Dim sql = "select * from discound_cashing where desccode=N'" & (desccode.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            desccode.Text = getlastcode("discound_cashing", "desccode") + 1
            Exit Sub
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============
            dr!desccode = desccode.Text
            dr!descdate = descdate.Value
            dr!customername = customername.Text
            dr!bill_number = bill_number.Text
            dr!cash_type = cash_type.Text
            dr!Amount = Amount.Text
            dr!Cuscusgroup = Cuscusgroup.Text
            dr!cuscity = cuscity.Text
            dr!cusGovernorate = cusGovernorate.Text
            dr!code_print = 1
            dr!state_Amount = amount_string.Text

            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            adp = New SqlDataAdapter("select * from customer where cusname=N'" & (customername.Text) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr5 = dt.Rows(0)
            dr5!Accounts_balace = Val(dr5!Accounts_balace) - Val(Amount.Text)
            Dim cmd5 As New SqlCommandBuilder(adp)
            adp.Update(dt)
            set_customertrans(customername.Text, "سند خصم", desccode.Text, descdate.Value, bill_number.Text, cash_type.Text, 0, Amount.Text, Val(Accounts_balace.Text) - Val(Amount.Text), Amount.Text)

            new_cash()
            My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
        End If
    End Sub
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub descound_customer_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.Enter Then
            save_print_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub ComboBoxEdit1_SelectedIndexChanged(sender As Object, e As EventArgs)
        Try
            customername.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(customername.Text)
            Cuscusgroup.Text = getCuscusgroup(customername.Text)
            cusGovernorate.Text = getcusGovernorate(customername.Text)
            cuscity.Text = getcuscity(customername.Text)
            If customername.Text.Trim() <> Nothing Then
                Label4.Visible = True
                Accounts_balace.Visible = True
            Else
                Label4.Visible = False
                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_TextChanged(Nothing, Nothing)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub customername_SelectedIndexChanged(sender As Object, e As EventArgs) Handles customername.SelectedIndexChanged
        Try
            customername.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(customername.Text)
            Cuscusgroup.Text = getCuscusgroup(customername.Text)
            cusGovernorate.Text = getcusGovernorate(customername.Text)
            cuscity.Text = getcuscity(customername.Text)
            If customername.Text.Trim() <> Nothing Then
                Label4.Visible = True
                Accounts_balace.Visible = True
            Else
                Label4.Visible = False
                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_TextChanged(Nothing, Nothing)
           
        Catch ex As Exception

        End Try
        Dim str = "select sum(custrans_Debtor), sum(custrans_Creditor) from customer_trans where customer_name=N'" & customername.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("customer_trans")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        Dim sumcredit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
        Accounts_balace.Text = Format(Val(sumdebit), "#,0.00") - Format(Val(sumcredit), "#,0.00")
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        Me.Dispose()
    End Sub

    Private Sub حفظطباعةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظطباعةToolStripMenuItem.Click
        save_print_Click(Nothing, Nothing)
    End Sub

    Private Sub حفظToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظToolStripMenuItem.Click
        save_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub جديدToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles جديدToolStripMenuItem.Click
        new_cash()
    End Sub

    Private Sub save_print_Click(sender As Object, e As EventArgs) Handles save_print.Click

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from desc_cus_print where desccode=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet

        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "desc_cus_s4vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

End Class