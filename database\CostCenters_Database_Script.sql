-- =============================================
-- سكريبت إنشاء نظام مراكز التكلفة
-- Cost Centers Database Script
-- =============================================

-- 1. إنشاء جدول مراكز التكلفة الرئيسي
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[cost_centers] (
        [cost_center_id] [int] IDENTITY(1,1) NOT NULL,
        [cost_center_code] [nvarchar](50) NOT NULL,
        [cost_center_name] [nvarchar](255) NOT NULL,
        [cost_center_description] [nvarchar](500) NULL,
        [parent_cost_center_id] [int] NULL,
        [cost_center_type] [nvarchar](50) NOT NULL DEFAULT 'عام',
        [is_active] [bit] NOT NULL DEFAULT 1,
        [created_date] [datetime] NOT NULL DEFAULT GETDATE(),
        [created_by] [nvarchar](100) NULL,
        [modified_date] [datetime] NULL,
        [modified_by] [nvarchar](100) NULL,
        [budget_amount] [decimal](18,2) NULL DEFAULT 0,
        [actual_amount] [decimal](18,2) NULL DEFAULT 0,
        [notes] [nvarchar](1000) NULL,
        
        CONSTRAINT [PK_cost_centers] PRIMARY KEY CLUSTERED ([cost_center_id] ASC),
        CONSTRAINT [UK_cost_centers_code] UNIQUE ([cost_center_code]),
        CONSTRAINT [FK_cost_centers_parent] FOREIGN KEY ([parent_cost_center_id]) 
            REFERENCES [dbo].[cost_centers] ([cost_center_id])
    )
    
    PRINT 'تم إنشاء جدول مراكز التكلفة بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول مراكز التكلفة موجود بالفعل'
END

-- 2. إضافة فهارس لتحسين الأداء
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_cost_centers_name')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_cost_centers_name] 
    ON [dbo].[cost_centers] ([cost_center_name])
    
    PRINT 'تم إنشاء فهرس اسم مركز التكلفة'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_cost_centers_active')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_cost_centers_active] 
    ON [dbo].[cost_centers] ([is_active])
    
    PRINT 'تم إنشاء فهرس حالة مركز التكلفة'
END

-- 3. إضافة عمود مركز التكلفة للجداول الموجودة
-- =============================================

-- إضافة مركز التكلفة لجدول الفواتير
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('invoice_add') AND name = 'cost_center_id')
BEGIN
    ALTER TABLE [dbo].[invoice_add] 
    ADD [cost_center_id] [int] NULL
    
    -- إضافة مفتاح خارجي
    ALTER TABLE [dbo].[invoice_add]
    ADD CONSTRAINT [FK_invoice_add_cost_center] 
    FOREIGN KEY ([cost_center_id]) REFERENCES [dbo].[cost_centers] ([cost_center_id])
    
    PRINT 'تم إضافة مركز التكلفة لجدول الفواتير'
END

-- إضافة مركز التكلفة لجدول المصروفات
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Expenses_add') AND name = 'cost_center_id')
BEGIN
    ALTER TABLE [dbo].[Expenses_add] 
    ADD [cost_center_id] [int] NULL
    
    -- إضافة مفتاح خارجي
    ALTER TABLE [dbo].[Expenses_add]
    ADD CONSTRAINT [FK_Expenses_add_cost_center] 
    FOREIGN KEY ([cost_center_id]) REFERENCES [dbo].[cost_centers] ([cost_center_id])
    
    PRINT 'تم إضافة مركز التكلفة لجدول المصروفات'
END

-- إضافة مركز التكلفة لجدول المشتريات
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Purchases_add') AND name = 'cost_center_id')
BEGIN
    ALTER TABLE [dbo].[Purchases_add] 
    ADD [cost_center_id] [int] NULL
    
    -- إضافة مفتاح خارجي
    ALTER TABLE [dbo].[Purchases_add]
    ADD CONSTRAINT [FK_Purchases_add_cost_center] 
    FOREIGN KEY ([cost_center_id]) REFERENCES [dbo].[cost_centers] ([cost_center_id])
    
    PRINT 'تم إضافة مركز التكلفة لجدول المشتريات'
END

-- إضافة مركز التكلفة لجدول الإيرادات
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Revenues_add') AND name = 'cost_center_id')
BEGIN
    ALTER TABLE [dbo].[Revenues_add] 
    ADD [cost_center_id] [int] NULL
    
    -- إضافة مفتاح خارجي
    ALTER TABLE [dbo].[Revenues_add]
    ADD CONSTRAINT [FK_Revenues_add_cost_center] 
    FOREIGN KEY ([cost_center_id]) REFERENCES [dbo].[cost_centers] ([cost_center_id])
    
    PRINT 'تم إضافة مركز التكلفة لجدول الإيرادات'
END

-- 4. إدراج بيانات أولية لمراكز التكلفة
-- =============================================
IF NOT EXISTS (SELECT * FROM cost_centers WHERE cost_center_code = 'CC001')
BEGIN
    INSERT INTO [dbo].[cost_centers] 
    ([cost_center_code], [cost_center_name], [cost_center_description], [cost_center_type], [budget_amount])
    VALUES 
    ('CC001', 'الإدارة العامة', 'مركز تكلفة الإدارة العامة والمصروفات الإدارية', 'إداري', 100000),
    ('CC002', 'المبيعات', 'مركز تكلفة قسم المبيعات والتسويق', 'تشغيلي', 150000),
    ('CC003', 'المشتريات', 'مركز تكلفة قسم المشتريات والمخازن', 'تشغيلي', 80000),
    ('CC004', 'الإنتاج', 'مركز تكلفة قسم الإنتاج والتصنيع', 'إنتاجي', 200000),
    ('CC005', 'الصيانة', 'مركز تكلفة قسم الصيانة والخدمات', 'خدمي', 50000),
    ('CC006', 'التسويق', 'مركز تكلفة أنشطة التسويق والإعلان', 'تسويقي', 75000),
    ('CC007', 'الموارد البشرية', 'مركز تكلفة قسم الموارد البشرية', 'إداري', 60000),
    ('CC008', 'تقنية المعلومات', 'مركز تكلفة قسم تقنية المعلومات', 'تقني', 90000)
    
    PRINT 'تم إدراج البيانات الأولية لمراكز التكلفة'
END

-- 5. إنشاء Views مفيدة لمراكز التكلفة
-- =============================================

-- View لعرض مراكز التكلفة مع التكاليف الفعلية
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_cost_centers_summary')
    DROP VIEW [dbo].[vw_cost_centers_summary]

CREATE VIEW [dbo].[vw_cost_centers_summary]
AS
SELECT 
    cc.cost_center_id,
    cc.cost_center_code,
    cc.cost_center_name,
    cc.cost_center_type,
    cc.budget_amount,
    cc.is_active,
    
    -- حساب التكاليف الفعلية من المصروفات
    ISNULL(exp_costs.total_expenses, 0) as actual_expenses,
    
    -- حساب الإيرادات من الفواتير
    ISNULL(inv_revenue.total_revenue, 0) as actual_revenue,
    
    -- حساب صافي الربح/الخسارة
    ISNULL(inv_revenue.total_revenue, 0) - ISNULL(exp_costs.total_expenses, 0) as net_profit_loss,
    
    -- نسبة استخدام الميزانية
    CASE 
        WHEN cc.budget_amount > 0 THEN 
            (ISNULL(exp_costs.total_expenses, 0) / cc.budget_amount) * 100
        ELSE 0 
    END as budget_utilization_percentage

FROM cost_centers cc

LEFT JOIN (
    SELECT 
        cost_center_id,
        SUM(total_Expenses) as total_expenses
    FROM Expenses_add 
    WHERE cost_center_id IS NOT NULL
    GROUP BY cost_center_id
) exp_costs ON cc.cost_center_id = exp_costs.cost_center_id

LEFT JOIN (
    SELECT 
        cost_center_id,
        SUM(total_invoice) as total_revenue
    FROM invoice_add 
    WHERE cost_center_id IS NOT NULL
    GROUP BY cost_center_id
) inv_revenue ON cc.cost_center_id = inv_revenue.cost_center_id

PRINT 'تم إنشاء View ملخص مراكز التكلفة'

-- 6. إنشاء Stored Procedures مفيدة
-- =============================================

-- Procedure لحساب تكاليف مركز معين في فترة محددة
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetCostCenterCosts')
    DROP PROCEDURE [dbo].[sp_GetCostCenterCosts]

CREATE PROCEDURE [dbo].[sp_GetCostCenterCosts]
    @CostCenterId INT,
    @StartDate DATE,
    @EndDate DATE
AS
BEGIN
    SELECT 
        cc.cost_center_name,
        
        -- المصروفات
        ISNULL(SUM(e.total_Expenses), 0) as total_expenses,
        
        -- الإيرادات
        ISNULL(SUM(i.total_invoice), 0) as total_revenue,
        
        -- صافي الربح/الخسارة
        ISNULL(SUM(i.total_invoice), 0) - ISNULL(SUM(e.total_Expenses), 0) as net_result
        
    FROM cost_centers cc
    LEFT JOIN Expenses_add e ON cc.cost_center_id = e.cost_center_id 
        AND e.Expenses_date BETWEEN @StartDate AND @EndDate
    LEFT JOIN invoice_add i ON cc.cost_center_id = i.cost_center_id 
        AND i.invoice_date BETWEEN @StartDate AND @EndDate
    WHERE cc.cost_center_id = @CostCenterId
    GROUP BY cc.cost_center_name
END

PRINT 'تم إنشاء Stored Procedure حساب تكاليف مركز التكلفة'

PRINT '============================================='
PRINT 'تم إكمال سكريبت مراكز التكلفة بنجاح!'
PRINT 'يمكنك الآن استخدام نظام مراكز التكلفة'
PRINT '============================================='
