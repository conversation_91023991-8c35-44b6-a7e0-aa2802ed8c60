-- حذف View إذا كان موجودًا
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_cost_centers_summary')
    DROP VIEW [dbo].[vw_cost_centers_summary]
GO

-- إنشاء View جديد
CREATE VIEW [dbo].[vw_cost_centers_summary]
AS
SELECT 
    cc.cost_center_id,
    cc.cost_center_code,
    cc.cost_center_name,
    cc.cost_center_type,
    cc.budget_amount,
    cc.is_active,
    
    ISNULL(exp_costs.total_expenses, 0) AS actual_expenses,
    ISNULL(inv_revenue.total_revenue, 0) AS actual_revenue,
    ISNULL(inv_revenue.total_revenue, 0) - ISNULL(exp_costs.total_expenses, 0) AS net_profit_loss,
    
    CASE 
        WHEN cc.budget_amount > 0 THEN 
            (ISNULL(exp_costs.total_expenses, 0) / cc.budget_amount) * 100
        ELSE 0 
    END AS budget_utilization_percentage

FROM cost_centers cc

LEFT JOIN (
    SELECT 
        cost_center_id,
        SUM(total_Expenses) AS total_expenses
    FROM Expenses_add 
    WHERE cost_center_id IS NOT NULL
    GROUP BY cost_center_id
) exp_costs ON cc.cost_center_id = exp_costs.cost_center_id

LEFT JOIN (
    SELECT 
        cost_center_id,
        SUM(total_invoice) AS total_revenue
    FROM invoice_add 
    WHERE cost_center_id IS NOT NULL
    GROUP BY cost_center_id
) inv_revenue ON cc.cost_center_id = inv_revenue.cost_center_id
GO

PRINT 'تم إنشاء العرض (View) الخاص بملخص مراكز التكلفة بنجاح.'
GO

-- حذف Procedure إذا كان موجودًا
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetCostCenterCosts')
    DROP PROCEDURE [dbo].[sp_GetCostCenterCosts]
GO

-- إنشاء Procedure جديد
CREATE PROCEDURE [dbo].[sp_GetCostCenterCosts]
    @CostCenterId INT,
    @StartDate DATE,
    @EndDate DATE
AS
BEGIN
    SELECT 
        cc.cost_center_name,
        ISNULL(SUM(e.total_Expenses), 0) AS total_expenses,
        ISNULL(SUM(i.total_invoice), 0) AS total_revenue,
        ISNULL(SUM(i.total_invoice), 0) - ISNULL(SUM(e.total_Expenses), 0) AS net_result
    FROM cost_centers cc
    LEFT JOIN Expenses_add e ON cc.cost_center_id = e.cost_center_id 
        AND e.Expenses_date BETWEEN @StartDate AND @EndDate
    LEFT JOIN invoice_add i ON cc.cost_center_id = i.cost_center_id 
        AND i.invoice_date BETWEEN @StartDate AND @EndDate
    WHERE cc.cost_center_id = @CostCenterId
    GROUP BY cc.cost_center_name
END
GO

PRINT 'تم إنشاء الإجراء المخزن لحساب تكاليف مركز التكلفة بنجاح.'
PRINT '==============================================='
PRINT 'تم إكمال سكريبت مراكز التكلفة بنجاح!'
PRINT 'يمكنك الآن استخدام نظام مراكز التكلفة بكفاءة.'
PRINT '==============================================='
GO
