-- سكريب<PERSON> إصلاح جدول الشيفتات الموجود
-- لحل مشكلة cashier_id NULL

USE [مبيعات 2022]
GO

-- التحقق من وجود الجدول وإصلاحه
IF EXISTS (SELECT * FROM sysobjects WHERE name='shifts' AND xtype='U')
BEGIN
    PRINT 'جدول shifts موجود - سيتم إصلاحه'
    
    -- التحقق من عمود cashier_id
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'cashier_id')
    BEGIN
        -- تحديث القيم NULL إلى 0
        UPDATE shifts SET cashier_id = 0 WHERE cashier_id IS NULL
        PRINT 'تم تحديث القيم NULL في cashier_id إلى 0'
        
        -- تعديل العمود ليكون له قيمة افتراضية
        ALTER TABLE shifts ADD CONSTRAINT DF_shifts_cashier_id DEFAULT (0) FOR cashier_id
        PRINT 'تم إضافة قيمة افتراضية لعمود cashier_id'
    END
    ELSE
    BEGIN
        -- إضافة العمود إذا لم يكن موجوداً
        ALTER TABLE shifts ADD cashier_id int NOT NULL DEFAULT (0)
        PRINT 'تم إضافة عمود cashier_id مع قيمة افتراضية'
    END
    
    -- التأكد من وجود باقي الأعمدة المطلوبة
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'store_name')
    BEGIN
        ALTER TABLE shifts ADD store_name nvarchar(100) NULL
        PRINT 'تم إضافة عمود store_name'
    END

    -- تحديث القيم NULL في store_name
    UPDATE shifts SET store_name = N'المتجر الرئيسي' WHERE store_name IS NULL OR store_name = ''
    PRINT 'تم تحديث القيم NULL في store_name'
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'total_cash')
    BEGIN
        ALTER TABLE shifts ADD total_cash decimal(18,2) NULL DEFAULT (0)
        PRINT 'تم إضافة عمود total_cash'
    END
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'total_credit')
    BEGIN
        ALTER TABLE shifts ADD total_credit decimal(18,2) NULL DEFAULT (0)
        PRINT 'تم إضافة عمود total_credit'
    END
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'total_invoices')
    BEGIN
        ALTER TABLE shifts ADD total_invoices int NULL DEFAULT (0)
        PRINT 'تم إضافة عمود total_invoices'
    END
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'cash_difference')
    BEGIN
        ALTER TABLE shifts ADD cash_difference decimal(18,2) NULL DEFAULT (0)
        PRINT 'تم إضافة عمود cash_difference'
    END
    
    PRINT 'تم إصلاح جدول shifts بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول shifts غير موجود - يجب تشغيل shifts_simple.sql أولاً'
END
GO

PRINT '============================================'
PRINT 'تم إصلاح جدول الشيفتات'
PRINT 'يمكن الآن استخدام النظام بدون مشاكل'
PRINT '============================================'
