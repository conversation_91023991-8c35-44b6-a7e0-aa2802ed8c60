﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.3.0" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="cus_trnas_print_a4" Margins="0, 0, 0, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="20.1" DataMember="cus_trans_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0.624974549" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="106.6667">
      <Controls>
        <Item1 Ref="6" ControlType="XRLabel" Name="lblco" Text="ااا" TextAlignment="MiddleRight" SizeF="382.999969,36.1433029" LocationFloat="407.918274,12.2617073" Font="Dexef, 20pt, style=Bold" ForeColor="255,192,64,0" BackColor="Transparent" Padding="2,2,0,0,100" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[sec_name]" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="lblfield" Text="ااا" TextAlignment="MiddleRight" SizeF="363.666626,23" LocationFloat="343.4096,48.40501" Font="Dexef, 12pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[sec_s1]" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="12" ControlType="XRPictureBox" Name="picture" Sizing="StretchImage" SizeF="140.000031,70" LocationFloat="36.0816956,12.2617073">
          <ExpressionBindings>
            <Item1 Ref="13" EventName="BeforePrint" PropertyName="ImageSource" Expression="[sec_pic]" />
          </ExpressionBindings>
        </Item3>
        <Item4 Ref="14" ControlType="XRLabel" Name="label10" Text="ااا" TextAlignment="MiddleRight" SizeF="363.666626,23" LocationFloat="343.4096,71.405" Font="Dexef, 12pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[sec_phone]" />
          </ExpressionBindings>
          <StylePriority Ref="16" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="17" ControlType="PageHeaderBand" Name="PageHeader" HeightF="258.3333">
      <Controls>
        <Item1 Ref="18" ControlType="XRPageInfo" Name="XrPageInfo1" RightToLeft="Yes" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="253.483063,23" LocationFloat="413.885681,153.1041" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom">
          <StylePriority Ref="19" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="20" ControlType="XRLabel" Name="LblOrangeDt1" Text="في الفترة من" TextAlignment="MiddleCenter" SizeF="92.708374,28.4112167" LocationFloat="489.6544,49.0522156" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100">
          <StylePriority Ref="21" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="22" ControlType="XRLabel" Name="DT1" TextFormatString="{0:yyyy-MM-dd}" Text="Dateto" TextAlignment="MiddleCenter" SizeF="119.212463,28.4112167" LocationFloat="210.604263,49.0523758" Font="Droid Arabic Kufi, 8.25pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="255,192,97,109" Borders="Left, Right, Bottom">
          <ExpressionBindings>
            <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[Dateto]" />
          </ExpressionBindings>
          <StylePriority Ref="24" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="25" ControlType="XRLabel" Name="LblOrangeDt2" Text="الى" TextAlignment="MiddleCenter" SizeF="40.62506,28.4112167" LocationFloat="329.816864,49.0523758" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100">
          <StylePriority Ref="26" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="27" ControlType="XRLabel" Name="Dt2" TextFormatString="{0:yyyy-MM-dd}" TextAlignment="MiddleCenter" SizeF="119.212524,28.4112167" LocationFloat="370.441864,49.0523758" Font="Droid Arabic Kufi, 8.25pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="255,192,97,109" Borders="Left, Right, Bottom">
          <ExpressionBindings>
            <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Datefrom]" />
          </ExpressionBindings>
          <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="30" ControlType="XRLabel" Name="lbldealing" Text="كشف حساب" TextAlignment="TopCenter" SizeF="727,48.5315475" LocationFloat="44.7084274,0.520813" Font="Dexef, 17pt, style=Bold" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="255,192,97,109" Borders="Bottom">
          <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="32" ControlType="XRLabel" Name="lblname" Text="lblname" TextAlignment="MiddleRight" SizeF="253.4831,25.0832977" LocationFloat="413.8858,128.020813" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[customername]" />
          </ExpressionBindings>
          <StylePriority Ref="34" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="35" ControlType="XRLabel" Name="label2" TextTrimming="None" Text="الفرع" TextAlignment="MiddleCenter" SizeF="105.905334,25.0833435" LocationFloat="671.136169,178.187454" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="36" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="37" ControlType="XRLabel" Name="label4" Text="label4" TextAlignment="MiddleRight" SizeF="253.4831,25.0832977" LocationFloat="413.885681,178.187515" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="38" EventName="BeforePrint" PropertyName="Text" Expression="[store]" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="40" ControlType="XRLabel" Name="label8" Text="المستخدم" TextAlignment="MiddleCenter" SizeF="105.905365,25.0833435" LocationFloat="294.168518,178.187454" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="41" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="42" ControlType="XRLabel" Name="label7" Text="الرصيد الحالي" TextAlignment="MiddleCenter" SizeF="105.905365,25.083313" LocationFloat="294.168762,128.020813" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="43" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="44" ControlType="XRLabel" Name="label12" Text="label12" TextAlignment="MiddleRight" SizeF="223.2748,25.0833282" LocationFloat="70.89397,128.020813" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[Accounts_balace]" />
          </ExpressionBindings>
          <StylePriority Ref="46" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="47" ControlType="XRLabel" Name="label1" TextTrimming="None" Text="الاسم" TextAlignment="MiddleCenter" SizeF="105.905334,25.0833282" LocationFloat="671.1363,128.020813" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="49" ControlType="XRTable" Name="table2" RightToLeft="Yes" TextAlignment="MiddleCenter" SizeF="795.000061,35" LocationFloat="15.9999695,222.812485" Font="Droid Arabic Kufi, 8.25pt, charSet=0" ForeColor="White" BackColor="255,0,120,215" BorderColor="White" Borders="All">
          <Rows>
            <Item1 Ref="50" ControlType="XRTableRow" Name="tableRow2" Weight="11.5">
              <Cells>
                <Item1 Ref="51" ControlType="XRTableCell" Name="tableCell5" Weight="0.77471649499827033" Text="الرصيد" />
                <Item2 Ref="52" ControlType="XRTableCell" Name="tableCell6" Weight="0.76665665505677294" Text="دائن" />
                <Item3 Ref="53" ControlType="XRTableCell" Name="tableCell7" Weight="0.798447465420561" Text="مدين" />
                <Item4 Ref="54" ControlType="XRTableCell" Name="tableCell8" Weight="2.0120044958465813" Text="البيان" />
                <Item5 Ref="55" ControlType="XRTableCell" Name="tableCell9" Weight="0.812596065511535" Text="التاريخ" />
                <Item6 Ref="56" ControlType="XRTableCell" Name="tableCell10" Weight="0.87596442863256829" Text="التعامل" />
                <Item7 Ref="57" ControlType="XRTableCell" Name="tableCell11" Weight="0.25645239505197526" Text="م" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="58" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="59" ControlType="XRLabel" Name="label9" TextTrimming="None" Text="التاريخ" TextAlignment="MiddleCenter" SizeF="105.905334,25.0833282" LocationFloat="671.136169,153.1041" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="60" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="61" ControlType="XRLabel" Name="label6" Text="label12" TextAlignment="MiddleRight" SizeF="223.2748,25.0833282" LocationFloat="70.89397,153.1041" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="62" EventName="BeforePrint" PropertyName="Text" Expression="[past_balace]" />
          </ExpressionBindings>
          <StylePriority Ref="63" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="64" ControlType="XRLabel" Name="label5" Text="الرصيد السابق" TextAlignment="MiddleCenter" SizeF="105.905365,25.083313" LocationFloat="294.168762,153.1041" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="65" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="66" ControlType="XRLabel" Name="label3" Text="lblname" TextAlignment="MiddleRight" SizeF="223.274551,25.0832977" LocationFloat="70.89397,178.187515" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="67" EventName="BeforePrint" PropertyName="Text" Expression="[user_name]" />
          </ExpressionBindings>
          <StylePriority Ref="68" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item18>
      </Controls>
    </Item5>
    <Item6 Ref="69" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="25.08331">
      <Controls>
        <Item1 Ref="70" ControlType="XRTable" Name="XrTable5" TextAlignment="MiddleCenter" SizeF="198.100525,25" LocationFloat="113.810921,0" Font="Droid Arabic Kufi, 8.25pt" Borders="Top">
          <Rows>
            <Item1 Ref="71" ControlType="XRTableRow" Name="XrTableRow5" Weight="11.5">
              <Cells>
                <Item1 Ref="72" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.2287708339572625" Font="Droid Arabic Kufi, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="73" EventName="BeforePrint" PropertyName="Text" Expression="Sum([custrans_Creditor])" />
                  </ExpressionBindings>
                  <StylePriority Ref="74" UseFont="false" />
                </Item1>
                <Item2 Ref="75" ControlType="XRTableCell" Name="XrTableCell19" Weight="0.23943985793977743" Font="Droid Arabic Kufi, 8.25pt">
                  <Summary Ref="76" Running="Report" />
                  <ExpressionBindings>
                    <Item1 Ref="77" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([custrans_Debtor])" />
                  </ExpressionBindings>
                  <StylePriority Ref="78" UseFont="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="79" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="80" ControlType="XRPageInfo" Name="pageInfo1" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="667.8232,1.99998224" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100">
          <StylePriority Ref="81" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="82" ControlType="XRLabel" Name="label11" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="716.7816,0" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100">
          <StylePriority Ref="83" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item6>
    <Item7 Ref="84" ControlType="PageFooterBand" Name="PageFooter" HeightF="0" />
    <Item8 Ref="85" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="86" ControlType="DetailBand" Name="Detail1" HeightF="34.99997">
          <Controls>
            <Item1 Ref="87" ControlType="XRTable" Name="table1" RightToLeft="Yes" SizeF="795.5,34.99997" LocationFloat="15.5000305,0" Font="Droid Arabic Kufi, 8.25pt">
              <Rows>
                <Item1 Ref="88" ControlType="XRTableRow" Name="tableRow1" Weight="11.5">
                  <Cells>
                    <Item1 Ref="89" ControlType="XRTableCell" Name="tableCell1" Weight="0.61231204583009924" TextFormatString="{0:0.##;[0.##];0}" Text="1" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt" ForeColor="Black" BackColor="0,0,0,0">
                      <Summary Ref="90" Func="RecordNumber" />
                      <ExpressionBindings>
                        <Item1 Ref="91" EventName="BeforePrint" PropertyName="Text" Expression="[customer_balace]" />
                      </ExpressionBindings>
                      <StylePriority Ref="92" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="93" ControlType="XRTableCell" Name="tableCell2" Weight="0.59980872584337108" TextFormatString="{0:#.00}" Text="XrTableCell1" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="94" EventName="BeforePrint" PropertyName="Text" Expression="[custrans_Creditor]" />
                      </ExpressionBindings>
                      <StylePriority Ref="95" UseFont="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="96" ControlType="XRTableCell" Name="tableCell3" Weight="0.62468246560757" TextFormatString="{0:#.00}" Text="XrTableCell2" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="97" EventName="BeforePrint" PropertyName="Text" Expression="[custrans_Debtor]" />
                      </ExpressionBindings>
                      <StylePriority Ref="98" UseFont="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="99" ControlType="XRTableCell" Name="tableCell4" Weight="1.5741311523126949" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="100" EventName="BeforePrint" PropertyName="ForeColor" Expression="Iif([Deleted] == True, '128, 0, 0', [Updated] == True, '0, 128, 0', ?)" />
                        <Item2 Ref="101" EventName="BeforePrint" PropertyName="Font.Size" Expression="Iif([Deleted] == True, '9', [Updated] == True, '9', ?)" />
                        <Item3 Ref="102" EventName="BeforePrint" PropertyName="Font.Italic" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" />
                        <Item4 Ref="103" EventName="BeforePrint" PropertyName="Font.Strikeout" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" />
                        <Item5 Ref="104" EventName="BeforePrint" PropertyName="Font.Underline" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" />
                        <Item6 Ref="105" EventName="BeforePrint" PropertyName="Text" Expression="[custrans_descrebtion]" />
                      </ExpressionBindings>
                      <StylePriority Ref="106" UseFont="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="107" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.63575180262322628" TextFormatString="{0:yyyy-MM-dd}" Text="XrTableCell4" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="108" EventName="BeforePrint" PropertyName="Text" Expression="[custrans_date]" />
                      </ExpressionBindings>
                      <StylePriority Ref="109" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="110" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.68223050612893421" Text="XrTableCell5" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="111" EventName="BeforePrint" PropertyName="Text" Expression="[custrans_type]" />
                      </ExpressionBindings>
                      <StylePriority Ref="112" UseFont="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="113" ControlType="XRTableCell" Name="LblTotal" Weight="0.20064071644355336" Text="0" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="255,0,120,215">
                      <Summary Ref="114" Running="Report" />
                      <ExpressionBindings>
                        <Item1 Ref="115" EventName="BeforePrint" PropertyName="Text" Expression="sumRecordNumber([custrans_descrebtion])" />
                      </ExpressionBindings>
                      <StylePriority Ref="116" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <ExpressionBindings>
                <Item1 Ref="117" EventName="BeforePrint" PropertyName="ForeColor" Expression="Iif([Deleted] == True, '128, 0, 0', [Updated] == True, '0, 128, 0', ?)" />
                <Item2 Ref="118" EventName="BeforePrint" PropertyName="Font.Name" Expression="Iif([Deleted] == True, 'Dexef', [Updated] == True, 'Dexef', ?)" />
                <Item3 Ref="119" EventName="BeforePrint" PropertyName="Font.Size" Expression="Iif([Deleted] == True, '9', [Updated] == True, '9', ?)" />
                <Item4 Ref="120" EventName="BeforePrint" PropertyName="Font.Italic" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" />
                <Item5 Ref="121" EventName="BeforePrint" PropertyName="Font.Strikeout" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" />
                <Item6 Ref="122" EventName="BeforePrint" PropertyName="Font.Underline" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" />
              </ExpressionBindings>
              <StylePriority Ref="123" UseFont="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v20.1" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>