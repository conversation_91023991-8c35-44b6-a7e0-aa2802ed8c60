﻿Imports System.Data.OleDb
Imports System.Management
Imports DevExpress.XtraEditors
Imports System.Net
Imports System.Data.SqlClient
Imports Security.FingerPrint
Imports MicroVisionSerial.Encryption.Activation
Imports System.Net.NetworkInformation

Public Class ready
    Dim web As New WebClient
    Dim a = Microsoft.Win32.Registry.GetValue("HKEY_CURRENT_USER\ITFY-Edu", "t", "10000")
    Dim a_ = Microsoft.Win32.Registry.GetValue("HKEY_CURRENT_USER\ITFY-Edu", "A", "OFF")
    Private sqlconn2 As New SqlConnection

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        Try
            login.Show()
        Catch ex As Exception
        End Try
        XtraForm1.BarStaticItem5.Caption = "الايام المتبقية :" + "مدي الحياة"

        Me.Dispose()
        Timer1.Stop()
    End Sub


    Sub check()

        Try
            Win32_Processor0.Text = CpuId()
        Catch ex As Exception

        End Try
        '===========================================
        Try
            '  value_num0.Text = Value()
        Catch ex As Exception

        End Try

    End Sub

    Private Function CpuId() As String
        Dim computer As String = "."
        Dim wmi As Object = GetObject("winmgmts:" & _
            "{impersonationLevel=impersonate}!\\" & _
            computer & "\root\cimv2")
        Dim processors As Object = wmi.ExecQuery("Select * from Win32_Processor")

        Dim cpu_ids As String = ""
        For Each cpu As Object In processors
            cpu_ids = cpu_ids & ", " & cpu.ProcessorId
        Next cpu
        If cpu_ids.Length > 0 Then cpu_ids = cpu_ids.Substring(2)

        Return cpu_ids
    End Function
    Private Sub start_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim Chr_ As String() = Split("115 112 97 120 101 116 50 48 50 48 ", " ")
        For ii As Integer = 0 To Chr_.Length - 2
            If ii = 0 Then spaxet_pass = Nothing
            spaxet_pass += conv1256(ChrW(Chr_(ii)))
        Next
        'ConStr = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=|DataDirectory|\setting.mdb;Jet OLEDB:Database Password='" & (spaxet_pass) & "'"
        'accses_Con = New OleDbConnection(ConStr)
        checkkk()
        check()

        If Win32_Processor.Text <> Win32_Processor0.Text Then
            start_trial.ShowDialog()
            Me.Dispose()
            Exit Sub
        End If
        openconnection()
        Try
            If My.Computer.Network.Ping("www.google.com") Then
                If web.DownloadString("https://dl.dropbox.com/s/02qwtqlyemu0r8c/spaxet.txt?dl=0") <> 10.7 Then
                    If XtraMessageBox.Show("بوحد تحديث للبرنامج هل تريج تثبيته الان ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
                        Process.Start("spaxet Update.exe")
                        End
                    End If
                End If
                Timer1.Start()
            Else
                Timer1.Start()
            End If
        Catch ex As Exception
            Timer1.Start()
        End Try
    End Sub
    'Private Sub ssa()
    '    '===========
    '    Try
    '        If sqlconn2.State = 1 Then sqlconn2.Close()
    '        Try
    '            sqlconn2.ConnectionString = web.DownloadString("https://dl.dropbox.com/s/r5yhhzes9vi1qzk/connection.txt?dl=0")
    '            sqlconn2.Open()
    '        Catch ex As Exception
    '            sqlconn2.Close()
    '            Exit Sub
    '        End Try
    '    Catch ex As Exception
    '    End Try
    '    Dim sql = "select (id),(active_num),(last_seen),(active) from user_spaxet where active_num=N'" & Value() & "'"
    '    Dim adp As New SqlDataAdapter(sql, sqlconn2)
    '    Dim ds As New DataSet
    '    adp.Fill(ds)
    '    Dim dt = ds.Tables(0)
    '    If dt.Rows.Count > 0 Then
    '        Dim dr = dt.Rows(0)
    '        '========= بيانات اساسية============
    '        dr!last_seen = Now
    '        '============================
    '        Dim cmd2 As New SqlCommandBuilder(adp)
    '        adp.Update(dt)
    '        If dr!active = False Then
    '            If accses_Con.State = ConnectionState.Closed Then
    '                accses_Con.Open()
    '            End If
    '            Dim cmd As New OleDb.OleDbCommand
    '            cmd.Connection = accses_Con
    '            cmd.CommandType = CommandType.Text
    '            cmd.CommandText = "UPDATE seial_check SET MACAddress= @MACAddress,Win32_Processor= @Win32_Processor,SerialNumber= @SerialNumber,value_num= @value_num WHERE idsetting = " & 1 & " "
    '            cmd.Parameters.AddWithValue("MACAddress", "")
    '            cmd.Parameters.AddWithValue("Win32_Processor", "")
    '            cmd.Parameters.AddWithValue("SerialNumber", "")
    '            cmd.Parameters.AddWithValue("value_num", "")
    '            cmd.ExecuteScalar()
    '            accses_Con.Close()
    '            sqlconn2.Close()
    '            XtraMessageBox.Show("لقد تم حظر هذا الحساب من الاستخدام مرة أخري", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '            End
    '        End If
    '    End If
    '    '=========
    'End Sub

    Sub checkkk()
        'Dim sql = "select (Win32_Processor),(SerialNumber),(MACAddress),(value_num) from seial_check WHERE idsetting = " & 1 & " "
        'Dim adp As New OleDbDataAdapter(sql, accses_Con)
        'Dim ds As New DataSet
        'adp.Fill(ds)
        'Dim dt = ds.Tables(0)
        'If dt.Rows.Count > 0 Then
        '    Dim dr = dt.Rows(0)
        Win32_Processor.Text = My.Settings.Win32_Processor
            SerialNumber.Text = My.Settings.SerialNumber
            MACAddress.Text = My.Settings.MACAddress
            value_num.Text = My.Settings.value_num
        'End If
    End Sub

    Private Sub PictureBox1_Click(sender As Object, e As EventArgs) Handles PictureBox1.Click

    End Sub
End Class