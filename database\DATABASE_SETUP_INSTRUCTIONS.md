# تعليمات إعداد قاعدة البيانات لمراكز التكلفة

## نظرة عامة
هذا الملف يحتوي على التعليمات اللازمة لإضافة نظام مراكز التكلفة إلى قاعدة البيانات الموجودة.

## ⚠️ تحذير مهم
**قم بعمل نسخة احتياطية من قاعدة البيانات قبل تشغيل السكريبت!**

## 📋 متطلبات التشغيل
- SQL Server 2008 أو أحدث
- صلاحيات إدارية على قاعدة البيانات
- اتصال بقاعدة البيانات الخاصة بالنظام

## 🚀 خطوات التشغيل

### الخطوة 1: النسخ الاحتياطي
```sql
-- قم بتشغيل هذا الأمر لعمل نسخة احتياطية
BACKUP DATABASE [اسم_قاعدة_البيانات] 
TO DISK = 'C:\Backup\database_backup_before_cost_centers.bak'
```

### الخطوة 2: تشغيل السكريبت
1. افتح SQL Server Management Studio
2. اتصل بقاعدة البيانات
3. افتح ملف `CostCenters_Database_Script.sql`
4. تأكد من اختيار قاعدة البيانات الصحيحة
5. اضغط F5 أو Execute لتشغيل السكريبت

### الخطوة 3: التحقق من النتائج
```sql
-- تحقق من إنشاء الجدول
SELECT * FROM cost_centers

-- تحقق من البيانات الأولية
SELECT cost_center_code, cost_center_name FROM cost_centers

-- تحقق من إضافة الأعمدة للجداول الموجودة
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'cost_center_id'
```

## 📊 ما سيتم إنشاؤه

### 1. جدول مراكز التكلفة الرئيسي
- `cost_centers`: الجدول الرئيسي لمراكز التكلفة
- يحتوي على: الكود، الاسم، الوصف، النوع، الميزانية، إلخ

### 2. تحديث الجداول الموجودة
سيتم إضافة عمود `cost_center_id` للجداول التالية:
- `invoice_add` (فواتير المبيعات)
- `Expenses_add` (المصروفات)
- `Purchases_add` (المشتريات)
- `Revenues_add` (الإيرادات)

### 3. الفهارس
- فهرس على اسم مركز التكلفة
- فهرس على حالة النشاط

### 4. Views
- `vw_cost_centers_summary`: ملخص شامل لمراكز التكلفة مع التكاليف

### 5. Stored Procedures
- `sp_GetCostCenterCosts`: حساب تكاليف مركز معين في فترة محددة

### 6. البيانات الأولية
سيتم إدراج 8 مراكز تكلفة أولية:
- الإدارة العامة
- المبيعات
- المشتريات
- الإنتاج
- الصيانة
- التسويق
- الموارد البشرية
- تقنية المعلومات

## 🔍 التحقق من نجاح التشغيل

### اختبار الجدول الرئيسي
```sql
-- عرض جميع مراكز التكلفة
SELECT * FROM cost_centers ORDER BY cost_center_code

-- عرض ملخص مراكز التكلفة
SELECT * FROM vw_cost_centers_summary
```

### اختبار الربط مع الجداول الأخرى
```sql
-- تحقق من إمكانية ربط الفواتير بمراكز التكلفة
SELECT i.invoice_number, cc.cost_center_name 
FROM invoice_add i
LEFT JOIN cost_centers cc ON i.cost_center_id = cc.cost_center_id
```

### اختبار Stored Procedure
```sql
-- اختبار حساب تكاليف مركز معين
EXEC sp_GetCostCenterCosts 
    @CostCenterId = 1, 
    @StartDate = '2024-01-01', 
    @EndDate = '2024-12-31'
```

## ❌ في حالة حدوث أخطاء

### خطأ: الجدول موجود بالفعل
```
الرسالة: "There is already an object named 'cost_centers' in the database"
الحل: الجدول موجود بالفعل، لا حاجة لإعادة التشغيل
```

### خطأ: العمود موجود بالفعل
```
الرسالة: "Column names in each table must be unique"
الحل: العمود موجود بالفعل، لا حاجة لإعادة التشغيل
```

### خطأ: صلاحيات غير كافية
```
الرسالة: "Permission denied"
الحل: تأكد من وجود صلاحيات إدارية على قاعدة البيانات
```

## 🔄 التراجع عن التغييرات (إذا لزم الأمر)

### حذف الجدول والبيانات
```sql
-- حذف المفاتيح الخارجية أولاً
ALTER TABLE invoice_add DROP CONSTRAINT FK_invoice_add_cost_center
ALTER TABLE Expenses_add DROP CONSTRAINT FK_Expenses_add_cost_center
ALTER TABLE Purchases_add DROP CONSTRAINT FK_Purchases_add_cost_center
ALTER TABLE Revenues_add DROP CONSTRAINT FK_Revenues_add_cost_center

-- حذف الأعمدة المضافة
ALTER TABLE invoice_add DROP COLUMN cost_center_id
ALTER TABLE Expenses_add DROP COLUMN cost_center_id
ALTER TABLE Purchases_add DROP COLUMN cost_center_id
ALTER TABLE Revenues_add DROP COLUMN cost_center_id

-- حذف الجدول الرئيسي
DROP TABLE cost_centers

-- حذف Views و Procedures
DROP VIEW vw_cost_centers_summary
DROP PROCEDURE sp_GetCostCenterCosts
```

## 📞 الدعم
في حالة مواجهة أي مشاكل:
1. تأكد من عمل نسخة احتياطية
2. راجع رسائل الخطأ بعناية
3. تحقق من الصلاحيات
4. استخدم سكريبت التراجع إذا لزم الأمر

---

**ملاحظة**: هذا السكريبت آمن ولن يؤثر على البيانات الموجودة، فقط سيضيف جداول وأعمدة جديدة.
