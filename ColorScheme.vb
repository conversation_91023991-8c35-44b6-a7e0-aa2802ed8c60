''' <summary>
''' نظام الألوان الموحد للتطبيق
''' يحتوي على جميع الألوان المستخدمة في واجهة المستخدم
''' </summary>
Public Class ColorScheme
    
    ' الألوان الأساسية
    Public Shared ReadOnly PrimaryBlue As Color = Color.FromArgb(41, 128, 185)      ' #2980B9
    Public Shared ReadOnly PrimaryBlueHover As Color = Color.FromArgb(52, 152, 219) ' #3498DB
    
    Public Shared ReadOnly SuccessGreen As Color = Color.FromArgb(46, 204, 113)     ' #2ECC71
    Public Shared ReadOnly SuccessGreenHover As Color = Color.FromArgb(56, 224, 133) ' #38E085
    
    Public Shared ReadOnly DangerRed As Color = Color.FromArgb(231, 76, 60)         ' #E74C3C
    Public Shared ReadOnly DangerRedHover As Color = Color.FromArgb(251, 96, 80)    ' #FB6050
    
    Public Shared ReadOnly WarningOrange As Color = Color.FromArgb(230, 126, 34)    ' #E67E22
    Public Shared ReadOnly WarningOrangeHover As Color = Color.FromArgb(250, 146, 54) ' #FA9236
    
    Public Shared ReadOnly InfoPurple As Color = Color.FromArgb(155, 89, 182)       ' #9B59B6
    Public Shared ReadOnly InfoPurpleHover As Color = Color.FromArgb(175, 109, 202) ' #AF6DCA
    
    Public Shared ReadOnly WarningYellow As Color = Color.FromArgb(255, 193, 7)     ' #FFC107
    Public Shared ReadOnly WarningYellowHover As Color = Color.FromArgb(255, 213, 27) ' #FFD51B
    
    Public Shared ReadOnly SecondaryGray As Color = Color.FromArgb(52, 73, 94)      ' #34495E
    Public Shared ReadOnly SecondaryGrayHover As Color = Color.FromArgb(72, 93, 114) ' #485D72
    
    Public Shared ReadOnly TealGreen As Color = Color.FromArgb(26, 188, 156)        ' #1ABC9C
    Public Shared ReadOnly TealGreenHover As Color = Color.FromArgb(46, 208, 176)   ' #2ED0B0
    
    ' ألوان الخلفية
    Public Shared ReadOnly BackgroundLight As Color = Color.FromArgb(248, 249, 250) ' #F8F9FA
    Public Shared ReadOnly BackgroundWhite As Color = Color.White                   ' #FFFFFF
    Public Shared ReadOnly BackgroundDark As Color = Color.FromArgb(44, 62, 80)     ' #2C3E50
    
    ' ألوان النصوص
    Public Shared ReadOnly TextDark As Color = Color.FromArgb(64, 64, 64)           ' #404040
    Public Shared ReadOnly TextLight As Color = Color.White                         ' #FFFFFF
    Public Shared ReadOnly TextMuted As Color = Color.FromArgb(108, 117, 125)       ' #6C757D
    
    ''' <summary>
    ''' تطبيق نظام الألوان على زر
    ''' </summary>
    ''' <param name="button">الزر المراد تطبيق الألوان عليه</param>
    ''' <param name="primaryColor">اللون الأساسي</param>
    ''' <param name="hoverColor">لون التمرير</param>
    Public Shared Sub ApplyButtonColors(button As DevExpress.XtraEditors.SimpleButton, 
                                       primaryColor As Color, 
                                       hoverColor As Color)
        button.Appearance.BackColor = primaryColor
        button.Appearance.ForeColor = TextLight
        button.Appearance.Options.UseBackColor = True
        button.Appearance.Options.UseForeColor = True
        
        button.AppearanceHovered.BackColor = hoverColor
        button.AppearanceHovered.Options.UseBackColor = True
    End Sub
    
    ''' <summary>
    ''' تطبيق نظام الألوان على بلاطة
    ''' </summary>
    ''' <param name="tile">البلاطة المراد تطبيق الألوان عليها</param>
    ''' <param name="primaryColor">اللون الأساسي</param>
    ''' <param name="hoverColor">لون التمرير</param>
    Public Shared Sub ApplyTileColors(tile As DevExpress.XtraEditors.TileItem, 
                                     primaryColor As Color, 
                                     hoverColor As Color)
        tile.AppearanceItem.Normal.BackColor = primaryColor
        tile.AppearanceItem.Normal.ForeColor = TextLight
        tile.AppearanceItem.Normal.Options.UseBackColor = True
        tile.AppearanceItem.Normal.Options.UseForeColor = True
        
        tile.AppearanceItem.Hovered.BackColor = hoverColor
        tile.AppearanceItem.Hovered.Options.UseBackColor = True
    End Sub
    
    ''' <summary>
    ''' تطبيق خط موحد
    ''' </summary>
    ''' <param name="size">حجم الخط</param>
    ''' <param name="bold">هل الخط عريض</param>
    ''' <returns>الخط المطلوب</returns>
    Public Shared Function GetStandardFont(size As Single, Optional bold As Boolean = False) As Font
        Dim style As FontStyle = If(bold, FontStyle.Bold, FontStyle.Regular)
        Return New Font("Segoe UI", size, style)
    End Function
    
End Class
