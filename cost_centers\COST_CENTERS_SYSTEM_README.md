# نظام مراكز التكلفة - Cost Centers System

## نظرة عامة
تم إنشاء نظام شامل لإدارة مراكز التكلفة في النظام المحاسبي. هذا النظام يوفر إمكانيات متقدمة لتتبع التكاليف والإيرادات حسب مراكز التكلفة المختلفة، مما يساعد في اتخاذ قرارات إدارية أفضل.

## 🎯 الهدف من النظام
- **تتبع التكاليف**: مراقبة المصروفات حسب مراكز التكلفة
- **تحليل الربحية**: حساب الأرباح والخسائر لكل مركز
- **التحكم في الميزانية**: مقارنة التكاليف الفعلية بالميزانية المخططة
- **اتخاذ القرارات**: توفير تقارير مفصلة لدعم القرارات الإدارية

## 📊 مكونات النظام

### 1. قاعدة البيانات
#### الجدول الرئيسي: `cost_centers`
```sql
- cost_center_id (المعرف الفريد)
- cost_center_code (كود مركز التكلفة)
- cost_center_name (اسم مركز التكلفة)
- cost_center_description (وصف المركز)
- parent_cost_center_id (المركز الأب للهيكل الهرمي)
- cost_center_type (نوع المركز: إداري، تشغيلي، إنتاجي، إلخ)
- is_active (حالة النشاط)
- budget_amount (الميزانية المخططة)
- actual_amount (التكلفة الفعلية)
- created_date, modified_date (تواريخ الإنشاء والتعديل)
- created_by, modified_by (المستخدم المنشئ والمعدل)
- notes (ملاحظات)
```

#### الجداول المحدثة
تم إضافة عمود `cost_center_id` للجداول التالية:
- `invoice_add` (فواتير المبيعات)
- `Expenses_add` (المصروفات)
- `Purchases_add` (المشتريات)
- `Revenues_add` (الإيرادات)

#### Views والإجراءات المخزنة
- `vw_cost_centers_summary`: ملخص شامل لمراكز التكلفة
- `sp_GetCostCenterCosts`: حساب تكاليف مركز معين في فترة محددة

### 2. شاشة إدارة مراكز التكلفة
**الملف**: `cost_centers/CostCentersManagement.vb`

#### المميزات:
- **إضافة مراكز جديدة**: مع توليد كود تلقائي
- **تعديل المراكز الموجودة**: تحديث جميع البيانات
- **حذف المراكز**: مع التحقق من الارتباطات
- **البحث والتصفية**: عرض وتصفية المراكز
- **الهيكل الهرمي**: دعم المراكز الفرعية
- **أنواع المراكز**: تصنيف المراكز (إداري، تشغيلي، إنتاجي، إلخ)

#### طريقة الوصول:
```
ملف → مراكز التكلفة
أو
الشاشة الرئيسية → البيانات الأساسية → مراكز التكلفة
```

### 3. شاشة تقارير مراكز التكلفة
**الملف**: `cost_centers/CostCenterReports.vb`

#### التبويبات:
1. **الملخص**: عرض إجمالي التكاليف والإيرادات والأرباح
2. **التفاصيل**: عرض جميع العمليات المرتبطة بمراكز التكلفة
3. **الرسوم البيانية**: مخططات بصرية للمصروفات والإيرادات

#### المعايير:
- **مركز التكلفة**: اختيار مركز محدد أو جميع المراكز
- **الفترة الزمنية**: من تاريخ إلى تاريخ
- **التصدير**: إمكانية تصدير التقارير إلى Excel

#### طريقة الوصول:
```
الحسابات → تقارير مراكز التكلفة
```

### 4. كلاس المساعد
**الملف**: `cost_centers/CostCenterHelper.vb`

#### الوظائف المتاحة:
- `LoadActiveCostCenters()`: تحميل مراكز التكلفة النشطة
- `GetCostCenterName()`: الحصول على اسم المركز
- `GetCostCenterCode()`: الحصول على كود المركز
- `CalculateCostCenterExpenses()`: حساب مصروفات المركز
- `CalculateCostCenterRevenues()`: حساب إيرادات المركز
- `GetCostCenterSummary()`: ملخص شامل للمركز

## 🚀 كيفية الاستخدام

### الخطوة 1: تشغيل سكريبت قاعدة البيانات
1. قم بعمل نسخة احتياطية من قاعدة البيانات
2. شغل ملف `database/CostCenters_Database_Script.sql`
3. تحقق من إنشاء الجداول والبيانات الأولية

### الخطوة 2: إعداد مراكز التكلفة
1. افتح شاشة إدارة مراكز التكلفة
2. أضف المراكز الرئيسية (الإدارة، المبيعات، الإنتاج، إلخ)
3. حدد الميزانيات المخططة لكل مركز
4. أضف المراكز الفرعية إذا لزم الأمر

### الخطوة 3: ربط العمليات بمراكز التكلفة
- عند إدخال فواتير المبيعات: اختر مركز التكلفة المناسب
- عند إدخال المصروفات: حدد المركز المسؤول عن المصروف
- عند إدخال المشتريات: اربطها بالمركز المستفيد

### الخطوة 4: مراجعة التقارير
1. افتح شاشة تقارير مراكز التكلفة
2. حدد الفترة الزمنية المطلوبة
3. اختر مركز التكلفة أو جميع المراكز
4. راجع النتائج في التبويبات المختلفة

## 📈 البيانات الأولية المدرجة

تم إدراج 8 مراكز تكلفة أولية:

| الكود | الاسم | النوع | الميزانية |
|-------|-------|--------|-----------|
| CC001 | الإدارة العامة | إداري | 100,000 |
| CC002 | المبيعات | تشغيلي | 150,000 |
| CC003 | المشتريات | تشغيلي | 80,000 |
| CC004 | الإنتاج | إنتاجي | 200,000 |
| CC005 | الصيانة | خدمي | 50,000 |
| CC006 | التسويق | تسويقي | 75,000 |
| CC007 | الموارد البشرية | إداري | 60,000 |
| CC008 | تقنية المعلومات | تقني | 90,000 |

## 🔧 التخصيص والتطوير

### إضافة أنواع جديدة لمراكز التكلفة
يمكن تعديل قائمة الأنواع في:
```vb
CostCenterTypeCombo.Properties.Items.AddRange(New Object() {
    "عام", "إداري", "تشغيلي", "إنتاجي", "خدمي", "تسويقي", "تقني"
})
```

### إضافة حقول جديدة
1. أضف الحقل إلى جدول `cost_centers`
2. حدث شاشة الإدارة لتشمل الحقل الجديد
3. حدث التقارير لعرض الحقل الجديد

### ربط مراكز التكلفة بشاشات أخرى
استخدم `CostCenterHelper.LoadActiveCostCenters()` لإضافة مراكز التكلفة لأي شاشة:

```vb
' في شاشة الفواتير
CostCenterHelper.LoadActiveCostCenters(CostCenterCombo, True)
```

## 📊 التقارير المتاحة

### 1. تقرير الملخص
- إجمالي المصروفات لكل مركز
- إجمالي الإيرادات لكل مركز
- صافي الربح/الخسارة
- هامش الربح كنسبة مئوية
- نسبة استخدام الميزانية

### 2. تقرير التفاصيل
- جميع العمليات المرتبطة بمراكز التكلفة
- تاريخ ونوع ورقم كل عملية
- المبلغ ومركز التكلفة المرتبط

### 3. الرسوم البيانية
- رسم بياني للمصروفات حسب مراكز التكلفة
- رسم بياني للإيرادات حسب مراكز التكلفة
- مقارنات بصرية بين المراكز

## 🔒 الصلاحيات

### إدارة مراكز التكلفة
- مرتبطة بصلاحية الإعدادات (`m1`)
- تشمل: إضافة، تعديل، حذف المراكز

### تقارير مراكز التكلفة
- مرتبطة بصلاحية التقارير (`m15`)
- تشمل: عرض وتصدير التقارير

## 🚨 نصائح مهمة

### عند الحذف
- لا يمكن حذف مركز له مراكز فرعية
- لا يمكن حذف مركز مرتبط بعمليات مالية
- قم بإلغاء تفعيل المركز بدلاً من حذفه

### عند التعديل
- تأكد من عدم تكرار أكواد المراكز
- احرص على تحديث الميزانيات دورياً
- راجع الارتباطات قبل تغيير نوع المركز

### للحصول على أفضل النتائج
- ادخل مراكز التكلفة قبل بدء العمليات المالية
- اربط جميع العمليات بمراكز التكلفة المناسبة
- راجع التقارير دورياً لمتابعة الأداء

---

**تم إنشاء نظام مراكز التكلفة بنجاح! 🎉**

للدعم الفني أو الاستفسارات، يرجى مراجعة الملفات المرفقة أو الاتصال بفريق التطوير.
