﻿Imports DevExpress.XtraEditors
Imports System.Data.SqlClient
Imports DevExpress.XtraReports.UI
Imports System.IO
Imports System.Drawing.Imaging

Public Class item_trans_show
    Private dtt As DataTable
    Sub column_devexpress()
        dtt = New DataTable
        dtt.Columns.Add("itemname")
        dtt.Columns.Add("itemcount")
        dtt.Columns.Add("Datefrom")
        dtt.Columns.Add("Dateto")
        dtt.Columns.Add("item_tybe")
        dtt.Columns.Add("item_date")
        dtt.Columns.Add("invoice_number")
        dtt.Columns.Add("ward")
        dtt.Columns.Add("monsarf")
        dtt.Columns.Add("item_count")
        dtt.Columns.Add("itemcount2")
        dtt.Columns.Add("sec_name")
        dtt.Columns.Add("sec_phone")
        dtt.Columns.Add("sec_address")
        dtt.Columns.Add("sec_email")
        dtt.Columns.Add("sec_web")
        dtt.Columns.Add("sec_number")
        dtt.Columns.Add("sec_s1")
        dtt.Columns.Add("sec_s2")
        dtt.Columns.Add("user_name")
        dtt.Columns.Add("store")
    End Sub
    Private Sub cus_trans_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_account()
        column_devexpress()
    End Sub
    Sub fill_account()
        itemname.Properties.DataSource = Nothing
        Dim adp As New SqlClient.SqlDataAdapter("select (itemcode),(itemnamearabic),(itemcount) from item order by itemnamearabic", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        itemname.Properties.DataSource = dt
        itemname.Properties.DisplayMember = "itemnamearabic"
        itemname.Properties.ValueMember = "itemcode"
        day_option.SelectedIndex = 6
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Me.Dispose()
    End Sub

    Private Sub day_option_SelectedIndexChanged(sender As Object, e As EventArgs) Handles day_option.SelectedIndexChanged
        If day_option.SelectedIndex = 0 Then
            Datefrom.EditValue = Now.Date
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 1 Then
            Datefrom.EditValue = DateAdd("d", -1, Now.Date)
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 2 Then
            Datefrom.EditValue = DateAdd("ww", -1, Now.Date)
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 3 Then
            Datefrom.EditValue = DateAdd("ww", -2, Now.Date)
            Dateto.EditValue = DateAdd("ww", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 4 Then
            Datefrom.EditValue = DateAdd("m", -1, Now.Date)
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 5 Then
            Datefrom.EditValue = DateAdd("m", -2, Now.Date)
            Dateto.EditValue = DateAdd("m", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 6 Then
            Datefrom.EditValue = DateAdd("m", -3, Now.Date)
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 7 Then
            Datefrom.EditValue = DateAdd("m", -6, Now.Date)
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 8 Then
            Datefrom.EditValue = DateAdd("yyyy", -1, Now.Date)
            Dateto.EditValue = Now.Date
        ElseIf day_option.SelectedIndex = 9 Then
            Datefrom.EditValue = DateAdd("yyyy", -2, Now.Date)
            Dateto.EditValue = DateAdd("yyyy", -1, Now.Date)
        End If
    End Sub

    Private Sub searsh_btn_Click(sender As Object, e As EventArgs) Handles searsh_btn.Click
        'Try
        '    If itemname.Text = "" Then
        '        XtraMessageBox.Show("أختار أسم الصنف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        '    End If
        '    filldgv("select * from item_trans where item_name=N'" & (itemname.Text) & "' and item_date>='" & Format(Datefrom.EditValue, "yyy/MM/dd") & "'  and item_date<='" & Format(Dateto.EditValue, "yyy/MM/dd") & "'")

        '    dgv.Rows(0).Cells(5).Value = (Val(dgv.Rows(0).Cells(3).Value.ToString) - Val(dgv.Rows(0).Cells(4).Value.ToString))
        '    For i = 1 To dgv.Rows.Count - 1
        '        dgv.Rows(i).Cells(5).Value = (Val(dgv.Rows(i).Cells(3).Value.ToString) - Val(dgv.Rows(i).Cells(4).Value.ToString)) + Val(dgv.Rows(i - 1).Cells(5).Value.ToString)
        '    Next
        '    Try
        '        dtt.Clear()
        '    Catch ex As Exception

        '    End Try
        '    For i = 1 To dgv.Rows.Count - 1
        '        Dim dr As DataRow = dtt.NewRow()
        '        dr(0) = itemname.Text
        '        dr(1) = itemcount.Text
        '        dr(2) = Format(Datefrom.EditValue, "yyyy/MM/dd")
        '        dr(3) = Format(Dateto.EditValue, "yyyy/MM/dd")
        '        dr(4) = dgv.Rows(i).Cells(0).Value
        '        dr(5) = Format(dgv.Rows(i).Cells(1).Value, "yyyy/MM/dd")
        '        dr(6) = dgv.Rows(i).Cells(2).Value
        '        dr(7) = dgv.Rows(i).Cells(3).Value
        '        dr(8) = dgv.Rows(i).Cells(4).Value
        '        dr(9) = dgv.Rows(i).Cells(5).Value
        '        dr(10) = dgv.Rows(0).Cells(5).Value
        '        dr(11) = XtraForm1.sec_name.Text
        '        dr(12) = XtraForm1.sec_phone.Text
        '        dr(13) = XtraForm1.sec_address.Text
        '        dr(14) = XtraForm1.sec_email.Text
        '        dr(15) = XtraForm1.sec_web.Text
        '        dr(16) = XtraForm1.sec_number.Text
        '        dr(17) = XtraForm1.sec_s1.Text
        '        dr(18) = XtraForm1.sec_s2.Text
        '        ''============حفظ الصورة=============
        '        dr(19) = XtraForm1.user_name.Text
        '        dr(20) = XtraForm1.store.Text
        '        dtt.Rows.Add(dr)
        '    Next

        '    Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "item_trnas_print_a4.repx"), True)
        '    rep.DataSource = dtt
        '    rep.ShowPrintMarginsWarning = False
        '    Dim frm As New preview
        '    preview.DocumentViewer1.DocumentSource = rep
        '    preview.Show()
        'Catch ex As Exception

        'End Try

    End Sub
    Sub filldgv(sql)
        dgv.Rows.Clear()
        Dim str = "select sum(ward), sum(monsarf) from item_trans where item_name=N'" & itemname.Text & "' And item_date<'" & Format(Datefrom.EditValue, "yyyy/MM/dd") & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("item_trans")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        Dim sumcredit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
        Dim i = dgv.Rows.Add
        dgv.Rows(i).Cells(0).Value = "كمية سابقة"
        dgv.Rows(i).Cells(3).Value = Format(Val(sumdebit), "#,0.00")
        dgv.Rows(i).Cells(4).Value = Format(Val(sumdebit), "#,0.00")
        dgv.Rows(i).Cells(5).Value = Format(Val(sumcredit), "#,0.00")
        Dim adp = New SqlDataAdapter(sql, sqlconn)
        Dim ds = New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim g = dt.Rows.Count
        For i = 0 To dt.Rows.Count - 1
            Dim j = dgv.Rows.Add
            dgv.Rows(j).Cells(0).Value = dt.Rows(i).Item("item_tybe")
            dgv.Rows(j).Cells(1).Value = dt.Rows(i).Item("item_date")
            dgv.Rows(j).Cells(2).Value = dt.Rows(i).Item("invoice_number")
            dgv.Rows(j).Cells(3).Value = dt.Rows(i).Item("ward")
            dgv.Rows(j).Cells(4).Value = dt.Rows(i).Item("monsarf")
            dgv.Rows(j).Cells(5).Value = dt.Rows(i).Item("item_count")
        Next
       
    End Sub

    Private Sub itemname_EditValueChanged(sender As Object, e As EventArgs) Handles itemname.EditValueChanged
        Try
            Dim sql = "select(itemcount),(itemcode) from item where itemcode=N'" & (itemname.EditValue) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)

                itemcount.Text = dr!itemcount

            End If
        Catch ex As Exception

        End Try
       
    End Sub
End Class