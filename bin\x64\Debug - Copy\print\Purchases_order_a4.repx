﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.invoice_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="invoice_a4" Margins="0, 0, 2, 2" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="2.083333" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="2.083333" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeaderBand1" HeightF="104.875">
      <Controls>
        <Item1 Ref="6" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="145.4165,104.875" LocationFloat="59.18767, 0">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="8" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="542.8594, 42.95832" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="9" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="10" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="11" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="542.8594, 11.62499" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="12" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="14" ControlType="PageHeaderBand" Name="PageHeader" HeightF="325.5417">
      <Controls>
        <Item1 Ref="15" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="277.0832,31.25" LocationFloat="383.5105, 94.27084" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="16" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="17" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="18" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="المخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="660.5938, 94.27084" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="19" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="20" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="277.0833,31.24999" LocationFloat="383.5103, 128.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="21" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="22" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="23" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="225.1043,31.25004" LocationFloat="26.11453, 128.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="24" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="25" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="26" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="660.5938, 128.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="27" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="28" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="251.2187, 128.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="29" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="30" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="26.11453, 76.58331" Padding="2,2,0,0,100">
          <StylePriority Ref="31" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="32" ControlType="XRLabel" Name="XrLabel19" Text="طلبية مشتريات" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="26.11453, 0" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="33" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="34" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="676.2186, 6.392598E-06">
          <ExpressionBindings>
            <Item1 Ref="35" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item9>
        <Item10 Ref="36" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="86.11455, 31.33331">
          <ExpressionBindings>
            <Item1 Ref="37" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item10>
        <Item11 Ref="38" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="766.7709,33.33334" LocationFloat="26.11453, 292.2083" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="39" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="40" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.63319838340209311" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="41" UsePadding="false" />
                </Item1>
                <Item2 Ref="42" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.58995300925039273" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="43" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.542263146654033" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="44" UsePadding="false" />
                </Item3>
                <Item4 Ref="45" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.64086488759827032" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="46" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5482460841812116" Text="اسم الصنف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="47" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="48" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم المورد:" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="660.5941, 172.4167" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="49" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="50" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="634.4794,31.24998" LocationFloat="26.11453, 172.4167" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="51" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="52" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="53" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="634.4794,31.24998" LocationFloat="26.11453, 203.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="54" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="55" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="56" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="660.5941, 203.6667" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="57" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="58" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="660.5938, 234.9167" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="59" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="60" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="634.4792,31.24998" LocationFloat="26.11453, 234.9167" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="61" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="62" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
      </Controls>
    </Item5>
    <Item6 Ref="63" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="372.8447">
      <Controls>
        <Item1 Ref="64" ControlType="XRLabel" Name="XrLabel27" Text="XrLabel27" TextAlignment="TopRight" SizeF="250.6404,23" LocationFloat="461.8596, 339.2386" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="65" Expression="[user_invoice]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="66" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="67" ControlType="XRLabel" Name="XrLabel18" RightToLeft="Yes" Text="المستخدم :" TextAlignment="MiddleRight" SizeF="77.48438,25.08331" LocationFloat="712.5, 339.2386" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="68" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="69" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="506.4173,25" LocationFloat="286.4685, 173.1136" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="70" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="71" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="72" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="506.4167,25" LocationFloat="286.4686, 198.1136" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="73" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="74" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="75" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="37, 2.083333" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="76" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="77" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="78" Expression="[invoice_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="79" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="80" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="81" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="82" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="83" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="84" Expression="[invoice_pound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="85" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="86" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="87" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="88" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="89" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="37, 301.6555" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="90" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="91" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="37, 337.1553" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="92" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="93" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="504.8646, 265.2804" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="94" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="95" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="504.8646, 240.197" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="96" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="97" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="86.11455, 267.3636" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="98" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="99" UseFont="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="100" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="86.11455, 240.197" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="101" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="102" UseFont="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="103" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="461.8595, 309.0303" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="104" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="105" UseFont="false" />
        </Item12>
        <Item13 Ref="106" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="200,100" LocationFloat="37, 52.08333" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="107" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
              <Cells>
                <Item1 Ref="108" ControlType="XRTableCell" Name="XrTableCell17" Weight="1" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="109" Expression="[invoice_descound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="110" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="111" ControlType="XRTableCell" Name="XrTableCell18" Weight="1" Text="الخصم" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="112" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="113" ControlType="XRTableRow" Name="XrTableRow6" Weight="1">
              <Cells>
                <Item1 Ref="114" ControlType="XRTableCell" Name="XrTableCell19" Weight="1" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="115" Expression="[invoice_tax]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="116" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="117" ControlType="XRTableCell" Name="XrTableCell20" Weight="1" Text="الضريبة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="118" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
            <Item3 Ref="119" ControlType="XRTableRow" Name="XrTableRow10" Weight="1">
              <Cells>
                <Item1 Ref="120" ControlType="XRTableCell" Name="XrTableCell27" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="121" Expression="[total_invoice]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="122" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="123" ControlType="XRTableCell" Name="XrTableCell28" Weight="1" Text="صافي الفاتورة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="124" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item3>
            <Item4 Ref="125" ControlType="XRTableRow" Name="XrTableRow11" Weight="1">
              <Cells>
                <Item1 Ref="126" ControlType="XRTableCell" Name="XrTableCell29" Weight="1" Text="XrTableCell29" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="127" Expression="[past_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="128" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="129" ControlType="XRTableCell" Name="XrTableCell30" Weight="1" Text="السابق" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="130" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item4>
          </Rows>
          <StylePriority Ref="131" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="132" ControlType="XRTable" Name="XrTable5" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="36.99997, 153.9469" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="133" ControlType="XRTableRow" Name="XrTableRow7" Weight="1">
              <Cells>
                <Item1 Ref="134" ControlType="XRTableCell" Name="XrTableCell21" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="135" Expression="[money_plus]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="136" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="137" ControlType="XRTableCell" Name="XrTableCell22" Weight="1" Text="المطلوب" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="138" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="139" ControlType="XRTableRow" Name="XrTableRow8" Weight="1">
              <Cells>
                <Item1 Ref="140" ControlType="XRTableCell" Name="XrTableCell23" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="141" Expression="[pay]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="142" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="143" ControlType="XRTableCell" Name="XrTableCell24" Weight="1" Text="المدفوع" TextAlignment="MiddleRight" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="144" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="145" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="146" ControlType="XRTable" Name="XrTable6" TextAlignment="MiddleCenter" SizeF="200,25" LocationFloat="36.99997, 207.4886" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" Borders="Bottom">
          <Rows>
            <Item1 Ref="147" ControlType="XRTableRow" Name="XrTableRow9" Weight="1">
              <Cells>
                <Item1 Ref="148" ControlType="XRTableCell" Name="XrTableCell25" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" ForeColor="White" BackColor="Chocolate" Padding="2,2,0,0,100" BorderColor="Black">
                  <ExpressionBindings>
                    <Item1 Ref="149" Expression="[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="150" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="151" ControlType="XRTableCell" Name="XrTableCell26" Weight="1" Text="الباقي" TextAlignment="MiddleRight" BackColor="Chocolate" Padding="2,2,0,0,100">
                  <StylePriority Ref="152" UseBackColor="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="153" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item15>
      </Controls>
    </Item6>
    <Item7 Ref="154" ControlType="PageFooterBand" Name="PageFooter" HeightF="27.16662">
      <Controls>
        <Item1 Ref="155" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="37, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="156" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="157" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="747.6927, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="158" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="159" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,23" LocationFloat="698.7344, 0" Padding="2,2,0,0,100">
          <StylePriority Ref="160" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="161" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="162" ControlType="DetailBand" Name="Detail1" HeightF="26.04167">
          <Controls>
            <Item1 Ref="163" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="766.7708,25" LocationFloat="26.11453, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="164" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="165" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.0005448213163186" TextFormatString="{0:n2}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="166" Expression="[invoice_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="167" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="168" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.93220917081262" TextFormatString="{0:n2}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="169" Expression="[invoice_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="170" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="171" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.85685326008936014" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="172" Expression="[invoice_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="173" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.0126570260107606" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="174" Expression="[invoice_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="175" ControlType="XRTableCell" Name="XrTableCell8" Weight="4.0265895569054457" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="176" Expression="[invoice_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="177" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="178" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="179" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="180" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="181" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="182" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="183" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="184" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>