# إضافة التقارير المتقدمة للقائمة الرئيسية

## 🎯 الهدف
إضافة عنصر جديد في قائمة التقارير للوصول السريع للتقارير المتقدمة لمراكز التكلفة.

## 📋 الخطوات المطلوبة

### 1. إضافة عنصر القائمة في `main.Designer.vb`

#### أ. إضافة المتغير:
```vb
Friend WithEvents تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
```

#### ب. إضافة العنصر للقائمة:
```vb
' في قسم التقارير
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()

' إعدادات العنصر
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Name = "تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem"
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Size = New System.Drawing.Size(200, 22)
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Text = "تقارير مراكز التكلفة المتقدمة"

' إضافة للقائمة الفرعية للتقارير
Me.التقاريرToolStripMenuItem.DropDownItems.Add(Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem)
```

### 2. إضافة معالج الحدث في `main.vb`

```vb
Private Sub تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Click
    main_page.OpenAdvancedCostCenterReports()
End Sub
```

### 3. إضافة للصلاحيات (اختياري)

#### في تبويب الصلاحيات:
```vb
' إضافة checkbox للتحكم في صلاحية الوصول
Dim chkAdvancedCostReports As New CheckBox()
chkAdvancedCostReports.Text = "تقارير مراكز التكلفة المتقدمة"
chkAdvancedCostReports.Name = "chk_advanced_cost_reports"
```

## 🎨 التنسيق المقترح

### موقع العنصر في القائمة:
```
📊 التقارير
├── 📈 تقارير الحسابات
├── 📊 تقارير مراكز التكلفة  ← (الموجود حالياً)
├── 📊 تقارير مراكز التكلفة المتقدمة  ← (الجديد)
├── 📋 تقارير العملاء
├── 📦 تقارير المخزن
└── ...
```

### أيقونة مقترحة:
- 📊 أو 📈 أو 🔍 أو 📋

## 🔧 الكود الكامل للإضافة

### في `main.Designer.vb`:

```vb
' إضافة المتغير في بداية الكلاس
Friend WithEvents تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem

' في دالة InitializeComponent
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()

' إعدادات العنصر
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Name = "تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem"
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Size = New System.Drawing.Size(250, 22)
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Text = "📊 تقارير مراكز التكلفة المتقدمة"
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Image = My.Resources.chart_icon ' إذا كان لديك أيقونة

' إضافة للقائمة (بعد عنصر تقارير مراكز التكلفة العادي)
Me.التقاريرToolStripMenuItem.DropDownItems.Add(Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem)
```

### في `main.vb`:

```vb
Private Sub تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.Click
    Try
        ' التحقق من الصلاحية (اختياري)
        If Not CheckUserPermission("advanced_cost_reports") Then
            XtraMessageBox.Show("ليس لديك صلاحية للوصول لهذا التقرير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        ' فتح التقرير
        main_page.OpenAdvancedCostCenterReports()
    Catch ex As Exception
        XtraMessageBox.Show("خطأ في فتح التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
End Sub
```

## 🎯 بديل: إضافة كزر في شريط الأدوات

### إضافة زر في شريط الأدوات:
```vb
' إضافة زر جديد
Dim btnAdvancedCostReports As New ToolStripButton()
btnAdvancedCostReports.Text = "تقارير التكلفة المتقدمة"
btnAdvancedCostReports.Image = My.Resources.advanced_reports_icon
btnAdvancedCostReports.ToolTipText = "تقارير مراكز التكلفة المتقدمة"

' إضافة معالج الحدث
AddHandler btnAdvancedCostReports.Click, AddressOf btnAdvancedCostReports_Click

' إضافة للشريط
ToolStrip1.Items.Add(btnAdvancedCostReports)
```

## 🔐 إدارة الصلاحيات

### إضافة صلاحية جديدة:
```sql
-- إضافة عمود جديد لجدول الصلاحيات
ALTER TABLE user_permissions ADD advanced_cost_reports BIT DEFAULT 0;

-- منح الصلاحية للمدير
UPDATE user_permissions SET advanced_cost_reports = 1 WHERE user_id = 1;
```

### التحقق من الصلاحية:
```vb
Private Function CheckUserPermission(permissionName As String) As Boolean
    Try
        Dim sql As String = "SELECT " & permissionName & " FROM user_permissions WHERE user_id = " & CurrentUserId
        Using cmd As New SqlCommand(sql, sqlconn)
            If sqlconn.State = ConnectionState.Closed Then
                sqlconn.Open()
            End If
            Dim result = cmd.ExecuteScalar()
            Return Convert.ToBoolean(result)
        End Using
    Catch
        Return True ' السماح في حالة الخطأ
    End Try
End Function
```

## 📱 إضافة اختصار لوحة المفاتيح

```vb
' إضافة اختصار Ctrl+Shift+R
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.ShortcutKeys = Keys.Control Or Keys.Shift Or Keys.R
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.ShowShortcutKeys = True
```

## 🎨 تحسينات إضافية

### 1. إضافة أيقونة مخصصة:
- إنشاء أيقونة 16x16 للقائمة
- إنشاء أيقونة 32x32 لشريط الأدوات

### 2. إضافة تلميح:
```vb
Me.تقاريرمراكزالتكلفةالمتقدمةToolStripMenuItem.ToolTipText = "تقارير متقدمة لتحليل مراكز التكلفة والموازنة"
```

### 3. إضافة للقائمة السريعة:
```vb
' إضافة للقائمة السريعة في الشاشة الرئيسية
Dim quickButton As New Button()
quickButton.Text = "تقارير التكلفة المتقدمة"
quickButton.Size = New Size(150, 40)
AddHandler quickButton.Click, AddressOf btnAdvancedCostReports_Click
```

## ✅ قائمة التحقق

- [ ] إضافة المتغير في main.Designer.vb
- [ ] إضافة العنصر للقائمة
- [ ] إضافة معالج الحدث في main.vb
- [ ] اختبار فتح التقرير
- [ ] إضافة الصلاحيات (اختياري)
- [ ] إضافة الأيقونة (اختياري)
- [ ] إضافة الاختصار (اختياري)
- [ ] اختبار شامل للوظيفة

---

**ملاحظة**: بعد إضافة هذه العناصر، ستكون التقارير المتقدمة متاحة بسهولة من القائمة الرئيسية للنظام.
