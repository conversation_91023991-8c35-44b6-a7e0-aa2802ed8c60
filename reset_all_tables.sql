-- سكريبت تصفير جميع الجداول تلقائياً
-- يتعامل مع جميع الجداول في قاعدة البيانات

USE data_a
GO

PRINT '============================================'
PRINT 'بدء عملية تصفير جميع الجداول تلقائياً'
PRINT 'تحذير: سيتم حذف جميع البيانات!'
PRINT '============================================'

-- إنشاء جدول مؤقت لحفظ أسماء الجداول
CREATE TABLE #TablesToReset (
    TableName NVARCHAR(128),
    HasIdentity BIT
)

-- الحصول على جميع الجداول في قاعدة البيانات
INSERT INTO #TablesToReset (TableName, HasIdentity)
SELECT 
    TABLE_NAME,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM sys.columns c
            INNER JOIN sys.tables t ON c.object_id = t.object_id
            WHERE t.name = TABLE_NAME AND c.is_identity = 1
        ) THEN 1
        ELSE 0
    END
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
AND TABLE_NAME NOT LIKE 'sys%'
AND TABLE_NAME NOT LIKE 'MS%'
ORDER BY TABLE_NAME

-- تعطيل فحص المفاتيح الخارجية
EXEC sp_MSforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all"
PRINT 'تم تعطيل فحص المفاتيح الخارجية'

-- متغيرات للحلقة
DECLARE @TableName NVARCHAR(128)
DECLARE @HasIdentity BIT
DECLARE @SQL NVARCHAR(MAX)
DECLARE @Count INT

-- إنشاء مؤشر للتنقل عبر الجداول
DECLARE table_cursor CURSOR FOR
SELECT TableName, HasIdentity FROM #TablesToReset

OPEN table_cursor
FETCH NEXT FROM table_cursor INTO @TableName, @HasIdentity

WHILE @@FETCH_STATUS = 0
BEGIN
    BEGIN TRY
        -- حذف البيانات من الجدول
        SET @SQL = 'DELETE FROM [' + @TableName + ']'
        EXEC sp_executesql @SQL
        
        -- إعادة تعيين Identity إذا كان موجوداً
        IF @HasIdentity = 1
        BEGIN
            SET @SQL = 'DBCC CHECKIDENT (''' + @TableName + ''', RESEED, 0)'
            EXEC sp_executesql @SQL
            PRINT 'تم تصفير جدول ' + @TableName + ' مع إعادة تعيين Identity'
        END
        ELSE
        BEGIN
            PRINT 'تم تصفير جدول ' + @TableName
        END
    END TRY
    BEGIN CATCH
        PRINT 'خطأ في تصفير جدول ' + @TableName + ': ' + ERROR_MESSAGE()
    END CATCH
    
    FETCH NEXT FROM table_cursor INTO @TableName, @HasIdentity
END

CLOSE table_cursor
DEALLOCATE table_cursor

-- حذف الجدول المؤقت
DROP TABLE #TablesToReset

-- إعادة تفعيل فحص المفاتيح الخارجية
EXEC sp_MSforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all"
PRINT 'تم إعادة تفعيل فحص المفاتيح الخارجية'

-- عرض إحصائيات النتائج
PRINT ''
PRINT 'إحصائيات التصفير:'
PRINT '=================='

-- عد الجداول الفارغة
SELECT 
    t.name AS 'اسم الجدول',
    p.rows AS 'عدد الصفوف'
FROM sys.tables t
INNER JOIN sys.partitions p ON t.object_id = p.object_id
WHERE p.index_id IN (0,1)
AND t.name NOT LIKE 'sys%'
ORDER BY t.name

PRINT '============================================'
PRINT 'تم تصفير جميع الجداول بنجاح'
PRINT 'جميع الـ Identity تبدأ من 1'
PRINT '============================================'
