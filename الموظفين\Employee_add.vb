﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox


Public Class Employee_add
    Public Sub show_data(x)
        Dim sql = "select * from employees where emp_code=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("لم يتم العثور علي بيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim dr = dt.Rows(0)
            emp_code.Text = dr!emp_code
            emp_name.Text = dr!emp_name
            emp_mange.Text = dr!emp_mange
            Section.Text = dr!Section
            Date_Birth.Text = dr!Date_Birth
            card_number.Text = dr!card_number
            start_Function.Text = dr!start_Function
            end_Function.Text = dr!end_Function
            Number_insurance.Text = dr!Number_insurance
            Social_status.Text = dr!Social_status
            Rosacea.Text = dr!Rosacea
            emp_phone1.Text = dr!emp_phone1
            emp_phone2.Text = dr!emp_phone2
            emp_address.Text = dr!emp_address
            emp_email.Text = dr!emp_email
            Salary.Text = dr!Salary
            account_type.Text = dr!account_type
            Day_absence.Text = dr!Day_absence
            clock_absence.Text = dr!clock_absence
            extra_hour.Text = dr!extra_hour
            Day_absence2.Text = dr!Day_absence2
            clock_absence2.Text = dr!clock_absence2
            extra_hour2.Text = dr!extra_hour2


            meal_allowance.Text = dr!meal_allowance
            Transfer_allowance.Text = dr!Transfer_allowance
            Insurances.Text = dr!Insurances
            code_edit.Text = 1

        End If
    End Sub
    Sub fillemp_mange()
        emp_mange.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from emp_mange order by mange_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            emp_mange.Properties.Items.Add(dt.Rows(i).Item("mange_name"))
        Next
    End Sub
    Sub fillemp_Functioned()
        emp_Function.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from emp_Functioned order by Function_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            emp_Function.Properties.Items.Add(dt.Rows(i).Item("Function_name"))
        Next
    End Sub
    Sub fillRosacea()
        Rosacea.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Rosacea order by Rosacea_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            Rosacea.Properties.Items.Add(dt.Rows(i).Item("Rosacea_name"))
        Next
    End Sub
    Sub fillemp_Section()
        Section.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from emp_Section order by Section_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            Section.Properties.Items.Add(dt.Rows(i).Item("Section_name"))
        Next
    End Sub
    Sub fill()
        fillemp_mange()
        fillemp_Functioned()
        fillemp_Section()
        fillRosacea()
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If code_edit.Text = 0 Then
            Dim sql1 = "select * from employees where emp_name=N'" & (emp_name.Text) & "'"
            Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
            Dim ds1 As New DataSet
            adp1.Fill(ds1)
            Dim dt1 = ds1.Tables(0)
            If dt1.Rows.Count > 0 Then
                XtraMessageBox.Show("يوجد موظف بهذا الاسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Exit Sub
            End If
        End If


        Dim sql = "select * from employees where emp_code=N'" & (emp_code.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            '========= بيانات اساسية============
            dr!emp_name = emp_name.Text
            dr!emp_mange = emp_mange.Text
            dr!Section = Section.Text
            dr!Date_Birth = Date_Birth.Text
            dr!card_number = card_number.Text
            dr!start_Function = start_Function.Text
            dr!end_Function = end_Function.Text
            dr!Number_insurance = Number_insurance.Text
            dr!Social_status = Social_status.Text
            dr!Rosacea = Rosacea.Text
            dr!emp_phone1 = emp_phone1.Text
            dr!emp_phone2 = emp_phone2.Text
            dr!emp_address = emp_address.Text
            dr!emp_email = emp_email.Text
            dr!Salary = Salary.Text
            dr!account_type = account_type.Text
            dr!Day_absence = Day_absence.Text
            dr!clock_absence = clock_absence.Text
            dr!extra_hour = extra_hour.Text
            dr!Day_absence2 = Day_absence2.Text
            dr!clock_absence2 = clock_absence2.Text
            dr!extra_hour2 = extra_hour2.Text

            dr!meal_allowance = meal_allowance.Text
            dr!Transfer_allowance = Transfer_allowance.Text
            dr!Insurances = Insurances.Text
            '============================
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            delete_collection()
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============
            dr!emp_code = emp_code.Text
            dr!emp_name = emp_name.Text
            dr!emp_mange = emp_mange.Text
            dr!Section = Section.Text
            dr!Date_Birth = Date_Birth.Text
            dr!card_number = card_number.Text
            dr!start_Function = start_Function.Text
            dr!end_Function = end_Function.Text
            dr!Number_insurance = Number_insurance.Text
            dr!Social_status = Social_status.Text
            dr!Rosacea = Rosacea.Text
            dr!emp_phone1 = emp_phone1.Text
            dr!emp_phone2 = emp_phone2.Text
            dr!emp_address = emp_address.Text
            dr!emp_email = emp_email.Text
            dr!Salary = Salary.Text
            dr!account_type = account_type.Text
            dr!Day_absence = Day_absence.Text
            dr!clock_absence = clock_absence.Text
            dr!extra_hour = extra_hour.Text
            dr!Day_absence2 = Day_absence2.Text
            dr!clock_absence2 = clock_absence2.Text
            dr!extra_hour2 = extra_hour2.Text

            dr!meal_allowance = meal_allowance.Text
            dr!Transfer_allowance = Transfer_allowance.Text
            dr!Insurances = Insurances.Text
            '============================
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)

        End If

        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.Background)
        new_cus()
    End Sub
    Sub new_cus()
        emp_code.Text = getlastcode("employees", "emp_code") + 1
        fill()
        emp_name.Text = ""
        emp_mange.Text = ""
        Section.Text = ""
        emp_Function.Text = ""
        Date_Birth.Value = Now.Date
        card_number.Text = ""
        start_Function.Value = Now.Date
        start_Function.Value = Now.Date
        emp_type.SelectedIndex = 0
        card_number.Text = ""
        emp_type.SelectedIndex = 0
        Number_insurance.Text = ""
        Social_status.SelectedIndex = 0
        Rosacea.SelectedIndex = 0
        Salary.Text = 0
        meal_allowance.Text = 0
        Transfer_allowance.Text = 0
        Transfer_allowance.Text = 0
        Insurances.Text = 0
        emp_phone1.Text = ""
        emp_phone2.Text = ""
        emp_address.Text = ""
        emp_email.Text = ""
        Salary.Text = 0
        Rosacea.SelectedIndex = 0
        account_type.SelectedIndex = 0
        Day_absence.Text = 0
        clock_absence.Text = 0
        extra_hour.Text = 0
        Day_absence2.Text = 0
        clock_absence2.Text = 0
        extra_hour2.Text = 0
        code_edit.Text = 0
    End Sub

    Private Sub emp_name_TextChanged(sender As Object, e As EventArgs) Handles emp_name.TextChanged
        If emp_name.Text <> Nothing Then
            Dim sql = "select * from employees where emp_name=N'" & (emp_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                PictureBox1.Image = My.Resources.wrong
            Else
                PictureBox1.Image = My.Resources.check_true
            End If
        Else
            PictureBox1.Image = Nothing
        End If
    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        emp_manged.ShowDialog()
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        emp_Section.ShowDialog()
    End Sub

    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        emp_Functioned.ShowDialog()
    End Sub

    Private Sub emp_mange_MouseEnter(sender As Object, e As EventArgs) Handles Section.MouseEnter, emp_mange.MouseEnter, emp_Function.MouseEnter
        fill()
    End Sub
 
   
    Private Sub Employee_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
    Sub delete_collection()
        For i2 = 0 To 20
            Dim sql = "select * from emp_course where emp_code=N'" & emp_code.Text & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub

End Class