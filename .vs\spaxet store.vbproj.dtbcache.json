{"RootPath": "E:\\مبيعات 2022\\New folder", "ProjectFileName": "spaxet store.vbproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "customer\\customer_export.Designer.vb"}, {"SourceFile": "customer\\customer_export.vb"}, {"SourceFile": "customer\\customer_edit.Designer.vb"}, {"SourceFile": "customer\\customer_edit.vb"}, {"SourceFile": "customer\\setting\\cus_visitor.Designer.vb"}, {"SourceFile": "customer\\setting\\cus_visitor.vb"}, {"SourceFile": "customer\\dec\\discound_show .Designer.vb"}, {"SourceFile": "customer\\dec\\discound_show .vb"}, {"SourceFile": "customer\\Statistic_customer.Designer.vb"}, {"SourceFile": "customer\\Statistic_customer.vb"}, {"SourceFile": "customer\\tax\\tax_customer.Designer.vb"}, {"SourceFile": "customer\\tax\\tax_customer.vb"}, {"SourceFile": "customer\\dec\\descound_customer.Designer.vb"}, {"SourceFile": "customer\\dec\\descound_customer.vb"}, {"SourceFile": "customer\\customer_Detailed.Designer.vb"}, {"SourceFile": "customer\\customer_Detailed.vb"}, {"SourceFile": "customer\\trans\\cus_trans_show.Designer.vb"}, {"SourceFile": "customer\\trans\\cus_trans_show.vb"}, {"SourceFile": "Expenses\\Expenses_add2.Designer.vb"}, {"SourceFile": "Expenses\\Expenses_add2.vb"}, {"SourceFile": "Expenses\\Expenses_show2.Designer.vb"}, {"SourceFile": "Expenses\\Expenses_show2.vb"}, {"SourceFile": "Expenses\\Expense_item2.Designer.vb"}, {"SourceFile": "Expenses\\Expense_item2.vb"}, {"SourceFile": "Expenses\\Revenues_show.Designer.vb"}, {"SourceFile": "Expenses\\Revenues_show.vb"}, {"SourceFile": "Expenses\\Revenues_add.Designer.vb"}, {"SourceFile": "Expenses\\Revenues_add.vb"}, {"SourceFile": "Expenses\\Revenues_item.Designer.vb"}, {"SourceFile": "Expenses\\Revenues_item.vb"}, {"SourceFile": "Expenses\\Expense_item.Designer.vb"}, {"SourceFile": "Expenses\\Expense_item.vb"}, {"SourceFile": "Expenses\\Expenses_add.Designer.vb"}, {"SourceFile": "Expenses\\Expenses_add.vb"}, {"SourceFile": "Expenses\\Expenses_show.Designer.vb"}, {"SourceFile": "Expenses\\Expenses_show.vb"}, {"SourceFile": "frmdatabase.designer.vb"}, {"SourceFile": "frmdatabase.vb"}, {"SourceFile": "importer\\desc\\discound_show_imp.Designer.vb"}, {"SourceFile": "importer\\desc\\discound_show_imp.vb"}, {"SourceFile": "importer\\importer_edit.Designer.vb"}, {"SourceFile": "importer\\importer_edit.vb"}, {"SourceFile": "importer\\imp_cash\\cashing_imp_show.Designer.vb"}, {"SourceFile": "importer\\imp_cash\\cashing_imp_show.vb"}, {"SourceFile": "importer\\importer_export.Designer.vb"}, {"SourceFile": "importer\\importer_export.vb"}, {"SourceFile": "importer\\imp_cash\\cashing_importer.Designer.vb"}, {"SourceFile": "importer\\imp_cash\\cashing_importer.vb"}, {"SourceFile": "importer\\trans\\imp_trans_show.Designer.vb"}, {"SourceFile": "importer\\trans\\imp_trans_show.vb"}, {"SourceFile": "invoice\\invoice_bac.Designer.vb"}, {"SourceFile": "invoice\\invoice_bac.vb"}, {"SourceFile": "invoice\\invoice_back_show.Designer.vb"}, {"SourceFile": "invoice\\invoice_back_show.vb"}, {"SourceFile": "invoice\\order_item.Designer.vb"}, {"SourceFile": "invoice\\order_item.vb"}, {"SourceFile": "invoice\\pos\\pos_add.Designer.vb"}, {"SourceFile": "invoice\\pos\\pos_add.vb"}, {"SourceFile": "invoice\\pos\\pos_add2.Designer.vb"}, {"SourceFile": "invoice\\pos\\pos_add2.vb"}, {"SourceFile": "invoice\\عرض اسعار\\suplier_show.Designer.vb"}, {"SourceFile": "invoice\\عرض اسعار\\suplier_show.vb"}, {"SourceFile": "invoice\\عرض اسعار\\suplier_add.Designer.vb"}, {"SourceFile": "invoice\\عرض اسعار\\suplier_add.vb"}, {"SourceFile": "invoice\\فاتورة حجز\\Reservation_show.Designer.vb"}, {"SourceFile": "invoice\\فاتورة حجز\\Reservation_show.vb"}, {"SourceFile": "invoice\\فاتورة حجز\\Reservation_add.Designer.vb"}, {"SourceFile": "invoice\\فاتورة حجز\\Reservation_add.vb"}, {"SourceFile": "item\\itemReport1.designer.vb"}, {"SourceFile": "item\\itemReport1.vb"}, {"SourceFile": "item\\item_export.Designer.vb"}, {"SourceFile": "item\\item_export.vb"}, {"SourceFile": "item\\item_edit.Designer.vb"}, {"SourceFile": "item\\item_edit.vb"}, {"SourceFile": "item\\barcode_export.Designer.vb"}, {"SourceFile": "item\\barcode_export.vb"}, {"SourceFile": "item\\item_unit.Designer.vb"}, {"SourceFile": "item\\item_unit.vb"}, {"SourceFile": "item\\اعداد الصنف\\serial_item.Designer.vb"}, {"SourceFile": "item\\اعداد الصنف\\serial_item.vb"}, {"SourceFile": "item\\اعداد الصنف\\expire_item.Designer.vb"}, {"SourceFile": "item\\اعداد الصنف\\expire_item.vb"}, {"SourceFile": "item\\حركة صنف\\item_trans_show.Designer.vb"}, {"SourceFile": "item\\حركة صنف\\item_trans_show.vb"}, {"SourceFile": "item\\مسحوبات\\Drawings_item.Designer.vb"}, {"SourceFile": "item\\مسحوبات\\Drawings_item.vb"}, {"SourceFile": "invoice\\item_bac.Designer.vb"}, {"SourceFile": "invoice\\item_bac.vb"}, {"SourceFile": "Purchases\\طلبية مشتريات\\Purchases_order.Designer.vb"}, {"SourceFile": "Purchases\\طلبية مشتريات\\Purchases_order.vb"}, {"SourceFile": "Purchases\\طلبية مشتريات\\order_show.Designer.vb"}, {"SourceFile": "Purchases\\طلبية مشتريات\\order_show.vb"}, {"SourceFile": "Purchases\\مرتجع المشتريات\\Purchases_back.Designer.vb"}, {"SourceFile": "Purchases\\مرتجع المشتريات\\Purchases_back.vb"}, {"SourceFile": "Purchases\\مرتجع المشتريات\\Purchases_back_show.Designer.vb"}, {"SourceFile": "Purchases\\مرتجع المشتريات\\Purchases_back_show.vb"}, {"SourceFile": "ready.Designer.vb"}, {"SourceFile": "ready.vb"}, {"SourceFile": "report\\item\\item_earn2.Designer.vb"}, {"SourceFile": "report\\item\\item_earn2.vb"}, {"SourceFile": "report\\item\\item_earn1.Designer.vb"}, {"SourceFile": "report\\item\\item_earn1.vb"}, {"SourceFile": "report\\item\\item_expire.Designer.vb"}, {"SourceFile": "report\\item\\item_expire.vb"}, {"SourceFile": "report\\التقارير\\cus_trnas_print_a4.Designer.vb"}, {"SourceFile": "report\\التقارير\\cus_trnas_print_a4.vb"}, {"SourceFile": "report\\الحسابات\\Company_evaluation.Designer.vb"}, {"SourceFile": "report\\الحسابات\\Company_evaluation.vb"}, {"SourceFile": "report\\الحسابات\\express_all3.Designer.vb"}, {"SourceFile": "report\\الحسابات\\express_all3.vb"}, {"SourceFile": "report\\الحسابات\\express_all2.Designer.vb"}, {"SourceFile": "report\\الحسابات\\express_all2.vb"}, {"SourceFile": "report\\الحسابات\\General_budget.Designer.vb"}, {"SourceFile": "report\\الحسابات\\General_budget.vb"}, {"SourceFile": "report\\الحسابات\\income_list.Designer.vb"}, {"SourceFile": "report\\الحسابات\\income_list.vb"}, {"SourceFile": "report\\الحسابات\\Balance_Review.Designer.vb"}, {"SourceFile": "report\\الحسابات\\Balance_Review.vb"}, {"SourceFile": "report\\الحسابات\\Asset_report.Designer.vb"}, {"SourceFile": "report\\الحسابات\\Asset_report.vb"}, {"SourceFile": "report\\الحسابات\\asstet_all.Designer.vb"}, {"SourceFile": "report\\الحسابات\\asstet_all.vb"}, {"SourceFile": "setting\\format_data.Designer.vb"}, {"SourceFile": "setting\\format_data.vb"}, {"SourceFile": "setting\\Form2.Designer.vb"}, {"SourceFile": "setting\\Form2.vb"}, {"SourceFile": "importer\\importer_Detailed.Designer.vb"}, {"SourceFile": "importer\\importer_Detailed.vb"}, {"SourceFile": "importer\\desc\\descound_importer.Designer.vb"}, {"SourceFile": "importer\\desc\\descound_importer.vb"}, {"SourceFile": "importer\\importer_show.Designer.vb"}, {"SourceFile": "importer\\importer_show.vb"}, {"SourceFile": "importer\\importer_add2.Designer.vb"}, {"SourceFile": "importer\\importer_add2.vb"}, {"SourceFile": "customer\\tax\\tax_show.Designer.vb"}, {"SourceFile": "customer\\tax\\tax_show.vb"}, {"SourceFile": "customer\\cashing\\cashing_customer.Designer.vb"}, {"SourceFile": "customer\\cashing\\cashing_customer.vb"}, {"SourceFile": "customer\\cashing\\cashing_show.Designer.vb"}, {"SourceFile": "customer\\cashing\\cashing_show.vb"}, {"SourceFile": "customer\\customer_add.Designer.vb"}, {"SourceFile": "customer\\customer_add.vb"}, {"SourceFile": "customer\\customer_show.Designer.vb"}, {"SourceFile": "customer\\customer_show.vb"}, {"SourceFile": "importer\\TAX\\tax_importer.Designer.vb"}, {"SourceFile": "importer\\TAX\\tax_importer.vb"}, {"SourceFile": "importer\\TAX\\taximp_show.Designer.vb"}, {"SourceFile": "importer\\TAX\\taximp_show.vb"}, {"SourceFile": "invoice\\invoice_add.Designer.vb"}, {"SourceFile": "invoice\\invoice_add.vb"}, {"SourceFile": "invoice\\invoice_show.Designer.vb"}, {"SourceFile": "invoice\\invoice_show.vb"}, {"SourceFile": "item\\price_item.Designer.vb"}, {"SourceFile": "item\\price_item.vb"}, {"SourceFile": "item\\Alternative_item.Designer.vb"}, {"SourceFile": "item\\Alternative_item.vb"}, {"SourceFile": "item\\item_Detailed.Designer.vb"}, {"SourceFile": "item\\item_Detailed.vb"}, {"SourceFile": "item\\item_add.Designer.vb"}, {"SourceFile": "item\\item_add.vb"}, {"SourceFile": "item\\مسحوبات\\Drawings_show.Designer.vb"}, {"SourceFile": "item\\مسحوبات\\Drawings_show.vb"}, {"SourceFile": "item\\Statistic_item.Designer.vb"}, {"SourceFile": "item\\Statistic_item.vb"}, {"SourceFile": "item\\تحويلات\\trans_show.Designer.vb"}, {"SourceFile": "item\\تحويلات\\trans_show.vb"}, {"SourceFile": "item\\تحويلات\\trans_item.Designer.vb"}, {"SourceFile": "item\\تحويلات\\trans_item.vb"}, {"SourceFile": "item\\جرد\\inventory_show.Designer.vb"}, {"SourceFile": "item\\جرد\\inventory_show.vb"}, {"SourceFile": "item\\توالف\\damage_show.Designer.vb"}, {"SourceFile": "item\\توالف\\damage_show.vb"}, {"SourceFile": "item\\توالف\\damage_item.Designer.vb"}, {"SourceFile": "item\\توالف\\damage_item.vb"}, {"SourceFile": "item\\جرد\\inventory_item.Designer.vb"}, {"SourceFile": "item\\جرد\\inventory_item.vb"}, {"SourceFile": "item\\item_show.Designer.vb"}, {"SourceFile": "item\\item_show.vb"}, {"SourceFile": "main_page.Designer.vb"}, {"SourceFile": "main_page.vb"}, {"SourceFile": "item\\NewFolder1\\Treasury_add.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\Treasury_add.vb"}, {"SourceFile": "item\\NewFolder1\\set_store.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\set_store.vb"}, {"SourceFile": "preview.Designer.vb"}, {"SourceFile": "preview.vb"}, {"SourceFile": "Purchases\\Purchases_add.Designer.vb"}, {"SourceFile": "Purchases\\Purchases_add.vb"}, {"SourceFile": "report\\cus\\الشيكات\\check_chashing.Designer.vb"}, {"SourceFile": "report\\cus\\الشيكات\\check_chashing.vb"}, {"SourceFile": "report\\cus\\الشيكات\\check_at_let.Designer.vb"}, {"SourceFile": "report\\cus\\الشيكات\\check_at_let.vb"}, {"SourceFile": "report\\cus\\الشيكات\\check_late.Designer.vb"}, {"SourceFile": "report\\cus\\الشيكات\\check_late.vb"}, {"SourceFile": "report\\cus\\dele_litle_earn.Designer.vb"}, {"SourceFile": "report\\cus\\dele_litle_earn.vb"}, {"SourceFile": "report\\cus\\dele_best_earn.Designer.vb"}, {"SourceFile": "report\\cus\\dele_best_earn.vb"}, {"SourceFile": "report\\cus\\dele_cus_earn.Designer.vb"}, {"SourceFile": "report\\cus\\dele_cus_earn.vb"}, {"SourceFile": "report\\cus\\gov_cus_earn.Designer.vb"}, {"SourceFile": "report\\cus\\gov_cus_earn.vb"}, {"SourceFile": "report\\cus\\gov_litle_earn.Designer.vb"}, {"SourceFile": "report\\cus\\gov_litle_earn.vb"}, {"SourceFile": "report\\cus\\gov_best_earn.Designer.vb"}, {"SourceFile": "report\\cus\\gov_best_earn.vb"}, {"SourceFile": "report\\cus\\city_litle_earn.Designer.vb"}, {"SourceFile": "report\\cus\\city_litle_earn.vb"}, {"SourceFile": "report\\cus\\cus_item.Designer.vb"}, {"SourceFile": "report\\cus\\cus_item.vb"}, {"SourceFile": "report\\cus\\city_best_earn.Designer.vb"}, {"SourceFile": "report\\cus\\city_best_earn.vb"}, {"SourceFile": "report\\cus\\group_litle_earn.Designer.vb"}, {"SourceFile": "report\\cus\\group_litle_earn.vb"}, {"SourceFile": "report\\cus\\group_best_earn.Designer.vb"}, {"SourceFile": "report\\cus\\group_best_earn.vb"}, {"SourceFile": "report\\cus\\cus_litleearn.Designer.vb"}, {"SourceFile": "report\\cus\\cus_litleearn.vb"}, {"SourceFile": "report\\cus\\cus_bestearn.Designer.vb"}, {"SourceFile": "report\\cus\\cus_bestearn.vb"}, {"SourceFile": "report\\cus\\cus_minus.Designer.vb"}, {"SourceFile": "report\\cus\\cus_minus.vb"}, {"SourceFile": "report\\cus\\cus_Balance.Designer.vb"}, {"SourceFile": "report\\cus\\cus_Balance.vb"}, {"SourceFile": "report\\cus\\cus_balace.Designer.vb"}, {"SourceFile": "report\\cus\\cus_balace.vb"}, {"SourceFile": "report\\cus\\group_cus_earn.Designer.vb"}, {"SourceFile": "report\\cus\\group_cus_earn.vb"}, {"SourceFile": "report\\cus\\Statistic_cus_earn.Designer.vb"}, {"SourceFile": "report\\cus\\Statistic_cus_earn.vb"}, {"SourceFile": "report\\cus\\city_cus_earn.Designer.vb"}, {"SourceFile": "report\\cus\\city_cus_earn.vb"}, {"SourceFile": "report\\imp\\imp_item.Designer.vb"}, {"SourceFile": "report\\imp\\imp_item.vb"}, {"SourceFile": "report\\imp\\imp_minus.Designer.vb"}, {"SourceFile": "report\\imp\\imp_minus.vb"}, {"SourceFile": "report\\imp\\imp_Balance.Designer.vb"}, {"SourceFile": "report\\imp\\imp_Balance.vb"}, {"SourceFile": "report\\imp\\imp_balace.Designer.vb"}, {"SourceFile": "report\\imp\\imp_balace.vb"}, {"SourceFile": "report\\imp\\شيكات\\imp_check_late.Designer.vb"}, {"SourceFile": "report\\imp\\شيكات\\imp_check_late.vb"}, {"SourceFile": "report\\imp\\شيكات\\imp_check_chashing.Designer.vb"}, {"SourceFile": "report\\imp\\شيكات\\imp_check_chashing.vb"}, {"SourceFile": "report\\imp\\شيكات\\imp_check_at_let.Designer.vb"}, {"SourceFile": "report\\imp\\شيكات\\imp_check_at_let.vb"}, {"SourceFile": "report\\invo\\bac\\1\\all_back_inv.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\all_back_inv.vb"}, {"SourceFile": "report\\invo\\bac\\1\\day_back_inv.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\day_back_inv.vb"}, {"SourceFile": "report\\invo\\bac\\1\\dele_best_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\dele_best_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\1\\dele_cus_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\dele_cus_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\1\\month_back_inv.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\month_back_inv.vb"}, {"SourceFile": "report\\invo\\bac\\1\\Statistic_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\Statistic_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\1\\year_back_inv.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\1\\year_back_inv.vb"}, {"SourceFile": "report\\invo\\bac\\2\\city_best_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\city_best_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\city_cus_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\city_cus_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\gov_best_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\gov_best_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\gov_cus_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\gov_cus_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\group_best_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\group_best_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\group_cus_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\group_cus_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\stoer_best_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\stoer_best_invoice_back.vb"}, {"SourceFile": "report\\invo\\bac\\2\\store_cus_invoice_back.Designer.vb"}, {"SourceFile": "report\\invo\\bac\\2\\store_cus_invoice_back.vb"}, {"SourceFile": "report\\invo\\inv\\1\\all_inv.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\all_inv.vb"}, {"SourceFile": "report\\invo\\inv\\1\\store_cus_earn.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\store_cus_earn.vb"}, {"SourceFile": "report\\invo\\inv\\1\\Statistic_earn.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\Statistic_earn.vb"}, {"SourceFile": "report\\invo\\inv\\1\\Statistic_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\Statistic_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\1\\year_inv.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\year_inv.vb"}, {"SourceFile": "report\\invo\\inv\\1\\month_inv.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\month_inv.vb"}, {"SourceFile": "report\\invo\\inv\\1\\day_inv.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\1\\day_inv.vb"}, {"SourceFile": "report\\invo\\inv\\2\\city_best_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\city_best_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\city_cus_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\city_cus_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\dele_best_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\dele_best_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\dele_cus_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\dele_cus_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\gov_best_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\gov_best_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\gov_cus_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\gov_cus_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\group_best_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\group_best_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\group_cus_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\group_cus_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\stoer_best_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\stoer_best_invoice.vb"}, {"SourceFile": "report\\invo\\inv\\2\\store_cus_invoice.Designer.vb"}, {"SourceFile": "report\\invo\\inv\\2\\store_cus_invoice.vb"}, {"SourceFile": "report\\item\\cat\\catoregy_litle_earn.Designer.vb"}, {"SourceFile": "report\\item\\cat\\catoregy_litle_earn.vb"}, {"SourceFile": "report\\item\\cat\\catoregy_best_earn.Designer.vb"}, {"SourceFile": "report\\item\\cat\\catoregy_best_earn.vb"}, {"SourceFile": "report\\item\\cat\\catoregy_earn.Designer.vb"}, {"SourceFile": "report\\item\\cat\\catoregy_earn.vb"}, {"SourceFile": "report\\item\\comp\\company_litle_earn.Designer.vb"}, {"SourceFile": "report\\item\\comp\\company_litle_earn.vb"}, {"SourceFile": "report\\item\\comp\\company_best_earn.Designer.vb"}, {"SourceFile": "report\\item\\comp\\company_best_earn.vb"}, {"SourceFile": "report\\item\\comp\\company_earn.Designer.vb"}, {"SourceFile": "report\\item\\comp\\company_earn.vb"}, {"SourceFile": "report\\item\\group\\catoregy_earn.Designer.vb"}, {"SourceFile": "report\\item\\group\\catoregy_earn.vb"}, {"SourceFile": "report\\item\\group\\grou_litle_earn.Designer.vb"}, {"SourceFile": "report\\item\\group\\grou_litle_earn.vb"}, {"SourceFile": "report\\item\\group\\grou_best_earn.Designer.vb"}, {"SourceFile": "report\\item\\group\\grou_best_earn.vb"}, {"SourceFile": "report\\item\\item_stagnet.Designer.vb"}, {"SourceFile": "report\\item\\item_stagnet.vb"}, {"SourceFile": "report\\item\\item_earn.Designer.vb"}, {"SourceFile": "report\\item\\item_earn.vb"}, {"SourceFile": "report\\item\\damage_list.Designer.vb"}, {"SourceFile": "report\\item\\damage_list.vb"}, {"SourceFile": "report\\item\\Drawings_list.Designer.vb"}, {"SourceFile": "report\\item\\Drawings_list.vb"}, {"SourceFile": "report\\item\\item_bad.Designer.vb"}, {"SourceFile": "report\\item\\item_bad.vb"}, {"SourceFile": "report\\item\\section\\sec_litle_earn2.Designer.vb"}, {"SourceFile": "report\\item\\section\\sec_litle_earn2.vb"}, {"SourceFile": "report\\item\\section\\sec_best_earn.Designer.vb"}, {"SourceFile": "report\\item\\section\\sec_best_earn.vb"}, {"SourceFile": "report\\item\\section\\sec_earn.Designer.vb"}, {"SourceFile": "report\\item\\section\\sec_earn.vb"}, {"SourceFile": "report\\item\\trans_list.Designer.vb"}, {"SourceFile": "report\\item\\trans_list.vb"}, {"SourceFile": "report\\item\\item_inventory.Designer.vb"}, {"SourceFile": "report\\item\\item_inventory.vb"}, {"SourceFile": "report\\item\\item_Purchases.Designer.vb"}, {"SourceFile": "report\\item\\item_Purchases.vb"}, {"SourceFile": "report\\item\\item_invoice.Designer.vb"}, {"SourceFile": "report\\item\\item_invoice.vb"}, {"SourceFile": "report\\item\\item_minus.Designer.vb"}, {"SourceFile": "report\\item\\item_minus.vb"}, {"SourceFile": "report\\item\\item_zero.Designer.vb"}, {"SourceFile": "report\\item\\item_zero.vb"}, {"SourceFile": "report\\item\\item_Deficiencies.Designer.vb"}, {"SourceFile": "report\\item\\item_Deficiencies.vb"}, {"SourceFile": "report\\Pur\\back\\all_pur_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\all_pur_back.vb"}, {"SourceFile": "report\\Pur\\back\\day_pur_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\day_pur_back.vb"}, {"SourceFile": "report\\Pur\\back\\month_pur_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\month_pur_back.vb"}, {"SourceFile": "report\\Pur\\back\\Statistic_Purchases_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\Statistic_Purchases_back.vb"}, {"SourceFile": "report\\Pur\\back\\stoer_best_Purchases_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\stoer_best_Purchases_back.vb"}, {"SourceFile": "report\\Pur\\back\\store_cus_invoice_Purchases_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\store_cus_invoice_Purchases_back.vb"}, {"SourceFile": "report\\Pur\\back\\year_pur_back.Designer.vb"}, {"SourceFile": "report\\Pur\\back\\year_pur_back.vb"}, {"SourceFile": "report\\Pur\\pur\\all_pur.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\all_pur.vb"}, {"SourceFile": "report\\Pur\\pur\\Statistic_Purchases.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\Statistic_Purchases.vb"}, {"SourceFile": "report\\Pur\\pur\\stoer_best_Purchases.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\stoer_best_Purchases.vb"}, {"SourceFile": "report\\Pur\\pur\\store_cus_invoice_Purchases.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\store_cus_invoice_Purchases.vb"}, {"SourceFile": "report\\Pur\\pur\\year_pur.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\year_pur.vb"}, {"SourceFile": "report\\Pur\\pur\\month_pur.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\month_pur.vb"}, {"SourceFile": "report\\Pur\\pur\\day_pur.Designer.vb"}, {"SourceFile": "report\\Pur\\pur\\day_pur.vb"}, {"SourceFile": "report\\report_show.Designer.vb"}, {"SourceFile": "report\\report_show.vb"}, {"SourceFile": "report\\الحسابات\\balancereview.Designer.vb"}, {"SourceFile": "report\\الحسابات\\balancereview.vb"}, {"SourceFile": "report\\الحسابات\\user_cus_earn.Designer.vb"}, {"SourceFile": "report\\الحسابات\\user_cus_earn.vb"}, {"SourceFile": "report\\الحسابات\\user_litle_earn.Designer.vb"}, {"SourceFile": "report\\الحسابات\\user_litle_earn.vb"}, {"SourceFile": "report\\الحسابات\\user_best_earn.Designer.vb"}, {"SourceFile": "report\\الحسابات\\user_best_earn.vb"}, {"SourceFile": "report\\الحسابات\\express_all.Designer.vb"}, {"SourceFile": "report\\الحسابات\\express_all.vb"}, {"SourceFile": "setting\\frmbarcode.designer.vb"}, {"SourceFile": "setting\\frmbarcode.vb"}, {"SourceFile": "setting\\help_system.Designer.vb"}, {"SourceFile": "setting\\help_system.vb"}, {"SourceFile": "setting\\start.Designer.vb"}, {"SourceFile": "setting\\start.vb"}, {"SourceFile": "setting\\user\\active_pro.Designer.vb"}, {"SourceFile": "setting\\user\\active_pro.vb"}, {"SourceFile": "setting\\user\\user_reg.Designer.vb"}, {"SourceFile": "setting\\user\\user_reg.vb"}, {"SourceFile": "setting\\user\\Power_user.Designer.vb"}, {"SourceFile": "setting\\user\\Power_user.vb"}, {"SourceFile": "setting\\recovey.Designer.vb"}, {"SourceFile": "setting\\recovey.vb"}, {"SourceFile": "setting\\login.Designer.vb"}, {"SourceFile": "setting\\login.vb"}, {"SourceFile": "main.Designer.vb"}, {"SourceFile": "main.vb"}, {"SourceFile": "الحسابات\\Destruction_ethol.Designer.vb"}, {"SourceFile": "الحسابات\\Destruction_ethol.vb"}, {"SourceFile": "الحسابات\\partners_show.Designer.vb"}, {"SourceFile": "الحسابات\\partners_show.vb"}, {"SourceFile": "الحسابات\\Partners_add.Designer.vb"}, {"SourceFile": "الحسابات\\Partners_add.vb"}, {"SourceFile": "الحسابات\\Fixed_assets.Designer.vb"}, {"SourceFile": "الحسابات\\Fixed_assets.vb"}, {"SourceFile": "الحسابات\\dele_money.Designer.vb"}, {"SourceFile": "الحسابات\\dele_money.vb"}, {"SourceFile": "الحسابات\\الضربية\\detial_tax.Designer.vb"}, {"SourceFile": "الحسابات\\الضربية\\detial_tax.vb"}, {"SourceFile": "الحسابات\\الضربية\\pay_tax_show.Designer.vb"}, {"SourceFile": "الحسابات\\الضربية\\pay_tax_show.vb"}, {"SourceFile": "الحسابات\\الضربية\\pay_tax.Designer.vb"}, {"SourceFile": "الحسابات\\الضربية\\pay_tax.vb"}, {"SourceFile": "الحسابات\\حزينة\\trans_money.Designer.vb"}, {"SourceFile": "الحسابات\\حزينة\\trans_money.vb"}, {"SourceFile": "الحسابات\\حزينة\\Withdrawal.Designer.vb"}, {"SourceFile": "الحسابات\\حزينة\\Withdrawal.vb"}, {"SourceFile": "الحسابات\\حزينة\\Deposit.Designer.vb"}, {"SourceFile": "الحسابات\\حزينة\\Deposit.vb"}, {"SourceFile": "الحسابات\\حزينة\\Treasury.Designer.vb"}, {"SourceFile": "الحسابات\\حزينة\\Treasury.vb"}, {"SourceFile": "setting\\backup.Designer.vb"}, {"SourceFile": "setting\\backup.vb"}, {"SourceFile": "Module1.vb"}, {"SourceFile": "Module2.vb"}, {"SourceFile": "My Project\\AssemblyInfo.vb"}, {"SourceFile": "My Project\\Application.Designer.vb"}, {"SourceFile": "My Project\\Resources.Designer.vb"}, {"SourceFile": "My Project\\Settings.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\company.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\company.vb"}, {"SourceFile": "item\\NewFolder1\\whada.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\whada.vb"}, {"SourceFile": "item\\NewFolder1\\Category.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\Category.vb"}, {"SourceFile": "customer\\setting\\Governorate.Designer.vb"}, {"SourceFile": "customer\\setting\\Governorate.vb"}, {"SourceFile": "customer\\setting\\city.Designer.vb"}, {"SourceFile": "customer\\setting\\city.vb"}, {"SourceFile": "item\\NewFolder1\\contact.Designer.vb"}, {"SourceFile": "item\\NewFolder1\\contact.vb"}, {"SourceFile": "customer\\setting\\group.Designer.vb"}, {"SourceFile": "customer\\setting\\group.vb"}, {"SourceFile": "customer\\setting\\delegate.Designer.vb"}, {"SourceFile": "customer\\setting\\delegate.vb"}, {"SourceFile": "setting\\setting_fatora.Designer.vb"}, {"SourceFile": "setting\\setting_fatora.vb"}, {"SourceFile": "setting\\user\\users.Designer.vb"}, {"SourceFile": "setting\\user\\users.vb"}, {"SourceFile": "Purchases\\Purchases_show.Designer.vb"}, {"SourceFile": "Purchases\\Purchases_show.vb"}, {"SourceFile": "الموظفين\\Attending_leaving.Designer.vb"}, {"SourceFile": "الموظفين\\Attending_leaving.vb"}, {"SourceFile": "الموظفين\\cashing_emp.Designer.vb"}, {"SourceFile": "الموظفين\\cashing_emp.vb"}, {"SourceFile": "الموظفين\\Attending_show.Designer.vb"}, {"SourceFile": "الموظفين\\Attending_show.vb"}, {"SourceFile": "الموظفين\\Employee_show.Designer.vb"}, {"SourceFile": "الموظفين\\Employee_show.vb"}, {"SourceFile": "الموظفين\\Employee_add.Designer.vb"}, {"SourceFile": "الموظفين\\Employee_add.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\holiday_show.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\holiday_show.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\holiday.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\holiday.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Discounts_show.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Discounts_show.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Discounts.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Discounts.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Rewards_show.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Rewards_show.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Rewards.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Rewards.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\ancestor.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\ancestor.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\ancestor_show.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\ancestor_show.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\emp_Functioned.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\emp_Functioned.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Rosacea.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\Rosacea.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\emp_Section.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\emp_Section.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\emp_manged.Designer.vb"}, {"SourceFile": "الموظفين\\NewFolder1\\emp_manged.vb"}, {"SourceFile": "الموظفين\\cashing_emp_show.Designer.vb"}, {"SourceFile": "الموظفين\\cashing_emp_show.vb"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.5.2.AssemblyAttributes.vb"}], "References": [{"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.BonusSkins.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Charts.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.CodeParser.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Data.Desktop.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Data.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.DataAccess.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Images.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Office.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Pdf.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.PivotGrid.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Printing.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.RichEdit.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.RichEdit.v20.1.Export.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Sparkline.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Utils.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Utils.v20.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.Xpo.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraBars.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraCharts.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraCharts.v20.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraCharts.v20.1.Wizard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraEditors.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraGauges.v20.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraGrid.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraLayout.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraNavBar.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraPrinting.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraReports.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraReports.v20.1.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\DevExpress.XtraVerticalGrid.v20.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft SQL Server\\120\\SDK\\Assemblies\\Microsoft.SqlServer.ConnectionInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft SQL Server\\120\\SDK\\Assemblies\\Microsoft.SqlServer.ServiceBrokerEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft SQL Server\\120\\SDK\\Assemblies\\Microsoft.SqlServer.Smo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft SQL Server\\120\\SDK\\Assemblies\\Microsoft.SqlServer.SmoExtended.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft SQL Server\\120\\SDK\\Assemblies\\Microsoft.SqlServer.SqlEnum.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\MicroVisionPCID.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\MicroVisionSerial.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.ComponentModel.DataAnnotations\\v4.0_4.0.0.0__31bf3856ad364e35\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Core\\v4.0_4.0.0.0__b77a5c561934e089\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Data.DataSetExtensions\\v4.0_4.0.0.0__b77a5c561934e089\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_32\\System.Data\\v4.0_4.0.0.0__b77a5c561934e089\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Data.Linq\\v4.0_4.0.0.0__b77a5c561934e089\\System.Data.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Deployment\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System\\v4.0_4.0.0.0__b77a5c561934e089\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Drawing\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Management\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_32\\System.Web\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Web.Services\\v4.0_4.0.0.0__b03f5f7f11d50a3a\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Windows.Forms\\v4.0_4.0.0.0__b77a5c561934e089\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Xml\\v4.0_4.0.0.0__b77a5c561934e089\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\System.Xml.Linq\\v4.0_4.0.0.0__b77a5c561934e089\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\spaxet store.exe", "OutputItemRelativePath": "spaxet store.exe"}, {"OutputItemFullPath": "E:\\مبيعات 2022\\New folder\\bin\\Debug\\spaxet store.pdb", "OutputItemRelativePath": "spaxet store.pdb"}], "CopyToOutputEntries": []}