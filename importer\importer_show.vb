﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors.XtraMessageBox

Public Class importer_show
    Private ctr As Integer = 0
    Sub fill_dgv()
        Dim adp As New SqlDataAdapter("select * from importer", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt

        Dim gridFormatRule As New GridFormatRule()
        Dim formatConditionRuleDataBar As New FormatConditionRuleDataBar()
        gridFormatRule.Column = Me.GridColumn7
        formatConditionRuleDataBar.PredefinedName = "Blue Gradient"
        gridFormatRule.Rule = formatConditionRuleDataBar
        Me.GridView2.FormatRules.Add(gridFormatRule)
    End Sub
    Private Sub customer_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        fill_dgv()
        view_grid.SelectedIndex = 0

        For i = 0 To GridView2.RowCount - 1
            Try
                Dim str = "select sum(imptrans_Debtor), sum(imptrans_Creditor) from importer_trans where imptomer_name=N'" & GridView2.GetRowCellValue(i, "impname").ToString & "'"
                Dim cmd As New SqlCommand(str, sqlconn)
                Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
                Dim dta As New DataTable("importer_trans")
                da.Fill(dta)
                Dim sumdebit As Decimal = 0.0
                Dim sumcredit As Decimal = 0.0
                If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
                If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
                GridView2.SetRowCellValue(i, "Accounts_balace", Math.Round(Format(Val(sumdebit), "#,0.00") - Format(Val(sumcredit), "#,0.00"), 2))

            Catch ex As Exception

            End Try
           
        Next
        save_try()
    End Sub

    Private Sub save_try()
        For i = 0 To GridView2.RowCount - 1
            Try
                Dim sql = "select (impcode),(impname),(Accounts_balace) from importer where impname=N'" & GridView2.GetRowCellValue(i, "impname").ToString & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)

                Dim dr = dt.Rows(0)
                '========= بيانات اساسية============
                dr!Accounts_balace = Val(GridView2.GetRowCellValue(i, "Accounts_balace").ToString)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

            Catch ex As Exception

            End Try
        Next

    End Sub
    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        Dim f As importer_add2 = New importer_add2()
        f.Text = "اضافة مورد"
        f.new_imp()
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub delete_btn_Click(sender As Object, e As EventArgs) Handles delete_btn.Click
        If XtraForm1.m36.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد مورد للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If XtraMessageBox.Show("يوجد تعاملات سابقة مع هذا المورد هل انت متاكد من حذفة ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then

            Dim sql = "select * from importer where impcode=N'" & GridView2.GetFocusedRowCellValue("impcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                delete()
                XtraMessageBox.Show("تم حذف المورد بنجاح")
                fill_dgv()
            End If
        End If
    End Sub
    Sub delete()
        For i2 = 0 To 1000
            Dim sql = "select * from importer_trans where imptomer_name=N'" & GridView2.GetFocusedRowCellValue("impname") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If XtraForm1.m35.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد مورد للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As importer_add2 = New importer_add2()
        f.Text = "تسجيل مورد"
        f.new_imp()
        f.MdiParent = XtraForm1
        f.show_data(GridView2.GetFocusedRowCellValue("impcode"))
        f.Show()
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub
    Private Sub tans_btn_Click(sender As Object, e As EventArgs) Handles tans_btn.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد مورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        imp_trans_show.ShowDialog()
    End Sub

    Private Sub view_grid_SelectedIndexChanged(sender As Object, e As EventArgs) Handles view_grid.SelectedIndexChanged
        If view_grid.SelectedIndex = 0 Then
            dgv.MainView = GridView2
        ElseIf view_grid.SelectedIndex = 1 Then
            dgv.MainView = CardView1
        ElseIf view_grid.SelectedIndex = 2 Then
            dgv.MainView = LayoutView1
        End If
    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub
  

    Private Sub Cashing_btn_Click(sender As Object, e As EventArgs) Handles Cashing_btn.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد مورد لاستلام النقدية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_importer = New cashing_importer()
        f.Text = "سند صرف"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Text = GridView2.GetFocusedRowCellValue("impname")
    End Sub

    Private Sub report_btn_Click(sender As Object, e As EventArgs) Handles report_btn.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد مورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As importer_Detailed = New importer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = XtraForm1
        f.Show()
        f.importername.Text = GridView2.GetFocusedRowCellValue("impname")
        f.search()
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles W9.Click
        Me.Dispose()
    End Sub
    Private Sub W1_Click(sender As Object, e As EventArgs) Handles W1.Click
        new_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W2_Click(sender As Object, e As EventArgs) Handles W2.Click
        edit_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W3_Click(sender As Object, e As EventArgs) Handles W3.Click
        delete_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W4_Click(sender As Object, e As EventArgs) Handles W4.Click
        Cashing_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W5_Click(sender As Object, e As EventArgs) Handles W5.Click
        print_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W6_Click(sender As Object, e As EventArgs) Handles W6.Click
        tans_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W7_Click(sender As Object, e As EventArgs) Handles W7.Click
        report_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub W8_Click(sender As Object, e As EventArgs) Handles W8.Click
        fill_dgv()
    End Sub

    Private Sub importer_show_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            w1_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            w2_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            w3_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            w4_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F5 Then
            w5_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F6 Then
            w6_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F7 Then
            w7_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F8 Then
            w8_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        Dim f As importer_edit = New importer_edit()
        f.Text = "التعديل السريع"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub Button11_Click(sender As Object, e As EventArgs) Handles Button11.Click
        GridView2.ShowCustomization()
    End Sub
End Class