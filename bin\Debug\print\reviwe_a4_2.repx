﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.invoice_a4_2, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="invoice_a4_2" Margins="0, 1, 0, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="1.041667" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="75.08335">
      <Controls>
        <Item1 Ref="6" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="MiddleCenter" SizeF="329.4815,31.33333" LocationFloat="463.5085, 2.249988" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="MiddleCenter" SizeF="329.4815,41.04168" LocationFloat="463.5085, 33.58332" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="12" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="107.3429,74.625" LocationFloat="44.75856, 0">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="14" ControlType="PageHeaderBand" Name="PageHeader" HeightF="145.2083">
      <Controls>
        <Item1 Ref="15" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="765.0885,33.33334" LocationFloat="28.75027, 111.875" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="16" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="17" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.63319838340209311" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="18" UsePadding="false" />
                </Item1>
                <Item2 Ref="19" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.58995300925039273" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="20" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.542263146654033" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="21" UsePadding="false" />
                </Item3>
                <Item4 Ref="22" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.64086488759827032" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="23" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5542779871805661" Text="اسم الصنف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="24" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="25" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="175.8543,31.24998" LocationFloat="28.75027, 64.62498" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="26" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="27" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="28" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="94.79167,31.25" LocationFloat="204.6047, 64.62499" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="30" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="94.79167,31.25" LocationFloat="204.6047, 24.66664" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="32" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="175.8543,31.24999" LocationFloat="28.75027, 24.66666" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="33" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="34" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="35" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="226.7293,31.24998" LocationFloat="462.9424, 64.62496" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="36" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="37" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="38" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="104.1666,31.25" LocationFloat="689.6722, 64.62499" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="39" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="40" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="104.1672,31.25" LocationFloat="689.6716, 24.66674" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="41" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="42" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="226.7292,31.25004" LocationFloat="462.9424, 24.66671" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="43" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="44" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="45" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="132.9167,31.25" LocationFloat="316.3937, 64.62495" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="46" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="47" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="48" ControlType="XRLabel" Name="XrLabel19" Text="بيان اسعار" TextAlignment="MiddleCenter" SizeF="132.9167,31.33332" LocationFloat="316.3937, 24.66672" Font="Droid Arabic Kufi, 12pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="49" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item5>
    <Item6 Ref="50" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="249.0416">
      <Controls>
        <Item1 Ref="51" ControlType="XRLabel" Name="XrLabel27" Text="XrLabel27" TextAlignment="TopRight" SizeF="250.6404,23" LocationFloat="444.446, 215.7083" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="52" Expression="[user_invoice]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="54" ControlType="XRLabel" Name="XrLabel18" RightToLeft="Yes" Text="المستخدم :" TextAlignment="MiddleRight" SizeF="77.48438,25.08331" LocationFloat="695.0864, 215.7083" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="55" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="56" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="444.446, 192.7084" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="57" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="58" UseFont="false" />
        </Item3>
        <Item4 Ref="59" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="44.75856, 127" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="60" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="62" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="44.75856, 154.1667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="63" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="65" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="463.5085, 127" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="66" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="67" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="463.5085, 152.0835" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="68" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="69" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="45.60735, 215.7083" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="71" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="45.60735, 180.2084" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="72" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="73" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="526.5314,25" LocationFloat="266.4586, 100.5208" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="74" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="76" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="526.5315,25" LocationFloat="266.4586, 75.52087" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="77" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="78" UseFont="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="79" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="748.2314,62.5" LocationFloat="45.60735, 10.00001" Font="Droid Arabic Kufi, 8pt" Borders="All">
          <Rows>
            <Item1 Ref="80" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="81" ControlType="XRTableCell" Name="XrTableCell22" Weight="1.1721783522022122" Text="الباقي" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="82" UseBackColor="false" UsePadding="false" />
                </Item1>
                <Item2 Ref="83" ControlType="XRTableCell" Name="XrTableCell21" Weight="1.1480901595333732" Text="المدفوع" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="84" UseBackColor="false" UsePadding="false" />
                </Item2>
                <Item3 Ref="85" ControlType="XRTableCell" Name="XrTableCell14" Weight="1.1925784949628904" Text="المطلوب" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="86" UseBackColor="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="87" ControlType="XRTableCell" Name="XrTableCell11" Weight="1.2675382422227925" Text="السابق" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="88" UseBackColor="false" />
                </Item4>
                <Item5 Ref="89" ControlType="XRTableCell" Name="XrTableCell19" Weight="1.373977689752139" Text="صافي الفاتورة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="90" UseBackColor="false" UsePadding="false" />
                </Item5>
                <Item6 Ref="91" ControlType="XRTableCell" Name="XrTableCell20" Weight="1.0821246348483362" Text="الضريبة" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="92" UseBackColor="false" UsePadding="false" />
                </Item6>
                <Item7 Ref="93" ControlType="XRTableCell" Name="XrTableCell12" Weight="1.0551685273331897" Text="الخصم" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="94" UseBackColor="false" />
                </Item7>
                <Item8 Ref="95" ControlType="XRTableCell" Name="XrTableCell18" Weight="1.2424510860491627" Text="الاجمالي" BackColor="WhiteSmoke" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <StylePriority Ref="96" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="97" ControlType="XRTableCell" Name="XrTableCell13" Weight="0.999871232633571" Text="عدد الاصناف" BackColor="WhiteSmoke" Padding="2,2,0,0,100">
                  <StylePriority Ref="98" UseBackColor="false" />
                </Item9>
              </Cells>
            </Item1>
            <Item2 Ref="99" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="100" ControlType="XRTableCell" Name="XrTableCell23" Weight="1.1721785833909919" TextFormatString="{0:n2}" Text="XrTableCell23" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="101" Expression="[invoice_print].[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="102" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="103" ControlType="XRTableCell" Name="XrTableCell24" Weight="1.1480894386640024" TextFormatString="{0:n2}" Text="XrTableCell24" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="104" Expression="[invoice_print].[pay]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="105" UsePadding="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="106" ControlType="XRTableCell" Name="XrTableCell25" Weight="1.1925780613556163" TextFormatString="{0:n2}" Text="XrTableCell25" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="107" Expression="[invoice_print].[money_plus]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="108" UsePadding="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="109" ControlType="XRTableCell" Name="XrTableCell26" Weight="1.2675386957322281" TextFormatString="{0:n2}" Text="XrTableCell26" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="110" Expression="[invoice_print].[past_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="111" UsePadding="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="112" ControlType="XRTableCell" Name="XrTableCell27" Weight="1.3739798780977919" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="113" Expression="[invoice_print].[total_invoice]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="114" UsePadding="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="115" ControlType="XRTableCell" Name="XrTableCell28" Weight="1.0821229162811132" Text="XrTableCell28" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="116" Expression="[invoice_print].[invoice_tax]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="117" UsePadding="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="118" ControlType="XRTableCell" Name="XrTableCell29" Weight="1.0551685273331894" TextFormatString="{0:#,#}" Text="XrTableCell29" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="119" Expression="[invoice_print].[invoice_descound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="120" UsePadding="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="121" ControlType="XRTableCell" Name="XrTableCell31" Weight="1.2424511150292568" TextFormatString="{0:n2}" Text="XrTableCell31" TextAlignment="MiddleCenter" Padding="2,2,0,0,100" Borders="Top, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="122" Expression="[invoice_print].[invoice_pound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="123" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="124" ControlType="XRTableCell" Name="XrTableCell32" Weight="0.99987120365347693" Text="XrTableCell12" TextAlignment="MiddleCenter" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="125" Expression="[invoice_print].[invoice_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="126" UsePadding="false" UseTextAlignment="false" />
                </Item9>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="127" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item12>
      </Controls>
    </Item6>
    <Item7 Ref="128" ControlType="PageFooterBand" Name="PageFooter" HeightF="25.08332">
      <Controls>
        <Item1 Ref="129" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="44.75856, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="130" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="131" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="695.0864, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="132" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="133" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,23" LocationFloat="646.128, 0" Padding="2,2,0,0,100">
          <StylePriority Ref="134" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="135" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="136" ControlType="DetailBand" Name="Detail1" HeightF="26.04167">
          <Controls>
            <Item1 Ref="137" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="765.0884,25" LocationFloat="28.75027, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="138" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="139" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.0005448213163186" TextFormatString="{0:n2}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="140" Expression="[invoice_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="141" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="142" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.93220917081262" TextFormatString="{0:n2}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="143" Expression="[invoice_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="144" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="145" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.85685326008936014" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="146" Expression="[invoice_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="147" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.0126570260107606" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="148" Expression="[invoice_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="149" ControlType="XRTableCell" Name="XrTableCell8" Weight="4.0361229340974081" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="150" Expression="[invoice_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="151" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="152" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>