# إعداد سريع لنظام مراكز التكلفة

## ✅ تم إنشاء الملفات التالية في المجلد الرئيسي:

### 1. ملفات شاشة إدارة مراكز التكلفة:
- `CostCentersManagement.vb` - الكود الرئيسي
- `CostCentersManagement.Designer.vb` - تصميم الشاشة

### 2. ملف الكلاس المساعد:
- `CostCenterHelper.vb` - وظائف مساعدة لمراكز التكلفة

### 3. ملفات قاعدة البيانات:
- `database/CostCenters_Database_Script.sql` - سكريبت إنشاء الجداول
- `database/DATABASE_SETUP_INSTRUCTIONS.md` - تعليمات التشغيل

## 🚀 خطوات التشغيل السريع:

### الخطوة 1: تشغيل سكريبت قاعدة البيانات
```sql
-- افتح SQL Server Management Studio
-- شغل ملف: database/CostCenters_Database_Script.sql
```

### الخطوة 2: إضافة الملفات للمشروع (إذا لزم الأمر)
الملفات موجودة الآن في المجلد الرئيسي وجاهزة للاستخدام.

### الخطوة 3: الوصول للشاشة
```
القائمة: ملف → مراكز التكلفة
أو
الشاشة الرئيسية → البيانات الأساسية → مراكز التكلفة
```

## 📊 المميزات المتاحة:

### شاشة إدارة مراكز التكلفة:
- ✅ إضافة مراكز جديدة
- ✅ تعديل المراكز الموجودة  
- ✅ حذف المراكز (مع التحقق من الارتباطات)
- ✅ البحث والتصفية
- ✅ الهيكل الهرمي (مراكز فرعية)
- ✅ أنواع مختلفة للمراكز
- ✅ إدارة الميزانيات

### الكلاس المساعد:
- ✅ تحميل مراكز التكلفة في ComboBox
- ✅ حساب التكاليف والإيرادات
- ✅ الحصول على ملخص المراكز
- ✅ تحديث ربط العمليات بالمراكز

## 🔧 استخدام الكلاس المساعد:

### تحميل مراكز التكلفة في ComboBox:
```vb
' في أي شاشة تريد إضافة مراكز التكلفة
CostCenterHelper.LoadActiveCostCenters(YourComboBox, True)
```

### الحصول على اسم مركز التكلفة:
```vb
Dim centerName = CostCenterHelper.GetCostCenterName(costCenterId)
```

### حساب تكاليف مركز معين:
```vb
Dim expenses = CostCenterHelper.CalculateCostCenterExpenses(costCenterId, startDate, endDate)
```

## 📋 البيانات الأولية:

سيتم إدراج 8 مراكز تكلفة أولية:
- CC001 - الإدارة العامة
- CC002 - المبيعات  
- CC003 - المشتريات
- CC004 - الإنتاج
- CC005 - الصيانة
- CC006 - التسويق
- CC007 - الموارد البشرية
- CC008 - تقنية المعلومات

## 🔒 الصلاحيات:

- **إدارة مراكز التكلفة**: مرتبطة بصلاحية الإعدادات (`m1`)
- **تقارير مراكز التكلفة**: مرتبطة بصلاحية التقارير (`m15`)

## ⚠️ ملاحظات مهمة:

1. **قم بعمل نسخة احتياطية** من قاعدة البيانات قبل تشغيل السكريبت
2. **تأكد من الصلاحيات** قبل الوصول للشاشات
3. **لا يمكن حذف مركز** له مراكز فرعية أو مرتبط بعمليات مالية
4. **استخدم الكلاس المساعد** لربط مراكز التكلفة بالشاشات الأخرى

## 🆘 في حالة وجود مشاكل:

### خطأ "Type 'CostCentersManagement' is not defined":
- تأكد من وجود الملفات في المجلد الرئيسي
- أعد بناء المشروع (Build → Rebuild Solution)

### خطأ في قاعدة البيانات:
- تأكد من تشغيل سكريبت قاعدة البيانات
- تحقق من الاتصال بقاعدة البيانات

### مشاكل في الصلاحيات:
- تأكد من تفعيل الصلاحيات المطلوبة للمستخدم

---

**النظام جاهز للاستخدام! 🎉**

للمزيد من التفاصيل، راجع الملفات:
- `cost_centers/COST_CENTERS_SYSTEM_README.md` - دليل شامل
- `database/DATABASE_SETUP_INSTRUCTIONS.md` - تعليمات قاعدة البيانات
