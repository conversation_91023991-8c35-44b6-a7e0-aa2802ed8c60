﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="SimpleButton10.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACV0RVh0VGl0
        bGUAQ2xlYXI7RXJhc2U7UmVtb3ZlO0JhcnM7UmliYm9uO4eAg3MAAAKSSURBVDhPlZN7SFNhGMbf8jZb
        Iyv/MIkiy0yRcKbRLF0wRTaCSmF2sbSQbFqaGeUlrcwLhUpJ5j/ZsilYOMLLmpZCjSgEZ05nuqaWYokR
        pM4VZvnETmcyZ0F98PAe+Pj9vo/znEP0b2sJETkQkSMRLbXfJM2JvfS+6BS9K0imoYIkGsyX0cBVGRnz
        TlphJ21GfGlP9nFtkyx6t0WkzzxGPRnxvwWtcRICsCgs7NyeHnt3zmQETEb05Seba2LF4RYpu09Unlvy
        N9jlRUqM/OdEP8zqSpjVdzA3aUB3Yao5J9An0iJnBOk1b/4Ec9oSo+79+KyHuaEC0w0VMDdWYHa0A2/j
        JHgo2vY1zW+9ZJGAhV3V8XuqZsdew6Qsw7TyJqaVZfg+9BLGo2IMHBGj71AkbgV6TzCCNIXeFl726EDE
        /ZmRdphqi2GqLWHmTP8zGGLCYZCKYJCGQyMW4LKXp4YRnJZ3WWFufGmront4HFOKIkxWFzHzW1cLeveF
        sRFCExGMKxs8urdyXbwYAdst9/A1dfXzETNymsbR/ECFSXkezO310IkF0ElCoBOHoE0YgItr3Xt8Oc7e
        bBPMycul+Y01TwenGNia4U4dOkVB0IqCmPlE4I8LHm56HxfHzbY1cvbn1lU29n1ZAFdrPyEsW4XC2HR0
        CPloDvbFOXde7yYnRx8r3CHkM9fnRV1tMWXVf5yHK1+NITTrMUKzVEyyo2U4s5Lbu9HRYQvbPQNbBZzQ
        hBsZB4s1c+frhnFbM4qdmSrsYiM4qwQ/rrx/DW+1nz1sFVheII8vvZQUllL9ISS5BjtkCmxPrEJQgnzW
        P+a6esW6gPmTU12dyDa2LbgS0Soi8iAiTzaWZ7cF3739sv8H/je/AF6Ksk1KCWUoAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="SimpleButton4.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAZdEVYdFRpdGxlAE9wZW5IeXBlcmxpbms7T3Blbjsh
        S+aWAAADY0lEQVQ4T33TW0yTZxgH8Dc6LMN3Rq+3i+mFyiFzsgzSZRMnRSbTcjIY6pqCytrhmIapE1Gz
        LXPicOJSouNYoYL4URhyCCIVFI+0GBB6oBoXS6kUrC0U6spX+v2Xb3CjW3bxy5M87/P+r56HACD/Z/vR
        NkIIWSxWCBMlR4/o9hRq2Yyf2/XJB9QphJCgf314HSFkkVj+SWrmsQRUXy+AxeNH2U0rJCeuQSQvyyCU
        0kWU0sUL3liofI9/4wOWJO7PNVVoT0FvY2Cc9OOhi8WxpseI21s7TIYq4ruNqngYK+cZyuPRWxybzocM
        lm7mA4Jjs+vYwhtjaDB70Dvu+6ee7nFgw5c1LBkq3wxuzrZgBOyMEUOqJGeRYv3blNIlQUFBy0JFsRbJ
        919DpuyEVHkfp1oVyC2R4T1xjoUMnBeBmzWCm+lAwNMOztMK1tWJgVIx+n7bCBOzD5XKPESKRdh1ci/O
        3J7Ad5UKRKduwSrhR3LyQLkJnG8Q3FQzOHcjOBeDgPMyAtNd4GZNCPjMeDn9J5grNUjak4nI9EqEJSgG
        V0VHZRFCQoj+TAy4v/rBvWAQGK+C3/47/CNK+K1FYJ+eBvv4JHzDP2HmSSncT+rR33gWAoHgXYFAsDwk
        JCSY3CuIAefVYe5ZORxdX2H4YirM1akwXUiGQZUEQ0UihsrEeFiyFQPnP8f1I9HQ5kWj83AUOg5+OEF6
        fvwY3tEWuPT5sFySIuA1gHtpAuc1IDDTj4BHjzn3Hfid3WAdVzE7egU+62WM3SpAU877etKdL8SYrhhm
        9Q7Yuk9gzn0X033H4dHlY+p+HqbuHcLknQNw39oP940cuLqy4dTK0Xd2Cy7IIn4g1w5FwVD/LXp/jcOk
        6SK85pJXBp0dWXjevhvP22SYaJHC0ZwOxx87cPWbD3BQtHIdadsXiZ5fkqEr2gbvoyo4tdkYb5FivFkC
        R1MaxjTb8UyTAjuTBHvdNozWJMBybhNqpWFGSulbRJMVgdZcIYwqGV7cPAw7kwj7pflBm/oz2NRxGKkS
        war6FFZVDKwVG3A7PxLFKasLKaXBpFqyVqfJDEfj7gg07opAQ2Y4GjLCoZGFoZ4nncd8EQZmZyjqJKFQ
        p621yYXvhPLrzh/Mm5TSZZTS5f9hxWv43lL+4GrT1hDe3zqPbRmqgZ9jAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="SimpleButton2.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACN0RVh0VGl0
        bGUAQ2FuY2VsO1N0b3A7RXhpdDtCYXJzO1JpYmJvbjtMlpayAAALOklEQVRYR5WXB1BU1xrHj++l+IIY
        jV3pCoI06dJROgqioihGjaKGiASNAgJSLYiJWMEa0RgUFGFBRbHQpIPAUgSWJp2FXUCkCPLm/+bc3SUk
        k3lv3p35zb2z957v//++U/Yc0nYqlLSdOiYg/G84dYy0hh8jrWGUUEIImfLfeBfkT5qC/EhjgC9pCvAl
        jf7epMHXizQc8SL1PodInfdPhHP4IOEc8qSxCGk7GUL+3dsk5B0Z/xsmCfyDEPJPQshnhJDP/wL9jULf
        0++mfOp8Sz51VpFPHZRK8qmtgoy1sclYaxmp9vQQGGg5EUTG+Q1knN/4Z3qbJgszooVeBzVrjvqG1gX6
        59QH+pU0BvmNNwT4jnP8vUs5vt655V4Hj6ft26tNCPlCaEZghBEvnxAfayklVfv3CQw0hwaQcV49Gec1
        /IlJGX/OPnJ4U32gH6f5lzDwku9jkJ2N4beFGOc1YJxXj6GqfHwoyUR3QgwaTwTj7eGDnDx3t62EkC9F
        RsaogRaB+FjzG1L+g5vAQGOgH/nUzRFSx9xFWae5uy2u9T+S3xZ1HoNl2fjUWY2R6jwM5j/Dh+xH6H92
        F/1Pf8dARiIGspIwzM7AWGs5BorT0RhxCmwP98Lo9esUhRVhqjH6rpiMNhWRsj27BQbq/Y+QT101EwjF
        P8v39LDkHD3S2/uchdF3ZfiQ+wT9T2MEpNxB35M76Ht8G33J0ehl3URvwg3w46+C/+Ay+lPvYqQmD9yk
        WJR7uPcmbdlsJ6yGwERdHineuVNggONzmIy1VZKx9qoJ8ex9P1hxAv1HB4rSMVT8UiD0SEDvo1voS4pG
        X9JN9Cb+it6E6+h9eA38B1fAi4sE7+5F9Px+Dt23IzCQFo++1yko9XAfS3DaMGFipCabFG7bLjBQfdCT
        jDaXTZSd9d02xSqfw7yBwld4nxYPPhVJ/JW5MyRcB58KxlPRq+DfvwxeXBR49y6hJ+Y8uu+cRfetX9B9
        8zS418OYqvRmJKPA1ZV/0dJSVdQduVtcBAbYu3aS0YZi+kjL80WZx/5cbnIs+lNjwaPB71+ZBBUTCsZG
        CkSZjEXCZ9Ad/TO4N8LRde0Eui4fQ8elEPTEXERrzA1kOm/JJ4RMEw5MmjAhRc6bJ0qfuXvX1trQQAxk
        stAdc4FpKKLt2mmkOK3Fb9o6ePHtJnRFn0HPb2eZUnfeCEeq8zrcUFEDy84SzRFH0RkZio4LQWg/dxTt
        Z3zBZ91E0Y/7cc/Gjnb+VGHChOQ7bRRl/2Xx3r2cniex4N45B270GXBvRTC0RZ0Ey9YKD70C8TTpNR7s
        9cDjtXbouBqGjisnkbTGGnG73ZHCysJ9T1/EGhmgMcwL7RF+aP3ZB63hh9F62hvtMVeR6rCunhAyfXIV
        mOwfWNvasH/yBJ8VjY6rJ9F5LYyh6/opPN3gAJZvCLKL6lHf1I3quk48dPPEI3tbPFpjg7jd+5GRV4Pq
        +k7UNvWA5RUIluUqtIR7oSXsJ7QcO4B3wfuZWNk7duCijr7DpFnBGPgixWb12bcBh9F8zBNtF4LRHhnK
        0BEZijhDA3S2dKGxlY++gVH0ffiI2oYuxkTcHg+k59agpqkbvIGP4L0fQS+Xj5uq6mg57ol3IT+iKcgd
        jf7fo8HbFaWH9uOOntElQoiYcIVlXExNtbbL4QR4otbVATXfr0Pz8QNoO3sUbecCkL7DBTlHAzDycQx9
        Ax/Bf/+REatp4CK/pBE1jd3o6R9hoN9kePkgZZ29QPioG+oO7cDbXQ6o3mWPKp99iNUzpoORdsOEga9S
        LWx4HO89qNpug6pvrVG1zQa1bk5o8HdDe9RxPN+4HnnBwYwAI/ZeINg9iZGRMbz280OyrSXehfug7sA2
        1Lg6MrEqt1qhcqs1an5yRZy2EZ8QMlM4DhgXYk9WWY1xPLaiwtkSFVssUelCG1ihkprZYYcGPzekrLZC
        pq8fuH1D4PaPgNs3PEEnbxDpPr5IMjdF/RGaiB0jSOPQeOXO5mBvMkeNuzPuaeiPEUK+Ef6LMgamJRub
        j1XtdATbaSXYm1ahfJM506his4UggIsVnpibIPF7D1RyuODyR8DlD6OLP4wO/jDaewbxeN8BJJnog72F
        JmGB8s2CGDRemZMZyjaYouK7NbijqkcNzPqTgUQDU17JZluUrTdD2XpTlG0wA9vJDOyNK8HeuAqPzfSR
        sNcDL7OqUVHHRQdvGO0Mg4x4e88w2DWdeOC6Dwn6WiilgpT1pihdb4JSR2OUOpqgyMkS0UpatAsmDNAx
        IHZP2yg3b60FShyN8cbBCKWOFGOUrjPGM3NDJLr9iBdZb1HO4QoFBzE0PIrB4VG0dQ+iVUjZ23Y83L0P
        SYbaKHE0wZu1Rnhjb4hiewMmboaNKaLk1QsnjwFq4F831HQvvLAwQ7GDCYrtVqBojT6K1xig2N4QiSaG
        qOe0gl3byYi19QxicGgUBSFByA8OYp5buIMMzV0f0N7CxV0tTaZ90eoVKLRbgUIbPRStMUCygR7CpZSu
        /HUWfHlCQdUhXtcABXYGKLDSQaGVLgpsdFFko4dUW3NUXo7E+8FRdPCGGMGi0BA8tTJDiqUpY+TD0ChT
        FfpN2YXzeLTSiGlfaKWDAktt5FtoIc9aD7eV1HFonqyzcB2YWIhoKb6+qaTV8MJQk/k435zeNVFgoY1S
        l9VIsTJjTAx3daEoJBjPrFeiYrs9KnbY46mlCQqCAzHU1YmyixfAMjFAyRZb5FloIm+VBvJWLkeeuSZS
        dFRwTkKJ7vXmClfCKeSWotbEWnBcZtmeGGUNZJstR46pOnLN1JFLG1toMSZe2JkjeaUxXq2xQJmLHWO0
        wEILbBdbPLddhXhDfaRYmODNZlvkmWsxMXJM1JBjrIYsIzVck1XCodnS7pPKP4X8Kq8hqgIdkTMi5VSL
        EtWUkWWogmxDVWQbqyHbWBU5Zhoo3WSJ8u1rUeJkgTzT5cgxoQLqyDVdjpINFqjYZo/SjZbIMV2O10aC
        9q8NlJG5YhliFeQRPncJ3Xj8kT29rsmpkbRVdCMrGIy7ZkusiJJR7k1WV0CGjhLTOGuFMl7rL0OWvjKy
        DJYxQV8bqEyCvqe/KSNLXwlZTBslZOgpIl1bAQmKcoiYu7hv/bQ5JsL9gCB7ekVJK5MXK1QYhGNh2sE5
        0huiJJXGHiotxktNeaTrLEWmzlKk6yoiU1cRGbqKyBLe/2ApQzpFRwFpOgp4vlwOcfLSiJgtN+YqPn8L
        rbCw0lN+ninD6JNLi5TIMw15kqIhP7krprt/I7nx3HyF/hg5GTxRlsELDTm80lyCV1ryDGkTLEGapjzS
        mHdL8EpTHs/V5PBomRRuS0kh/Bvp9zvF5tL9F1166XZsCktFloRPlxYYOL9gKUlWkSXJyrIiAyIT4o7T
        ZuuemiVXGrVAFrGykkheKoHHy6TwXFUGz1Vl8Up9MV6qL0aqqgxSVWSQskwKLIVFiJFehAuzpRAyXZJt
        PXWGgTBzRpzyUEGCnBSXEhiImLOEsBQkSILCIsKSXzTZBO0OOlfneExf6H5ypkzz2dnSuLZAAr9LLUSM
        9ELEL56PeLn5iJFagDuSC3F1/iKcmSmJ4+KSLW5i8+jZaz5NRFR2yn3ZBeS+7HxyQkxSYOCXWXIkXnYe
        eSgnRHbeZBN0sNARS6fNPJev5th7iy+MDBaXZIeKS9WGTZdCmLgUQqZJ1AaJSbAPfbUgynnqrLWEkAXC
        rOneT7T1mhInNYeIOCEmITBweqYsOT1DhoRTvpYm4TOkBffpTIkmG6ElpBWhgecIs1sohD7T6UXXdzrK
        RUcypj0V+zv+n0tkhE5V0cmYGqJCFPosOiEzpx8h//P6D1Wcml8FEabCAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="SimpleButton1.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACF0RVh0VGl0
        bGUAQXBwbHk7T0s7Q2hlY2s7QmFycztSaWJib247ZGPIaAAACslJREFUWEeNlwlQVEcax9vNuZts9ki5
        62azcSsCCgoCgqAIQriGIyCJXGpABEU8QPAERQERhQGRG8EBBEGTeHA54wy3nGIUBYmAwMBwiAhym4qV
        qv9W97whE7PXq/rX62le9//3ff118x45kWFJwgVW5GSONYnK45HofB7hX7Ehcd/akvjrtiSh0JYkFduR
        lBI7kiq0J4SQBf9NSaU8cq7YisTfsCSxVy3Ima/NSVSBGYnIMyUnsk3IsUxjEpxmRA4lr6VzEXI8w4IM
        zlwkQzO5ZHg2jzydu8Q0MpdPRuYKyLOXBcqmvyGEvEEIeZMQ8tZron1U9O/0uQWDM/lkYOYSkU3nkb6p
        XCKdzCG9E9mk+4WAHEheIwc4dt6cDMzkMIChWTnEMIPIVzZmplF55roJhbyIFJFNfZrY5n66xPanjHLb
        n1JF1i0pQuuGs9csIk9cMNYjhLzNwTAQ2VQe6ZvMJb0KgPEsciDJkANI/4zIprPIwHQ2GaQgsxeZlCJ+
        60y+uUtCsXVXVsVGlD86iLbhs+gcTcbI3GWmztEUtA3HQdK2HxfKvkDcDfOu8BzjzYSQdxQgfZx5z4ss
        8mT8AtmfyAGEpJqS/qkMIpvOJAPTF8jgjEBh/sbhxLVL+N9YNOVVbULrIB9DswJ0T8Sh/XkEHj0/jpaR
        g7g/cgCto6FoGz2BrvFYyKYFeDDIx8VKN5y6bNLsE6q1jMsIy0b3eCZ5MpZBghIN5ABHUoyJdDKN9E2l
        k/6p8wrzN0PS11nGfG3xorL9MKRTSXj47CiahwJwd5hqH9N3T2nbH83De9E0uAcNA7tRL/PD3aGD6HqR
        CEnrQZwqWP/Cn7/KlssGg+gcSyeB5/TlAIeSjEjPZBKRTibPmx9KWGMV+43Vj3elEWgbPY4GmR8aB/zQ
        OLgLTYO7cGdIfleocdAPDQM7USfbgdt9PqiRbkNljxfuDYWgsTsCUflmr3ZFac9DdDxPJQHxtFQIIYHx
        BqR7In4+7b7hOstO5pqO3ZUex53BANT2eTPV9fugTrYd9QM+StqOehnt90atzBs1fV6oknqiotcDaSIe
        tp3UwM1WLzQ8CUOowGjcNWCZpmI5/ONWyQG2n9IiXS/4tEnT83ZwumGD8D5N5y5U9GxBVe9XqO71QLXU
        A7f7PXG7f+vP9z5P1PR7oKbPA1XSr1Ap3YIbLc7widRCaFIQEvPi4R2pCXGHN4ru7kVQwqomQsj7XGHS
        gAn5KnT5fOr9Y3Q3x37NQ/NQEMRPXCB54oLybjemih53VPS6o1K6CVVMm1mbivaX97gjo8wa7gdX45pY
        gg7pGLqkPdgQuASpEkvUSvfh9CUrbA1d7kUIeZcLmJAtx9QV0b+zP1G/q+zRHog6nXGzwwnCTifc6voS
        kidfQty9EZInzijrpnKBhN57XCCh6nbGuUJTbA3hoe67Tgw8m8H41Dj2nrbD1UZflHzvimutDhC2+GMP
        X7ubEPKBchZY9C6BarywLFNU93qj6JEdih/bo+Tx5yh97IDSDkeIOh0h6trAdKvLCSKmDRB1OiGywBAe
        wdZofNCDp2MvMTE9iZ0nrZFf44WSdhdcabFGwT0r3OrYhtALptjgp+KgtCsYwNvuh5fFJxe5oeCeDa62
        WuN6Gw832m1Q2G6D4se2KH5sh9IOe5RSqA4qeftIpi68jtrhTqsUQ89fYmxyAjvCrHBe6Iai751xucUS
        +fctkddsjtw7PKQUu8Ht4DK65d7jTlhG8e6mYPV6QYU7kquNkXp7HQrumeNqqyWu3LdAiEAHV1utUNjO
        Y0BURe02CL+kh61H7dHc2oehsTlMzPyIiHQ/nBBYsrQXtFjg0j1z5DSZIbXGGKk165FdtRmbgtVpMdJl
        mAf4nXuw+lh2rTMSKtbgXKUhEivXIrV6HTYfV4XXMVt4nFiOvLtmuNpmiWttVogrXgPXA+tQ39KFwedz
        GBn/AZLGa/AK04Kow4MZZ98xxflaYyRXrUNi1VokVhkhp94FW45qjBNC/sTVAaN4z+2I+qvMOnvElevj
        bLkB05YwFQTFbMV37YO4fLMYrodXILViHZLKjOAUuBzC2ibIns0yANlIP5z3a+LbZk9k1JsircYEKTXr
        kFxtxMzPVRjibLkhBPUO2Byi8YoQ8mfuvygDeN/1kPqr1Gpz8CX6iCtbjTOlBnDYtxhND+7g6fgP6B2a
        QmF5JTbu14JDgAquiG5COjyFgdFZjE29RBB/I+KuOSCj7jMlYyOcq1yDcxVrWEBxZfpIq7GA+xF1CvDh
        LwA27l82dvaWCWIleogV6yO5aj0EFa7YclQPI2NDGB5/iZ7hKQhrm5H2jQDdAxOQjc6ywrtRIYBPpA5y
        mxyRzFK9FgnM2BDxFQY4W7YasWX6TPHi9XA5uIwuwTwArYH3nPYubYi6aozYW/qIEa0CX7wK6bWWSBNt
        hG+kKUYnRjHwfA69w9P4XjqO/mez6Hs2i/6RAWzYp4qsGhe5qcKYW0YaNTWm88WJ9XH6mgmc9qo1K9cA
        Bfitna9K4pHMteCL9XFGpItosS4blFFrhbM37BEQY4up2VkWNTXuG5lhS3Na4IdjAlMkV6+fN6UR02WM
        k+iDL9EDX6yHGJEuYiWrEXzBCLY+S9Jf3wXvmLktdvAK0wZfbIDTN7VxRkilw7KRWcdD1GUrhKZuwuTs
        S0hHZpgedDbBLUQFWfWOLFIWLTXkFCNexQI5I9Jhc8WKDbAtXBvGGz9x5c6B+YOIpuIPn/up9oQW6DKA
        qJsrORAdNoGgzg7Hsk3Az92NqbkfMToxhz3RlkgotWfGLEomuWm0SBdnhFQ68/OEXl4Fh91qUkLIX7iT
        cAGx9VWdPwvM3BZv3xKqiWihHiJLtHCqVIuBRHETZNc74ECaITKuH4OwLhe7YlcipcqMmckNdRBNgalo
        EEJtRJWuZPPECPXgcVwTJs6f7FZK/wJis0NFkQVakX+03aFyd1+aFk6VaDOIyFJlkJW42LAB/gm6+OLQ
        J0gR27LoTgt1mBlrc9mL4saxsaXaCEzXgu0OlQe/iJ5ePB8VImqnUPJi1DZfZOiwZ+mLkEtaOFmihZPF
        muzOMsIB5TY64XrLVsSJDeQmN2mU8kip6LOKMZElKxGSvxKOu1UnVhgvNOHeB+TR08vaW4WUtvmRkjZf
        +pPWwvuGDh9/ucFf7dWBbA2EF61ARJEmThavkMMwaSGS6hdGFJaDLtFCRLEmG0fncApQe6Vv95E7zTCX
        6QW87SzzvwJQLMUHq+3/7my/U3Vyd7I6g1Aoovh1aXJmKxBe/PNzVHtS1GG/U21Kn/fRJu7opa9jC0ra
        /Ii1N6s9BYCvMoAC4vfqhgtXW2/7tMXliBoO5GggrGgFwoqWI7xwBRP9TY1Yv1IffdY1eCmsvJY8VNP/
        kH6D0ciZuRzAl/nOA9AOKgqiBEGXg+7VhYaOH++23rak3ylQFd7RSxEkUMehixo4UbgcYTeW41CuBoKy
        NOATvRROgWqw3qYiM3T4x15CyCIaiCLtCnPq8xqAH5cFuhR+yhC0WGjF0m3zV+3PFn1u4vrPFAvPJQ8t
        PT/t5PmogMrSc0mnheenD01cF6euNFvkSAj5Gxc1ffdTvHox838L8J/0GghNIc0InXghF91HnGibbi96
        vtMqV3ySsfGvz6s0//99KUDoVlV8GVMgakRF24ovZPb1w+l/Xv8CrR1y7Uuyey8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="sec_pic.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAA2kAAALpCAYAAAApT27KAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        wAAADsABataJCQAAEHlJREFUeF7t1zEBADAMgLD6N93+MzCO5MQBswAAAGTMGwAAAPjHpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwa
        AABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAh
        Jg0AACDEpAEAAISYNAAAgBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhJg0AACDEpAEAAISYNAAA
        gBCTBgAAEGLSAAAAQkwaAABAiEkDAAAIMWkAAAAhB5DQ4vGQDksXAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="OpenFileDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>36</value>
  </metadata>
</root>