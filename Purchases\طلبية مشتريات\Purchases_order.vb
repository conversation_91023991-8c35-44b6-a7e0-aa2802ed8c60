﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI
Public Class Purchases_order
    Dim check As Integer = 0
    Private dtt As DataTable
    Private rng As New Random
    Dim itemsize As Integer = 0
    Dim itemloc As Integer = 0
    Sub desc_value()
        Dim t As Double = 0.0
        Dim d As Double = 0.0
        Dim s = (Val(descound_Rate.Text) / 100)
        Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
        descound_Values.Text = Format(Val(sum), "0.00")
        invoice_descound.Text = Format(Val(descound_Values.Text), "0.00")
    End Sub
    Sub fill_item()

        item_name.Properties.DataSource = Nothing
        item_name.EditValue = ""
        Dim adp As New SqlDataAdapter("select (itemnamearabic),(itemcode) from item where itemstore=N'" & (store.Text) & "' and item_active = 'true' order by itemnamearabic ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        item_name.Properties.DataSource = dt
        item_name.Properties.DisplayMember = "itemnamearabic"
        item_name.Properties.ValueMember = "itemcode"
    End Sub
    Sub fill_treasury_name()
        Try
            type_pay.Items.Clear()
            Dim adp As New SqlDataAdapter("select treasury_name from treasury_name where Treasury_active='true'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                type_pay.Items.Add(dt.Rows(i).Item("treasury_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try

    End Sub
    Sub fill_store()
        Try
            store.Properties.Items.Clear()
            Dim adp As New SqlDataAdapter("select store_name from store where store_active=N'true'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                store.Properties.Items.Add(dt.Rows(i).Item("store_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub

    Sub fill_account()
        Accounts_name.Properties.DataSource = Nothing
        Dim adp As New SqlDataAdapter("select (impname),(impcode) from importer where imp_active = 'true' order by impname ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Accounts_name.Properties.DataSource = dt
        Accounts_name.Properties.DisplayMember = "impname"
        Accounts_name.Properties.ValueMember = "impcode"
    End Sub
    Sub fill_accountcus()
        Dim adp As New SqlDataAdapter("select (cusname),(Cuscode) from customer where cus_active = 'true' and Check_imp = 'true' order by cusname ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Accounts_name.Properties.DataSource = dt
        Accounts_name.Properties.DisplayMember = "cusname"
        Accounts_name.Properties.ValueMember = "Cuscode"
    End Sub
    Sub fill_delegate()

        delegate_name.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select delegate_name from delegate", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            delegate_name.Properties.Items.Add(dt.Rows(i).Item("delegate_name"))
        Next


    End Sub
    Function gettreasury(subname) As String

        Dim sql = "select balace from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function
    Function getcodeaccounts(subname) As String
        Dim sql = "select Cuscode from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Cuscode") Else Return ""
    End Function

 
    Private Sub inserte_Click(sender As Object, e As EventArgs) Handles inserte.Click
        '  code.Text = getcode(item_name.Text)
        If block_sale.Text = True Then
            MsgBox("هذا الصنف ممنوع من البيع")
            Exit Sub
        End If

        If item_name.Text = "" Then
            MsgBox("أدخل اسم الصنف")
            Exit Sub
        End If
       
        For i = 0 To GridView2.RowCount - 1
            If GridView2.GetRowCellValue(i, "كود").ToString = item_name.EditValue And GridView2.GetRowCellValue(i, "الوحدة").ToString = item_unit.Text Then
                If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
                    MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
                    Exit Sub
                End If
                If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون بتاريخ صلاحية" Then
                    MsgBox("لايمكن التعديل علي صنف مخزون بتاريخ صلاحية يمكن حذفه فقط")
                    Exit Sub
                End If
                If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون بسريل" Then
                    MsgBox("لايمكن التعديل علي صنف بسريل يمكن حذفه فقط")
                    Exit Sub
                End If
                ' Try
                GridView2.SetRowCellValue(i, "الكمية", GridView2.GetRowCellValue(i, "الكمية") + 1)
                '  Try
                Dim aa As Decimal
                Dim sql1 = "select * from item where itemcode=N'" & GridView2.GetRowCellValue(i, "كود").ToString & "'"
                Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
                Dim ds1 As New DataSet
                adp1.Fill(ds1)
                Dim dt1 = ds1.Tables(0)
                If dt1.Rows.Count > 0 Then
                    Dim dr1 = dt1.Rows(0)

                    aa = dr1!itemcount
                End If

                GridView2.SetRowCellValue(i, "الكمية الحالية", aa - GridView2.GetRowCellValue(i, "الكمية").ToString)

                Dim ss As Integer
                Dim sql = "select * from unit_item where code=N'" & GridView2.GetRowCellValue(i, "كود").ToString & "' and unit=N'" & GridView2.GetRowCellValue(i, "الوحدة").ToString & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count > 0 Then
                    Dim dr55 = dt.Rows(0)
                    ss = dr55!count
                End If
                GridView2.SetRowCellValue(i, "الرصيد", ss * GridView2.GetRowCellValue(i, "الكمية").ToString)

                Dim t As Double = 0.0
                Dim d As Double = 0.0
                Dim s = (Val(GridView2.GetRowCellValue(i, "الخصم").ToString / 100))
                Dim sum = Format(Val(GridView2.GetRowCellValue(i, "السعر").ToString) * Val(s), "0.00")
                GridView2.SetRowCellValue(i, "خصم نقدي", Val(sum))

                GridView2.SetRowCellValue(i, "بعد الخصم", Val(GridView2.GetRowCellValue(i, "السعر").ToString) - Val(GridView2.GetRowCellValue(i, "خصم نقدي").ToString))
                ' GridView2.SetRowCellValue(i, "الربح", (Val(GridView2.GetRowCellValue(i, "السعر").ToString) - Val(GridView2.GetRowCellValue(i, "خصم نقدي").ToString) - Val(GridView2.GetRowCellValue(i, "سعر الشراء").ToString)) * Val(GridView2.GetRowCellValue(i, "الكمية").ToString))
                Dim x0 = GridView2.GetRowCellValue(i, "السعر").ToString - GridView2.GetRowCellValue(i, "خصم نقدي").ToString
                x0 = x0 * GridView2.GetRowCellValue(i, "الكمية").ToString
                GridView2.SetRowCellValue(i, "الأجمالي", x0)
                plus_invoice()
                'Catch ex As Exception
                'End Try
                'Catch ex As Exception
                '    MsgBox("فشل العملية")
                'End Try
                Exit Sub
            End If

        Next
        Dim dr As DataRow = dtt.NewRow()
        dr(0) = code2.Text
        dr(1) = item_name.Text
        dr(2) = item_unit.Text
        dr(3) = item_count.Text
        dr(4) = item_price.Text
        dr(5) = disc_rate.Text
        Dim x = Val(item_price.Text) - Val(item_descound.Text)
        x = x * Val(item_count.Text)
        dr(6) = Format(x, "0.00")

        dr(7) = item_catorgey.Text
        dr(8) = item_company.Text
        dr(9) = tax_add.Text
        dr(10) = item_tax_add.Text
        If item_type.Text = "مخزون تجميع" Then
            dr(11) = 0
        Else
            dr(11) = Val(show_count.Text) - Val(item_count.Text)
        End If
        dr(12) = item_group.Text
        If item_type.Text = "مخزون تجميع" Then
            dr(13) = 0
        Else
            dr(13) = Val(item_count.Text) * Val(number_unit.Text)
        End If
        dr(14) = item_type.Text
        dr(15) = Val(item_price.Text) - Val(item_descound.Text)
        dr(16) = Val(item_descound.Text)
        dtt.Rows.Add(dr)
        GridControl1.DataSource = dtt
        plus_invoice()
        If item_type.Text = "مخزون تجميع" Then
            For h = 0 To dgv_total.Rows.Count - 1
                item_name.Text = dgv_total.Rows(h).Cells(1).Value
                item_count.Text = dgv_total.Rows(h).Cells(2).Value * GridView2.GetRowCellValue(GridView2.DataRowCount - 1, "الكمية")
                inserte_total()
            Next
            item_name.Text = GridView2.GetRowCellValue(GridView2.DataRowCount - 1, "أسم الصنف")
            item_name.Select()
        End If
        Dim value As Integer = GridView2.RowCount
        GridView2.TopRowIndex = value
        GridView2.FocusedRowHandle = value
    End Sub
    Private Sub inserte_total()
        If block_sale.Text = True Then
            MsgBox("هذا الصنف ممنوع من البيع")
            Exit Sub
        End If

        If item_name.Text = "" Then
            MsgBox("أدخل اسم الصنف")
            Exit Sub
        End If

        If Val(item_count.Text) > Val(itemcouta.Text) Then
            MsgBox("الكمية اكبر من الكوتة", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        If XtraForm1.w1.Text = False Then
            If Val(show_buy.Text) > Val(item_price.Text) Then
                MsgBox("لايمكن البيع باقل من سعر الشراء", MsgBoxStyle.Critical, "خطأ")
                Exit Sub
            End If
        End If
        If XtraForm1.w2.Text = False Then
            If Val(item_count.Text) * Val(number_unit.Text) > Val(show_count.Text) Then
                MsgBox("لايوجد كمية في المخزن", MsgBoxStyle.Critical, "خطأ")
                Exit Sub
            End If
        End If
        dgv_10.Rows.Add()
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(0).Value = code2.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(1).Value = item_name.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(2).Value = item_unit.Text
        Dim x = Val(item_price.Text) - Val(item_descound.Text)
        x = x * Val(item_count.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(6).Value = Format(x, "0.00")
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(3).Value = item_count.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(14).Value = show_count.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(4).Value = item_price.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(5).Value = disc_rate.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(8).Value = (Val(item_price.Text) - Val(item_descound.Text) - Val(show_buy.Text)) * Val(item_count.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(9).Value = item_catorgey.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(10).Value = item_company.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(12).Value = tax_add.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(13).Value = item_tax_add.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(15).Value = item_group.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(14).Value = Val(show_count.Text) - Val(item_count.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(16).Value = Val(item_count.Text) * Val(number_unit.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(17).Value = GridView2.GetRowCellValue(GridView2.DataRowCount - 1, "كود").ToString
    End Sub
    Sub plus_invoice()

        Try
            GridView2.UpdateTotalSummary()
            invoice_pound.Text = GridView2.Columns("الأجمالي").SummaryItem.SummaryValue
            If Val(tax_Rate.Text) > 0 Then
                tax_value()
            End If
            If Val(descound_Rate.Text) > 0 Then
                desc_value()
            End If
            total_invoice.Text = Val(invoice_pound.Text) - Val(invoice_descound.Text) + Val(invoice_tax.Text)
            after_total.Text = Val(invoice_pound.Text) - Val(invoice_descound.Text)
            invoice_count.Text = GridView2.Columns("الكمية").SummaryItem.SummaryValue
            If monetary.Checked = True Then
                pay_money.Text = Val(total_invoice.Text)
            End If
            pay_money_EditValueChanged(Nothing, Nothing)
        Catch ex As Exception

        End Try

    End Sub
    Sub tax_value()
        Dim t As Double = 0.0
        Dim d As Double = 0.0
        Dim s = (Val(tax_Rate.Text) / 100)
        Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
        tax_Values.Text = Format(Val(sum), "0.00")
        invoice_tax.Text = Format(Val(tax_Values.Text), "0.00")
    End Sub
    Private Sub total_damage_TextChanged(sender As Object, e As EventArgs) Handles total_invoice.TextChanged
        Try
            show_total.Text = total_invoice.Text
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub
    Private Sub item_name_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            item_count.Focus()
        End If
    End Sub
    Private Sub reason_damage_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            item_name.Focus()
            plus_invoice()
        End If
    End Sub
    Private Sub dgv_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs)
        If e.ColumnIndex = 7 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub
    Sub delete_iem()
        Try
            For i2 = 0 To 1000
                If dgv_10.Rows(i2).Cells(17).Value = GridView2.GetFocusedRowCellValue("كود") Then
                    dgv_10.Rows.Remove(dgv_10.Rows(i2))
                End If
            Next
        Catch ex As Exception

        End Try

    End Sub
    Sub msg_new()
        new_btn_Click(Nothing, Nothing)
    End Sub

    Sub account_trans()
        Dim adp As New SqlDataAdapter("select * from customer_trans where custome_rname=N'" & (Accounts_name.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_3.AutoGenerateColumns = False
        dgv_3.DataSource = dt
    End Sub
    Private Sub code3_TextChanged(sender As Object, e As EventArgs)
        Try
            show_buy.Text = ""
            show_count.Text = ""
            show_datebuy.Text = ""
            show_datesale.Text = ""
            dgv_2.DataSource = Nothing
            dgv_2.Rows.Clear()
            Dim sql = "select * from item where itemcode=N'" & (code3.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                code2.Text = dr!itemcode
                show_buy.Text = dr!itembuyprice
                show_count.Text = dr!itemcount
            End If
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub
    '=========================== show data
    Public Sub show_data(x)
        new_btn_Click(Nothing, Nothing)
        Dim sql = "select * from order_add where invoice_number=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لم يتم العثور علي بيانات")
        Else
            Dim dr = dt.Rows(0)
            invoice_number.Text = dr!invoice_number

            invoice_date.Text = dr!invoice_date
            store.Text = dr!store
           
                Accounts_name.Text = dr!Accounts_name

            Accounts_name.Text = dr!Accounts_name
            Accounts_code.Text = dr!Accounts_code
            invoice_note.Text = dr!invoice_note
            invoice_descound.Text = dr!invoice_descound
            invoice_tax.Text = dr!invoice_tax
            invoice_count.Text = dr!invoice_count
            total_invoice.Text = dr!total_invoice
            delegate_name.Text = dr!delegate_name
            type_pay.Text = dr!type_pay
            If dr!type_money = "نقدي" Then
                monetary.Checked = True
            End If
            If dr!type_money = "أجل" Then
                Yup.Checked = True
            End If
            pay_money.Text = dr!pay_money

            '========================================
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()

            adp = New SqlDataAdapter("select * from order_list where invoice_number=N'" & (x) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                Dim dra As DataRow = dtt.NewRow()
                dra(0) = dt.Rows(s).Item("item_code")
                dra(1) = dt.Rows(s).Item("item_name")
                dra(2) = dt.Rows(s).Item("item_unit")
                dra(3) = dt.Rows(s).Item("item_count")
                dra(4) = dt.Rows(s).Item("item_price")
                dra(5) = dt.Rows(s).Item("item_descound")
                dra(6) = dt.Rows(s).Item("item_total")
                dra(7) = dt.Rows(s).Item("item_catorgey")
                dra(8) = dt.Rows(s).Item("item_company")
                dra(9) = dt.Rows(s).Item("tax_add")
                dra(10) = dt.Rows(s).Item("item_tax_add")
                dra(11) = dt.Rows(s).Item("new_count")
                dra(12) = dt.Rows(s).Item("item_group")
                dra(13) = dt.Rows(s).Item("item_balace")
                dra(14) = dt.Rows(s).Item("item_type")
                dra(15) = dt.Rows(s).Item("after_desc")
                dra(16) = dt.Rows(s).Item("desc_money")
                dtt.Rows.Add(dra)
                GridControl1.DataSource = dtt
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()

            adp = New SqlDataAdapter("select * from order_collection where invoice_number=N'" & (x) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            For w = 0 To dt.Rows.Count - 1
                dgv_10.Rows.Add()
                dgv_10.Rows(w).Cells(0).Value = dt.Rows(w).Item("item_code")
                dgv_10.Rows(w).Cells(1).Value = dt.Rows(w).Item("item_name")
                dgv_10.Rows(w).Cells(2).Value = dt.Rows(w).Item("item_unit")
                dgv_10.Rows(w).Cells(3).Value = dt.Rows(w).Item("item_count")
                dgv_10.Rows(w).Cells(4).Value = dt.Rows(w).Item("item_price")
                dgv_10.Rows(w).Cells(5).Value = dt.Rows(w).Item("item_descound")
                dgv_10.Rows(w).Cells(6).Value = dt.Rows(w).Item("item_total")
                dgv_10.Rows(w).Cells(8).Value = dt.Rows(w).Item("item_earn")
                dgv_10.Rows(w).Cells(9).Value = dt.Rows(w).Item("item_catorgey")
                dgv_10.Rows(w).Cells(10).Value = dt.Rows(w).Item("item_company")
                dgv_10.Rows(w).Cells(12).Value = dt.Rows(w).Item("tax_add")
                dgv_10.Rows(w).Cells(13).Value = dt.Rows(w).Item("item_tax_add")
                dgv_10.Rows(w).Cells(14).Value = dt.Rows(w).Item("new_count")
                dgv_10.Rows(w).Cells(15).Value = dt.Rows(w).Item("item_group")
                dgv_10.Rows(w).Cells(16).Value = dt.Rows(w).Item("item_balace")
                dgv_10.Rows(w).Cells(17).Value = dt.Rows(w).Item("code_first")
            Next
            '========================================
            plus_invoice()
            pay_money_EditValueChanged(Nothing, Nothing)
            lock_fatora()
            If XtraForm1.w10.Text = True Then
                edit_btn.Enabled = True
            End If
            save_btn.Enabled = False

            If XtraForm1.w12.Text = True Then
                delet_btn.Enabled = True
            End If
            new_balace.Text = (Val(Accounts_balace.Text) - Val(total_invoice.Text)) + Val(pay_money.Text)
        End If

    End Sub

    Private Sub Accounts_name_SelectedIndexChanged(sender As Object, e As EventArgs)
        If XtraForm1.w14.Text = True Then
            If Accounts_name.Text <> Nothing Then
                Label19.Visible = True
                Accounts_balace.Visible = True
            Else
                Label19.Visible = False
                Accounts_balace.Visible = False
            End If
        End If

        Accounts_code.Text = getcodeaccounts(Accounts_name.Text)
    End Sub
    Private Sub Accounts_code_TextChanged(sender As Object, e As EventArgs) Handles Accounts_code.TextChanged

          
            Accounts_balace.Text = ""
            Accounts_phone1.Text = ""
            Accounts_phone2.Text = ""
            data_sale.Value = Now.Date
            Dim sql = "select * from importer where impcode=N'" & (Accounts_code.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                Accounts_balace.Text = dr!Accounts_balace
                Accounts_phone1.Text = dr!imp_phone1
                Accounts_phone2.Text = dr!imp_phone2
                Accounts_adress.Text = dr!imp_address
            End If
            date_sale()
            day_sale.Text = (Now.Date - data_sale.Value).Days
            If report_imp.Checked = True Then
                fill_report()
            End If
        Dim str = "select sum(imptrans_Debtor), sum(imptrans_Creditor) from importer_trans where imptomer_name=N'" & Accounts_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("importer_trans")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        Dim sumcredit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
        Accounts_balace.Text = Format(Val(sumdebit), "#,0.00") - Format(Val(sumcredit), "#,0.00")
    End Sub
    Sub balace_cus()
        Dim sql = "select * from customer where Cuscode=N'" & (Accounts_code.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            Accounts_balace.Text = dr!Accounts_balace
        End If
    End Sub
    Sub date_sale()
        Try
            Dim str = "select max(Purchases_number) from order_add where Accounts_code=N'" & Accounts_code.Text & "'"
            Dim cmd As New SqlCommand(str, sqlconn)
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            Dim dta As New DataTable("order_add")
            da.Fill(dta)
            Dim sumdebit As Decimal = 0.0
            If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
            Label22.Text = Val(sumdebit)


            Dim sql = "select * from order_add where Purchases_number=N'" & (Label22.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                data_sale.Value = dr!Purchases_date
            End If
        Catch ex As Exception

        End Try
       
    End Sub
    Private Sub Accounts_balace_TextChanged(sender As Object, e As EventArgs)
        balace_show.Text = Accounts_balace.Text
    End Sub
    Private Sub item_descound_KeyDown(sender As Object, e As KeyEventArgs) Handles item_descound.KeyDown
        If e.KeyCode = Keys.Enter Then
            If e.KeyCode = Keys.Enter Then
                inserte_Click(Nothing, Nothing)
                item_name.Focus()
                plus_invoice()
            End If
        End If
    End Sub
    Private Sub monetary_CheckedChanged(sender As Object, e As EventArgs) Handles monetary.CheckedChanged
        If monetary.Checked = True Then

            monetary.ForeColor = Color.Black
            Yup.ForeColor = Color.Black
            monetary.BackColor = Color.Gainsboro
            Yup.BackColor = Color.Gainsboro
        End If
        plus_invoice()
    End Sub
    Private Sub Yup_CheckedChanged(sender As Object, e As EventArgs) Handles Yup.CheckedChanged
        If Yup.Checked = True Then

            monetary.ForeColor = Color.White
            Yup.ForeColor = Color.White
            monetary.BackColor = Color.RoyalBlue
            Yup.BackColor = Color.RoyalBlue
        End If
        plus_invoice()
        If Yup.Checked = True Then
            pay_money.Text = 0
        End If
    End Sub
    Private Sub Label45_Click(sender As Object, e As EventArgs) Handles Label45.Click
        If XtraForm1.m54.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تعديل صنف "
        f.MdiParent = XtraForm1
        f.show_data(code.Text)
        f.Show()
    End Sub
    Private Sub Label46_Click(sender As Object, e As EventArgs) Handles Label46.Click
        fill_item()
        code.Text = item_name.EditValue
    End Sub
    Private Sub print_button_Click(sender As Object, e As EventArgs)
        day_sale.Text = (Now.Date - data_sale.Value).Days
    End Sub
    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged
        Dim sql = "select balace from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)

        Dim dr = dt.Rows(0)
        treasury_balace.Text = dr!balace

    End Sub
   
    Private Sub item_barcode_TextChanged(sender As Object, e As EventArgs)
        Try
            Dim sql = "select * from item_add where code=N'" & (code.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                code2.Text = dr!code
            End If
            inserte_Click(Nothing, Nothing)
            item_barcode.Focus()

        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub

    Private Sub store_SelectedIndexChanged(sender As Object, e As EventArgs)
        fill_item()
    End Sub
 

    Function getcode(subname) As String

        Dim sql = "select * from item where itemstore=N'" & (store.Text) & "' and itemnamearabic=N'" & (item_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("itemcode") Else Return ""
    End Function

    Private Sub invoice_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        new_btn_Click(Nothing, Nothing)
        item_barcode.Visible = False
        set_fatore()
        column_devexpress()
        column_visible()
        SplitContainerControl1.SplitterPosition = My.Settings.f_size
    End Sub
  
   
    Sub lock_fatora()
        Accounts_name.Enabled = False
        invoice_note.Enabled = False
        store.Enabled = False
        invoice_number.Enabled = False
        invoice_date.Enabled = False
        GroupBox1.Enabled = False
        descound_Rate.Enabled = False
        descound_Values.Enabled = False
        tax_Rate.Enabled = False
        tax_Values.Enabled = False
        delegate_name.Enabled = False

        type_pay.Enabled = False
        Date_pay.Enabled = False
        pay_money.Enabled = False

        monetary.Enabled = False
        Yup.Enabled = False
    End Sub
    Sub open_fatora()
        Accounts_name.Enabled = True
        invoice_note.Enabled = True
        store.Enabled = True
        invoice_number.Enabled = True
        invoice_date.Enabled = True
        GroupBox1.Enabled = True
        descound_Rate.Enabled = True
        descound_Values.Enabled = True
        tax_Rate.Enabled = True
        tax_Values.Enabled = True
        delegate_name.Enabled = True

        type_pay.Enabled = True
        Date_pay.Enabled = True
        pay_money.Enabled = True
        monetary.Enabled = True
        Yup.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        save_btn.Enabled = True
    End Sub
    Sub column_visible()
        Dim sql = "select * from column_visible where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            GridColumn2.Visible = dr!GridColumn2
            GridColumn7.Visible = dr!GridColumn7
            GridColumn8.Visible = dr!GridColumn8
            GridColumn9.Visible = dr!GridColumn9
            GridColumn10.Visible = dr!GridColumn10
            disc_rate.Visible = dr!GridColumn10
            Label5.Visible = dr!GridColumn10
            item_total.Visible = dr!GridColumn10
            Label17.Visible = dr!GridColumn10
            inserte.Visible = dr!GridColumn10
            GridColumn13.Visible = dr!GridColumn13
            GridColumn12.Visible = dr!GridColumn12
            GridColumn5.Visible = dr!GridColumn5
            GridColumn22.Visible = dr!GridColumn22
            GridColumn23.Visible = dr!GridColumn23
            GridColumn1.Visible = dr!GridColumn1
            GridColumn14.Visible = dr!GridColumn14
            GridColumn11.Visible = dr!GridColumn11
            GridColumn15.Visible = dr!GridColumn15
            GridColumn16.Visible = dr!GridColumn16
            GridColumn17.Visible = dr!GridColumn17
            GridColumn18.Visible = dr!GridColumn18
            GridColumn19.Visible = dr!GridColumn19
            GridColumn20.Visible = dr!GridColumn20
            GridColumn3.Visible = dr!GridColumn3
            GridColumn4.Visible = dr!GridColumn4
            GridColumn6.Visible = dr!GridColumn6
            GridColumn21.Visible = dr!GridColumn21
        End If
    End Sub

    Sub column_devexpress()
        dtt = New DataTable
        dtt.Columns.Add("كود")
        dtt.Columns.Add("أسم الصنف")
        dtt.Columns.Add("الوحدة")
        dtt.Columns.Add("الكمية")
        dtt.Columns.Add("السعر")
        dtt.Columns.Add("الخصم")
        dtt.Columns.Add("الأجمالي")
        dtt.Columns.Add("التصنيف")
        dtt.Columns.Add("الشركة")
        dtt.Columns.Add("ضريبة الصنف")
        dtt.Columns.Add("مصنعية التركيب")
        dtt.Columns.Add("الكمية الحالية")
        dtt.Columns.Add("المجموعة")
        dtt.Columns.Add("الرصيد")
        dtt.Columns.Add("نوع الصنف")
        dtt.Columns.Add("بعد الخصم")
        dtt.Columns.Add("خصم نقدي")
        GridControl1.DataSource = dtt
    End Sub


    Sub set_fatore()
        If XtraForm1.w3.Text = False Then
            item_price.Enabled = False
        End If

        If XtraForm1.w5.Text = False Then
            descound_Rate.Enabled = False
            descound_Values.Enabled = False
        End If
        If XtraForm1.w6.Text = False Then
            tax_Rate.Enabled = False
            tax_Values.Enabled = False
        End If
        If XtraForm1.w7.Text = False Then
            Yup.Enabled = False
        End If
        If XtraForm1.w8.Text = False Then
            XtraTabPage7.PageVisible = False
        End If
        If XtraForm1.w9.Text = False Then
            XtraTabPage8.PageVisible = False
        End If
        If XtraForm1.w16.Text = False Then
            item_descound.Enabled = False
        End If
        If XtraForm1.d21.Text = False Then
            invoice_note.Enabled = False
        End If
    End Sub

    Private Sub code_TextChanged(sender As Object, e As EventArgs) Handles code.TextChanged
        count_text.Text = ""
        item_count.Text = 1
        item_type.Text = ""
        show_count.Text = ""
        show_datebuy.Text = ""
        item_total2.Text = 0
        item_total.Text = 0
        show_datesale.Text = ""
        dgv_2.DataSource = Nothing
        dgv_2.Rows.Clear()
        Dim sql = "select (itemcode),(itemnamearabic),(itemcount),(item_notes),(item_type),(Itemtacategory),(itemcompany),(item_group),(item_tax_add),(tax_add),(block_sale),(itemcouta) from item where itemcode=N'" & (code.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            code2.Text = dr!itemcode
            item_name.Text = dr!itemnamearabic
            show_count.Text = dr!itemcount
            item_notes.Text = dr!item_notes
            item_type.Text = dr!item_type
            fill_unit()
            fill_unit_item()
          
            item_catorgey.Text = dr!Itemtacategory
            item_company.Text = dr!itemcompany
            item_group.Text = dr!item_group
            item_tax_add.Text = dr!item_tax_add
            tax_add.Text = dr!tax_add
            block_sale.Text = dr!block_sale
            itemcouta.Text = dr!itemcouta
           
        End If
        If Alternativeitem.Checked = True Then
            fill_itemalternavite()
        End If
        buybeta()
        salebeta()
        If tax_add.Text > 0 Then
            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(tax_add.Text) / 100)
            Dim sum = Format(Val(show_buy.Text) * Val(s), "0.00")
            item_price.Text = Val(sum) + Val(show_buy.Text)
        End If
        If XtraForm1.sale_buy.Text = "متوسط الشراء" Then
            Dim str = "select sum(price), count(price) from avg_item where code=N'" & code.Text & "'"
            Dim cmd As New SqlCommand(str, sqlconn)
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            Dim dta As New DataTable("avg_item")
            da.Fill(dta)

            Dim sumdebit As Decimal = 0.0
            Dim sumcredit As Decimal = 0.0
            If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
            If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
            show_buy.Text = Format((Val(sumdebit) / Val(sumcredit)), "0.00")
        End If

        item_unit_SelectedIndexChanged(Nothing, Nothing)

    End Sub
    Sub fill_unit()
        Try
            item_unit.Properties.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from unit_item where CODE=N'" & (code.Text) & "'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                item_unit.Properties.Items.Add(dt.Rows(i).Item("unit"))
            Next
            item_unit.SelectedIndex = 0
        Catch ex As Exception

        End Try

    End Sub
    Sub fill_unit_item()
        Dim adp As New SqlDataAdapter("select * from unit_item where code=N'" & (code.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        DataGridView1.AutoGenerateColumns = False
        DataGridView1.DataSource = dt
        Try

            If DataGridView1.Rows.Count = 3 Then
                threeunit()
            End If
            If DataGridView1.Rows.Count = 2 Then
                twounit()
            End If
            If DataGridView1.Rows.Count = 1 Then
                oneunit()
            End If

        Catch ex As Exception

        End Try
        For i = 0 To DataGridView1.Rows.Count - 1
            If DataGridView1.Rows(i).Cells(2).Value <> 0 Then
                count_text.Text = count_text.Text & " و " & DataGridView1.Rows(i).Cells(2).Value & " " & DataGridView1.Rows(i).Cells(0).Value
            End If
        Next
    End Sub
    Private Sub threeunit()
        Try


            Dim unit1, unit2, unit3, qty1, qty2, qty3, qtyo2, qtyo3, fin1, fin2 As Double
            Dim qty = Val(show_count.Text)
            unit1 = Val(DataGridView1.Rows(0).Cells(1).Value)
            unit2 = Val(DataGridView1.Rows(1).Cells(1).Value)
            unit3 = Val(DataGridView1.Rows(2).Cells(1).Value)

            qty3 = Format(qty / unit3, "0000.")
            If qty3 * unit3 > qty Then
                qty3 = qty3 - 1
            End If

            qtyo3 = qty3 * unit3
            fin1 = qty - qtyo3



            qty2 = Format(fin1 / unit2, "00.")
            If qty2 * unit2 > fin1 Then
                qty2 = qty2 - 1
            End If

            qtyo2 = qty2 * unit2
            fin2 = fin1 - qtyo2
            qty1 = fin2 / unit1




            DataGridView1.Rows(0).Cells(2).Value = qty1

            DataGridView1.Rows(1).Cells(2).Value = qty2

            DataGridView1.Rows(2).Cells(2).Value = qty3
        Catch ex As Exception

        End Try
    End Sub
    Private Sub twounit()
        Try


            Dim unit2, unit3, qty2, qty3, qtyo3, fin1 As Double
            Dim qty = Val(show_count.Text)

            unit2 = Val(DataGridView1.Rows(0).Cells(1).Value)
            unit3 = Val(DataGridView1.Rows(1).Cells(1).Value)

            qty3 = Format(qty / unit3, "0000.")
            If qty3 * unit3 > qty Then
                qty3 = qty3 - 1
            End If

            qtyo3 = qty3 * unit3
            fin1 = qty - qtyo3
            qty2 = fin1 / unit2




            DataGridView1.Rows(0).Cells(2).Value = qty2

            DataGridView1.Rows(1).Cells(2).Value = qty3


        Catch ex As Exception

        End Try
    End Sub
    Private Sub oneunit()
        Try


            Dim unit1, qty1 As Double
            Dim qty = Val(show_count.Text)
            unit1 = Val(DataGridView1.Rows(0).Cells(1).Value)



            qty1 = qty / unit1


            DataGridView1.Rows(0).Cells(2).Value = qty1


        Catch ex As Exception

        End Try

    End Sub
    Sub fill_itemalternavite()
        Dim adp As New SqlDataAdapter("select * from Alternative_item where itemname=N'" & (item_name.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_2.AutoGenerateColumns = False
        dgv_2.DataSource = dt
    End Sub
    Sub fill_report()
        Dim adp As New SqlDataAdapter("select * from importer_trans where imptomer_name=N'" & (Accounts_name.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_3.AutoGenerateColumns = False
        dgv_3.DataSource = dt
    End Sub

    Sub buybeta()

        Dim str = "select max(invoice_number) from order_list where item_name=N'" & item_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("order_list")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        code_pharche.Text = Val(sumdebit)


        Dim sql = "select * from order_list where invoice_number=N'" & (code_pharche.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            show_datebuy.Text = dr!item_date
        End If
    End Sub
    Sub salebeta()

        Dim str = "select max(invoice_number) from invoice_list where item_name=N'" & item_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("invoice_list")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        code_invoice.Text = Val(sumdebit)


        Dim sql = "select * from invoice_list where invoice_number=N'" & (code_invoice.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            show_datesale.Text = dr!item_date
        End If
    End Sub
  
    Private Sub new_btn_Click(sender As Object, e As EventArgs)
        fill_store()
        fill_item()
        item_total2.Text = 0
        item_total.Text = 0
        fill_account()
        GroupBox6.Visible = False
        fill_treasury_name()
        invoice_note.Text = ""
        invoice_number.Text = getlastcode("order_add", "invoice_number") + 1
        Date_pay.EditValue = Now.Date
        invoice_date.EditValue = Now.Date
        item_name.Text = ""
        new_balace.Text = 0
        item_count.Text = 0
        item_price.Text = 0
        Accounts_code.Text = 0
        show_count.Text = ""
        GridControl1.DataSource = Nothing
        GridControl2.DataSource = Nothing
        show_buy.Text = ""
        delegate_name.Text = ""
        disc_rate.Text = 0
        Try
            dtt.Clear()
        Catch ex As Exception

        End Try
        'fill_curency()

        fill_catorhey()
        item_notes.Text = ""
        open_fatora()
        cuscity.Text = ""
        item_tax_add.Text = 0
        tax_add.Text = 0
        Label19.Visible = False
        cusGovernorate.Text = ""
        CusCreditlimit.Text = 0
        pay_money.Text = 0
        show_datebuy.Text = ""
        cus_price_private.Text = 0
        Cuscusgroup.Text = ""
        tax_Values.Text = 0
        balace_show.Text = ""
        Accounts_phone2.Text = ""

        show_datesale.Text = ""
        invoice_tax.Text = 0
        tax_Values.Text = 0
        tax_Rate.Text = XtraForm1.tax_pur.Text
        Accounts_phone1.Text = ""
        total_invoice.Text = 0
        Accounts_adress.Text = ""
        total_invoice.Text = 0
        descound_Rate.Text = 0
        item_descound.Text = 0
        descound_Values.Text = 0
        after_total.Text = 0
        show_total.Text = 0
        data_sale.Value = Now.Date
        invoice_descound.Text = 0
        invoice_count.Text = 0
        invoice_pound.Text = 0
        monetary.Checked = True
        dgv_10.DataSource = Nothing
        dgv_10.Rows.Clear()
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        save_btn.Enabled = True

        store.Text = XtraForm1.store.Text
        type_pay.SelectedIndex = 0
        If XtraForm1.m74.Checked = False Then
            show_buy.Visible = False
        End If
        If XtraForm1.m75.Checked = False Then
            show_count.Visible = False
        End If
        fill_delegate()
        show_buy.Text = ""
        item_from.Value = Now.Date
        item_to.Value = Now.Date
        ComboBoxEdit1.SelectedIndex = 0
        ComboBoxEdit2.SelectedIndex = 0
        new_balace.Text = 0
    End Sub
    Private Sub SimpleButton9_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs)
        If XtraForm1.m72.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If MsgBox("هل انت متأكد بحذف الفاتورة المحدد؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        show_data(invoice_number.Text)
        Dim sql = "select * from order_add where invoice_number=N'" & invoice_number.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        Try
            For a = 0 To GridView2.RowCount - 1
                sql = "select * from order_list where invoice_number=N'" & invoice_number.Text.Trim() & "'"
                adp = New SqlDataAdapter(sql, sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
        Catch ex As Exception

        End Try
        Try
            For a = 0 To dgv_10.Rows.Count - 1
                sql = "select * from order_collection where invoice_number=N'" & invoice_number.Text.Trim() & "'"
                adp = New SqlDataAdapter(sql, sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
        Catch ex As Exception

        End Try
        new_btn_Click(Nothing, Nothing)
        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
    End Sub

    Sub delete_order()
        Dim sql = "select * from order_add where invoice_number=N'" & invoice_number.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        adp.Dispose()
        ds.Dispose()
        dt.Dispose()
        Try
            For a = 0 To GridView2.RowCount - 1
                sql = "select * from order_list where invoice_number=N'" & invoice_number.Text.Trim() & "'"
                adp = New SqlDataAdapter(sql, sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
        Catch ex As Exception

        End Try
        Try
            For a = 0 To dgv_10.Rows.Count - 1
                sql = "select * from order_collection where invoice_number=N'" & invoice_number.Text.Trim() & "'"
                adp = New SqlDataAdapter(sql, sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
        Catch ex As Exception

        End Try
    End Sub
    Private Sub edit_btn_Click(sender As Object, e As EventArgs)
        If XtraForm1.m73.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If MsgBox("هل انت متأكد بتعديل الفاتورة المحددة ؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        Try
            show_data(invoice_number.Text)
            Dim sql = "select * from order_add where invoice_number=N'" & invoice_number.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            Try
                For a = 0 To GridView2.RowCount - 1
                    sql = "select * from order_list where invoice_number=N'" & invoice_number.Text.Trim() & "'"
                    adp = New SqlDataAdapter(sql, sqlconn)
                    ds = New DataSet
                    adp.Fill(ds)
                    dt = ds.Tables(0)
                    If dt.Rows.Count = 0 Then
                    Else
                        Dim dr = dt.Rows(0)
                        dr.Delete()
                        Dim cmd As New SqlCommandBuilder(adp)
                        adp.Update(dt)
                    End If
                Next
                adp.Dispose()
                ds.Dispose()
                dt.Dispose()
            Catch ex As Exception

            End Try
            Try
                For a = 0 To dgv_10.Rows.Count - 1
                    sql = "select * from order_collection where invoice_number=N'" & invoice_number.Text.Trim() & "'"
                    adp = New SqlDataAdapter(sql, sqlconn)
                    ds = New DataSet
                    adp.Fill(ds)
                    dt = ds.Tables(0)
                    If dt.Rows.Count = 0 Then
                    Else
                        Dim dr = dt.Rows(0)
                        dr.Delete()
                        Dim cmd As New SqlCommandBuilder(adp)
                        adp.Update(dt)
                    End If
                Next
                adp.Dispose()
                ds.Dispose()
                dt.Dispose()
            Catch ex As Exception

            End Try
            balace_cus()
            edit_btn.Enabled = False
            delet_btn.Enabled = False
            save_btn.Enabled = True
        Catch ex As Exception
            MsgBox("فشل تعديل الفاتورة اعد المحاولة مرة أعد المحاولة مرة أخري")
        End Try
    End Sub

    Private Sub invoice_number_TextChanged(sender As Object, e As EventArgs)
    End Sub

    Private Sub Alternativeitem_CheckedChanged(sender As Object, e As EventArgs) Handles Alternativeitem.CheckedChanged
        code_TextChanged(Nothing, Nothing)
    End Sub

    Private Sub report_imp_CheckedChanged(sender As Object, e As EventArgs) Handles report_imp.CheckedChanged
        Accounts_code_TextChanged(Nothing, Nothing)
    End Sub


    Private Sub print_btn_Click()
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_qq(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from order_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "Purchases_a4.repx"), True)
        rep.DataSource = dt
        rep.ShowPrintMarginsWarning = False
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            If XtraForm1.paper2.Text = 1 Then
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_defult_2.Text)
                End Using
            End If

            If XtraForm1.paper2.Text = 2 Then
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_defult_2.Text)
                End Using
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_defult_2.Text)
                End Using
            End If

        End If
    End Sub

    'Sub 'fill_curency()

    '    curency_item.Properties.DataSource = Nothing
    '    curency_item.EditValue = ""
    '    Dim adp As New SqlDataAdapter("select * from currency", sqlconn)
    '    Dim ds As New DataSet
    '    adp.Fill(ds)
    '    Dim dt = ds.Tables(0)
    '    curency_item.Properties.DataSource = dt
    '    curency_item.Properties.DisplayMember = "c1"
    '    curency_item.Properties.ValueMember = "c2"
    'End Sub

    Sub fill_catorhey()
        Dim adp As New SqlDataAdapter("select * from Category", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_catarogy.AutoGenerateColumns = False
        dgv_catarogy.DataSource = dt

    End Sub
  

    Private Sub invoice_add_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            new_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            If save_btn.Enabled = True Then
                save_btn_Click(Nothing, Nothing)
            End If

        End If
        If e.KeyCode = Keys.F3 Then
            edit_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then

            delet_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F5 Then
            If print_btn.Enabled = True Then
                Button6_Click(Nothing, Nothing)
            End If

        End If
        If e.KeyCode = Keys.F6 Then
            Accounts_name.Focus()
        End If

        If e.KeyCode = Keys.F7 Then
            item_name.Focus()
        End If
        If e.KeyCode = Keys.F8 Then
            pay_money.Focus()
        End If
        If e.KeyCode = Keys.F9 Then
            If show_itemcus.Checked = False Then
                show_itemcus.Checked = True
                SimpleButton1_Click(Nothing, Nothing)
                Exit Sub
            End If
            If show_itemcus.Checked = True Then
                show_itemcus.Checked = False

                Exit Sub
            End If
        End If
        If e.KeyCode = Keys.F12 Then
            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If

        If e.KeyCode = Keys.Escape Then
            If TextEdit1.Text <> Nothing Then
                TextEdit1.Text = ""
                TextEdit1.Focus()
                Exit Sub
            End If

            If MsgBox("هل انت متأكد من الخروج؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
                Exit Sub
            End If
            Me.Dispose()
        End If
    End Sub

    Private Sub pay_money_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Double.TryParse((pay_money.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub PictureBox2_Click(sender As Object, e As EventArgs)
        Process.Start("https://www.facebook.com/Beta-store-361440301309862/?ref=aymt_homepage_panel&eid=ARDbnMA3vmMHk2XGLGVvVV8J1evZLRO5o7Tvq5jTv8YqAAkiBzn4VxmaIZi4mobvru532j8T3ry184b9")
    End Sub


    Private Sub TextBox24_TextChanged_1(sender As Object, e As EventArgs) Handles lastpay.TextChanged

        Dim sql = "select * from invoice_list where id=N'" & (lastpay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            item_price.Text = dr!item_price
            disc_rate.Text = dr!item_descound
        End If
    End Sub
    Private Sub item_barcode_KeyDown(sender As Object, e As KeyEventArgs) Handles item_barcode.KeyDown
        If e.KeyCode = Keys.Enter Then

            Dim sql = "select * from unit_item where item_unit=N'" & (item_barcode.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                code.Text = dr!code
            Else
                MsgBox("لايوجد باركود لهذا الرقم")
                item_barcode.Text = ""
                item_barcode.Focus()
                Exit Sub
            End If

            Try
                Dim sql7 = "select * from unit_item where item_unit=N'" & (item_barcode.Text) & "'"
                Dim adp7 As New SqlDataAdapter(sql7, sqlconn)
                Dim ds7 As New DataSet
                adp7.Fill(ds7)
                Dim dt7 = ds7.Tables(0)
                If dt7.Rows.Count > 0 Then
                    Dim dr7 = dt7.Rows(0)
                    item_unit.Text = dr7!unit
                End If
            Catch ex As Exception

            End Try


            inserte_Click(Nothing, Nothing)
            item_barcode.Text = ""
            item_barcode.Focus()
        End If
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs)
        code.Text = getcode(item_name.Text)
    End Sub
    Private Sub Accounts_name_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                If MsgBox("هذا العميل غير موجود هل تريد أضافتة ؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Ok Then
                    Dim f As customer_add = New customer_add()
                    f.Text = "تسجيل عميل"
                    f.new_cus()
                    f.MdiParent = XtraForm1
                    f.Cusname.Text = Accounts_name.Text
                    f.Cusname.Focus()
                    f.Show()
                End If


            End If
            item_name.Focus()
        End If
    End Sub

    Private Sub TextEdit1_KeyDown(sender As Object, e As KeyEventArgs) Handles item_count.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_price.Focus()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_price.Text.Length
        End If
        If e.KeyCode = Keys.Left Then
            item_price.Focus()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_price.Text.Length
        End If
        If e.KeyCode = Keys.Right Then
            item_unit.Select()
            item_unit.SelectionStart = 0
            item_unit.SelectionLength = item_unit.Text.Length
        End If
    End Sub

    Private Sub TextEdit1_KeyDown_1(sender As Object, e As KeyEventArgs) Handles item_price.KeyDown
        If e.KeyCode = Keys.Enter Then
            If disc_rate.Visible = False Then
                item_total2.Text = Val(item_price.Text) * Val(item_count.Text)
                item_total2.Select()
                item_total2.SelectionStart = 0
                item_total2.SelectionLength = item_total2.Text.Length

            Else

                disc_rate.Select()
                disc_rate.SelectionStart = 0
                disc_rate.SelectionLength = disc_rate.Text.Length
            End If
        End If
        If e.KeyCode = Keys.Left Then
            If disc_rate.Visible = False Then
                item_total2.Text = Val(item_price.Text) * Val(item_count.Text)
                item_total2.Select()
                item_total2.SelectionStart = 0
                item_total2.SelectionLength = item_total2.Text.Length

            Else

                disc_rate.Select()
                disc_rate.SelectionStart = 0
                disc_rate.SelectionLength = disc_rate.Text.Length
            End If
        End If
        If e.KeyCode = Keys.Right Then
            item_count.Select()
            item_count.SelectionStart = 0
            item_count.SelectionLength = item_count.Text.Length
        End If
    End Sub

    Private Sub item_name_KeyDown_1(sender As Object, e As KeyEventArgs) Handles item_name.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from item where itemnamearabic=N'" & (item_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                If MsgBox("هذا الصنف غير موجود هل تريد أضافتة ؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Ok Then
                    Dim f As item_add = New item_add()
                    f.Text = "تسجيل صنف"
                    f.MdiParent = XtraForm1
                    f.new_item()
                    f.Show()
                    f.new_unit()
                    f.itemunit()
                    f.itemnamearabic.Text = item_name.Text
                    f.itemnamearabic.Focus()
                End If


            End If
            code.Text = item_name.EditValue
            check = 2
            item_unit.Focus()
        End If
        If e.KeyCode = Keys.Delete Then
            item_name.Text = ""
        End If
        If e.KeyCode = Keys.ControlKey Then
            If disc_rate.Visible = True Then
                disc_rate.Select()
            Else
                item_price.Select()
            End If
        End If
    End Sub

    Private Sub item_name_EditValueChanged(sender As Object, e As EventArgs) Handles item_name.EditValueChanged
        code.Text = item_name.EditValue
    End Sub

    Private Sub Button1_Click_1(sender As Object, e As EventArgs) Handles Button1.Click
        inserte_Click(Nothing, Nothing)
    End Sub

    Private Sub item_type_TextChanged(sender As Object, e As EventArgs) Handles item_type.TextChanged
        If item_type.Text = "مخزون تجميع" Then
            dgv_total.DataSource = Nothing
            dgv_total.Rows.Clear()
            Dim adp As New SqlDataAdapter("select * from item_collection  where item_first=N'" & (code.Text) & "'", sqlconn)
            Dim ds = New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                dgv_total.Rows.Add()
                dgv_total.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
                dgv_total.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_name")
                dgv_total.Rows(s).Cells(2).Value = dt.Rows(s).Item("item_count")
            Next
        End If
    End Sub

    Private Sub Button2_Click_1(sender As Object, e As EventArgs) Handles new_btn.Click
        new_btn_Click(Nothing, Nothing)
    End Sub
    Private Sub Button4_Click_1(sender As Object, e As EventArgs) Handles edit_btn.Click
        edit_btn_Click(Nothing, Nothing)
        open_fatora()
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        delet_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        If save_btn.Enabled = False Then
            Dim adp As New SqlDataAdapter("select * from order_print where invoice_number=N'" & invoice_number.Text & "'", sqlconn)
            Dim ds As New DataSet

            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
                Exit Sub
            End If
            Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "Purchases_order_a4.repx"), True)
            rep.DataSource = dt
            If XtraForm1.RadioButton1.Checked = True Then
                Dim frm As New preview
                preview.DocumentViewer1.DocumentSource = rep
                preview.Show()
            Else
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(XtraForm1.printer_name.Text)
                End Using
            End If
        Else
           
                print_btn_Click()

        End If
       
    End Sub

    Private Sub CalcEdit1_KeyDown(sender As Object, e As KeyEventArgs) Handles descound_Rate.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(descound_Rate.Text) / 100)
            Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
            descound_Values.Text = Format(Val(sum), "0.00")
            invoice_descound.Text = Format(Val(descound_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub

    Private Sub CalcEdit2_KeyDown(sender As Object, e As KeyEventArgs) Handles descound_Values.KeyDown
        If e.KeyCode = Keys.Enter Then
            invoice_descound.Text = Format(Val(descound_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub

    Private Sub CalcEdit3_KeyDown(sender As Object, e As KeyEventArgs) Handles tax_Rate.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(tax_Rate.Text) / 100)
            Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
            tax_Values.Text = Format(Val(sum), "0.00")
            invoice_tax.Text = Format(Val(tax_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub

    Private Sub CalcEdit4_KeyDown(sender As Object, e As KeyEventArgs) Handles tax_Values.KeyDown
        If e.KeyCode = Keys.Enter Then
            invoice_tax.Text = Format(Val(tax_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs)
        save_qq(Nothing, Nothing)
    End Sub

    Private Sub pay_money_EditValueChanged(sender As Object, e As EventArgs) Handles pay_money.EditValueChanged
        new_balace.Text = (Val(Accounts_balace.Text) - Val(total_invoice.Text)) + Val(pay_money.Text)
        money_plus.Text = (Val(Accounts_balace.Text) - Val(total_invoice.Text))
        TextBox8.Text = Val(pay_money.Text)
    End Sub

    Private Sub save_qq(sender As Object, e As EventArgs)
        Try
            If GridView2.RowCount = 0 Then
                MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
                Exit Sub
            End If
            invoice_number.Text = getlastcode("order_add", "invoice_number") + 1
            printcode.Text = getlastcode("order_add", "invoice_number") + 1
            pay_money_EditValueChanged(Nothing, Nothing)
            If monetary.Checked = True Then
                pay_money.Text = Val(show_total.Text)
            End If

            Dim sql = "select * from order_add where invoice_number=N'" & (invoice_number.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
            Else
                Dim dr = dt.NewRow
                dr!invoice_number = invoice_number.Text
                dr!store = store.Text
                dr!invoice_date = invoice_date.EditValue
               
                    dr!Accounts_name = Accounts_name.Text


                dr!Accounts_code = Accounts_code.Text
                dr!invoice_note = invoice_note.Text
                dr!invoice_descound = invoice_descound.Text
                dr!invoice_tax = invoice_tax.Text
                dr!invoice_count = invoice_count.Text
                dr!total_invoice = total_invoice.Text
                dr!delegate_name = delegate_name.Text
                dr!type_pay = type_pay.Text
                dr!Cuscusgroup = Cuscusgroup.Text
                dr!cusGovernorate = cusGovernorate.Text
                dr!cuscity = cuscity.Text
                dr!pay_money = pay_money.Text
                dr!code_print = 1
                dr!user_invoice = XtraForm1.user_name.Text
                dr!total_string = amount_string.Text
                dr!invoice_pound = invoice_pound.Text
                dr!pay = pay_money.Text
                dr!new_balace = new_balace.Text
                dr!Accounts_adress = Accounts_adress.Text
                dr!Accounts_phone1 = Accounts_phone1.Text
                dr!past_balace = Accounts_balace.Text

                Try
                    dr!past_balace = Accounts_balace.Text
                Catch ex As Exception
                    dr!past_balace = 0
                End Try

                dr!money_plus = money_plus.Text
                dr!Date_pay = Date_pay.Text


                If monetary.Checked = True Then
                    dr!type_money = "نقدي"
                End If
                If Yup.Checked = True Then
                    dr!type_money = "أجل"
                End If

                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            '    '============حفظ السحل================
            adp = New SqlDataAdapter("select * from order_list", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            For i = 0 To GridView2.RowCount - 1
                Dim Dr = dt.NewRow

                Dr!invoice_number = invoice_number.Text
                Dr!store = store.Text
                Dr!Accounts_code = Accounts_code.Text
                
                    Dr!Accounts_name = Accounts_name.Text


                Dr!item_code = GridView2.GetRowCellValue(i, "كود").ToString
                Dr!item_name = GridView2.GetRowCellValue(i, "أسم الصنف").ToString
                Dr!item_unit = GridView2.GetRowCellValue(i, "الوحدة").ToString
                Dr!item_count = GridView2.GetRowCellValue(i, "الكمية").ToString
                Dr!item_price = GridView2.GetRowCellValue(i, "السعر").ToString
                Dr!item_descound = GridView2.GetRowCellValue(i, "الخصم").ToString
                Dr!item_total = GridView2.GetRowCellValue(i, "الأجمالي").ToString
                Dr!item_date = invoice_date.EditValue
                Dr!item_catorgey = GridView2.GetRowCellValue(i, "التصنيف").ToString
                Dr!item_company = GridView2.GetRowCellValue(i, "الشركة").ToString
                Dr!tax_add = GridView2.GetRowCellValue(i, "ضريبة الصنف").ToString
                Dr!item_tax_add = GridView2.GetRowCellValue(i, "مصنعية التركيب").ToString
                Dr!new_count = GridView2.GetRowCellValue(i, "الكمية الحالية").ToString
                Dr!item_group = GridView2.GetRowCellValue(i, "المجموعة").ToString
                Dr!item_balace = GridView2.GetRowCellValue(i, "الرصيد").ToString
                Dr!item_type = GridView2.GetRowCellValue(i, "نوع الصنف").ToString
                Dr!after_desc = GridView2.GetRowCellValue(i, "بعد الخصم").ToString
                Dr!desc_money = GridView2.GetRowCellValue(i, "خصم نقدي").ToString
                dt.Rows.Add(Dr)
                Dim cmdbuilder As New SqlCommandBuilder(adp)
                adp.Update(dt)

            Next
            '    '======================================== حفظ الحركات
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            '    '============حفظ السحل================
            If dgv_10.Rows.Count > 0 Then
                adp = New SqlDataAdapter("select * from order_collection", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                For g = 0 To dgv_10.Rows.Count - 1
                    Dim Dr = dt.NewRow
                    Dr!invoice_number = invoice_number.Text
                    Dr!store = store.Text
                    Dr!Accounts_code = Accounts_code.Text
                    Dr!Accounts_name = Accounts_name.Text
                    Dr!item_code = dgv_10.Rows(g).Cells(0).Value
                    Dr!item_name = dgv_10.Rows(g).Cells(1).Value
                    Dr!item_unit = dgv_10.Rows(g).Cells(2).Value
                    Dr!item_count = dgv_10.Rows(g).Cells(3).Value
                    Dr!item_price = dgv_10.Rows(g).Cells(4).Value
                    Dr!item_descound = dgv_10.Rows(g).Cells(5).Value
                    Dr!item_total = Convert.ToDecimal(dgv_10.Rows(g).Cells(6).Value)
                    Dr!item_date = invoice_date.EditValue
                    Dr!item_earn = Convert.ToDecimal(dgv_10.Rows(g).Cells(8).Value)
                    Dr!item_catorgey = dgv_10.Rows(g).Cells(9).Value
                    Dr!item_company = dgv_10.Rows(g).Cells(10).Value
                    Dr!tax_add = dgv_10.Rows(g).Cells(12).Value
                    Dr!item_tax_add = dgv_10.Rows(g).Cells(13).Value
                    Dr!new_count = dgv_10.Rows(g).Cells(14).Value
                    Dr!item_group = dgv_10.Rows(g).Cells(15).Value
                    Dr!item_balace = dgv_10.Rows(g).Cells(16).Value
                    Dr!code_first = dgv_10.Rows(g).Cells(17).Value
                    dt.Rows.Add(Dr)
                    Dim cmdbuilder As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                Next
            End If


            new_btn_Click(Nothing, Nothing)
            My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.Background)
        Catch ex As Exception
            MsgBox("فشل حفظ الفاتورة اعد المحاولة", MsgBoxStyle.Exclamation, "تعليمات")
        End Try

    End Sub

    Private Sub show_total_TextChanged(sender As Object, e As EventArgs) Handles show_total.TextChanged
        If show_total.Text = 0 Then
            amount_string.Visible = False
        Else
            amount_string.Visible = True
        End If
        amount_string.Text = NoToTxt(show_total.Text, XtraForm1.curn1.Text, XtraForm1.curn2.Text) & " " & "فقط لاغير"
    End Sub
    Private Sub CalcEdit1_KeyDown_1(sender As Object, e As KeyEventArgs) Handles disc_rate.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            item_name.Text = ""
            TextEdit1.Text = ""
            TextEdit1.Focus()

        End If
        If e.KeyCode = Keys.Left Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()
            If check = 1 Then
                item_name.Text = ""
                TextEdit1.Text = ""
                TextEdit1.Focus()
            ElseIf check = 2 Then
                item_name.Text = ""
                item_name.Focus()
            End If
        End If
        If e.KeyCode = Keys.Right Then
            item_price.Select()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_price.Text.Length
        End If
    End Sub

    Private Sub disc_rate_EditValueChanged(sender As Object, e As EventArgs) Handles disc_rate.EditValueChanged
        Dim t As Double = 0.0
        Dim d As Double = 0.0
        Dim s = (Val(disc_rate.Text) / 100)
        Dim sum = Format(Val(item_price.Text) * Val(s), "0.00")
        item_descound.Text = Val(sum)
    End Sub
    Sub clac()
        Try
            Dim aa As Integer
            Dim sql1 = "select * from item where itemcode=N'" & (GridView2.GetFocusedRowCellValue("كود")) & "'"
            Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
            Dim ds1 As New DataSet
            adp1.Fill(ds1)
            Dim dt1 = ds1.Tables(0)
            If dt1.Rows.Count > 0 Then
                Dim dr1 = dt1.Rows(0)

                aa = dr1!itemcount
            End If

            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الكمية الحالية", aa - GridView2.GetFocusedRowCellValue("الكمية"))

            Dim ss As Integer
            Dim sql = "select * from unit_item where code=N'" & GridView2.GetFocusedRowCellValue("كود") & "' and unit=N'" & GridView2.GetFocusedRowCellValue("الوحدة") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                ss = dr!count
            End If
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الرصيد", ss * GridView2.GetFocusedRowCellValue("الكمية"))

            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(GridView2.GetFocusedRowCellValue("الخصم")) / 100)
            Dim sum = Format(Val(GridView2.GetFocusedRowCellValue("السعر")) * Val(s), "0.00")
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "خصم نقدي", Val(sum))

            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "بعد الخصم", Val(GridView2.GetFocusedRowCellValue("السعر")) - Val(GridView2.GetFocusedRowCellValue("خصم نقدي")))
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الربح", (Val(GridView2.GetFocusedRowCellValue("السعر")) - Val(GridView2.GetFocusedRowCellValue("خصم نقدي")) - Val(GridView2.GetFocusedRowCellValue("سعر الشراء"))) * Val(GridView2.GetFocusedRowCellValue("الكمية")))
            Dim x = GridView2.GetFocusedRowCellValue("السعر") - GridView2.GetFocusedRowCellValue("خصم نقدي")
            x = x * GridView2.GetFocusedRowCellValue("الكمية")
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الأجمالي", x)
            plus_invoice()
        Catch ex As Exception
            MsgBox("فشل التعديل")
        End Try

    End Sub
    Private Sub RepositoryItemButtonEdit2_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit2.Click
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        clac()
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        If XtraForm1.m39.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تسجيل صنف"
        f.MdiParent = XtraForm1
        f.new_item()
        f.Show()
    End Sub

    Private Sub RepositoryItemButtonEdit3_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit3.Click
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        Try
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الكمية", GridView2.GetFocusedRowCellValue("الكمية") + 1)
            clac()
        Catch ex As Exception
            MsgBox("فشل العملية")
        End Try

    End Sub

    Private Sub RepositoryItemButtonEdit4_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit4.Click
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        Try
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الكمية", GridView2.GetFocusedRowCellValue("الكمية") - 1)
            clac()
        Catch ex As Exception
            MsgBox("فشل العملية")
        End Try
    End Sub


    Private Sub new_balace_TextChanged(sender As Object, e As EventArgs) Handles new_balace.TextChanged
        new_balace.Text = Math.Round(Val(new_balace.Text), 2)
    End Sub

    Private Sub TextBox8_TextChanged(sender As Object, e As EventArgs) Handles TextBox8.TextChanged
        TextBox8.Text = Math.Round(Val(TextBox8.Text), 2)
    End Sub

    Private Sub Button9_Click(sender As Object, e As EventArgs) Handles Button9.Click
        Dim sql = "select * from column_visible where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            ''========= بيانات اساسية============
            dr!GridColumn2 = GridColumn2.Visible
            dr!GridColumn7 = GridColumn7.Visible
            dr!GridColumn8 = GridColumn8.Visible
            dr!GridColumn9 = GridColumn9.Visible
            dr!GridColumn10 = GridColumn10.Visible
            dr!GridColumn13 = GridColumn13.Visible
            dr!GridColumn12 = GridColumn12.Visible
            dr!GridColumn5 = GridColumn5.Visible
            dr!GridColumn22 = GridColumn22.Visible
            dr!GridColumn23 = GridColumn23.Visible
            dr!GridColumn1 = GridColumn1.Visible
            dr!GridColumn14 = GridColumn14.Visible
            dr!GridColumn11 = GridColumn11.Visible
            dr!GridColumn15 = GridColumn15.Visible
            dr!GridColumn16 = GridColumn16.Visible
            dr!GridColumn17 = GridColumn17.Visible
            dr!GridColumn18 = GridColumn18.Visible
            dr!GridColumn19 = GridColumn19.Visible
            dr!GridColumn20 = GridColumn20.Visible
            dr!GridColumn3 = GridColumn3.Visible
            dr!GridColumn4 = GridColumn4.Visible
            dr!GridColumn6 = GridColumn6.Visible
            dr!GridColumn21 = GridColumn21.Visible

            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        MsgBox("تم الحفظ")
    End Sub

    Private Sub Button8_Click(sender As Object, e As EventArgs) Handles Button8.Click
        GridView2.ShowPrintPreview()
    End Sub
    Private Sub Button10_Click(sender As Object, e As EventArgs) Handles Button10.Click
        TextBox23.ForeColor = Color.White
        TextBox21.ForeColor = Color.White
        TextBox12.ForeColor = Color.White
        TextBox11.ForeColor = Color.White
        TextBox9.ForeColor = Color.White
        TextBox23.BackColor = Color.DimGray
        TextBox21.BackColor = Color.DimGray
        TextBox12.BackColor = Color.DimGray
        TextBox11.BackColor = Color.DimGray
        TextBox9.BackColor = Color.DimGray
        TextBox17.ForeColor = Color.White
        TextBox17.BackColor = Color.DimGray
        count_text.ForeColor = Color.White
        show_buy.ForeColor = Color.White
        show_datebuy.ForeColor = Color.White
        show_datesale.ForeColor = Color.White
        item_notes.ForeColor = Color.White
        count_text.BackColor = Color.DimGray
        show_buy.BackColor = Color.DimGray
        show_datebuy.BackColor = Color.DimGray
        show_datesale.BackColor = Color.DimGray
        item_notes.BackColor = Color.DimGray
        DataGridView1.BackColor = Color.DimGray
        dgv_2.BackColor = Color.DimGray

        LabelControl5.ForeColor = Color.White
        LabelControl3.ForeColor = Color.White
        LabelControl4.ForeColor = Color.White
        Accounts_balace.ForeColor = Color.White
        Label19.ForeColor = Color.White

        Label47.ForeColor = Color.White
        Label13.ForeColor = Color.White
        Label14.ForeColor = Color.White
        Label17.ForeColor = Color.White
        Label24.ForeColor = Color.White
        Label23.ForeColor = Color.White
        Label25.ForeColor = Color.White
        Label9.ForeColor = Color.White
        Label16.ForeColor = Color.White
        Label3.ForeColor = Color.White
        Label1.ForeColor = Color.White

        LabelControl1.ForeColor = Color.White
        LabelControl2.ForeColor = Color.White

        show_total.ForeColor = Color.White
        amount_string.ForeColor = Color.White
        amount_string.BackColor = Color.DimGray
        invoice_descound.BackColor = Color.DimGray
        invoice_tax.BackColor = Color.DimGray
        TextBox8.BackColor = Color.DimGray
        new_balace.BackColor = Color.DimGray

        show_total.BackColor = Color.DimGray
        Me.BackColor = Color.DimGray
        GridView2.OptionsView.EnableAppearanceEvenRow = False
        DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle("DevExpress Dark Style")
        XtraTabControl2.LookAndFeel.SkinName = ("DevExpress Dark Style")
    End Sub

    Private Sub store_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles store.SelectedIndexChanged
        fill_item()
    End Sub

    Private Sub Button11_Click(sender As Object, e As EventArgs) Handles Button11.Click
        GridView2.ShowCustomization()
    End Sub

    Private Sub item_unit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles item_unit.SelectedIndexChanged
        show_buy.Text = ""
        item_price.Text = 0
        disc_rate.Text = 0
        Dim sql = "select * from unit_item where code=N'" & (code.Text) & "' and unit=N'" & (item_unit.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            number_unit.Text = dr!count
            show_buy.Text = dr!price_buy
            item_price.Text = dr!price_buy
            disc_rate.Text = dr!disc_buy
        End If
        If XtraForm1.sale_buy2.Text = "متوسط الشراء" Then
            Dim str = "select sum(item_price), count(item_count) from purchases_list where item_code=N'" & code.Text & "' and item_unit=N'" & item_unit.Text & "'"
            Dim cmd As New SqlCommand(str, sqlconn)
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            Dim dta As New DataTable("purchases_list")
            da.Fill(dta)

            Dim sumdebit As Decimal = 0.0
            Dim sumcredit As Decimal = 0.0
            If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
            If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
            show_buy.Text = Format((Val(sumdebit) / Val(sumcredit)), "0.00")
        End If
    End Sub

    Private Sub ComboBoxEdit3_KeyDown(sender As Object, e As KeyEventArgs) Handles item_unit.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_count.Select()
            item_count.SelectionStart = 0
            item_count.SelectionLength = item_count.Text.Length
        End If
        If e.KeyCode = Keys.Left Then
            item_count.Select()
            item_count.SelectionStart = 0
            item_count.SelectionLength = item_count.Text.Length
        End If
        If e.KeyCode = Keys.Right Then

            TextEdit1.Select()
            TextEdit1.SelectionStart = 0
            TextEdit1.SelectionLength = TextEdit1.Text.Length

        End If
    End Sub

    Private Sub ComboBoxEdit3_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxEdit1.SelectedIndexChanged
        If ComboBoxEdit1.SelectedIndex = 0 Then
            descound_Values.Visible = False
            descound_Rate.Visible = True
        ElseIf ComboBoxEdit1.SelectedIndex = 1 Then
            descound_Values.Visible = True
            descound_Rate.Visible = False
        End If
    End Sub

    Private Sub ComboBoxEdit4_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxEdit2.SelectedIndexChanged
        If ComboBoxEdit1.SelectedIndex = 0 Then
            tax_Values.Visible = False
            tax_Rate.Visible = True
        ElseIf ComboBoxEdit1.SelectedIndex = 1 Then
            tax_Values.Visible = True
            tax_Rate.Visible = False
        End If
    End Sub

    Private Sub ComboBoxEdit3_SelectedIndexChanged_1(sender As Object, e As EventArgs)
        code_TextChanged(Nothing, Nothing)
        item_unit_SelectedIndexChanged(Nothing, Nothing)
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click
        price_item.code.Text = GridView2.GetFocusedRowCellValue("كود")
        price_item.item_name.Text = GridView2.GetFocusedRowCellValue("أسم الصنف")
        price_item.Show()
    End Sub
    Private Sub RepositoryItemButtonEdit5_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit5.Click
        If MsgBox("هل انت متأكد بحذف الصنف المحدد؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        Try
            For h = 0 To dgv_10.Rows.Count - 1
                delete_iem()
            Next
            GridView2.DeleteRow(GridView2.FocusedRowHandle)
            plus_invoice()
        Catch ex As Exception

        End Try
    End Sub



    Private Sub Button7_Click(sender As Object, e As EventArgs) Handles Button7.Click
        System.Diagnostics.Process.Start("CALC")
    End Sub

  
    Private Sub item_name_MouseDown(sender As Object, e As MouseEventArgs) Handles item_name.MouseDown
        item_name.Select()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click

        Dim adp As New SqlDataAdapter("select * from order_list where item_name=N'" & (item_name.Text) & "' and item_date>='" & Format(item_from.Value, "yyy/MM/dd") & "'  and item_date<='" & Format(item_to.Value, "yyy/MM/dd") & "' order by item_date", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_5.AutoGenerateColumns = False
        dgv_5.DataSource = dt
        Try
            dgv_5.FirstDisplayedScrollingRowIndex = dgv_5.RowCount - 1
        Catch ex As Exception

        End Try

    End Sub

    Private Sub show_itemcus_CheckedChanged(sender As Object, e As EventArgs) Handles show_itemcus.CheckedChanged
        If show_itemcus.Checked = True Then
            dgv_5.Visible = True
            dgv_5.BringToFront()
            GroupBox2.Visible = True
        Else
            dgv_5.Visible = False
            GroupBox2.Visible = False
        End If
    End Sub

    Private Sub Accounts_name_EditValueChanged(sender As Object, e As EventArgs) Handles Accounts_name.EditValueChanged
        If XtraForm1.w14.Text = True Then
            If Accounts_name.Text <> Nothing Then
                Label19.Visible = True
                Accounts_balace.Visible = True
            Else
                Label19.Visible = False
                Accounts_balace.Visible = False
            End If
        End If
        Accounts_code.Text = Accounts_name.EditValue
        Accounts_code_TextChanged(Nothing, Nothing)
    End Sub
    Private Sub Button13_Click(sender As Object, e As EventArgs) Handles Button13.Click
        If dgv_8.Visible = True Then
            dgv_8.Visible = False
            dgv_8.BringToFront()
        Else
            dgv_8.Visible = True
            dgv_8.BringToFront()
        End If
        Dim adp As New SqlDataAdapter("select * from order_item", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_8.AutoGenerateColumns = False
        dgv_8.DataSource = dt
    End Sub

    Private Sub dgv_8_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_8.CellClick
        Try
            If e.ColumnIndex = 4 Then

                item_name.Text = dgv_8.CurrentRow.Cells(0).Value.ToString
                item_unit.Text = dgv_8.CurrentRow.Cells(1).Value.ToString
                item_count.Text = dgv_8.CurrentRow.Cells(3).Value.ToString
                inserte_Click(Nothing, Nothing)
            End If


        Catch ex As Exception

        End Try
        Try
            If e.ColumnIndex = 5 Then
                If XtraMessageBox.Show("هل تريد حذف هذا الصنف من طلبية الاصناف", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then




                    Dim sql = "select * from order_item where item_name=N'" & dgv_8.CurrentRow.Cells(0).Value & "'"
                    Dim adp As New SqlDataAdapter(sql, sqlconn)
                    Dim ds As New DataSet
                    adp.Fill(ds)
                    Dim dt = ds.Tables(0)
                    If dt.Rows.Count = 0 Then
                        MsgBox("فشل في جلب البيانات")
                    Else
                        Dim dr = dt.Rows(0)
                        dr.Delete()
                        Dim cmd As New SqlCommandBuilder(adp)
                        adp.Update(dt)


                        dgv_8.Rows.Remove(dgv_8.CurrentRow)
                    End If
                End If
            End If


        Catch ex As Exception

        End Try
    End Sub

    Private Sub dgv_8_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv_8.CellPainting
        If e.ColumnIndex = 4 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.check_true
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
        If e.ColumnIndex = 5 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub

    Private Sub Button14_Click(sender As Object, e As EventArgs) Handles Button14.Click
        If dgv_9.Visible = True Then
            dgv_9.Visible = False
            dgv_9.BringToFront()
        Else
            dgv_9.Visible = True
            dgv_9.BringToFront()
        End If
        Dim adp As New SqlDataAdapter("select * from [dbo].[item] where [itemcount] <= [Demand_limit]", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_9.AutoGenerateColumns = False
        dgv_9.DataSource = dt
    End Sub

    Private Sub dgv_9_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_9.CellClick
        Try
            If e.ColumnIndex = 4 Then
                item_name.Text = dgv_9.CurrentRow.Cells(0).Value.ToString
                item_count.Text = dgv_9.CurrentRow.Cells(3).Value.ToString
                inserte_Click(Nothing, Nothing)
            End If


        Catch ex As Exception

        End Try
    End Sub

    Private Sub dgv_9_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv_9.CellPainting
        If e.ColumnIndex = 4 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.check_true
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub

    Private Sub GridView2_KeyUp(sender As Object, e As KeyEventArgs) Handles GridView2.KeyUp
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        clac()
    End Sub

    Private Sub Button5_Click_1(sender As Object, e As EventArgs) Handles Button5.Click
        order_item.Show()
        order_item.item_name.Text = item_name.Text
        order_item.code.Text = code.Text
    End Sub
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Dim f As frmbarcode = New frmbarcode()
        f.Text = "طباعة باركود"
        f.MdiParent = XtraForm1
        f.Show()
        f.txtpname.Text = item_name.Text
        f.txtPrice.Text = item_price.Text
        Dim sql = "select * from barcode where itemcode=N'" & (GridView2.GetFocusedRowCellValue("itemcode")) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            f.txtbarcode.Text = dr!barcode
        End If
    End Sub
    Private Sub TextEdit1_KeyDown_2(sender As Object, e As KeyEventArgs) Handles TextEdit1.KeyDown
        If e.KeyCode = Keys.Enter Then

            If TextEdit1.Text = "" Then
                Exit Sub
            End If
            item_name.Text = ""
            item_name.EditValue = 0

            code.Text = 0
            If GridView1.RowCount = 0 Then
                Dim sss As Integer = 0
                Dim sql = "select code from unit_item where item_unit=N'" & (TextEdit1.Text) & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count > 0 Then
                    Dim dr = dt.Rows(0)
                    code.Text = dr!code
                Else
                    If XtraForm1.balance_check.Text = True Then
                        sss = 1
                        Dim sql4 = "select code from unit_item where item_unit=N'" & Mid(TextEdit1.Text, 3, XtraForm1.balance_w1.Text) & "'"
                        Dim adp4 As New SqlDataAdapter(sql4, sqlconn)
                        Dim ds4 As New DataSet
                        adp4.Fill(ds4)
                        Dim dt4 = ds4.Tables(0)
                        If dt4.Rows.Count > 0 Then
                            Dim dr4 = dt4.Rows(0)
                            code.Text = dr4!code

                        Else
                            MsgBox("لايوجد باركود لهذا الرقم")
                            TextEdit1.Text = ""
                            TextEdit1.Focus()

                            Exit Sub
                        End If
                    Else
                        MsgBox("لايوجد باركود لهذا الرقم")
                        TextEdit1.Text = ""
                        TextEdit1.Focus()

                        Exit Sub
                    End If


                End If
                Try
                    Dim sql7 = "select unit from unit_item where item_unit=N'" & (TextEdit1.Text) & "'"
                    Dim adp7 As New SqlDataAdapter(sql7, sqlconn)
                    Dim ds7 As New DataSet
                    adp7.Fill(ds7)
                    Dim dt7 = ds7.Tables(0)
                    If dt7.Rows.Count > 0 Then
                        Dim dr7 = dt7.Rows(0)
                        item_unit.Text = dr7!unit
                    End If
                Catch ex As Exception
                End Try
                Try
                    If sss = 1 Then
                        Dim aa As Decimal = Mid(TextEdit1.Text, XtraForm1.balance_w1.Text + 3, XtraForm1.balance_w2.Text)
                        item_count.Text = aa / 1000
                    End If
                Catch ex As Exception

                End Try


                inserte_Click(Nothing, Nothing)
                TextEdit1.Text = ""
                TextEdit1.Focus()
                Exit Sub
            Else
                GridControl2.DefaultView.Focus()
            End If
        End If

        If e.KeyCode = Keys.Down Then
            GridControl2.DefaultView.Focus()
        End If
    End Sub

    Private Sub GridView1_KeyDown(sender As Object, e As KeyEventArgs) Handles GridView1.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_name.Text = ""
            item_name.Text = GridView1.GetFocusedRowCellValue("itemnamearabic")
            check = 1
            code_TextChanged(Nothing, Nothing)
            item_unit.Focus()
        End If
    End Sub

    Private Sub DataGridView2_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_catarogy.CellClick
        Dim adp As New SqlDataAdapter("select * from item where itemstore=N'" & (store.Text) & "' and item_active = 'true' and Itemtacategory=N'" & (dgv_catarogy.CurrentRow.Cells(0).Value) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_touch.DataSource = dt
    End Sub

    Private Sub CardView2_Click(sender As Object, e As EventArgs) Handles CardView2.Click
        Try
            item_name.Text = CardView2.GetFocusedRowCellValue("itemnamearabic").ToString
            inserte_Click(Nothing, Nothing)

        Catch ex As Exception

        End Try
    End Sub

    Private Sub SimpleButton42_Click(sender As Object, e As EventArgs)
        save_qq(Nothing, Nothing)
    End Sub

    Private Sub SimpleButton43_Click(sender As Object, e As EventArgs)
        print_btn_Click()
    End Sub

   
    Private Sub qq1_KeyDown(sender As Object, e As KeyEventArgs)
        If e.Control = True And e.KeyCode = Keys.Enter Then

            save_qq(Nothing, Nothing)
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter Then
            print_btn_Click()
        End If
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click

        save_qq(Nothing, Nothing)

    End Sub

    Private Sub Button15_Click(sender As Object, e As EventArgs) Handles Button15.Click
        Dim str = "select max(invoice_number) from order_add"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("order_add")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)

        show_data(sumdebit)
    End Sub

    Private Sub TextEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles TextEdit1.EditValueChanged
    If TextEdit1.Text = "" Then
            GridControl2.DataSource = Nothing
            Exit Sub
        End If


        Dim adp As New SqlDataAdapter("select (itemcode),(itemnamearabic),(itemcount),(itembuyprice) from item where itemstore=N'" & (store.Text) & "' and itemnamearabic like N'%" & (TextEdit1.Text) & "%'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            GridControl2.DataSource = dt

    End Sub
    '===================== وحدة الصنف
    Private Sub GridView2_DoubleClick(sender As Object, e As EventArgs) Handles GridView2.DoubleClick
        Label6.Text = GridView2.GetFocusedRowCellValue("أسم الصنف")
        Dim adp As New SqlDataAdapter("select (unit),(count),(price1) from unit_item where code=N'" & GridView2.GetFocusedRowCellValue("كود") & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        GridControl3.DataSource = dt
        GroupBox6.Visible = True
    End Sub
    Private Sub GridView3_DoubleClick(sender As Object, e As EventArgs) Handles GridView3.DoubleClick
        GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الوحدة", GridView3.GetFocusedRowCellValue("unit"))
        GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "السعر", GridView3.GetFocusedRowCellValue("price1"))
        clac()
        GroupBox6.Visible = False
    End Sub

    Private Sub SimpleButton10_Click(sender As Object, e As EventArgs) Handles SimpleButton10.Click
        GroupBox6.Visible = False
    End Sub
    Private Sub item_total_KeyDown(sender As Object, e As KeyEventArgs) Handles item_total.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()

        End If


        If e.KeyCode = Keys.Left Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If

        If e.KeyCode = Keys.Right Then
            disc_rate.Select()
            disc_rate.SelectionStart = 0
            disc_rate.SelectionLength = disc_rate.Text.Length
        End If
    End Sub

    Private Sub item_total2_KeyDown(sender As Object, e As KeyEventArgs) Handles item_total2.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()

        End If


        If e.KeyCode = Keys.Left Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If

        If e.KeyCode = Keys.Right Then
            item_price.Select()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_price.Text.Length
        End If
    End Sub
End Class