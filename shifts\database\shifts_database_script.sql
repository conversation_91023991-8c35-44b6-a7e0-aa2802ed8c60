-- =============================================
-- سكريبت إنشاء نظام إدارة الشيفتات (الورديات)
-- تاريخ الإنشاء: 2025-06-25
-- الوصف: إنشاء جداول إدارة الشيفتات وربطها بفواتير الكاشير
-- =============================================

USE [مبيعات 2022]
GO

-- =============================================
-- إنشاء جدول الشيفتات الرئيسي
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='shifts' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[shifts] (
        [shift_id] INT IDENTITY(1,1) PRIMARY KEY,
        [shift_code] NVARCHAR(20) NOT NULL,
        [cashier_name] NVARCHAR(100) NOT NULL,
        [cashier_id] INT NOT NULL,
        [store_name] NVARCHAR(100) NOT NULL,
        [start_date] DATETIME NOT NULL,
        [end_date] DATETIME NULL,
        [start_amount] DECIMAL(18,2) DEFAULT 0.00,
        [end_amount] DECIMAL(18,2) DEFAULT 0.00,
        [total_sales] DECIMAL(18,2) DEFAULT 0.00,
        [total_cash] DECIMAL(18,2) DEFAULT 0.00,
        [total_credit] DECIMAL(18,2) DEFAULT 0.00,
        [total_profit] DECIMAL(18,2) DEFAULT 0.00,
        [total_invoices] INT DEFAULT 0,
        [total_items] INT DEFAULT 0,
        [shift_status] NVARCHAR(20) DEFAULT 'مفتوح',
        [notes] NVARCHAR(500) NULL,
        [created_by] NVARCHAR(100) NOT NULL,
        [created_date] DATETIME DEFAULT GETDATE(),
        [closed_by] NVARCHAR(100) NULL,
        [closed_date] DATETIME NULL,
        [is_active] BIT DEFAULT 1
    );
    
    PRINT 'تم إنشاء جدول shifts بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول shifts موجود مسبقاً'
END
GO

-- =============================================
-- إنشاء فهارس للجدول
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_shifts_cashier_date')
BEGIN
    CREATE INDEX IX_shifts_cashier_date ON shifts (cashier_name, start_date);
    PRINT 'تم إنشاء فهرس IX_shifts_cashier_date'
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_shifts_status')
BEGIN
    CREATE INDEX IX_shifts_status ON shifts (shift_status, is_active);
    PRINT 'تم إنشاء فهرس IX_shifts_status'
END
GO

-- =============================================
-- إضافة عمود shift_id لجدول invoice_add
-- =============================================
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
              WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'shift_id')
BEGIN
    ALTER TABLE invoice_add ADD shift_id INT NULL;
    PRINT 'تم إضافة عمود shift_id لجدول invoice_add'
END
ELSE
BEGIN
    PRINT 'عمود shift_id موجود مسبقاً في جدول invoice_add'
END
GO

-- =============================================
-- إضافة عمود shift_id لجدول pos_add (إذا كان موجوداً)
-- =============================================
IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                  WHERE TABLE_NAME = 'pos_add' AND COLUMN_NAME = 'shift_id')
    BEGIN
        ALTER TABLE pos_add ADD shift_id INT NULL;
        PRINT 'تم إضافة عمود shift_id لجدول pos_add'
    END
    ELSE
    BEGIN
        PRINT 'عمود shift_id موجود مسبقاً في جدول pos_add'
    END
END
GO

-- =============================================
-- إضافة عمود shift_id لجدول pos_add2 (إذا كان موجوداً)
-- =============================================
IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add2' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                  WHERE TABLE_NAME = 'pos_add2' AND COLUMN_NAME = 'shift_id')
    BEGIN
        ALTER TABLE pos_add2 ADD shift_id INT NULL;
        PRINT 'تم إضافة عمود shift_id لجدول pos_add2'
    END
    ELSE
    BEGIN
        PRINT 'عمود shift_id موجود مسبقاً في جدول pos_add2'
    END
END
GO

-- =============================================
-- إنشاء جدول تفاصيل الشيفت (اختياري)
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='shift_details' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[shift_details] (
        [detail_id] INT IDENTITY(1,1) PRIMARY KEY,
        [shift_id] INT NOT NULL,
        [invoice_number] INT NOT NULL,
        [invoice_total] DECIMAL(18,2) NOT NULL,
        [invoice_profit] DECIMAL(18,2) DEFAULT 0.00,
        [payment_type] NVARCHAR(50) NOT NULL,
        [invoice_date] DATETIME NOT NULL,
        [customer_name] NVARCHAR(100) NULL,
        [notes] NVARCHAR(255) NULL,
        FOREIGN KEY (shift_id) REFERENCES shifts(shift_id)
    );
    
    PRINT 'تم إنشاء جدول shift_details بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول shift_details موجود مسبقاً'
END
GO

-- =============================================
-- إنشاء View لملخص الشيفتات
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_shifts_summary')
BEGIN
    DROP VIEW vw_shifts_summary;
END
GO

CREATE VIEW vw_shifts_summary AS
SELECT 
    s.shift_id,
    s.shift_code,
    s.cashier_name,
    s.store_name,
    s.start_date,
    s.end_date,
    s.start_amount,
    s.end_amount,
    s.total_sales,
    s.total_cash,
    s.total_credit,
    s.total_profit,
    s.total_invoices,
    s.shift_status,
    DATEDIFF(MINUTE, s.start_date, ISNULL(s.end_date, GETDATE())) AS shift_duration_minutes,
    CASE 
        WHEN s.end_date IS NULL THEN 'جاري'
        ELSE 'مكتمل'
    END AS shift_completion_status,
    s.notes
FROM shifts s
WHERE s.is_active = 1;
GO

PRINT 'تم إنشاء View vw_shifts_summary بنجاح'
GO

-- =============================================
-- إنشاء Stored Procedure لبدء شيفت جديد
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_StartNewShift')
BEGIN
    DROP PROCEDURE sp_StartNewShift;
END
GO

CREATE PROCEDURE sp_StartNewShift
    @cashier_name NVARCHAR(100),
    @cashier_id INT,
    @store_name NVARCHAR(100),
    @start_amount DECIMAL(18,2) = 0.00,
    @notes NVARCHAR(500) = NULL,
    @shift_id INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @shift_code NVARCHAR(20);
    DECLARE @today_date NVARCHAR(10) = FORMAT(GETDATE(), 'yyyyMMdd');
    DECLARE @shift_count INT;
    
    -- التحقق من وجود شيفت مفتوح للكاشير
    IF EXISTS (SELECT 1 FROM shifts WHERE cashier_name = @cashier_name AND shift_status = 'مفتوح')
    BEGIN
        RAISERROR('يوجد شيفت مفتوح بالفعل لهذا الكاشير', 16, 1);
        RETURN;
    END
    
    -- حساب عدد الشيفتات اليوم
    SELECT @shift_count = COUNT(*) + 1 
    FROM shifts 
    WHERE CAST(start_date AS DATE) = CAST(GETDATE() AS DATE);
    
    -- إنشاء كود الشيفت
    SET @shift_code = @today_date + '-' + RIGHT('00' + CAST(@shift_count AS NVARCHAR), 2);
    
    -- إدراج الشيفت الجديد
    INSERT INTO shifts (
        shift_code, cashier_name, cashier_id, store_name, 
        start_date, start_amount, notes, created_by
    )
    VALUES (
        @shift_code, @cashier_name, @cashier_id, @store_name,
        GETDATE(), @start_amount, @notes, @cashier_name
    );
    
    SET @shift_id = SCOPE_IDENTITY();
    
    PRINT 'تم بدء شيفت جديد برقم: ' + CAST(@shift_id AS NVARCHAR);
END
GO

PRINT 'تم إنشاء Stored Procedure sp_StartNewShift بنجاح'
GO

-- =============================================
-- إنشاء Stored Procedure لإنهاء الشيفت
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_EndShift')
BEGIN
    DROP PROCEDURE sp_EndShift;
END
GO

CREATE PROCEDURE sp_EndShift
    @shift_id INT,
    @end_amount DECIMAL(18,2) = 0.00,
    @closed_by NVARCHAR(100),
    @notes NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @total_sales DECIMAL(18,2) = 0;
    DECLARE @total_cash DECIMAL(18,2) = 0;
    DECLARE @total_credit DECIMAL(18,2) = 0;
    DECLARE @total_profit DECIMAL(18,2) = 0;
    DECLARE @total_invoices INT = 0;
    DECLARE @total_items INT = 0;
    
    -- التحقق من وجود الشيفت
    IF NOT EXISTS (SELECT 1 FROM shifts WHERE shift_id = @shift_id AND shift_status = 'مفتوح')
    BEGIN
        RAISERROR('الشيفت غير موجود أو مغلق بالفعل', 16, 1);
        RETURN;
    END
    
    -- حساب إجماليات الشيفت من الفواتير
    SELECT 
        @total_sales = ISNULL(SUM(total_invoice), 0),
        @total_cash = ISNULL(SUM(CASE WHEN type_money = 'نقدي' THEN total_invoice ELSE 0 END), 0),
        @total_credit = ISNULL(SUM(CASE WHEN type_money != 'نقدي' THEN total_invoice ELSE 0 END), 0),
        @total_profit = ISNULL(SUM(earn_invoice), 0),
        @total_invoices = COUNT(*),
        @total_items = ISNULL(SUM(invoice_count), 0)
    FROM invoice_add 
    WHERE shift_id = @shift_id;
    
    -- تحديث بيانات الشيفت
    UPDATE shifts 
    SET 
        end_date = GETDATE(),
        end_amount = @end_amount,
        total_sales = @total_sales,
        total_cash = @total_cash,
        total_credit = @total_credit,
        total_profit = @total_profit,
        total_invoices = @total_invoices,
        total_items = @total_items,
        shift_status = 'مغلق',
        closed_by = @closed_by,
        closed_date = GETDATE(),
        notes = ISNULL(@notes, notes)
    WHERE shift_id = @shift_id;
    
    PRINT 'تم إغلاق الشيفت رقم: ' + CAST(@shift_id AS NVARCHAR);
END
GO

PRINT 'تم إنشاء Stored Procedure sp_EndShift بنجاح'
GO

-- =============================================
-- إدراج بيانات تجريبية (اختياري)
-- =============================================
/*
-- مثال على شيفت تجريبي
DECLARE @test_shift_id INT;
EXEC sp_StartNewShift 
    @cashier_name = 'أحمد محمد',
    @cashier_id = 1,
    @store_name = 'المخزن الرئيسي',
    @start_amount = 1000.00,
    @notes = 'شيفت تجريبي',
    @shift_id = @test_shift_id OUTPUT;

PRINT 'تم إنشاء شيفت تجريبي برقم: ' + CAST(@test_shift_id AS NVARCHAR);
*/

-- =============================================
-- رسائل النجاح النهائية
-- =============================================
PRINT '================================================='
PRINT 'تم إنشاء نظام إدارة الشيفتات بنجاح!'
PRINT '================================================='
PRINT 'الجداول المنشأة:'
PRINT '- shifts (الجدول الرئيسي)'
PRINT '- shift_details (تفاصيل الشيفت)'
PRINT ''
PRINT 'الأعمدة المضافة:'
PRINT '- shift_id في جدول invoice_add'
PRINT '- shift_id في جدول pos_add (إن وجد)'
PRINT '- shift_id في جدول pos_add2 (إن وجد)'
PRINT ''
PRINT 'Views والإجراءات المخزنة:'
PRINT '- vw_shifts_summary'
PRINT '- sp_StartNewShift'
PRINT '- sp_EndShift'
PRINT '================================================='
