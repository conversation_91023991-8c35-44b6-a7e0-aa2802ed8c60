﻿Imports System.Data.SqlClient

Public Class Rosacea
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        new_btn_Click(Nothing, Nothing)
    End Sub

    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Rosacea order by <PERSON><PERSON>_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("Rosacea_name"))
        Next
    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from Rosacea where Rosa<PERSON>_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Rosacea_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        Rosacea_name.Text = ""
        Dim sql = "select * from Rosacea where Rosacea_id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            Rosacea_name.Text = dr!Rosacea_name
            time_attendance.EditValue = dr!time_attendance
            Time_leave.EditValue = dr!Time_leave
            clock_work.Text = dr!clock_work
            emp_Login.Text = dr!emp_Login
            emp_exit.Text = dr!emp_exit
            If dr!Holiday1 = "nothing" Then Holiday1.SelectedIndex = 0
            If dr!Holiday1 = "Saturday" Then Holiday1.SelectedIndex = 1
            If dr!Holiday1 = "Sunday" Then Holiday1.SelectedIndex = 2
            If dr!Holiday1 = "Monday" Then Holiday1.SelectedIndex = 3
            If dr!Holiday1 = "Tuesday" Then Holiday1.SelectedIndex = 4
            If dr!Holiday1 = "Wednesday" Then Holiday1.SelectedIndex = 5
            If dr!Holiday1 = "Thursday" Then Holiday1.SelectedIndex = 6
            If dr!Holiday1 = "Friday" Then Holiday1.SelectedIndex = 7

            If dr!Holiday2 = "nothing" Then Holiday1.SelectedIndex = 0
            If dr!Holiday2 = "Saturday" Then Holiday1.SelectedIndex = 1
            If dr!Holiday2 = "Sunday" Then Holiday1.SelectedIndex = 2
            If dr!Holiday2 = "Monday" Then Holiday1.SelectedIndex = 3
            If dr!Holiday2 = "Tuesday" Then Holiday1.SelectedIndex = 4
            If dr!Holiday2 = "Wednesday" Then Holiday1.SelectedIndex = 5
            If dr!Holiday2 = "Thursday" Then Holiday1.SelectedIndex = 6
            If dr!Holiday2 = "Friday" Then Holiday1.SelectedIndex = 7
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub
    Private Sub exit_button_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        Rosacea_name.Text = ""
        time_attendance.EditValue = "09:00:00"
        Time_leave.EditValue = "18:00:00"
        clock_work.Text = 0
        Holiday1.SelectedIndex = 0
        Holiday2.SelectedIndex = 0
        emp_Login.Text = 0
        emp_exit.Text = 0
        fill_user()
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If Rosacea_name.Text = "" Then
            MsgBox("أدخل اسم الوردية")
            Rosacea_name.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from Rosacea where Rosacea_name=N'" & (Rosacea_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم الوردية موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!Rosacea_name = Rosacea_name.Text
                dr!time_attendance = time_attendance.Text
                dr!Time_leave = Time_leave.Text
                dr!clock_work = clock_work.Text
                dr!emp_Login = emp_Login.Text
                dr!emp_exit = emp_exit.Text
                Select Case Holiday1.SelectedIndex
                    Case 0
                        dr!Holiday1 = "nothing"
                    Case 1
                        dr!Holiday1 = "Saturday"
                    Case 2
                        dr!Holiday1 = "Sunday"
                    Case 3
                        dr!Holiday1 = "Monday"
                    Case 4
                        dr!Holiday1 = "Tuesday"
                    Case 5
                        dr!Holiday1 = "Wednesday"
                    Case 6
                        dr!Holiday1 = "Thursday"
                    Case 7
                        dr!Holiday1 = "Friday"
                End Select
                Select Case Holiday2.SelectedIndex
                    Case 0
                        dr!Holiday2 = "nothing"
                    Case 1
                        dr!Holiday2 = "Saturday"
                    Case 2
                        dr!Holiday2 = "Sunday"
                    Case 3
                        dr!Holiday2 = "Monday"
                    Case 4
                        dr!Holiday2 = "Tuesday"
                    Case 5
                        dr!Holiday2 = "Wednesday"
                    Case 6
                        dr!Holiday2 = "Thursday"
                    Case 7
                        dr!Holiday2 = "Friday"
                End Select
               
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة الوردية")
                new_btn_Click(Nothing, Nothing)
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ الوردية اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from Rosacea where Rosacea_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!Rosacea_name = Rosacea_name.Text
                dr!time_attendance = time_attendance.Text
                dr!Time_leave = Time_leave.Text
                dr!clock_work = clock_work.Text
                dr!emp_Login = emp_Login.Text
                dr!emp_exit = emp_exit.Text
                Select Case Holiday1.SelectedIndex
                    Case 0
                        dr!Holiday1 = "nothing"
                    Case 1
                        dr!Holiday1 = "Saturday"
                    Case 2
                        dr!Holiday1 = "Sunday"
                    Case 3
                        dr!Holiday1 = "Monday"
                    Case 4
                        dr!Holiday1 = "Tuesday"
                    Case 5
                        dr!Holiday1 = "Wednesday"
                    Case 6
                        dr!Holiday1 = "Thursday"
                    Case 7
                        dr!Holiday1 = "Friday"
                End Select
                Select Case Holiday2.SelectedIndex
                    Case 0
                        dr!Holiday2 = "nothing"
                    Case 1
                        dr!Holiday2 = "Saturday"
                    Case 2
                        dr!Holiday2 = "Sunday"
                    Case 3
                        dr!Holiday2 = "Monday"
                    Case 4
                        dr!Holiday2 = "Tuesday"
                    Case 5
                        dr!Holiday2 = "Wednesday"
                    Case 6
                        dr!Holiday2 = "Thursday"
                    Case 7
                        dr!Holiday2 = "Friday"
                End Select

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل الوردية", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل الوردية اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from Rosacea where Rosacea_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الوردية")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف الوردية", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الوردية اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim ts2 As TimeSpan = TimeSpan.Parse(time_attendance.Text)
        Dim ts4 As TimeSpan = TimeSpan.Parse(Time_leave.Text)
        Dim x = ts4 - ts2
        clock_work.Text = CType(x.TotalHours, String)
    End Sub
End Class