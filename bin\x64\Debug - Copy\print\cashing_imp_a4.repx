﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="spaxet.cashing_imp_a4, spaxet store, Version=*******, Culture=neutral, PublicKeyToken=null" Name="cashing_imp_a4" Margins="0, 3, 1, 4" PaperKind="A4" PageWidth="827" PageHeight="1169" ScriptLanguage="VisualBasic" Version="17.2" DataMember="cash_imp_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="293.2292" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="XrLabel18" Text="XrLabel18" TextAlignment="MiddleRight" SizeF="652.4584,31.25" LocationFloat="29.25002, 198.9583" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="4" Expression="[cash_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="5" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="6" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel16" TextAlignment="MiddleCenter" SizeF="296.3125,31.25" LocationFloat="385.3958, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[Cashingcode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="9" ControlType="XRLabel" Name="XrLabel14" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel14" TextAlignment="MiddleCenter" SizeF="223.8542,31.25" LocationFloat="29.25002, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="10" Expression="[chashingdate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="12" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel12" TextAlignment="MiddleRight" SizeF="652.4583,31.25" LocationFloat="29.25002, 151.5625" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[type_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="14" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="15" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="652.4584,31.25" LocationFloat="29.25002, 57.81253" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="16" Expression="[importername]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="17" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="18" ControlType="XRLabel" Name="XrLabel3" Text="XrLabel3" TextAlignment="MiddleRight" SizeF="652.4583,31.25002" LocationFloat="29.25002, 244.2708" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="19" Expression="[cash_delegate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="20" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="21" ControlType="XRLabel" Name="XrLabel2" Text="XrLabel2" TextAlignment="MiddleRight" SizeF="478.75,31.25" LocationFloat="29.25002, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="22" Expression="[amount_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="23" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="24" ControlType="XRLabel" Name="XrLabel1" TextFormatString="{0:#,#}" Text="XrLabel1" TextAlignment="MiddleCenter" SizeF="173.7083,31.25" LocationFloat="508.0001, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="0,0,0,0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="25" Expression="[Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="26" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="27" ControlType="XRLabel" Name="XrLabel5" RightToLeft="Yes" Text="تـــاريخ الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="253.1042, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1">
          <StylePriority Ref="28" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="29" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="اسم المورد :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="681.7084, 57.81253" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="30" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="31" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="مـبلغ وقدره :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="681.7084, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="32" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="33" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="نقدأ او شيك :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="681.7084, 151.5625" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="34" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="35" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="وذلك عن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="681.7084, 198.9583" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="36" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="37" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="مندوب التسليم :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="681.7084, 244.2708" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="38" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="39" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="كود الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="681.7084, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="40" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item15>
      </Controls>
    </Item1>
    <Item2 Ref="41" ControlType="TopMarginBand" Name="TopMargin" HeightF="1" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="42" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="4" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="43" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="107.2917">
      <Controls>
        <Item1 Ref="44" ControlType="XRLine" Name="XrLine2" SizeF="813.0001,4.791673" LocationFloat="5.499939, 2.499994" />
        <Item2 Ref="45" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="119.3854,89.99999" LocationFloat="14.86448, 7.291672">
          <ExpressionBindings>
            <Item1 Ref="46" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item2>
        <Item3 Ref="47" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="543.6459, 56.24998" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="48" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="49" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="50" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="543.6459, 24.91665" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="51" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="52" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="53" ControlType="PageHeaderBand" Name="PageHeader" HeightF="72.08333">
      <Controls>
        <Item1 Ref="54" ControlType="XRLine" Name="XrLine1" SizeF="813.0001,4.791673" LocationFloat="5.499939, 0" />
        <Item2 Ref="55" ControlType="XRLabel" Name="XrLabel19" Multiline="true" Text="إشعار بسداد نقدية" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="29.25002, 4.791673" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="56" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="57" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="29.25002, 49.08333" Padding="2,2,0,0,100">
          <StylePriority Ref="58" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item5>
    <Item6 Ref="59" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="137.6667">
      <Controls>
        <Item1 Ref="60" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="5.499935, 76.66664" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="62" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="5.499935, 112.1667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="63" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="64" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="508, 35.08332" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="66" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="508, 9.999974" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="68" ControlType="XRLabel" Name="XrLabel6" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="89.25002, 37.1666" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="69" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="71" ControlType="XRLabel" Name="XrLabel9" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="89.25002, 9.999974" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="72" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="73" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="74" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="490.3751, 112.1667" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="75" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="76" UseFont="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="77" ControlType="PageFooterBand" Name="PageFooter" HeightF="28.125">
      <Controls>
        <Item1 Ref="78" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="744.3437, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="79" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="80" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="695.3853, 0" Padding="2,2,0,0,100">
          <StylePriority Ref="81" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="82" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="14.86448, 0" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="83" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="84" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="85" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="86" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="87" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="88" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="89" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>