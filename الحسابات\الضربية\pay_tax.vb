﻿Imports System.Data.SqlClient

Public Class pay_tax

    Private Sub pay_tax_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_treasury_name()
        tax_amount.Text = 0
        Datefrom.Value = Now.Date
        Dateto.Value = Now.Date
        type_pay.SelectedIndex = 0
       
    End Sub
    Sub fill_treasury_name()
        Try
            type_pay.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from treasury_name where Treasury_active='true'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                type_pay.Items.Add(dt.Rows(i).Item("treasury_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try

    End Sub
    Function gettreasury(subname) As String

        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function

    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged
        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)

        Dim dr = dt.Rows(0)
        treasury_balace.Text = dr!balace
    End Sub

    Private Sub Label21_Click(sender As Object, e As EventArgs) Handles Label21.Click
        Me.Dispose()
    End Sub

    Private Sub Label16_Click(sender As Object, e As EventArgs) Handles Label16.Click
        If tax_amount.Text = 0 Then
            MsgBox("أدخل المبلغ")
            tax_amount.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from tax_pay"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.NewRow
            dr!tax_amount = tax_amount.Text
            dr!tax_from = Datefrom.Text
            dr!tax_to = Dateto.Text
            dr!tax_treasy = type_pay.Text
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr6 = dt.Rows(0)
            dr6!balace = Val(dr6!balace) - Val(tax_amount.Text)
            Dim cmd6 As New SqlCommandBuilder(adp)
            adp.Update(dt)
            treasury_pay(type_pay.Text, "تسديد ضريبة", Now.Date, Now.ToString("hh:mm:ss:tt"), "تسديد ضريبة", XtraForm1.user_name.Text, 0, Val(tax_amount.Text), Val(treasury_balace.Text) - Val(tax_amount.Text), Val(tax_amount.Text))
            MsgBox("تم أضافة الضريبة")
            tax_amount.Text = 0
            Datefrom.Value = Now.Date
            Dateto.Value = Now.Date
            type_pay.SelectedIndex = 0
        Catch ex As Exception
            MsgBox("فشل حفظ الضريبة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub
    Sub ss()

    End Sub
End Class