﻿Imports DevExpress.Utils.CommonDialogs
Imports DevExpress.XtraEditors
Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO

Public Class frm_store_data


    Private Sub SimpleButton1_Click_1(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If sec_name.Text = "" Then
            XtraMessageBox.Show("اسم المؤسسة فارغ")
            Exit Sub
        End If
        '   OpenFileDialog1.FileName = Nothing
        'Try
        Dim sql = "select * from section where code=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            ''========= بيانات اساسية============
            dr!sec_name = sec_name.Text
            dr!sec_name2 = txt_com_name.Text
            dr!sec_phone = sec_phone.Text
            dr!sec_address = sec_address.Text
            dr!sec_email = sec_email.Text
            dr!sec_web = sec_web.Text
            dr!sec_number = sec_number.Text
            dr!sec_reg = txt_Com_reg.Text
            dr!sec_s1 = sec_s1.Text
            dr!sec_s2 = sec_s2.Text
            If code_pic.Text = 1 Then
                ''============حفظ الصورة=============
                If OpenFileDialog1.FileName <> "" Then
                    Dim imagbytearray() As Byte
                    Dim stream As New MemoryStream
                    sec_pic.Image.Save(stream, ImageFormat.Jpeg)
                    imagbytearray = stream.ToArray
                    stream.Close()
                    dr!sec_pic = imagbytearray
                End If
            End If
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        MsgBox("تم الحفظ")
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        Me.Dispose()
    End Sub
End Class