﻿Imports System.Data.SqlClient
Imports DevExpress.XtraEditors

Public Class Fixed_assets


    Private Sub Fixed_assets_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fillstore()
        amount.Text = 0
        amount2.Text = 0
        amount3.Text = 0
        amount4.Text = 0
        origin_date.Value = Now.Date
    End Sub
    Private Sub fillstore()
        origin.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Fixed_assets", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            origin.Properties.Items.Add(dt.Rows(i).Item("origin"))
        Next
        origin.Text = ""
        fill_dgv()
    End Sub
    Sub fill_dgv()
        Dim adp As New SqlDataAdapter("select * from Destruction_ethol", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        GridControl1.DataSource = dt

    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        If origin.Text = "" Then
            MsgBox("أدخل مبلغ الاهلاك")
            origin.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from Destruction_ethol where id=N'" & (1) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)

            Dim dr = dt.NewRow
            dr!origin = origin.Text
            dr!amount = amount4.Text
            dr!origin_date = origin_date.Value
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            MsgBox("تم أضافة الاهلاك")
            fillstore()
            fill_dgv()
            amount.Text = 0
            amount2.Text = 0
            amount3.Text = 0
            amount4.Text = 0
            origin_date.Value = Now.Date

        Catch ex As Exception
            MsgBox("فشل حفظ الاهلاك اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub origin_SelectedIndexChanged(sender As Object, e As EventArgs) Handles origin.SelectedIndexChanged
        Dim sql = "select * from Fixed_assets where origin=N'" & (origin.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            amount.Text = dr!amount
        End If
        balace_imp()
    End Sub
    Sub balace_imp()
        Dim str = "select sum(amount) from Destruction_ethol where origin=N'" & (origin.Text) & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("Destruction_ethol")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        amount2.Text = Val(sumdebit)
    End Sub

    Private Sub amount2_EditValueChanged(sender As Object, e As EventArgs) Handles amount2.EditValueChanged
        amount3.Text = Val(amount.Text) - Val(amount2.Text)
    End Sub

    Private Sub RepositoryItemButtonEdit7_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit7.Click
        If XtraMessageBox.Show("هل تريد حذف هذا الاهلاك ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
            Dim sql = "select * from Destruction_ethol where id=N'" & GridView2.GetFocusedRowCellValue("id") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                XtraMessageBox.Show("تم حذف الاهلاك بنجاح")
            End If
            fill_dgv()
        End If
    End Sub
End Class