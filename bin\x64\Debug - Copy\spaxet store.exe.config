<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="spaxet.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="data" connectionString="XpoProvider=MSSqlServer;data source=.\SQLEXPRESS;user id=sa;password=***;initial catalog=data;Persist Security Info=true"/>
        <add name="localhost_data_Connection" connectionString="XpoProvider=MSSqlServer;data source=DESKTOP-N5OCSTJ\SPAXET;integrated security=SSPI;initial catalog=data"/>
    </connectionStrings>
    <system.diagnostics>
        <sources>
            <!-- This section defines the logging configuration for My.Application.Log -->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog"/>
                    <!-- Uncomment the below section to write to the Application Event Log -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information"/>
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2"/></startup>
    <userSettings>
        <spaxet.My.MySettings>
            <setting name="f1" serializeAs="String">
                <value>false</value>
            </setting>
            <setting name="f2" serializeAs="String">
                <value>60</value>
            </setting>
            <setting name="f3" serializeAs="String">
                <value />
            </setting>
            <setting name="u1" serializeAs="String">
                <value />
            </setting>
            <setting name="u2" serializeAs="String">
                <value />
            </setting>
            <setting name="f_size" serializeAs="String">
                <value>333</value>
            </setting>
            <setting name="sern" serializeAs="String">
                <value />
            </setting>
            <setting name="datan" serializeAs="String">
                <value />
            </setting>
            <setting name="un" serializeAs="String">
                <value />
            </setting>
            <setting name="pn" serializeAs="String">
                <value />
            </setting>
            <setting name="tn" serializeAs="String">
                <value />
            </setting>
        </spaxet.My.MySettings>
    </userSettings>
</configuration>
