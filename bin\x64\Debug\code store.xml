﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
code store
</name>
</assembly>
<members>
<member name="T:spaxet.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources._1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources._error">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources._true">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources._when">
<summary>
  Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.attention">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.bell">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.boy">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.check">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.check_true">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.clear1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.close">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.cross">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.hhhjjjj">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.hhhjjjj1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.Info">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.logo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.SettingsBlue">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.suc">
<summary>
  Looks up a localized resource of type System.IO.UnmanagedMemoryStream similar to System.IO.MemoryStream.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.talibov180200087">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.white">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:spaxet.My.Resources.Resources.wrong">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
</members>
</doc>
