# نظام مراكز التكلفة

## نظرة عامة
نظام مراكز التكلفة يسمح بتتبع وتصنيف المصروفات والإيرادات حسب مراكز التكلفة المختلفة في الشركة. هذا يساعد في:
- تحليل الأداء المالي لكل قسم أو مشروع
- مراقبة التكاليف والإيرادات بشكل منفصل
- إعداد تقارير مالية مفصلة
- اتخاذ قرارات إدارية مدروسة

## المكونات الرئيسية

### 1. CostCenterHelper.vb
ملف مساعد يحتوي على جميع العمليات المتعلقة بمراكز التكلفة:
- إنشاء جدول مراكز التكلفة
- إضافة وتعديل وحذف مراكز التكلفة
- تحميل مراكز التكلفة في ComboBox
- ربط الفواتير والمصروفات بمراكز التكلفة

### 2. CostCenterManagement.vb
شاشة إدارة مراكز التكلفة التي تسمح بـ:
- عرض جميع مراكز التكلفة
- إضافة مراكز تكلفة جديدة
- تعديل مراكز التكلفة الموجودة
- حذف مراكز التكلفة
- تفعيل/إلغاء تفعيل مراكز التكلفة

### 3. CostCenterReport.vb
تقرير شامل لمراكز التكلفة يعرض:
- جميع العمليات المالية لكل مركز تكلفة
- فواتير المبيعات والمصروفات
- تحليل الأرباح والخسائر
- إمكانية التصدير والطباعة
- ملخص إجمالي للأداء

## كيفية الاستخدام

### إعداد مراكز التكلفة
1. افتح شاشة "إدارة مراكز التكلفة"
2. اضغط على "إضافة" لإنشاء مركز تكلفة جديد
3. أدخل:
   - كود مركز التكلفة (مثل: CC001)
   - اسم مركز التكلفة (مثل: قسم المبيعات)
   - وصف مختصر
   - حالة التفعيل
4. اضغط "حفظ"

### ربط الفواتير بمراكز التكلفة
- في شاشة فواتير المبيعات، ستجد حقل "مركز التكلفة"
- اختر مركز التكلفة المناسب قبل حفظ الفاتورة
- سيتم ربط الفاتورة تلقائياً بمركز التكلفة المحدد

### ربط المصروفات بمراكز التكلفة
- في شاشة المصروفات، ستجد حقل "مركز التكلفة"
- اختر مركز التكلفة المناسب قبل حفظ المصروف
- سيتم ربط المصروف تلقائياً بمركز التكلفة المحدد

### عرض التقارير
1. افتح "تقرير مراكز التكلفة"
2. حدد:
   - الفترة الزمنية (من تاريخ - إلى تاريخ)
   - مركز التكلفة (أو اتركه فارغاً لعرض الكل)
3. اضغط "عرض التقرير"
4. يمكنك:
   - طباعة التقرير
   - تصديره إلى Excel
   - عرض ملخص إجمالي

## هيكل قاعدة البيانات

### جدول cost_centers
```sql
CREATE TABLE cost_centers (
    cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
    cost_center_code NVARCHAR(50) NOT NULL UNIQUE,
    cost_center_name NVARCHAR(255) NOT NULL,
    description NVARCHAR(500),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE()
)
```

### تعديلات الجداول الموجودة
- إضافة عمود `cost_center_id` إلى جدول `invoice_add`
- إضافة عمود `cost_center_id` إلى جدول `Expenses_add`

## الميزات المتقدمة

### التقارير التفصيلية
- عرض جميع العمليات مع التفاصيل الكاملة
- تجميع البيانات حسب مركز التكلفة
- حساب الأرباح والخسائر لكل مركز
- مقارنة الأداء بين مراكز التكلفة المختلفة

### التصدير والطباعة
- تصدير التقارير إلى Excel
- طباعة التقارير مع تنسيق احترافي
- حفظ التقارير كملفات PDF

### الأمان والصلاحيات
- يمكن ربط النظام بصلاحيات المستخدمين
- تتبع من قام بإنشاء أو تعديل مراكز التكلفة
- سجل تاريخي للعمليات

## نصائح للاستخدام الأمثل

1. **تسمية مراكز التكلفة**: استخدم أسماء واضحة ومفهومة
2. **الأكواد**: استخدم نظام ترقيم منطقي للأكواد
3. **التصنيف**: قم بتجميع مراكز التكلفة المتشابهة
4. **المراجعة الدورية**: راجع أداء مراكز التكلفة بانتظام
5. **التدريب**: تأكد من تدريب المستخدمين على النظام

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **لا يظهر حقل مركز التكلفة**:
   - تأكد من تشغيل الكود لإنشاء الجدول
   - تحقق من وجود الجدول في قاعدة البيانات

2. **خطأ في حفظ البيانات**:
   - تأكد من صحة الاتصال بقاعدة البيانات
   - تحقق من صلاحيات المستخدم

3. **التقارير فارغة**:
   - تأكد من وجود بيانات في الفترة المحددة
   - تحقق من ربط الفواتير بمراكز التكلفة

## التطوير المستقبلي

يمكن تطوير النظام ليشمل:
- مراكز تكلفة هرمية (مراكز فرعية)
- موازنات تقديرية لكل مركز تكلفة
- تنبيهات عند تجاوز الموازنة
- تقارير مقارنة بين الفترات
- تحليل الاتجاهات والتوقعات
