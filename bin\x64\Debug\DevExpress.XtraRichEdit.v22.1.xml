<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <assembly>
    <name>DevExpress.XtraRichEdit.v22.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraEditors.Repository">
      <summary>
        <para>Contains classes representing repository items that store editor-specific settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit">
      <summary>
        <para>Contains settings specific to an in-place editor that displays RTF data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.AcceptsTab">
      <summary>
        <para>Gets or sets a value specifying whether a tab character can be inserted into the editor’s text.</para>
      </summary>
      <value>true if tab characters typed within the control are accepted and processed by an editor; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.Assign(DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Copies the settings of the specified repository item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object or its descendant that represents the source of the operation.</param>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.AutoHeight">
      <summary>
        <para>Gets whether the editor’s height is calculated automatically to fit the editor’s content. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>Always false.</value>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.ConvertEditValueToPlainText(System.Object)">
      <summary>
        <para>Converts the specified value to plain text.</para>
      </summary>
      <param name="editValue">A value to be converted to plain text.</param>
      <returns>A string that is the result of the conversion.</returns>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.CustomHeight">
      <summary>
        <para>Gets or sets the editor’s height.</para>
      </summary>
      <value>An integer value that specifies the editor’s height, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.DocumentFormat">
      <summary>
        <para>Gets or sets the format of the document contained in the editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration member, specifying the document format.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.EditorTypeName">
      <summary>
        <para>Gets the class name of an editor corresponding to the current repository item.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that identifies the class name of a corresponding editor.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.Encoding">
      <summary>
        <para>Gets or sets the character encoding of the document contained in the editor.</para>
      </summary>
      <value>A  <see cref="T:System.Text.Encoding"/> object specifying the character encoding.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.EncodingWebName">
      <summary>
        <para>Gets or sets the character encoding by specifying the name registered with the Internet Assigned Numbers Authority (IANA).</para>
      </summary>
      <value>A string, representing the IANA encoding.</value>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.GetBrick(DevExpress.XtraEditors.PrintCellHelperInfo)">
      <summary>
        <para>Returns a brick object that contains information on how the current editor should be printed.</para>
      </summary>
      <param name="info">A DevExpress.XtraEditors.PrintCellHelperInfo object that provides information on the editor’s state and appearance settings that will be used when the editor is printed.</param>
      <returns>An DevExpress.XtraPrinting.IVisualBrick object that provides information on how the current editor should be printed.</returns>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.HorizontalIndent">
      <summary>
        <para>Gets or sets a horizontal margin between the border and text.</para>
      </summary>
      <value>An integer value that specifies a horizontal margin, in pixels, between the border and text.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.MaxHeight">
      <summary>
        <para>Gets or sets the editor’s maximum height.</para>
      </summary>
      <value>An integer value that specifies the editor’s maximum height, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsBehavior">
      <summary>
        <para>Provides access to an object that enables you to apply restrictions on different editor operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions"/> class instance containing restriction specifications.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsExport">
      <summary>
        <para>Provides access to options specific for document export to different formats.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions"/> class instance containing export options.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsHorizontalScrollbar">
      <summary>
        <para>Provides access to the options specific to the horizontal scrollbar of the rich text editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions"/> object used to specify options for the horizontal scrollbar.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsImport">
      <summary>
        <para>Provides access to options specific for document import from different formats.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions"/> class instance containing import options.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsVerticalScrollbar">
      <summary>
        <para>Provides access to the options specific to the vertical scrollbar of the RichEditControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions"/> object used to specify options for the vertical scrollbar.</value>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.Register">
      <summary>
        <para>Registers the current <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit"/> instance within the default Repository.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.ShowCaretInReadOnly">
      <summary>
        <para>Gets or sets whether the mouse caret is displayed in read-only mode.</para>
      </summary>
      <value>true if the mouse caret is displayed in read-only mode; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.UseTextEditorForAutoFilter">
      <summary>
        <para>Gets whether to substitute this editor with the single-line text editor in auto-filter rows.</para>
      </summary>
      <value>Always true.</value>
    </member>
    <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.VerticalIndent">
      <summary>
        <para>Gets or sets a vertical margin between the border and text.</para>
      </summary>
      <value>An integer value that specifies a vertical margin, in pixels, between the border and text.</value>
    </member>
    <member name="T:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions">
      <summary>
        <para>Represents the storage of settings specifying end-user restrictions applied to document operations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions">
      <summary>
        <para>Represents the class containing options for the horizontal scrollbar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions"/> class.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraRichEdit">
      <summary>
        <para>Contains classes which implement the main functionality of the RichEdit suite.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.BookmarkFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.BookmarkFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.BookmarkFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Bookmark dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.BookmarkFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.BookmarkFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ColumnsSetupFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ColumnsSetupFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.ColumnsSetupFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.ColumnsSetupFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.ColumnsSetupFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.ColumnsSetupFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ColumnsSetupFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Columns dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.ColumnsSetupFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ColumnsSetupFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ColumnsSetupFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.ColumnsSetupFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="N:DevExpress.XtraRichEdit.Commands">
      <summary>
        <para>Contains command objects used in the RichEditControl end-user interface, and internally.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.Commands.FindNextCommand">
      <summary>
        <para>Looks for the next matching string as defined in the search criteria given by the preceding FindCommand.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.Commands.FindNextCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.Commands.FindNextCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">An object exposing the DevExpress.XtraRichEdit.IRichEditControl interface specifying the owner of the command (usually the RichEditControl).</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.Commands.FindNextCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An XtraRichEditStringId enumeration value, identifying the resource string.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.Commands.FindNextCommand.Id">
      <summary>
        <para>Gets the ID of the <see cref="T:DevExpress.XtraRichEdit.Commands.FindNextCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member, identifying the command.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.Commands.FindNextCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraRichEdit.Commands.FindNextCommand"/>.</para>
      </summary>
      <value>A XtraRichEditStringId enumeration value, identifying the resource string.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.Commands.FindPrevCommand">
      <summary>
        <para>Looks for the previous matching string as defined in the search criteria given by the preceding FindCommand.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.Commands.FindPrevCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.Commands.FindPrevCommand"/> class with the specified owner.</para>
      </summary>
      <param name="control">An object implementing the <see cref="T:DevExpress.XtraRichEdit.IRichEditControl"/> interface, specifying the owner of the command.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.Commands.FindPrevCommand.DescriptionStringId">
      <summary>
        <para>Gets a string resource identifier of a command description.</para>
      </summary>
      <value>An XtraRichEditStringId enumeration value, identifying the resource string.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.Commands.FindPrevCommand.Id">
      <summary>
        <para>Gets the ID of the <see cref="T:DevExpress.XtraRichEdit.Commands.FindPrevCommand"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member, identifying the command.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.Commands.FindPrevCommand.MenuCaptionStringId">
      <summary>
        <para>Gets a string resource identifier of a menu caption corresponding to <see cref="T:DevExpress.XtraRichEdit.Commands.FindPrevCommand"/>.</para>
      </summary>
      <value>A XtraRichEditStringId enumeration value, identifying the resource string.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ConvertNotesFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ConvertNotesFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.ConvertNotesFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.NotesFormController)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.ConvertNotesFormShowingEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="controller">A DevExpress.XtraRichEdit.Forms.NotesFormController object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ConvertNotesFormShowingEventArgs.Controller">
      <summary>
        <para>Gets the information for initializing the Convert Notes dialog control.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.NotesFormController object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ConvertNotesFormShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ConvertNotesFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DeleteTableCellsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DeleteTableCellsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.DeleteTableCellsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.DeleteTableCellsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.DeleteTableCellsFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.DeleteTableCellsFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.DeleteTableCellsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Delete Cells dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.DeleteTableCellsFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DeleteTableCellsFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DeleteTableCellsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.DeleteTableCellsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DocumentPropertiesFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentPropertiesFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.DocumentPropertiesFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.DocumentPropertiesFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.DocumentPropertiesFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DocumentPropertiesFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.DocumentPropertiesFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Document Properties dialog controls.</para>
      </summary>
      <value>A DocumentPropertiesFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DocumentPropertiesFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentPropertiesFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.DocumentPropertiesFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DocumentProtectionQueryNewPasswordFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionQueryNewPasswordFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.DocumentProtectionQueryNewPasswordFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.DocumentProtectionQueryNewPasswordFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.DocumentProtectionQueryNewPasswordFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.DocumentProtectionQueryNewPasswordFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.DocumentProtectionQueryNewPasswordFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Document Protection dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.DocumentProtectionQueryNewPasswordFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DocumentProtectionQueryNewPasswordFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionQueryNewPasswordFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.DocumentProtectionQueryNewPasswordFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DocumentProtectionQueryPasswordFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionQueryPasswordFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.DocumentProtectionQueryPasswordFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.DocumentProtectionQueryPasswordFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.DocumentProtectionQueryPasswordFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.DocumentProtectionQueryPasswordFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.DocumentProtectionQueryPasswordFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Unprotect Document dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.DocumentProtectionQueryPasswordFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.DocumentProtectionQueryPasswordFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionQueryPasswordFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.DocumentProtectionQueryPasswordFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.EditStyleFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.EditStyleFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.EditStyleFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.EditStyleFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.EditStyleFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.EditStyleFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.EditStyleFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Style Editor dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.EditStyleFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.EditStyleFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.EditStyleFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">An <see cref="T:DevExpress.XtraRichEdit.EditStyleFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.EncryptDocumentFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.EncryptDocumentFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.EncryptDocumentFormShowingEventArgs.#ctor(System.Windows.Forms.IWin32Window,DevExpress.XtraRichEdit.Forms.EncryptionInfo)">
      <summary>
        <para>Initializes a new instance of the EncryptDocumentFormShowingEventArgs class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="parent">An IWin32Window object.</param>
      <param name="encryptionInfo">An encryption information (password).</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.EncryptDocumentFormShowingEventArgs.EncryptionInfo">
      <summary>
        <para>Provides access to the encryption information.</para>
      </summary>
      <value>An object containing the encryption information.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.EncryptDocumentFormShowingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.EncryptDocumentFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event sender. Identifies the RichEditControl that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.EncryptDocumentFormShowingEventArgs"/> object that contains the event data.</param>
    </member>
    <member name="N:DevExpress.XtraRichEdit.Export">
      <summary>
        <para>Contains classes specific for document export in the RichEdit suite.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions">
      <summary>
        <para>Contains options used for saving (exporting) documents from the <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit"/> control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.FloatingInlineObjectLayoutOptionsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.FloatingInlineObjectLayoutOptionsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.FloatingInlineObjectLayoutOptionsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.FloatingInlineObjectLayoutOptionsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.FloatingInlineObjectLayoutOptionsFormShowingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.FloatingInlineObjectLayoutOptionsFormControllerParameters object.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.FloatingInlineObjectLayoutOptionsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Contains data for the form controller.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.FloatingInlineObjectLayoutOptionsFormControllerParameters object.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.FloatingInlineObjectLayoutOptionsFormShowingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.FloatingInlineObjectLayoutOptionsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.FloatingInlineObjectLayoutOptionsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.FontFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.FontFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.FontFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.FontFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.FontFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.FontFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.FontFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Font dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.FontFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.FontFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.FontFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.FontFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.FormShowingEventArgs">
      <summary>
        <para>Serves as the base for classes which provide data for the events which occur before the RichEditControl dialogs are shown.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.HorizontalScrollbarOptions">
      <summary>
        <para>Represents the class containing options for the horizontal scrollbar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.HorizontalScrollbarOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.HorizontalScrollbarOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.HyperlinkFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.HyperlinkFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.HyperlinkFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.HyperlinkFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.HyperlinkFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.HyperlinkFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.HyperlinkFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Edit Hyperlink dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.HyperlinkFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.HyperlinkFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.HyperlinkFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.HyperlinkFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.HyphenationFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.HyphenationFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.HyphenationFormShowingEventArgs.#ctor(System.Windows.Forms.IWin32Window,DevExpress.XtraRichEdit.Forms.HyphenationInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.HyphenationFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="parent">An IWin32Window object.</param>
      <param name="hyphenationInfo">An information about hyphenation options.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.HyphenationFormShowingEventArgs.HyphenationInfo">
      <summary>
        <para>Provides access to the hyphenation information.</para>
      </summary>
      <value>An object containing the hyphenation information.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.HyphenationFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.HyphenationFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.HyphenationFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ImeCloseStatus">
      <summary>
        <para>Lists possible results of finalizing the input and closing the input method editor window.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.ImeCloseStatus.ImeCompositionCancel">
      <summary>
        <para>Composition string in the input method editor is discarded.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.ImeCloseStatus.ImeCompositionComplete">
      <summary>
        <para>The composition string is used as the result string to insert in the document.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraRichEdit.Import">
      <summary>
        <para>Contains interfaces and classes specific for document import.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions">
      <summary>
        <para>Contains options used for loading (importing) documents from the <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit"/> control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.InsertMergeFieldFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.InsertMergeFieldFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.InsertMergeFieldFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.InsertMergeFieldFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.InsertMergeFieldFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.InsertMergeFieldFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.InsertMergeFieldFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Insert Merge Field dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.InsertMergeFieldFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.InsertMergeFieldFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.InsertMergeFieldFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.InsertMergeFieldFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.InsertTableCellsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.InsertTableCellsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.InsertTableCellsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.InsertTableCellsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.InsertTableCellsFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.InsertTableCellsFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.InsertTableCellsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Insert Cells dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.InsertTableCellsFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.InsertTableCellsFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.InsertTableCellsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.InsertTableCellsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.InsertTableFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.InsertTableFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.InsertTableFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.InsertTableFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.InsertTableFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.InsertTableFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.InsertTableFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Insert Table dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.InsertTableFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.InsertTableFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.InsertTableFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.InsertTableFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.LineNumberingFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.LineNumberingFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.LineNumberingFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.LineNumberingFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.LineNumberingFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.LineNumberingFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.LineNumberingFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Line Numbers dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.LineNumberingFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.LineNumberingFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.LineNumberingFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.LineNumberingFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="N:DevExpress.XtraRichEdit.Menu">
      <summary>
        <para>Contains classes that are used to implement context menus in a RichEdit control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu">
      <summary>
        <para>Represents a popup (context) menu of the RichEditControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu.#ctor(System.EventHandler)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu"/> class with the specified handler of the <see cref="E:DevExpress.XtraBars.PopupMenuBase.BeforePopup"/> event.</para>
      </summary>
      <param name="beforePopup">An event handler that will be invoked when menu is about to be displayed.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.NotesFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.NotesFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.NotesFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.NotesFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.NotesFormShowingEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.NumberingListFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.NotesFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Footnote and Endnote Dialog dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.NotesFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.NotesFormShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.NotesFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.NumberingListFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.NumberingListFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.NumberingListFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.NumberingListFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.NumberingListFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.NumberingListFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.NumberingListFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Bullets and Numbering dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.NumberingListFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.NumberingListFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.NumberingListFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.NumberingListFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.PageSetupFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PageSetupFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.PageSetupFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.PageSetupFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.PageSetupFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A <see cref="T:DevExpress.XtraRichEdit.Forms.PageSetupFormControllerParameters"/> object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.PageSetupFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Page Setup dialog controls.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.Forms.PageSetupFormControllerParameters"/> object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.PageSetupFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PageSetupFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.PageSetupFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ParagraphFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ParagraphFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.ParagraphFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.ParagraphFormControllerParameters,DevExpress.XtraRichEdit.TabsClickCallback)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.ParagraphFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A ParagraphFormControllerParameters object containing information to be displayed in the form.</param>
      <param name="tabsClickCallback"></param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ParagraphFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Paragraph dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.ParagraphFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ParagraphFormShowingEventArgs.TabsClickCallback">
      <summary>
        <para>Parameter required for one of the ParagraphForm constructors.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ParagraphFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ParagraphFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.ParagraphFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.PasteSpecialFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PasteSpecialFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.PasteSpecialFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.PasteSpecialFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.PasteSpecialFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.PasteSpecialFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.PasteSpecialFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Paste Special dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.PasteSpecialFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.PasteSpecialFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PasteSpecialFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.PasteSpecialFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Menu.RichEditPopupMenu)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">A <see cref="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu"/> which represents the event’s popup menu. This value is assigned to the <see cref="P:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs.Menu"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Menu.RichEditPopupMenu,DevExpress.XtraRichEdit.RichEditMenuType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs"/> class with the specified menu and menu type.</para>
      </summary>
      <param name="menu">A <see cref="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu"/> object representing a popup menu.</param>
      <param name="menuType">A  enumeration member specifying the menu type.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs.Menu">
      <summary>
        <para>Gets or sets the popup (context) menu for which this event was raised.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu"/> object, which represents the context menu for the event.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs.MenuType">
      <summary>
        <para>Gets a visual element for which the popup menu is invoked.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditMenuType"/> enumeration member specifying a visual element to which the context menu is invoked.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.PopupMenuShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.RichEdit.RichEditControl.PopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">The event sender (typically a <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.PopupMenuShowingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RangeEditingPermissionsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.RangeEditingPermissionsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RangeEditingPermissionsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.RangeEditingPermissionsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.RangeEditingPermissionsFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.RangeEditingPermissionsFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RangeEditingPermissionsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Editing Permissions dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.RangeEditingPermissionsFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RangeEditingPermissionsFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.RangeEditingPermissionsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.RangeEditingPermissionsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditAppearance">
      <summary>
        <para>Represents appearance settings specific to the RichEditControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditAppearance.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditAppearance"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditAppearance.Text">
      <summary>
        <para>Provides access to the appearance object containing appearance settings for the text.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> representing properties used to customize the look and feel of the text.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditBrush">
      <summary>
        <para>Defines a brush for custom draw on the layout canvas. Brushes are used to fill graphics shapes and draw text.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditBrush.#ctor(System.Drawing.Color)">
      <summary>
        <para>Initializes new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditBrush"></see> class with the specified settings.</para>
      </summary>
      <param name="color">Represents one of the ARGB color values.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditBrush.Color">
      <summary>
        <para>Gets or sets the color of a <see cref="T:DevExpress.XtraRichEdit.RichEditBrush"/> object.</para>
      </summary>
      <value>One of the ARGB color values indicating the brush color.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditControl">
      <summary>
        <para>Represents a RichEdit control which is a container for the rich-text document providing all the necessary functionality.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.About">
      <summary>
        <para>Invokes the About dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.AcceptsEscape">
      <summary>
        <para>Gets or sets a value indicating whether pressing the ESC key is processed by the RichEditControl.</para>
      </summary>
      <value>true if the input ESC key is processed by the RichEditControl , otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.AcceptsReturn">
      <summary>
        <para>Gets or sets a value indicating whether pressing the RETURN key is processed by the RichEditControl.</para>
      </summary>
      <value>true if the input RETURN key is processed by the RichEditControl , otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.AcceptsTab">
      <summary>
        <para>Gets or sets a value indicating whether pressing the TAB key types a TAB character instead of moving the focus to the next control in the tab order.</para>
      </summary>
      <value>true if users can enter tabs in a RichEditControl using the TAB key, false if pressing the TAB key moves the focus.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ActiveRecordChanged">
      <summary>
        <para>Occurs after traversing to the next data record in a mail merge data source.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ActiveRecordChanging">
      <summary>
        <para>Occurs before traversing to the next data record in a mail merge data source.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ActiveView">
      <summary>
        <para>Gets the View currently used by the RichEditControl to display the document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditView"/> class instance, which is one of the views listed in the <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ActiveViewChanged">
      <summary>
        <para>Occurs when the value of the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.ActiveView"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ActiveViewType">
      <summary>
        <para>Gets or sets the type of the RichEditControl’s View.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration value specifying the active View type.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ActualDpiScale">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="callback">A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="callback">A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.</param>
      <param name="promote">true to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.Object)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="serviceInstance">An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.Object,System.Boolean)">
      <summary>
        <para>Adds the specified service to the service container.</para>
      </summary>
      <param name="serviceType">The type of service to add.</param>
      <param name="serviceInstance">An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.</param>
      <param name="promote">true to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddToolbarToMenuManager(DevExpress.XtraRichEdit.RichEditToolbarType)">
      <summary>
        <para>Adds a set of toolbars to the <see cref="T:DevExpress.XtraBars.BarManager"/> at runtime.</para>
      </summary>
      <param name="toolbarType">One of the <see cref="T:DevExpress.XtraRichEdit.RichEditToolbarType"/> enumeration values indicating the toolbar(s) to be added.</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.AfterExport">
      <summary>
        <para>Occurs after the document is successfully exported.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.AllowDrop">
      <summary>
        <para>Gets or sets a value indicating whether the control allows drag-and-drop operations. This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>true if drag-and-drop is enabled in the control; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Appearance">
      <summary>
        <para>Provides access to the object containing appearance settings for the control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditAppearance"/> object containig appearance settings specific to the RichEditControl.</value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AssignShortcutKeyToCommand(System.Windows.Forms.Keys,System.Windows.Forms.Keys,DevExpress.XtraRichEdit.Commands.RichEditCommandId)">
      <summary>
        <para>Assigns a shortcut key to a command. The shortcut key is available for all RichEdit views.</para>
      </summary>
      <param name="key">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a key to assign.</param>
      <param name="modifier">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a modifier key.</param>
      <param name="commandId">A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> enumeration member specifying a command.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AssignShortcutKeyToCommand(System.Windows.Forms.Keys,System.Windows.Forms.Keys,DevExpress.XtraRichEdit.Commands.RichEditCommandId,DevExpress.XtraRichEdit.RichEditViewType)">
      <summary>
        <para>Assigns a shortcut key to a command. The shortcut key is available for the specified RichEditView.</para>
      </summary>
      <param name="key">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a key to assign.</param>
      <param name="modifier">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a modifier key.</param>
      <param name="commandId">A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> enumeration member specifying a command.</param>
      <param name="viewType">A <see cref="T:DevExpress.XtraRichEdit.RichEditView"/> specifying the view in which a shortcut will be effective</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.AssignShortcutKeyToCommand(System.Windows.Forms.Keys,System.Windows.Forms.Keys,DevExpress.XtraRichEdit.Commands.RichEditCommandId,System.Boolean)">
      <summary>
        <para>Assigns a shortcut key to a command. The shortcut key is available for all RichEdit views.</para>
      </summary>
      <param name="key">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a key to assign.</param>
      <param name="modifier">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a modifier key.</param>
      <param name="commandId">A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> enumeration member specifying a command.</param>
      <param name="updateToolTip">True to modify a tooltip of the <see cref="T:DevExpress.XtraBars.BarItem"/> to which a command is assigned; otherwise, false.</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.AutoCorrect">
      <summary>
        <para>Fires when text is typed in the control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.AutoSizeMode">
      <summary>
        <para>Gets or sets whether the RichEditControl resizes to accommodate the displayed text.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraRichEdit.AutoSizeMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.BackColor">
      <summary>
        <para>This property is not in effect for the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> class.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value representing the background color.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.BackgroundImage">
      <summary>
        <para>This property is not in effect for the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.BackgroundImageLayout">
      <summary>
        <para>This property is not in effect for the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeDispose">
      <summary>
        <para>Occurs before the RichEdit control is released from memory</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeEncodingDetection">
      <summary>
        <para>Occurs before detection of the loaded plain text or HTML encoding.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeExport">
      <summary>
        <para>Occurs before the document is saved (exported to a certain format).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeImport">
      <summary>
        <para>Occurs before a document is loaded (imported from an external source).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforePagePaint">
      <summary>
        <para>Enables you to specify a custom <see cref="T:DevExpress.XtraRichEdit.API.Layout.PagePainter"/> descendant to alter the way the layout elements are drawn.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> to prevent its visual updates until the RichEditControl.EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BookmarkFormShowing">
      <summary>
        <para>Occurs before the Bookmark dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.BorderShadingFormShowing">
      <summary>
        <para>Occurs before the Borders and Shading dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.BorderStyle">
      <summary>
        <para>Gets or sets the border style for the RichEdit control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value which specifies the border style of the scheduler control.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CalculateDocumentVariable">
      <summary>
        <para>Fires for the DOCVARIABLE field and allows you to update its value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CancelUpdate">
      <summary>
        <para>This method supports the internal infrastructure and is not intended to be called directly from your code. Use the RichEditControl.EndUpdate method instead.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.CanRedo">
      <summary>
        <para>Indicates whether there are actions that occurred recently within the control that can be reapplied.</para>
      </summary>
      <value>true if there are operations that have been undone that can be reapplied; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.CanUndo">
      <summary>
        <para>Indicates whether the user can undo the previous operation.</para>
      </summary>
      <value>true if the user can undo the previous opration; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ClearUndo">
      <summary>
        <para>Clears information about recent operations, so they cannot not be undone.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ClipboardSetDataException">
      <summary>
        <para>Occurs when an exception is thrown while trying to place data onto the Clipboard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CloseImeWindow(DevExpress.XtraRichEdit.ImeCloseStatus)">
      <summary>
        <para>Finalizes the input method editor composition and closes IME window.</para>
      </summary>
      <param name="closeStatus">A <see cref="T:DevExpress.XtraRichEdit.ImeCloseStatus"/> enumeration member specifying how the composition string is treated.</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CloseReviewingPane">
      <summary>
        <para>Occurs when closing the reviewing pane displaying document comments.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ColumnsSetupFormShowing">
      <summary>
        <para>Occurs before the Columns dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CommentInserted">
      <summary>
        <para>Occurs after a new comment is created in the document.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ContentChanged">
      <summary>
        <para>Occurs when the document content was changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ConvertNotesFormShowing">
      <summary>
        <para>Occurs before the Convert Notes dialog is invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Copy">
      <summary>
        <para>Copies the content of the selection to the Clipboard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateBars">
      <summary>
        <para>Creates a set of toolbars with <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>-specific actions at runtime.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraBars.BarManager"/> object containing all RichEditControl-specific toolbars.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateBars(DevExpress.XtraRichEdit.RichEditToolbarType)">
      <summary>
        <para>Creates a set of toolbars with <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>-specific actions at runtime.</para>
      </summary>
      <param name="toolbarType">One of the <see cref="T:DevExpress.XtraRichEdit.RichEditToolbarType"/> enumeration values indicating toolbar(s) to be added.</param>
      <returns>A <see cref="T:DevExpress.XtraBars.BarManager"/> object containing the given toolbars.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateCommand(DevExpress.XtraRichEdit.Commands.RichEditCommandId)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommand"/> object by the command identifier.</para>
      </summary>
      <param name="commandId">A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> structure member indicating a command.</param>
      <returns>A Rich Text Control command.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateDocumentServer">
      <summary>
        <para>Gets an object implementing the <see cref="T:DevExpress.XtraRichEdit.IRichEditDocumentServer"/> interface.</para>
      </summary>
      <returns>An object with the <see cref="T:DevExpress.XtraRichEdit.IRichEditDocumentServer"/> interface.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateMailMergeOptions">
      <summary>
        <para>Creates an object for specifying the options used in mail merge operations.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> class instance containing options used for mail merge.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateNewDocument(System.Boolean)">
      <summary>
        <para>Creates and loads a new empty document. Optionally, raises the DocumentClosing event.</para>
      </summary>
      <param name="raiseDocumentClosing">true, to raise the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.DocumentClosing"/> event; otherwise, false.</param>
      <returns>true if a new document is created and loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateRibbon">
      <summary>
        <para>Creates a set of ribbon tabs with <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>-specific actions at runtime.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object containing all <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>-specific ribbon tabs.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateRibbon(DevExpress.XtraRichEdit.RichEditToolbarType)">
      <summary>
        <para>Creates a set of <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>-specific ribbon tabs at runtime.</para>
      </summary>
      <param name="toolbarType">One of the <see cref="T:DevExpress.XtraRichEdit.RichEditToolbarType"/> enumeration values indicating the ribbon tabs to be added.</param>
      <returns>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object containing given ribbon tabs.</returns>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CustomDrawActiveView">
      <summary>
        <para>Occurs before the active RichEdit view is displayed, and enables you to draw graphics on the document area.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CustomizeMergeFields">
      <summary>
        <para>Fires when the ‘Insert Merge Field’ command button is clicked, and enables you to customize a drop-down field list.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CustomMarkDraw">
      <summary>
        <para>Fires before a custom mark is painted, and enables you to visualize the custom mark as required.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.CustomPropertiesChanged">
      <summary>
        <para>Occurs when one of the <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentCustomProperties"/> has changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Cut">
      <summary>
        <para>Removes the selection from the document, and places it on the Clipboard.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DecryptionFailed">
      <summary>
        <para>Occurs if the RichEditControl failed to open an encrypted file.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DefaultViewType">
      <summary>
        <para>Gets the default view type used by the RichEdit control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration member, which specifies the RichEdit view.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DeleteTableCellsFormShowing">
      <summary>
        <para>Occurs before the Delete Cells dialog is invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.DeselectAll">
      <summary>
        <para>Specifies that no content is selected in the control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DocBytes">
      <summary>
        <para>Gets or sets the document content as an array of bytes in Microsoft Word 97-2003 format (Doc) format.</para>
      </summary>
      <value>The document content in Doc format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DocmBytes">
      <summary>
        <para>Gets or sets the document content in DOCM (Microsoft Office Open XML Macro-Enabled Document) format.</para>
      </summary>
      <value>The document content in DOCM format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocmBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Document">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface specifying the control’s document.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface specifying a document loaded in the control.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentClosing">
      <summary>
        <para>Occurs when a document that contains unsaved changes is about to be closed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentEncryptionChanged">
      <summary>
        <para>Occurs after one of the DocumentEncryption properties has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DocumentLayout">
      <summary>
        <para>Provides access to Layout API.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.API.Layout.DocumentLayout"/> object that is the key object for Layout API.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentLoaded">
      <summary>
        <para>Occurs after a document is loaded into the RichEdit control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentPropertiesChanged">
      <summary>
        <para>Occurs after one of the <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentProperties"/> has changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentPropertiesFormShowing">
      <summary>
        <para>Occurs before the Document Properties dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionChanged">
      <summary>
        <para>Fires when the document protection is enforced or dropped.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionQueryNewPasswordFormShowing">
      <summary>
        <para>Occurs before the Document Protection dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentProtectionQueryPasswordFormShowing">
      <summary>
        <para>Occurs before the Unprotect Document dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DocumentViewDirection">
      <summary>
        <para>Specifies the page order in Print Layout view and in Print Preview.</para>
      </summary>
      <value>One of the DocumentViewDirection enumeration values indicating the page order.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DotBytes">
      <summary>
        <para>Gets or sets the document content in DOT (Microsoft Word 97-2003 Template) format.</para>
      </summary>
      <value>The document content in DOT format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DotBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DotmBytes">
      <summary>
        <para>Gets or sets the document content in DOTM (Microsoft Office Open XML Macro-Enabled Template) format.</para>
      </summary>
      <value>The document content in DOTM format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DotmBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DotxBytes">
      <summary>
        <para>Gets or sets the document content in DOTX (Microsoft Office Open XML Template) format.</para>
      </summary>
      <value>The document content in DOTX format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.DotxBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DpiX">
      <summary>
        <para>Gets the current dpi value for the X-coordinate.</para>
      </summary>
      <value>A Single dpi value.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DpiY">
      <summary>
        <para>Gets the current dpi value for the Y-coordinate.</para>
      </summary>
      <value>A Single dpi value.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.DragDropMode">
      <summary>
        <para>Gets or sets the drag-and-drop mode which is active in the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.DragDropMode"/> enumeration value.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.EditStyleFormShowing">
      <summary>
        <para>Fires before the Style Editor dialog is displayed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.EmptyDocumentCreated">
      <summary>
        <para>Occurs when a new document is created in the RichEdit Control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.EnableToolTips">
      <summary>
        <para>Gets or sets whether to display tooltips for data fields in documents.</para>
      </summary>
      <value>true, to display data field tooltips; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.EncryptDocumentFormShowing">
      <summary>
        <para>Occurs before the Encrypt Document Dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.EncryptedFileIntegrityCheckFailed">
      <summary>
        <para>Occurs when the encrypted file does not pass data integrity verification.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.EncryptedFilePasswordCheckFailed">
      <summary>
        <para>Occurs when the encryption password is empty or invalid.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.EncryptedFilePasswordRequested">
      <summary>
        <para>Occurs when the <see cref="P:DevExpress.XtraRichEdit.Import.RichEditDocumentImportOptions.EncryptionPassword"/> property is not set.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> object after a call to the RichEditControl.BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ExportToPdf(System.IO.Stream)">
      <summary>
        <para>Exports the content of the RichEditControl to the specified stream in PDF format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ExportToPdf(System.IO.Stream,DevExpress.Pdf.PdfCreationOptions,DevExpress.Pdf.PdfSaveOptions)">
      <summary>
        <para>Exports the content of the RichEditControl to the specified stream in PDF format, applying options for creating and saving.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="creationOptions">A <see cref="T:DevExpress.Pdf.PdfCreationOptions"/> object.</param>
      <param name="saveOptions">A <see cref="T:DevExpress.Pdf.PdfSaveOptions"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ExportToPdf(System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the content of the RichEditControl to the specified stream in PDF format, applying PDF-specific options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="pdfExportOptions">A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ExportToPdf(System.String)">
      <summary>
        <para>Exports the document content to the specified file path in PDF format.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ExportToPdf(System.String,DevExpress.Pdf.PdfCreationOptions,DevExpress.Pdf.PdfSaveOptions)">
      <summary>
        <para>Exports the content of the RichEditControl to the specified stream in PDF format, applying options for creating and saving.</para>
      </summary>
      <param name="fileName">A string that is the name of the file, to which the created document is exported.</param>
      <param name="creationOptions">A <see cref="T:DevExpress.Pdf.PdfCreationOptions"/> object.</param>
      <param name="saveOptions">A <see cref="T:DevExpress.Pdf.PdfSaveOptions"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ExportToPdf(System.String,DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the content of the RichEditControl to the specified file path in PDF format, applying PDF-specific options.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.</param>
      <param name="pdfExportOptions">A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options.</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FinishHeaderFooterEditing">
      <summary>
        <para>Occurs when the end-user finishes editing a header or footer.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.FlatOpcBytes">
      <summary>
        <para>Gets or sets the document content in FlatOpc (Microsoft Word XML Document) format.</para>
      </summary>
      <value>The document content in FlatOpc format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FlatOpcBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.FlatOpcMacroEnabledBytes">
      <summary>
        <para>Gets or sets the document content in FlatOpcMacroEnabled (Microsoft Word XML Macro-Enabled Document) format.</para>
      </summary>
      <value>The document content in FlatOpcMacroEnabled format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FlatOpcMacroEnabledBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.FlatOpcMacroEnabledTemplateBytes">
      <summary>
        <para>Gets or sets the document content in FlatOpcMacroEnabledTemplate (Microsoft Word XML Macro-Enabled Template) format.</para>
      </summary>
      <value>The document content in FlatOpcMacroEnabledTemplate format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FlatOpcMacroEnabledTemplateBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.FlatOpcTemplateBytes">
      <summary>
        <para>Gets or sets the document content in FlatOpcTemplate (Microsoft Word XML Template ) format.</para>
      </summary>
      <value>The document content in FlatOpcTemplate format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FlatOpcTemplateBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FloatingInlineObjectLayoutOptionsFormShowing">
      <summary>
        <para>Raised before the Inline and Floating Object Layout Dialog is shown.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Font">
      <summary>
        <para>Gets or sets the font of the text that has no direct font formatting or style applied.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Font"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.FontFormShowing">
      <summary>
        <para>Occurs before the Font dialog is invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ForceSyntaxHighlight">
      <summary>
        <para>Reserved for future use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ForeColor">
      <summary>
        <para>Gets or sets the color of the text that has no direct font formatting or style applied.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> object representing the color.</value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetBoundsFromPosition(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Gets bounds of the character at the specified position.</para>
      </summary>
      <param name="pos">A target position.</param>
      <returns>the area occupied by a character, or the System.Drawing.Rectangle.Empty value if the bounds could not be determined.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetBoundsFromPositionF(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Gets the rectangle representing the character at the specified position.</para>
      </summary>
      <param name="pos">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/>, specifying the position in the document.</param>
      <returns>A <see cref="T:System.Drawing.RectangleF"/> representing the area occupied by a character or the System.Drawing.Rectangle.Empty value if the bounds could not be determined. Measured in the units that are in effect.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetLayoutPhysicalBoundsFromPosition(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
      <summary>
        <para>Gets the rectangle representing the character at the specified position with coordinates in a layout model.</para>
      </summary>
      <param name="pos">A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> representing the position in the document.</param>
      <returns>A <see cref="T:System.Drawing.Rectangle"/> representing the character. Measured in layout units.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetPositionFromPoint(System.Drawing.PointF)">
      <summary>
        <para>Gets the position in the document closest to the specified point.</para>
      </summary>
      <param name="clientPoint">A <see cref="T:System.Drawing.PointF"/> object that specifies a point in the RichEditControl window.</param>
      <returns>A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object representing a position in the document.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetService(System.Type)">
      <summary>
        <para>Gets the service object of the specified type.</para>
      </summary>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <returns>A service object of the specified type,or a null reference (Nothing in Visual Basic) if there is no service object of this type.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetService``1">
      <summary>
        <para>Gets the specified service.</para>
      </summary>
      <typeparam name="T">The service type.</typeparam>
      <returns>A service object of the specified type or null for reference types and zero for numeric value types if a service is not available.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetShortcutKey(DevExpress.XtraRichEdit.Commands.RichEditCommandId,DevExpress.XtraRichEdit.RichEditViewType)">
      <summary>
        <para>Gets the shortcut key for the specified command.</para>
      </summary>
      <param name="commandId">A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> enumeration member specifying a command.</param>
      <param name="viewType">A <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration member specifying the RichEdit view.</param>
      <returns>A <see cref="T:System.Windows.Forms.Keys"/> enumeration member identifying shortcut keys.</returns>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.HtmlText">
      <summary>
        <para>Gets or sets the control’s content as HTML text.</para>
      </summary>
      <value>A string containing text in HTML format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.HtmlTextChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.HyperlinkClick">
      <summary>
        <para>Occurs when an end-user clicks the hyperlink to activate it.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.HyperlinkFormShowing">
      <summary>
        <para>Occurs before the Edit Hyperlink dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.HyphenationDictionaries">
      <summary>
        <para>Provides access to the collection of hyphenation dictionaries.</para>
      </summary>
      <value>A collection of objects implementing the <see cref="T:DevExpress.XtraRichEdit.API.Native.IHyphenationDictionary"/> interface.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.HyphenationFormShowing">
      <summary>
        <para>Occurs before the Hyphenation dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.InitializeDocument">
      <summary>
        <para>Occurs before a document is loaded. Handle this event to set initial document settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.InsertMergeFieldFormShowing">
      <summary>
        <para>Occurs before the Insert Merge Field dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.InsertObjectFormShowing">
      <summary>
        <para>Occurs before the Object Dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.InsertTableCellsFormShowing">
      <summary>
        <para>Occurs before the Insert Cells dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.InsertTableFormShowing">
      <summary>
        <para>Occurs before the Insert Table dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.InvalidFormatException">
      <summary>
        <para>Fires when the supplied data could not be recognized as data in the assumed format for import.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsFloatingObjectSelected">
      <summary>
        <para>Indicates whether any floating object in the document is selected.</para>
      </summary>
      <value>true if a floating object is selected; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.IsImeWindowOpen">
      <summary>
        <para>Determines whether the Input Method Editor (IME) composition window is active.</para>
      </summary>
      <returns>true if the IME window is open; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsPrintingAvailable">
      <summary>
        <para>Indicates whether the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> can be printed or exported.</para>
      </summary>
      <value>true if the control can be printed and exported; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsSelectionInComment">
      <summary>
        <para>Indicates whether the selection (CaretPosition) is located in a comment.</para>
      </summary>
      <value>true if the caret is located in the comment; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsSelectionInFooter">
      <summary>
        <para>Indicates whether the selection (CaretPosition) is located in the document footer.</para>
      </summary>
      <value>true if the caret is located in the footer; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsSelectionInHeader">
      <summary>
        <para>Indicates whether the selection (CaretPosition) is located in the document header.</para>
      </summary>
      <value>true if the caret is located in the header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsSelectionInHeaderOrFooter">
      <summary>
        <para>Indicates whether the selection (CaretPosition) is located in the main document or the header or footer.</para>
      </summary>
      <value>true if the caret is located in the header or footer; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.IsSelectionInTable">
      <summary>
        <para>Gets whether the entire selection is contained in one table.</para>
      </summary>
      <returns>true if a selection is within one table; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsSelectionInTextBox">
      <summary>
        <para>Indicates whether the selection (CaretPosition) is located in a text box.</para>
      </summary>
      <value>true if the caret is located in the text box; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsUpdateLocked">
      <summary>
        <para>Returns a value indicating whether the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> is locked for update.</para>
      </summary>
      <value>true if the control is locked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.LayoutUnit">
      <summary>
        <para>Gets or sets a unit of measure used for a document layout. Starting from v18.1, the property has no effect.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.XtraRichEdit.DocumentLayoutUnit"/> enumeration values.
The default is DocumentLayoutUnit.Pixel.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.LineNumberingFormShowing">
      <summary>
        <para>Occurs before the Line Numbers dialog is invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument">
      <summary>
        <para>Invokes the “Open…” file dialog, creates a specific importer and loads the file.</para>
      </summary>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.Byte[])">
      <summary>
        <para>Loads a document from a byte array. The file format is determined based on document content.</para>
      </summary>
      <param name="buffer">A byte array that contains document data.</param>
      <returns>true if the document was successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.Byte[],DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Loads a document in the specified format from a byte array.</para>
      </summary>
      <param name="buffer">A byte array that contains document data.</param>
      <param name="format">An enumeration member that specifies the format of the loaded document.</param>
      <returns>true if the document was successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.Byte[],DevExpress.XtraRichEdit.DocumentFormat,System.String)">
      <summary>
        <para>Loads a document in the specified format from a byte array. You can use this method to load HTML documents with connected files.</para>
      </summary>
      <param name="buffer">A byte array that contains document data.</param>
      <param name="format">An enumeration member that specifies the format of the loaded document.</param>
      <param name="sourceUri">The document’s URI. You can use this parameter to load files associated with an HTML document.</param>
      <returns>true if the document was successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.IO.Stream)">
      <summary>
        <para>Loads a document from the stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the stream used to load a document.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Loads a document from a stream, specifying the document format.</para>
      </summary>
      <param name="stream">The stream from which to load a document.</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration members indicating the document format.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat,System.Boolean)">
      <summary>
        <para>Loads a document from a stream, specifying the document format and optionally leaving the stream open.</para>
      </summary>
      <param name="stream">The stream from which to load a document.</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration members indicating the document format.</param>
      <param name="leaveOpen">true, to leave the stream open; otherwise, false.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat,System.String)">
      <summary>
        <para>Loads a document from the stream in the specified format. External content for HTML format is retrieved using the specified source (base) URI.</para>
      </summary>
      <param name="stream">The stream from which to load the document.</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration members, indicating the document format.</param>
      <param name="sourceUri">A string representing the document URI.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.String)">
      <summary>
        <para>Loads a document from the specified file. The file format is determined by its content.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Loads a document from a file, specifying the document format.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration members, indicating the document format.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat,System.String)">
      <summary>
        <para>Loads a document from the file in the specified format. External content for HTML format is retrieved using the specified source (base) URI.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration members, indicating the document format.</param>
      <param name="sourceUri">A string representing the document URI.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Open file dialog as a child of the specified parent window.</para>
      </summary>
      <param name="parent">The <see cref="T:System.Windows.Forms.IWin32Window"/> object that is the parent window.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocumentTemplate(System.Byte[])">
      <summary>
        <para>Loads a document template from a byte array.</para>
      </summary>
      <param name="buffer">A byte array that contains document data.</param>
      <returns>true if the document was successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocumentTemplate(System.Byte[],DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Loads a document template in the specified format from a byte array.</para>
      </summary>
      <param name="buffer">A byte array that contains document data.</param>
      <param name="documentFormat">An enumeration member that specifies the format of the loaded document.</param>
      <returns>true if the document was successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocumentTemplate(System.IO.Stream)">
      <summary>
        <para>Loads a document template from the stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the stream used to load a document.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocumentTemplate(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Loads a document template from a stream, specifying the document format.</para>
      </summary>
      <param name="stream">The stream from which to load a document.</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration members, indicating the document format.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocumentTemplate(System.String)">
      <summary>
        <para>Loads a document template from the specified file.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocumentTemplate(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Loads a document template from the file in the specified format.</para>
      </summary>
      <param name="fileName">A string specifying the file to load (including the full path).</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration values indicating the document format.</param>
      <returns>true if the document is successfully loaded; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.LookAndFeel">
      <summary>
        <para>Provides access to the settings that specify the RichEdit control’s look and feel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the control’s look and feel.</value>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(DevExpress.XtraRichEdit.API.Native.Document)">
      <summary>
        <para>Merges the current document and sends the result to the specified <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/>.</para>
      </summary>
      <param name="document">An object exposing the <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface to which the merged document is sent.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,DevExpress.XtraRichEdit.API.Native.Document)">
      <summary>
        <para>Merges the current document using the specified options, and sends the result to the specified <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/>.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface exposing options for the current merge.</param>
      <param name="targetDocument">An object exposing the <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface to which the merged document is sent.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,DevExpress.XtraRichEdit.IRichEditDocumentServer)">
      <summary>
        <para>Merges the current document using the specified options and sends the result to the specified RichEditDocumentServer instance.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface exposing options for the current merge.</param>
      <param name="targetDocumentServer">An object implementing the IRichEditDocumentServer interface, such as the <see cref="T:DevExpress.XtraRichEdit.RichEditDocumentServer"/> or the  <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> instance.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Merges the current document using the specified options and sends the result to the specified stream in a specified format.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface exposing options for the current merge.</param>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the output stream.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration member specifying the destination format.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(DevExpress.XtraRichEdit.API.Native.MailMergeOptions,System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Merges the current document using the specified options and saves the result to the specified file and format.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.XtraRichEdit.API.Native.MailMergeOptions"/> interface exposing options for the merge.</param>
      <param name="fileName">A name of the file to which the merged document is saved.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration member specifying the destination format.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(DevExpress.XtraRichEdit.IRichEditDocumentServer)">
      <summary>
        <para>Merges the current document and sends the result to the specified RichEditDocumentServer instance.</para>
      </summary>
      <param name="documentServer">An object implementing the IRichEditDocumentServer interface, such as the <see cref="T:DevExpress.XtraRichEdit.RichEditDocumentServer"/> or the  <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> instance.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Merges the current document and sends the result to the specified stream in a specified format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object that is the output stream.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration member specifying the destination format.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MailMerge(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Merges the current document and sends the result to the specified file in a specified format.</para>
      </summary>
      <param name="fileName">A name of the file to which the merged document is saved.</param>
      <param name="format">A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration member specifying the destination format.</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.MailMergeFinished">
      <summary>
        <para>Fires when mail merge is completed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.MailMergeRecordFinished">
      <summary>
        <para>Fires after each data record is merged with the document in the mail merge process.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.MailMergeRecordStarted">
      <summary>
        <para>Fires before each data record is merged with the document in the mail merge process.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.MailMergeStarted">
      <summary>
        <para>Fires before mail merge starts.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.MeasureSingleLineString(System.String,DevExpress.XtraRichEdit.API.Native.CharacterPropertiesBase)">
      <summary>
        <para>Calculates the length of a formatted string.</para>
      </summary>
      <param name="text">A string to measure.</param>
      <param name="properties">A <see cref="T:DevExpress.XtraRichEdit.API.Native.CharacterPropertiesBase"/> interface specifying formatting used to display a string.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> object that is the rectangle required to display a string.</returns>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.MenuManager">
      <summary>
        <para>Gets or sets the menu manager which controls the look and feel of context menus.</para>
      </summary>
      <value>An object that implements the DevExpress.Utils.Menu.IDXMenuManager interface.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.MhtText">
      <summary>
        <para>Gets or sets the control’s content as MHT text.</para>
      </summary>
      <value>A string containing text in MHT format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.MhtTextChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Model">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Modified">
      <summary>
        <para>Gets or sets whether the control’s content was modified since it was last saved.</para>
      </summary>
      <value>true if the control’s content was modified since it was last saved; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ModifiedChanged">
      <summary>
        <para>Occurs when the value of the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.Modified"/> property is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.NotesFormShowing">
      <summary>
        <para>Occurs before the Footnotes and Endnotes dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.NumberingListFormShowing">
      <summary>
        <para>Occurs before the Bullets and Numbering dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.OpenDocumentBytes">
      <summary>
        <para>Gets or sets the control’s content as an array of bytes in Open Office Text (.odt) format.</para>
      </summary>
      <value>An array of bytes containing data in the OpenDocument Text (.odt) format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.OpenDocumentBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.OpenXmlBytes">
      <summary>
        <para>Gets or sets the control’s content as an array of bytes in Office Open XML (Docx) format.</para>
      </summary>
      <value>An array of bytes representing the document in Docx format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.OpenXmlBytesChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Options">
      <summary>
        <para>Provides access to the variety of options which can be specified for the RichEditControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditControlOptions"/> object containing various RichEditControl’s options.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Overtype">
      <summary>
        <para>Specifies whether or not the overtype mode is enabled for the RichEdit control.</para>
      </summary>
      <value>true, to enable the overtype mode; otherwise false.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.OvertypeChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.Xpf.RichEdit.RichEditControl.Overtype"/> property value has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.PageSetupFormShowing">
      <summary>
        <para>Occurs before the Page Setup dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ParagraphFormShowing">
      <summary>
        <para>Occurs before the Paragraph dialog is invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Paste">
      <summary>
        <para>Inserts the contents of the Clipboard at the selection.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.PasteSpecialFormShowing">
      <summary>
        <para>Occurs before the Paste Special dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.PopupMenuShowing">
      <summary>
        <para>Occurs before a context (popup) menu is created for the control’s document every time a context menu is being invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.PreparePopupMenu">
      <summary>
        <para>Occurs before a context (popup) menu is created for the control’s document every time a context menu is being invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Print">
      <summary>
        <para>Prints the document to the default printer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Print(System.Drawing.Printing.PrinterSettings)">
      <summary>
        <para>Prints the document with the given printer settings.</para>
      </summary>
      <param name="printerSettings">A <see cref="T:System.Drawing.Printing.PrinterSettings"/> instance containing printer settings.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Print(System.Drawing.Printing.PrinterSettings,System.String)">
      <summary>
        <para>Prints the document with the given printer settings and document name.</para>
      </summary>
      <param name="printerSettings">A <see cref="T:System.Drawing.Printing.PrinterSettings"/> instance containing printer settings.</param>
      <param name="printDocumentName">A string indicating the document name to be printed.</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> class properties has changed its value.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.RangeEditingPermissionsFormShowing">
      <summary>
        <para>Occurs before the Editing Permissions dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ReadOnly">
      <summary>
        <para>Gets or sets whether document modifications are prohibited.</para>
      </summary>
      <value>true if the document is in a read-only state; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ReadOnlyChanged">
      <summary>
        <para>Occurs when the read-only state of the RichEdit control is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Redo">
      <summary>
        <para>Reapplies the last action that was undone in the control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveService(System.Type)">
      <summary>
        <para>Removes the service of specified type from the service container.</para>
      </summary>
      <param name="serviceType">The type of service to remove.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveService(System.Type,System.Boolean)">
      <summary>
        <para>Removes the service of the specified type from the service container.</para>
      </summary>
      <param name="serviceType">The type of service to remove.</param>
      <param name="promote">true to promote this request to any parent service containers; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveShortcutKey(System.Windows.Forms.Keys,System.Windows.Forms.Keys)">
      <summary>
        <para>Removes a command shortcut for all RichEdit views.</para>
      </summary>
      <param name="key">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a shortcut key.</param>
      <param name="modifier">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a modifier key.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveShortcutKey(System.Windows.Forms.Keys,System.Windows.Forms.Keys,DevExpress.XtraRichEdit.RichEditViewType)">
      <summary>
        <para>Removes a command shortcut for the specified RichEditView.</para>
      </summary>
      <param name="key">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a shortcut key.</param>
      <param name="modifier">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a modifier key.</param>
      <param name="viewType">A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> enumeration member specifying a command.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveShortcutKey(System.Windows.Forms.Keys,System.Windows.Forms.Keys,System.Boolean)">
      <summary>
        <para>Removes a command shortcut for all RichEdit views.</para>
      </summary>
      <param name="key">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a shortcut key.</param>
      <param name="modifier">A <see cref="T:System.Windows.Forms.Keys"/> enumeration member specifying a modifier key.</param>
      <param name="updateToolTip">True to modify a tooltip of the <see cref="T:DevExpress.XtraBars.BarItem"/> to which a command is assigned to remove a reference to the shortcut key; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ReplaceService``1(``0)">
      <summary>
        <para>Performs a service substitution.</para>
      </summary>
      <param name="newService">A service of the specified type that will be registered.</param>
      <typeparam name="T">The service type.</typeparam>
      <returns>Previously registered service of the specified type, or null (Nothing in Visual Basic) if the service does not exist.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ResetText">
      <summary>
        <para>Clears the content in the control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.RightToLeft">
      <summary>
        <para>This property is not in effect for the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.RtfText">
      <summary>
        <para>Gets or sets the formatted text content of the control.</para>
      </summary>
      <value>A string, containing the document’s content in rich text format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.RtfTextChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument">
      <summary>
        <para>Saves a document in its original format to its original location.</para>
      </summary>
      <returns>true if a document has been successfully saved; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Saves the document to a byte array in the specified file format.</para>
      </summary>
      <param name="documentFormat">An enumeration member that specifies the output document format.</param>
      <returns>A byte array that contains data in the specified format.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(DevExpress.XtraRichEdit.DocumentFormat,DevExpress.XtraRichEdit.API.Native.EncryptionSettings)">
      <summary>
        <para>Saves the document to a byte array in the specified file format and encrypts it with a password.</para>
      </summary>
      <param name="documentFormat">An enumeration member that specifies the output document format.</param>
      <param name="encryptionSettings">Specifies encryption settings.</param>
      <returns>A byte array that contains encrypted data in the specified format.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Saves the control’s document to a file and specifies the document’s format.</para>
      </summary>
      <param name="stream">The stream to output the document to.</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration values.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat,DevExpress.XtraRichEdit.API.Native.EncryptionSettings)">
      <summary>
        <para>Saves the document to a stream, specifying the document’s format and encryption settings.</para>
      </summary>
      <param name="stream">The stream used to output the document.</param>
      <param name="documentFormat">Indicates the document format.</param>
      <param name="encryptionSettings">Document encryption settings.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
      <summary>
        <para>Saves the control’s document to a file and specifies the document’s format.</para>
      </summary>
      <param name="fileName">A string value specifying the path to a file into which to save the control’s document.</param>
      <param name="documentFormat">One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration values.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat,DevExpress.XtraRichEdit.API.Native.EncryptionSettings)">
      <summary>
        <para>Saves the document to a file, specifying the document’s format and encryption settings.</para>
      </summary>
      <param name="fileName">A file name.</param>
      <param name="documentFormat">Indicates the document format.</param>
      <param name="encryptionSettings">Document encryption settings.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Saves a document in its original format to its original location. If original format and location are not specified, invokes the  Save As dialog that is shown modally as a child of the specified parent window.</para>
      </summary>
      <param name="parent">The <see cref="T:System.Windows.Forms.IWin32Window"/> that represents the parent window.</param>
      <returns>true if a document has been successfully saved; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocumentAs">
      <summary>
        <para>Invokes a Save As dialog and saves a document.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocumentAs(System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Invokes the Save As form which is shown modally as a child of the specified parent window.</para>
      </summary>
      <param name="parent">The <see cref="T:System.Windows.Forms.IWin32Window"/> that represents the parent window.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ScrollToCaret">
      <summary>
        <para>Scrolls the document to the caret position.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ScrollToCaret(System.Single)">
      <summary>
        <para>Scrolls the document so that the caret position is displayed at the specified relative vertical offset.</para>
      </summary>
      <param name="relativeVerticalPosition">A number specifying the relative vertical offset. Must be within the range 0.0 - 1.0</param>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.SearchFormShowing">
      <summary>
        <para>Occurs when a search form is invoked before it is displayed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.SelectAll">
      <summary>
        <para>Selects the entire contents of the RichEditControl.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.SelectionChanged">
      <summary>
        <para>Fires in response to changing a selection in the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ShowCaretInReadOnly">
      <summary>
        <para>Gets or sets whether the caret is shown if a RichEditControl’s content is read-only.</para>
      </summary>
      <value>true if the caret is shown; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ShowNotesFormShowing">
      <summary>
        <para>Occurs before the Show Notes dialog is invoked.’</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowPrintDialog">
      <summary>
        <para>Invokes the Print dialog.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowPrintPreview">
      <summary>
        <para>Invokes the Print Preview window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowReplaceForm">
      <summary>
        <para>Invokes the SearchForm dialog switched to the Replace tab.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowReviewingPaneForm">
      <summary>
        <para>Displays the Reviewing Pane that lists all document comments.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowSearchForm">
      <summary>
        <para>Invokes the “Find and Replace” dialog.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.SpellChecker">
      <summary>
        <para>Gets or sets the component used for spelling check by the RichEdit control.</para>
      </summary>
      <value>A component which provides the DevExpress.XtraSpellChecker.ISpellChecker interface.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.SpellingErrorLimitExceeded">
      <summary>
        <para>Raises when the number of found misspelled words exceeds the error limit.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.SplitTableCellsFormShowing">
      <summary>
        <para>Occurs before the Split Cells dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.StartHeaderFooterEditing">
      <summary>
        <para>Occurs when the end-user starts editing a header or footer.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.SymbolFormShowing">
      <summary>
        <para>Occurs before the Symbol dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.TableOptionsFormShowing">
      <summary>
        <para>Occurs before the Table Options dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.TablePositioningFormShowing">
      <summary>
        <para>Occurs before the Table Positioning dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.TablePropertiesFormShowing">
      <summary>
        <para>Occurs before the Table Properties dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.TableStyleFormShowing">
      <summary>
        <para>Occurs before a Table Style dialog is invoked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.TabsFormShowing">
      <summary>
        <para>Occurs before the Tabs dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Text">
      <summary>
        <para>Gets or sets the control’s content as a plain text.</para>
      </summary>
      <value>A string, containing the document’s unformatted text.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.ToolTipController">
      <summary>
        <para>Gets or sets the tooltip controller component that controls the appearance, position and the content of the hints displayed by the RichEditControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.ToolTipController"/> component which controls the appearance and behavior of the hints displayed by the RichEditControl.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.TrackedMovesConflict">
      <summary>
        <para>Occurs when moved text has been changed since it was moved.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.Undo">
      <summary>
        <para>Cancels changes caused by the last operation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.UnhandledException">
      <summary>
        <para>This event is raised when an unhandled exception of the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> occurs.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Unit">
      <summary>
        <para>Gets or sets a measure unit used within the control.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Office.DocumentUnit"/> enumeration values.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.UnitChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.Unit"/> property is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.UnitChanging">
      <summary>
        <para>Fires before the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.Unit"/> property is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControl.UpdateCommandUI">
      <summary>
        <para>Refreshes the command UI of the RichEditControl.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.UpdateUI">
      <summary>
        <para>Raised when changes occur which may affect the control’s UI.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.UseDeferredDataBindingNotifications">
      <summary>
        <para>Enables you to fire data binding events immediately for several text properties, resolving issues with multiple RichEdit controls bound to the same data source.</para>
      </summary>
      <value>true to process data bindings in a separate thread as it is in the normal control update; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.VerticalScrollPosition">
      <summary>
        <para>Gets or sets the top visible position in the scrolled document.</para>
      </summary>
      <value>An <see cref="T:System.Int64"/> value that is the distance from the top of the scrolled document. Measured in units specified by the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.LayoutUnit"/> property.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.VerticalScrollValue">
      <summary>
        <para>Gets or sets the top visible position in the scrolled document.</para>
      </summary>
      <value>An <see cref="T:System.Int64"/> value that is the distance from the top of the scrolled document in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.Views">
      <summary>
        <para>Contains settings of the Views that are used to display a document in the RichEdit Control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditViewRepository"/> object which stores the settings of the document Views.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.VisiblePagesChanged">
      <summary>
        <para>Fires when RichEditControl starts or finishes displaying a document page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.WatermarkFormShowing">
      <summary>
        <para>Occurs before the Watermark dialog is invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControl.WordMLText">
      <summary>
        <para>Gets or sets the control’s content as the text in WordProcessingML (Microsoft Office Word 2003 XML) format.</para>
      </summary>
      <value>A string of text in WordML format.</value>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.WordMLTextChanged">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraRichEdit.RichEditControl.ZoomChanged">
      <summary>
        <para>Fires when the zoom factor used to display the document is changed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditControlOptions">
      <summary>
        <para>Represents the storage of settings specific for the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditControlOptions.#ctor(DevExpress.XtraRichEdit.Internal.InnerRichEditDocumentServer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditControlOptions"/> class specific for the <see cref="T:DevExpress.XtraRichEdit.RichEditDocumentServer"/>.</para>
      </summary>
      <param name="documentServer">A <see cref="T:DevExpress.XtraRichEdit.RichEditDocumentServer"/> instance.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControlOptions.HorizontalScrollbar">
      <summary>
        <para>Provides access to the options specific to the horizontal scrollbar of the RichEditControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.HorizontalScrollbarOptions"/> object used to specify options for the horizontal scrollbar.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditControlOptions.VerticalScrollbar">
      <summary>
        <para>Provides access to the options specific for the vertical scrollbar of the RichEditControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions"/> object used to specify options for the vertical scrollbar.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.CustomMarkDraw"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs.#ctor(System.Drawing.Graphics,DevExpress.XtraRichEdit.Layout.Export.CustomMarkVisualInfoCollection)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="graphics">A <see cref="T:System.Drawing.Graphics"/> object that provides GDI+ drawing functionality.</param>
      <param name="visualInfoCollection">A DevExpress.XtraRichEdit.Layout.Export.CustomMarkVisualInfoCollection collection containing custom marks visual info.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs.Graphics">
      <summary>
        <para>Gets an object used for painting.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object which provides GDI+ drawing functionality.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs.VisualInfoCollection">
      <summary>
        <para>Provides access to information required to visualize custom marks.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Layout.Export.CustomMarkVisualInfoCollection collection containing custom marks visual info.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.CustomMarkDraw"/> event.</para>
      </summary>
      <param name="sender">The event sender (typically a <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditMenuType">
      <summary>
        <para>Lists menu types respective to visual elements for which a popup menu is invoked.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Comment">
      <summary>
        <para>Specifies a context menu invoked for a comment.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Endnote">
      <summary>
        <para>Specifies a context menu invoked for an endnote.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Field">
      <summary>
        <para>Specifies a context menu invoked for a field.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.FloatingObject">
      <summary>
        <para>Specifies a context menu invoked for a floating object - a picture or text box.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Footer">
      <summary>
        <para>Specifies a context menu invoked for a footer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Footnote">
      <summary>
        <para>Specifies a context menu invoked for a footnote.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Header">
      <summary>
        <para>Specifies a context menu invoked for a header.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Hyperlink">
      <summary>
        <para>Specifies a context menu invoked for a hyperlink.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.InlinePicture">
      <summary>
        <para>Specifies a context menu invoked for an inline picture.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.TableCell">
      <summary>
        <para>Specifies a context menu invoked for a table cell.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.Text">
      <summary>
        <para>Specifies a context menu invoked for a text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditMenuType.TextBox">
      <summary>
        <para>Specifies a context menu invoked for a text box.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditPen">
      <summary>
        <para>Defines an object used to draw lines on the layout canvas.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditPen.#ctor(System.Drawing.Color)">
      <summary>
        <para>Initializes new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditPen"></see> class with the specified settings.</para>
      </summary>
      <param name="color">Represents one of the ARGB color values.</param>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditPen.#ctor(System.Drawing.Color,System.Int32)">
      <summary>
        <para>Initializes new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditPen"></see> class with the specified settings.</para>
      </summary>
      <param name="color">Represents one of the ARGB color values.</param>
      <param name="thickness">An int value that is the line thickness.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditPen.Color">
      <summary>
        <para>Gets or sets the color of a <see cref="T:DevExpress.XtraRichEdit.RichEditPen"/> object.</para>
      </summary>
      <value>One of the ARGB color values indicating the pen color.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditScrollbarVisibility">
      <summary>
        <para>Specifies the visibility of a scroll bar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditScrollbarVisibility.Auto">
      <summary>
        <para>A scroll bar is automatically displayed when required.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditScrollbarVisibility.Hidden">
      <summary>
        <para>A scroll bar is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditScrollbarVisibility.Visible">
      <summary>
        <para>A scroll bar is visible.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditToolbarType">
      <summary>
        <para>Lists toolbars and ribbon tabs that can be created on the RichEditControl at runtime.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.All">
      <summary>
        <para>All ribbon tabs or toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.File">
      <summary>
        <para>File ribbon tab or Common and Info toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.FloatingObject">
      <summary>
        <para>Picture Tools contextual ribbon tab or Shape Styles and Arrange toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.HeaderFooter">
      <summary>
        <para>Header &amp; Footer Tools contextual tab or Navigation, Options and Close toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.Home">
      <summary>
        <para>Home ribbon tab or Clipboard, Font, Paragraph, Styles and Editing toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.Insert">
      <summary>
        <para>Insert ribbon tab or Pages, Tables, Illustrations, Links, Header &amp; Footer, Text and Symbols toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.MailMerge">
      <summary>
        <para>Mail Merge ribbon tab or toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.PageLayout">
      <summary>
        <para>Page Layout ribbon tab or Page Setup and Background toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.References">
      <summary>
        <para>References ribbon tab or Table of Contents and Captions toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.Review">
      <summary>
        <para>Review ribbon tab or Proofing, Protect, Comment and Tracking toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.Table">
      <summary>
        <para>Table Tools contextual tab with the Layout and Design ribbon tabs or Table Style Options, Table Styles, Borders &amp; Shading, Table, Rows &amp; Columns, Merge, Cell Size and Alignment toolbars.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraRichEdit.RichEditToolbarType.View">
      <summary>
        <para>View ribbon tab or Document Views, Show and Zoom toolbars.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditViewCustomDrawEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.CustomDrawActiveView"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.RichEditViewCustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.RichEditViewCustomDrawEventArgs"/> class with the specified graphics cache.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.RichEditViewCustomDrawEventArgs.Cache">
      <summary>
        <para>Gets an object specifying the storage for the most  used pens, fonts and brushes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.RichEditViewCustomDrawEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.CustomDrawActiveView"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.RichEditCustomMarkDrawEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ScrollbarOptions">
      <summary>
        <para>Represents the base class for scrollbar options.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ScrollbarOptions.Visibility">
      <summary>
        <para>Gets or sets a value that specifies the visibility of a scroll bar.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditScrollbarVisibility"/> enumeration member specifying the visibility mode.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.SearchFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.SearchFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.SearchFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.SearchFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.SearchFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.SearchFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.SearchFormShowingEventArgs.ActivePage">
      <summary>
        <para>Obtains what tab of the Find and Replace dialog (Find or Replace) is active.</para>
      </summary>
      <value>DevExpress.XtraRichEdit.Forms.SearchFormActivePage enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.SearchFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Find and Replace dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.SearchFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.SearchFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.SearchFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.SearchFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ShowFormEventArgs">
      <summary>
        <para>Serves as the base for classes which provide data for the events which occur before the RichEditControl dialogs are shown.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.ShowFormEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.ShowFormEventArgs"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ShowFormEventArgs.DialogResult">
      <summary>
        <para>Gets or sets the return value of a dialog box.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value that specifies the value that is returned by the dialog box.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ShowFormEventArgs.Handled">
      <summary>
        <para>Gets or sets whether an event was handled. If it was handled, the default actions are not required.</para>
      </summary>
      <value>true if it was handled and the default dialog doesn’t need to be shown; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraRichEdit.ShowFormEventArgs.Parent">
      <summary>
        <para>Gets or sets a parent of the form being shown.</para>
      </summary>
      <value>An <see cref="T:System.Windows.Forms.IWin32Window"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.ShowNotesFormShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.ShowNotesFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.BookmarkFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.SplitTableCellsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.SplitTableCellsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.SplitTableCellsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.SplitTableCellsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.SplitTableCellsFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.SplitTableCellsFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.SplitTableCellsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Split Cells dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.SplitTableCellsFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.SplitTableCellsFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.SplitTableCellsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.SplitTableCellsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.SymbolFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.SymbolFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.SymbolFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.RichEditInsertSymbolViewModel)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.SymbolFormShowingEventArgs"/> class with the specified view model.</para>
      </summary>
      <param name="viewModel">A DevExpress.XtraRichEdit.Forms.RichEditInsertSymbolViewModel object. This value is assigned to the <see cref="P:DevExpress.XtraRichEdit.SymbolFormShowingEventArgs.ViewModel"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.SymbolFormShowingEventArgs.ViewModel">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.SymbolFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.SymbolFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.SymbolFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TableOptionsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TableOptionsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.TableOptionsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.TableOptionsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.TableOptionsFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.TableOptionsFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.TableOptionsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Table Options dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.TableOptionsFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TableOptionsFormShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TableOptionsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.TableOptionsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TablePropertiesFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TablePropertiesFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.TablePropertiesFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.TablePropertiesFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.TablePropertiesFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.TablePropertiesFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.TablePropertiesFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Table Properties dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.TablePropertiesFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TablePropertiesFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TablePropertiesFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.TablePropertiesFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TableStyleFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TableStyleFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.TableStyleFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.TableStyleFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.TableStyleFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.TableStyleFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.TableStyleFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information required for initializing the Table Style Dialog dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.TableStyleFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TableStyleFormShowingEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TableStyleFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the RichEditControl, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.TableStyleFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TabsFormShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TabsFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.TabsFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.TabsFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.TabsFormShowingEventArgs"/> class with the specified information for initializing the dialog controls.</para>
      </summary>
      <param name="controllerParameters">A DevExpress.XtraRichEdit.Forms.TabsFormControllerParameters object containing information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.TabsFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Gets the information for initializing the Tabs dialog controls.</para>
      </summary>
      <value>A DevExpress.XtraRichEdit.Forms.TabsFormControllerParameters object containing information to be displayed in the form.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.TabsFormShowingEventHandler">
      <summary>
        <para>References a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.TabsFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>, which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraRichEdit.TabsFormShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions">
      <summary>
        <para>Represents the class containing options for the vertical scrollbar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.VerticalScrollbarOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraRichEdit.WatermarkFormShowingEventArgs">
      <summary>
        <para>Contains data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.WatermarkFormShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraRichEdit.WatermarkFormShowingEventArgs.#ctor(DevExpress.XtraRichEdit.Forms.WatermarkFormControllerParameters)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraRichEdit.WatermarkFormShowingEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="controllerParameters">Contains information to be displayed in the form.</param>
    </member>
    <member name="P:DevExpress.XtraRichEdit.WatermarkFormShowingEventArgs.ControllerParameters">
      <summary>
        <para>Returns information used to initialize the Watermark dialog controls.</para>
      </summary>
      <value>An object that contains information to be displayed in the dialog.</value>
    </member>
    <member name="T:DevExpress.XtraRichEdit.WatermarkFormShowingEventHandler">
      <summary>
        <para>A method that handles the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.WatermarkFormShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> that raised the event.</param>
      <param name="e">An object that contains event data.</param>
    </member>
  </members>
</doc>