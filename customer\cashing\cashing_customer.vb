﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class cashing_customer
    Private rng As New Random
    Private Sub new_cash()
        fill_treasury_name()
        fillcom()
        chashingdate.Text = Now.Date
        Amount.Text = 0
        Cashingcode.Text = getlastcode("customer_cashing", "Cashingcode") + 1
        Accounts_name.Text = ""
        Accounts_balace.Text = 0
        balace_now.Text = 0
        cash_type.Text = ""
        cash_pay.SelectedIndex = 0
        type_pay.SelectedIndex = 0
        check_number.Text = ""
        check_date.Value = Now.Date
        cuscity.Text = ""
        cusGovernorate.Text = ""
        Cuscusgroup.Text = ""
        type_type.Text = ""
        Accounts_name.Focus()
    
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
        If XtraForm1.type_pay.Text <> "type_pay" Then
            type_pay.Text = XtraForm1.type_pay.Text
        End If
    End Sub
    Sub fill_treasury_name()

        type_pay.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from treasury_name where cash='true' and Treasury_active='true'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            type_pay.Properties.Items.Add(dt.Rows(i).Item("treasury_name"))
        Next
    End Sub
    Function gettreasury(subname) As String

        Dim sql = "select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("balace") Else Return ""
    End Function
    Private Sub fillcom()
        Accounts_name.Properties.DataSource = Nothing
        Accounts_name.Properties.DisplayMember = Nothing
        Accounts_name.Properties.ValueMember = Nothing
        Dim adp As New SqlDataAdapter("select * from customer where cus_active = 'true' order by cusname ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Accounts_name.Properties.DataSource = dt
        Accounts_name.Properties.DisplayMember = "cusname"
        Accounts_name.Properties.ValueMember = "Cuscode"
    End Sub

    Private Sub cashing_customer_Load_1(sender As Object, e As EventArgs) Handles MyBase.Load
      
        new_cash()
        Accounts_name.Focus()

    End Sub

    Public Sub show_data(x)
        Dim sql = "select * from customer_cashing where Cashingcode=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("لم يتم العثور علي بيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim dr = dt.Rows(0)
            Cashingcode.Text = dr!Cashingcode
            chashingdate.Text = dr!chashingdate
            Accounts_name.Text = dr!Accounts_name
            Amount.Text = dr!Amount
            cash_type.Text = dr!cash_type
            cash_pay.Text = dr!cash_pay
            type_pay.Text = dr!type_pay
            check_number.Text = dr!check_number
            check_date.Text = dr!check_date
            cuscity.Text = dr!cuscity
            cusGovernorate.Text = dr!cusGovernorate
            Cuscusgroup.Text = dr!Cuscusgroup
            type_type.Text = dr!type_type
        End If
    End Sub
    Function getbalace(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Accounts_balace") Else Return ""
    End Function
    Function getCuscusgroup(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Cuscusgroup") Else Return ""
    End Function
    Function getcusGovernorate(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("cusGovernorate") Else Return ""
    End Function
    Function getcuscity(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("cuscity") Else Return ""
    End Function
    Private Sub cash_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cash_pay.SelectedIndexChanged
        If cash_pay.SelectedIndex = 0 Then
            fill_treasury_name()
            type_pay.SelectedIndex = 0

        End If
        If cash_pay.SelectedIndex = 0 Then
            Label17.Visible = False
            check_number.Visible = False
            Label18.Visible = False
            check_date.Visible = False
        End If
        If cash_pay.SelectedIndex = 1 Then
            Label17.Visible = True
            check_number.Visible = True
            Label18.Visible = True
            check_date.Visible = True
        End If
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
    End Sub
    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If Accounts_name.Text = "" Then
            XtraMessageBox.Show("اسم العميل فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If Amount.Text = 0 Then
            XtraMessageBox.Show("المبلغ = 0", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If Amount.Text = "" Then
            XtraMessageBox.Show("المبلغ فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Try
            cash_pay_SelectedIndexChanged(Nothing, Nothing)
            Cashingcode.Text = getlastcode("customer_cashing", "Cashingcode") + 1
            printcode.Text = getlastcode("customer_cashing", "Cashingcode") + 1
            Dim sql = "select * from customer_cashing where Cashingcode=N'" & (Cashingcode.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Cashingcode.Text = getlastcode("customer_cashing", "Cashingcode") + 1
                Exit Sub
            Else
                Dim dr = dt.NewRow
                '========= بيانات اساسية============
                dr!Cashingcode = Cashingcode.Text
                dr!chashingdate = chashingdate.Value
                dr!Accounts_name = Accounts_name.Text
                dr!Amount = Amount.Text
                dr!amount_string = amount_string.Text
                dr!cash_type = cash_type.Text
                dr!cash_pay = cash_pay.Text
                dr!type_pay = type_pay.Text
                dr!check_number = check_number.Text
                dr!check_date = check_date.Text
                dr!cuscity = cuscity.Text
                dr!cusGovernorate = cusGovernorate.Text
                dr!Cuscusgroup = Cuscusgroup.Text
                dr!type_type = type_type.Text
                dr!code_print = 1
                dr!push = False
                If cash_pay.SelectedIndex = 0 Then
                    dr!cash_check = False
                ElseIf cash_pay.SelectedIndex = 1 Then
                    dr!cash_check = True
                End If

                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                If cash_pay.SelectedIndex = 0 Then
                    '========= الخزينة============
                    adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & (type_pay.Text) & "'", sqlconn)
                    ds = New DataSet
                    adp.Fill(ds)
                    dt = ds.Tables(0)
                    Dim dr6 = dt.Rows(0)
                   
                        dr6!balace = Val(dr6!balace) + Val(Amount.Text)


                    Dim cmd6 As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
                '========= الرصيد============
                adp.Dispose()
                ds.Dispose()
                dt.Dispose()
                adp = New SqlDataAdapter("select * from customer where cusname=N'" & (Accounts_name.Text) & "'", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                Dim dr5 = dt.Rows(0)
                dr5!Accounts_balace = Val(dr5!Accounts_balace) - Val(Amount.Text)
                Dim cmd5 As New SqlCommandBuilder(adp)
                adp.Update(dt)
               

                set_customertrans(Accounts_name.Text, "تحصيل فواتبر", Cashingcode.Text, chashingdate.Value, "", cash_type.Text, 0, Amount.Text, Val(Accounts_balace.Text) - Val(Amount.Text), Amount.Text)
                If cash_pay.SelectedIndex = 0 Then
                    treasury_pay(type_pay.Text, "تحصيل فواتبر", chashingdate.Value, Now.ToString("hh:mm:ss:tt"), "استلام نقدية من " & Accounts_name.Text, XtraForm1.user_name.Text, Val(Amount.Text), 0, Val(treasury_balace.Text) + Val(Amount.Text), Cashingcode.Text)
                End If

                My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.Background)
                new_cash()
            End If
        Catch ex As Exception
            MsgBox("فشل اعد المحاوله")
        End Try

    End Sub
   

    Private Sub cash_delegate_SelectedIndexChanged(sender As Object, e As EventArgs)
        Accounts_name.Properties.ImmediatePopup = True
    End Sub

    Private Sub type_pay_SelectedIndexChanged(sender As Object, e As EventArgs) Handles type_pay.SelectedIndexChanged

        Accounts_name.Properties.ImmediatePopup = True
        If cash_pay.SelectedIndex = 0 Then
            Try
                Dim sql = "select (balace),(treasury_name) from treasury_name where treasury_name=N'" & (type_pay.Text) & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                Dim dr = dt.Rows(0)
                treasury_balace.Text = Val(dr!balace)
            Catch ex As Exception

            End Try
           

        End If
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If


    End Sub
    Sub refresh_cash()
        Try
            Accounts_name.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(Accounts_name.Text)
            Cuscusgroup.Text = getCuscusgroup(Accounts_name.Text)
            cusGovernorate.Text = getcusGovernorate(Accounts_name.Text)
            cuscity.Text = getcuscity(Accounts_name.Text)
            If Accounts_name.Text.Trim() <> Nothing Then

                Accounts_balace.Visible = True
            Else

                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_EditValueChanged(Nothing, Nothing)
        Catch ex As Exception

        End Try
    End Sub
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub cashing_customer_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.Enter Then
            save_print_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub save_print_Click(sender As Object, e As EventArgs) Handles save_print.Click
        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from cash_cus_print where Cashingcode=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet

        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "cashing_cus_a4.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub Amount_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Double.TryParse((Amount.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub check_number_TextChanged(sender As Object, e As EventArgs) Handles check_number.TextChanged
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
    End Sub

    Private Sub check_date_ValueChanged(sender As Object, e As EventArgs) Handles check_date.ValueChanged
        If cash_pay.SelectedIndex = 0 Then
            type_type.Text = "نقدا"
        End If
        If cash_pay.SelectedIndex = 1 Then
            type_type.Text = " بشيك" & " " & "علي بنك" & " " & type_pay.Text & " رقم الشيك" & " " & check_number.Text & " " & "بتاريخ استحقاق" & " " & check_date.Text
        End If
    End Sub

    Private Sub جديدToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles جديدToolStripMenuItem.Click
        new_cash()
    End Sub

    Private Sub حفظToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظToolStripMenuItem.Click
        save_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub حفظطباعةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظطباعةToolStripMenuItem.Click
        save_print_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        Me.Dispose()
    End Sub
   

    Private Sub Amount_EditValueChanged(sender As Object, e As EventArgs) Handles Amount.EditValueChanged
        Try
            balace_now.Text = Format(Val(Accounts_balace.Text) - Val(Amount.Text), "0.00")
            If Amount.Text.Trim() <> Nothing Then
                balace_now.Visible = True
            Else

                balace_now.Visible = False
            End If
            amount_string.Text = NoToTxt(Amount.Text, "جنية", "قرش") & " " & "فقط لاغير"

            Label5.Text = Val(Amount.Text)
        Catch ex As Exception

        End Try
       
    End Sub

    Private Sub Accounts_name_EditValueChanged(sender As Object, e As EventArgs) Handles Accounts_name.EditValueChanged
        Try
            Accounts_balace.Text = 0
            balace_now.Text = 0
            Accounts_name.Properties.ImmediatePopup = True
            Accounts_balace.Text = getbalace(Accounts_name.Text)
            Cuscusgroup.Text = getCuscusgroup(Accounts_name.Text)
            cusGovernorate.Text = getcusGovernorate(Accounts_name.Text)
            cuscity.Text = getcuscity(Accounts_name.Text)
            If Accounts_name.Text.Trim() <> Nothing Then

                Accounts_balace.Visible = True
            Else

                Accounts_balace.Visible = False
            End If
            If Accounts_balace.Text.Trim() < 0 Then
                Accounts_balace.BackColor = Color.Red
            End If
            If Accounts_balace.Text.Trim() >= 0 Then
                Accounts_balace.BackColor = Color.White
            End If
            Amount_EditValueChanged(Nothing, Nothing)
           

        Catch ex As Exception

        End Try
        Dim str = "select sum(custrans_Debtor), sum(custrans_Creditor) from customer_trans where customer_name=N'" & Accounts_name.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("customer_trans")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        Dim sumcredit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        If IsDBNull(dta.Rows(0).Item(1)) = False Then sumcredit = dta.Rows(0).Item(1)
        Accounts_balace.Text = Format(Val(sumdebit), "#,0.00") - Format(Val(sumcredit), "#,0.00")

    End Sub

    Private Sub dgv_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs)
        If e.ColumnIndex = 0 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.check_true
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub
    
End Class