﻿Imports System.Data.SqlClient

Public Class Power_user

    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        openconnection()
        fill_user()
    End Sub
    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from power_user order by power_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("power_name"))
        Next
    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from power_user where power_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("power_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        power_name.Text = ""
        Dim sql = "select * from power_user where power_id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            power_name.Text = dr!power_name

            m1.Checked = dr!m1
            m2.Checked = dr!m2
            m3.Checked = dr!m3
            m4.Checked = dr!m4
            m5.Checked = dr!m5
            m6.Checked = dr!m6
            m7.Checked = dr!m7
            m8.Checked = dr!m8
            m9.Checked = dr!m9

            m10.Checked = dr!m10
            m11.Checked = dr!m11
            m12.Checked = dr!m12
            m13.Checked = dr!m13
            m14.Checked = dr!m14
            m15.Checked = dr!m15
            m16.Checked = dr!m16
            m17.Checked = dr!m17
            m18.Checked = dr!m18
            m19.Checked = dr!m19

            m20.Checked = dr!m20
            m21.Checked = dr!m21
            m22.Checked = dr!m22
            m23.Checked = dr!m23
            m24.Checked = dr!m24
            m25.Checked = dr!m25
            m26.Checked = dr!m26
            m27.Checked = dr!m27
            m28.Checked = dr!m28
            m29.Checked = dr!m29

            m30.Checked = dr!m30
            m31.Checked = dr!m31
            m32.Checked = dr!m32
            m33.Checked = dr!m33
            m34.Checked = dr!m34
            m35.Checked = dr!m35
            m36.Checked = dr!m36
            m37.Checked = dr!m37
            m38.Checked = dr!m38
            m39.Checked = dr!m39

            m40.Checked = dr!m40
            m41.Checked = dr!m41
            m42.Checked = dr!m42
            m43.Checked = dr!m43
            m44.Checked = dr!m44
            m45.Checked = dr!m45
            m46.Checked = dr!m46
            m47.Checked = dr!m47
            m48.Checked = dr!m48
            m49.Checked = dr!m49

            m50.Checked = dr!m50
            m51.Checked = dr!m51
            m52.Checked = dr!m52
            m53.Checked = dr!m53
            m54.Checked = dr!m54
            m55.Checked = dr!m55
            m56.Checked = dr!m56
            m57.Checked = dr!m57
            m58.Checked = dr!m58
            m59.Checked = dr!m59

            m60.Checked = dr!m60
            m61.Checked = dr!m61
            m62.Checked = dr!m62
            m63.Checked = dr!m63
            m64.Checked = dr!m64
            m65.Checked = dr!m65
            m66.Checked = dr!m66
            m67.Checked = dr!m67
            m68.Checked = dr!m68
            m69.Checked = dr!m69

            m70.Checked = dr!m70
            m71.Checked = dr!m71
            m72.Checked = dr!m72
            m73.Checked = dr!m73
            m74.Checked = dr!m74
            m75.Checked = dr!m75
            m76.Checked = dr!m76
            m77.Checked = dr!m77
            m78.Checked = dr!m78
            m79.Checked = dr!m79

            m80.Checked = dr!m80
            m81.Checked = dr!m81
            m82.Checked = dr!m82
            m83.Checked = dr!m83
            m84.Checked = dr!m84
            m85.Checked = dr!m85
            m86.Checked = dr!m86
            m87.Checked = dr!m87
            m88.Checked = dr!m88
            m89.Checked = dr!m89



            w1.Checked = dr!w1
            w2.Checked = dr!w2
            w3.Checked = dr!w3
            w4.Checked = dr!w4
            w5.Checked = dr!w5
            w6.Checked = dr!w6
            w7.Checked = dr!w7
            w8.Checked = dr!w8
            w9.Checked = dr!w9
            w10.Checked = dr!w10

            w11.Checked = dr!w11
            w12.Checked = dr!w12
            w13.Checked = dr!w13
            w14.Checked = dr!w14
            w15.Checked = dr!w15
            w16.Checked = dr!w16
            w17.Checked = dr!w17
            w18.Checked = dr!w18
            w19.Checked = dr!w19
            w20.Checked = dr!w20

            w21.Checked = dr!w21
            w22.Checked = dr!w22
            w23.Checked = dr!w23
            w24.Checked = dr!w24
            w25.Checked = dr!w25
            w26.Checked = dr!w26
            w27.Checked = dr!w27
            w28.Checked = dr!w28
            w29.Checked = dr!w29
            w30.Checked = dr!w30

            w31.Checked = dr!w31
            w32.Checked = dr!w32
            w33.Checked = dr!w33
            w34.Checked = dr!w34
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub
    Private Sub exit_button_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        power_name.Text = ""
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        CheckBox1.Checked = False
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If power_name.Text = "" Then
            MsgBox("أدخل اسم الصلاحية")
            power_name.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from power_user where power_name=N'" & (power_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم الصلاحية موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!power_name = power_name.Text
                dr!m1 = m1.Checked
                dr!m2 = m2.Checked
                dr!m3 = m3.Checked
                dr!m4 = m4.Checked
                dr!m5 = m5.Checked
                dr!m6 = m6.Checked
                dr!m7 = m7.Checked
                dr!m8 = m8.Checked
                dr!m9 = m9.Checked

                dr!m10 = m10.Checked
                dr!m11 = m11.Checked
                dr!m12 = m12.Checked
                dr!m13 = m13.Checked
                dr!m14 = m14.Checked
                dr!m15 = m15.Checked
                dr!m16 = m16.Checked
                dr!m17 = m17.Checked
                dr!m18 = m18.Checked
                dr!m19 = m19.Checked

                dr!m20 = m20.Checked
                dr!m21 = m21.Checked
                dr!m22 = m22.Checked
                dr!m23 = m23.Checked
                dr!m24 = m24.Checked
                dr!m25 = m25.Checked
                dr!m26 = m26.Checked
                dr!m27 = m27.Checked
                dr!m28 = m28.Checked
                dr!m29 = m29.Checked

                dr!m30 = m30.Checked
                dr!m31 = m31.Checked
                dr!m32 = m32.Checked
                dr!m33 = m33.Checked
                dr!m34 = m34.Checked
                dr!m35 = m35.Checked
                dr!m36 = m36.Checked
                dr!m37 = m37.Checked
                dr!m38 = m38.Checked
                dr!m39 = m39.Checked

                dr!m40 = m40.Checked
                dr!m41 = m41.Checked
                dr!m42 = m42.Checked
                dr!m43 = m43.Checked
                dr!m44 = m44.Checked
                dr!m45 = m45.Checked
                dr!m46 = m46.Checked
                dr!m47 = m47.Checked
                dr!m48 = m48.Checked
                dr!m49 = m49.Checked

                dr!m50 = m50.Checked
                dr!m51 = m51.Checked
                dr!m52 = m52.Checked
                dr!m53 = m53.Checked
                dr!m54 = m54.Checked
                dr!m55 = m55.Checked
                dr!m56 = m56.Checked
                dr!m57 = m57.Checked
                dr!m58 = m58.Checked
                dr!m59 = m59.Checked

                dr!m60 = m60.Checked
                dr!m61 = m61.Checked
                dr!m62 = m62.Checked
                dr!m63 = m63.Checked
                dr!m64 = m64.Checked
                dr!m65 = m65.Checked
                dr!m66 = m66.Checked
                dr!m67 = m67.Checked
                dr!m68 = m68.Checked
                dr!m69 = m69.Checked

                dr!m70 = m70.Checked
                dr!m71 = m71.Checked
                dr!m72 = m72.Checked
                dr!m73 = m73.Checked
                dr!m74 = m74.Checked
                dr!m75 = m75.Checked
                dr!m76 = m76.Checked
                dr!m77 = m77.Checked
                dr!m78 = m78.Checked
                dr!m79 = m79.Checked
                dr!m80 = m80.Checked
                dr!m81 = m81.Checked
                dr!m82 = m82.Checked
                dr!m83 = m83.Checked
                dr!m84 = m84.Checked
                dr!m85 = m85.Checked
                dr!m86 = m86.Checked
                dr!m87 = m87.Checked
                dr!m88 = m88.Checked
                dr!m89 = m89.Checked
                dr!w1 = w1.Checked
                dr!w2 = w2.Checked
                dr!w3 = w3.Checked
                dr!w4 = w4.Checked
                dr!w5 = w5.Checked
                dr!w6 = w6.Checked
                dr!w7 = w7.Checked
                dr!w8 = w8.Checked
                dr!w9 = w9.Checked
                dr!w10 = w10.Checked
                dr!w11 = w11.Checked
                dr!w12 = w12.Checked
                dr!w13 = w13.Checked
                dr!w14 = w14.Checked
                dr!w15 = w15.Checked
                dr!w16 = w16.Checked
                dr!w17 = w17.Checked
                dr!w18 = w18.Checked
                dr!w19 = w19.Checked
                dr!w20 = w20.Checked
                dr!w21 = w21.Checked
                dr!w22 = w22.Checked
                dr!w23 = w23.Checked
                dr!w24 = w24.Checked
                dr!w25 = w25.Checked
                dr!w26 = w26.Checked
                dr!w27 = w27.Checked
                dr!w28 = w28.Checked
                dr!w29 = w29.Checked
                dr!w30 = w30.Checked
                dr!w31 = w31.Checked
                dr!w32 = w32.Checked
                dr!w33 = w33.Checked
                dr!w34 = w34.Checked
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة الصلاحية")
                new_btn_Click(Nothing, Nothing)
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ الصلاحية اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from power_user where power_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!power_name = power_name.Text
                dr!m1 = m1.Checked
                dr!m2 = m2.Checked
                dr!m3 = m3.Checked
                dr!m4 = m4.Checked
                dr!m5 = m5.Checked
                dr!m6 = m6.Checked
                dr!m7 = m7.Checked
                dr!m8 = m8.Checked
                dr!m9 = m9.Checked

                dr!m10 = m10.Checked
                dr!m11 = m11.Checked
                dr!m12 = m12.Checked
                dr!m13 = m13.Checked
                dr!m14 = m14.Checked
                dr!m15 = m15.Checked
                dr!m16 = m16.Checked
                dr!m17 = m17.Checked
                dr!m18 = m18.Checked
                dr!m19 = m19.Checked

                dr!m20 = m20.Checked
                dr!m21 = m21.Checked
                dr!m22 = m22.Checked
                dr!m23 = m23.Checked
                dr!m24 = m24.Checked
                dr!m25 = m25.Checked
                dr!m26 = m26.Checked
                dr!m27 = m27.Checked
                dr!m28 = m28.Checked
                dr!m29 = m29.Checked

                dr!m30 = m30.Checked
                dr!m31 = m31.Checked
                dr!m32 = m32.Checked
                dr!m33 = m33.Checked
                dr!m34 = m34.Checked
                dr!m35 = m35.Checked
                dr!m36 = m36.Checked
                dr!m37 = m37.Checked
                dr!m38 = m38.Checked
                dr!m39 = m39.Checked

                dr!m40 = m40.Checked
                dr!m41 = m41.Checked
                dr!m42 = m42.Checked
                dr!m43 = m43.Checked
                dr!m44 = m44.Checked
                dr!m45 = m45.Checked
                dr!m46 = m46.Checked
                dr!m47 = m47.Checked
                dr!m48 = m48.Checked
                dr!m49 = m49.Checked

                dr!m50 = m50.Checked
                dr!m51 = m51.Checked
                dr!m52 = m52.Checked
                dr!m53 = m53.Checked
                dr!m54 = m54.Checked
                dr!m55 = m55.Checked
                dr!m56 = m56.Checked
                dr!m57 = m57.Checked
                dr!m58 = m58.Checked
                dr!m59 = m59.Checked

                dr!m60 = m60.Checked
                dr!m61 = m61.Checked
                dr!m62 = m62.Checked
                dr!m63 = m63.Checked
                dr!m64 = m64.Checked
                dr!m65 = m65.Checked
                dr!m66 = m66.Checked
                dr!m67 = m67.Checked
                dr!m68 = m68.Checked
                dr!m69 = m69.Checked

                dr!m70 = m70.Checked
                dr!m71 = m71.Checked
                dr!m72 = m72.Checked
                dr!m73 = m73.Checked
                dr!m74 = m74.Checked
                dr!m75 = m75.Checked
                dr!m76 = m76.Checked
                dr!m77 = m77.Checked
                dr!m78 = m78.Checked
                dr!m79 = m79.Checked

                dr!m80 = m80.Checked
                dr!m81 = m81.Checked
                dr!m82 = m82.Checked
                dr!m83 = m83.Checked
                dr!m84 = m84.Checked
                dr!m85 = m85.Checked
                dr!m86 = m86.Checked
                dr!m87 = m87.Checked
                dr!m88 = m88.Checked
                dr!m89 = m89.Checked
                dr!w1 = w1.Checked
                dr!w2 = w2.Checked
                dr!w3 = w3.Checked
                dr!w4 = w4.Checked
                dr!w5 = w5.Checked
                dr!w6 = w6.Checked
                dr!w7 = w7.Checked
                dr!w8 = w8.Checked
                dr!w9 = w9.Checked
                dr!w10 = w10.Checked
                dr!w11 = w11.Checked
                dr!w12 = w12.Checked
                dr!w13 = w13.Checked
                dr!w14 = w14.Checked
                dr!w15 = w15.Checked
                dr!w16 = w16.Checked
                dr!w17 = w17.Checked
                dr!w18 = w18.Checked
                dr!w19 = w19.Checked
                dr!w20 = w20.Checked
                dr!w21 = w21.Checked
                dr!w22 = w22.Checked
                dr!w23 = w23.Checked
                dr!w24 = w24.Checked
                dr!w25 = w25.Checked
                dr!w26 = w26.Checked
                dr!w27 = w27.Checked
                dr!w28 = w28.Checked
                dr!w29 = w29.Checked
                dr!w30 = w30.Checked
                dr!w31 = w31.Checked
                dr!w32 = w32.Checked
                dr!w33 = w33.Checked
                dr!w34 = w34.Checked
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل الصلاحية", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل الصلاحية اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from power_user where power_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الصلاحية")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف الصلاحية", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الصلاحية اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub

    Private Sub CheckBox1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox1.CheckedChanged
        check1()
        check2()
        check3()
        check4()
        check5()
        check6()
        check7()
        check8()
        check9()
        check10()
        check11()
        check12()
        check13()
        check14()

    End Sub
    Sub check1()
        For Each c As Control In Me.GroupBox1.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check2()
        For Each c As Control In Me.GroupBox2.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check3()
        For Each c As Control In Me.GroupBox3.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
        Sub check4()
        For Each c As Control In Me.GroupBox4.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check5()
        For Each c As Control In Me.GroupBox5.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check6()
        For Each c As Control In Me.GroupBox6.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check7()
        For Each c As Control In Me.GroupBox7.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check8()
        For Each c As Control In Me.GroupBox8.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check9()
        For Each c As Control In Me.GroupBox9.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
          Sub check10()
        For Each c As Control In Me.GroupBox10.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check11()
        For Each c As Control In Me.GroupBox11.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check12()
        For Each c As Control In Me.GroupBox12.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check13()
        For Each c As Control In Me.GroupBox13.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
    Sub check14()
        For Each c As Control In Me.GroupBox14.Controls
            If CheckBox1.Checked = True Then
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = True
                End If
            Else
                If TypeOf c Is CheckBox Then
                    DirectCast(c, CheckBox).Checked = False
                End If
            End If
        Next
    End Sub
End Class