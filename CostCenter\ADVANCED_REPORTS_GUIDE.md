# 📊 دليل التقارير المتقدمة لمراكز التكلفة

## 🎯 نظرة عامة

تم إنشاء مجموعة شاملة من التقارير المتقدمة لإدارة وتحليل مراكز التكلفة، تشمل 4 تقارير رئيسية مع واجهات مستخدم متطورة ورسوم بيانية تفاعلية.

## 📋 التقارير المتاحة

### 1. 📊 تقرير تحليل التكاليف حسب مركز التكلفة

**الهدف**: يوضح إجمالي المصروفات على كل مركز تكلفة ويُستخدم لمقارنة الأداء بين المراكز.

**البيانات المعروضة**:
- مركز التكلفة
- عدد فواتير المبيعات
- إجمالي المبيعات
- عدد المصروفات
- إجمالي المصروفات
- صافي النتيجة
- نسبة العائد %

**المميزات**:
- ✅ رسم بياني عمودي لإجمالي المصروفات
- ✅ تنسيق الأرقام بالفواصل العشرية
- ✅ إجماليات في أسفل الجدول
- ✅ ترتيب حسب إجمالي المصروفات

### 2. 📈 تقرير مقارنة موازنة مركز تكلفة مقابل الفعلي

**الهدف**: يعرض الفرق بين ما تم تخصيصه كموازنة وما تم صرفه فعلياً.

**البيانات المعروضة**:
- مركز التكلفة
- الموازنة المخططة
- المصروفات الفعلية
- الفرق (موازنة - فعلي)
- نسبة الاستخدام %
- الحالة (تجاوز/مطابق/أقل من الموازنة)

**المميزات**:
- ✅ رسم بياني مقارن (موازنة مقابل فعلي)
- ✅ تلوين الخلايا حسب الحالة:
  - 🔴 أحمر: تجاوز الموازنة
  - 🟡 أصفر: مطابق للموازنة
  - 🟢 أخضر: أقل من الموازنة
- ✅ إنشاء جدول الموازنة تلقائياً إذا لم يكن موجوداً
- ✅ بيانات تجريبية للموازنة (50,000 لكل مركز)

### 3. 📉 تقرير أرباح وخسائر على مستوى مركز تكلفة

**الهدف**: عرض أرباح/خسائر لكل مركز (مفيد للمشاريع والمقاولات).

**البيانات المعروضة**:
- مركز التكلفة
- إجمالي الإيرادات
- إجمالي الأرباح الإجمالية
- إجمالي المصروفات
- صافي الربح
- هامش الربح الصافي %
- النتيجة (ربح/تعادل/خسارة)

**المميزات**:
- ✅ رسم بياني مختلط (أعمدة للإيرادات + خط لصافي الربح)
- ✅ تلوين الخلايا حسب النتيجة:
  - 🟢 أخضر: ربح
  - 🟡 أصفر: تعادل
  - 🔴 أحمر: خسارة
- ✅ حساب هامش الربح الصافي
- ✅ ترتيب حسب صافي الربح

### 4. 📋 تقرير توزيع المصروفات حسب مراكز التكلفة

**الهدف**: يوضح كيف تم توزيع المصروفات على مراكز مختلفة.

**البيانات المعروضة**:
- نوع المصروف
- مركز التكلفة
- عدد العمليات
- إجمالي المبلغ
- متوسط المبلغ
- النسبة من الإجمالي %

**المميزات**:
- ✅ رسم بياني دائري لتوزيع المصروفات
- ✅ حساب النسب المئوية من الإجمالي
- ✅ متوسط المبلغ لكل عملية
- ✅ ترتيب حسب إجمالي المبلغ

## 🎛️ عناصر التحكم

### فلاتر التقرير:
- **من تاريخ**: تاريخ البداية للفترة المطلوبة
- **إلى تاريخ**: تاريخ النهاية للفترة المطلوبة
- **مركز التكلفة**: اختيار مركز محدد أو "جميع مراكز التكلفة"

### أزرار التحكم:
- **تحديث**: إعادة تحميل البيانات
- **تصدير**: تصدير إلى Excel أو PDF
- **طباعة**: معاينة وطباعة التقرير
- **إغلاق**: إغلاق النافذة

## 🗄️ متطلبات قاعدة البيانات

### الجداول المطلوبة:
1. **cost_centers**: جدول مراكز التكلفة (موجود)
2. **invoice_add**: جدول فواتير المبيعات (موجود)
3. **Expenses_add**: جدول المصروفات (موجود)
4. **cost_center_budget**: جدول الموازنة (ينشأ تلقائياً)

### هيكل جدول الموازنة:
```sql
CREATE TABLE cost_center_budget (
    budget_id INT IDENTITY(1,1) PRIMARY KEY,
    cost_center_id INT NOT NULL,
    budget_year INT NOT NULL,
    budget_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
    created_date DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (cost_center_id) REFERENCES cost_centers(cost_center_id)
);
```

## 🚀 كيفية الاستخدام

### 1. فتح التقارير:
```vb
' من الكود
main_page.OpenAdvancedCostCenterReports()

' أو إنشاء مباشر
Dim f As New AdvancedCostCenterReports()
f.Show()
```

### 2. تخصيص الفترة:
- اختر التاريخ من والى
- اختر مركز التكلفة المطلوب أو "الكل"
- اضغط "تحديث"

### 3. تصدير التقارير:
- اختر التبويب المطلوب
- اضغط "تصدير"
- اختر نوع الملف (Excel/PDF)
- حدد مكان الحفظ

## 📈 الرسوم البيانية

### أنواع الرسوم المستخدمة:
1. **رسم عمودي**: لتحليل التكاليف
2. **رسم مقارن**: لمقارنة الموازنة مقابل الفعلي
3. **رسم مختلط**: للأرباح والخسائر (أعمدة + خط)
4. **رسم دائري**: لتوزيع المصروفات

### مميزات الرسوم:
- ✅ تفاعلية مع البيانات
- ✅ عناوين واضحة
- ✅ ألوان مميزة
- ✅ تحديث تلقائي مع البيانات

## 🎨 التنسيق والألوان

### ألوان الحالات:
- 🟢 **أخضر فاتح**: حالات إيجابية (ربح، أقل من الموازنة)
- 🟡 **أصفر فاتح**: حالات متوسطة (تعادل، مطابق للموازنة)
- 🔴 **أحمر فاتح**: حالات سلبية (خسارة، تجاوز الموازنة)

### تنسيق الأرقام:
- **العملة**: تنسيق بفواصل عشرية (N2)
- **النسب**: تنسيق بخانة عشرية واحدة (N1)
- **الأعداد**: تنسيق عادي

## 🔧 إعدادات متقدمة

### تخصيص الموازنة:
```sql
-- تحديث موازنة مركز تكلفة معين
UPDATE cost_center_budget 
SET budget_amount = 75000 
WHERE cost_center_id = 1 AND budget_year = 2024;

-- إضافة موازنة جديدة
INSERT INTO cost_center_budget (cost_center_id, budget_year, budget_amount)
VALUES (1, 2024, 100000);
```

### إضافة مراكز تكلفة للفواتير:
```sql
-- تحديث فواتير المبيعات
ALTER TABLE invoice_add ADD cost_center_id INT;
ALTER TABLE invoice_add ADD FOREIGN KEY (cost_center_id) REFERENCES cost_centers(cost_center_id);

-- تحديث فواتير المصروفات
ALTER TABLE Expenses_add ADD cost_center_id INT;
ALTER TABLE Expenses_add ADD FOREIGN KEY (cost_center_id) REFERENCES cost_centers(cost_center_id);
```

## 📊 أمثلة على الاستخدام

### مثال 1: تحليل أداء الفروع
- إنشاء مراكز تكلفة للفروع
- ربط المبيعات والمصروفات بكل فرع
- مقارنة الأداء بين الفروع

### مثال 2: إدارة المشاريع
- إنشاء مركز تكلفة لكل مشروع
- تتبع تكاليف وإيرادات كل مشروع
- حساب ربحية المشاريع

### مثال 3: مراقبة الأقسام
- إنشاء مراكز تكلفة للأقسام
- وضع موازنات لكل قسم
- مراقبة الالتزام بالموازنة

## 🎯 الفوائد المحققة

1. **رؤية شاملة**: للتكاليف والأرباح
2. **مراقبة الموازنة**: تجنب تجاوز المخصصات
3. **تحليل الأداء**: مقارنة بين المراكز
4. **اتخاذ قرارات**: بناءً على بيانات دقيقة
5. **تحسين الكفاءة**: تحديد المراكز الأكثر ربحية

---

**ملاحظة**: هذه التقارير تتطلب وجود بيانات في جداول مراكز التكلفة والفواتير والمصروفات للحصول على نتائج مفيدة.
