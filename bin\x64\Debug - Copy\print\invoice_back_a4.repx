﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.invoice_back_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="invoice_back_a4" Margins="0, 0, 0, 16" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="invoice_back_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="15.625" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="104.875">
      <Controls>
        <Item1 Ref="6" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="128.7499,104.875" LocationFloat="11.89328, 0">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="8" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="526.5311, 41.91666" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="9" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="10" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="11" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="526.5311, 10.58332" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="12" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="14" ControlType="PageHeaderBand" Name="PageHeader" HeightF="314.0834">
      <Controls>
        <Item1 Ref="15" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="796.8853,33.33334" LocationFloat="0, 280.75" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="16" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="17" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.6964836904534818" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="18" UsePadding="false" />
                </Item1>
                <Item2 Ref="19" ControlType="XRTableCell" Name="XrTableCell15" Weight="0.5983634254419532" Text="الخصم" Padding="2,2,0,0,100">
                  <StylePriority Ref="20" UsePadding="false" />
                </Item2>
                <Item3 Ref="21" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.58995300925039273" Text="السعر" Padding="2,2,0,0,100" />
                <Item4 Ref="22" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.542263146654033" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="23" UsePadding="false" />
                </Item4>
                <Item5 Ref="24" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.64086488759827032" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item6 Ref="25" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.165779289787741" Text="اسم الصنف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="26" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="27" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="634.4792,31.24998" LocationFloat="30.11438, 234.9167" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="28" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="30" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.5937, 234.9167" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="32" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.594, 203.6667" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="33" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="34" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="634.4794,31.24998" LocationFloat="30.11438, 203.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="35" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="36" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="37" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="634.4794,31.24998" LocationFloat="30.11438, 172.4167" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="38" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="40" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.594, 172.4167" Font="Droid Arabic Kufi, 10pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="41" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="42" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="90.1144, 31.33331">
          <ExpressionBindings>
            <Item1 Ref="43" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item8>
        <Item9 Ref="44" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="680.2185, 6.392598E-06">
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item9>
        <Item10 Ref="46" ControlType="XRLabel" Name="XrLabel19" Text="فاتورة مرتجع مبيعات" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="30.11438, 0" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="47" UseFont="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="48" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="30.11438, 76.58331" Padding="2,2,0,0,100">
          <StylePriority Ref="49" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="50" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="255.2186, 128.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="51" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="52" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.5937, 128.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="53" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="54" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="225.1043,31.25004" LocationFloat="30.11438, 128.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="55" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="56" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="57" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="277.0833,31.24999" LocationFloat="387.5102, 128.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="58" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="59" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="60" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="المخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="664.5937, 94.27084" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="61" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="62" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="277.0832,31.25" LocationFloat="387.5103, 94.27084" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="63" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="64" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
      </Controls>
    </Item5>
    <Item6 Ref="65" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="66" ControlType="DetailBand" Name="Detail1" HeightF="25">
          <Controls>
            <Item1 Ref="67" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="796.8852,25" LocationFloat="0.0001192093, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="68" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="69" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.1005432128906252" TextFormatString="{0:n2}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="70" Expression="[invoice_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="71" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="72" ControlType="XRTableCell" Name="XrTableCell16" Weight="0.94549874321534944" Text="XrTableCell16" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="73" Expression="[invoice_print].[item_descound]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="74" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="75" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.93220917081262" TextFormatString="{0:n2}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="76" Expression="[invoice_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="77" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="78" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.85685326008936014" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="79" Expression="[invoice_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="80" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.0126570260107606" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="81" Expression="[invoice_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item5>
                    <Item6 Ref="82" ControlType="XRTableCell" Name="XrTableCell8" Weight="3.42223806986298" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="83" Expression="[invoice_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="84" UseFont="false" UseTextAlignment="false" />
                    </Item6>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="85" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
        <Item2 Ref="86" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="421.8333">
          <Controls>
            <Item1 Ref="87" ControlType="XRTable" Name="XrTable6" TextAlignment="MiddleCenter" SizeF="200,25" LocationFloat="11.99995, 234.7917" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" Borders="Bottom">
              <Rows>
                <Item1 Ref="88" ControlType="XRTableRow" Name="XrTableRow9" Weight="1">
                  <Cells>
                    <Item1 Ref="89" ControlType="XRTableCell" Name="XrTableCell25" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" ForeColor="White" BackColor="Chocolate" Padding="2,2,0,0,100" BorderColor="Black">
                      <ExpressionBindings>
                        <Item1 Ref="90" Expression="[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="91" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="92" ControlType="XRTableCell" Name="XrTableCell26" Weight="1" Text="الباقي" TextAlignment="MiddleRight" BackColor="Chocolate" Padding="2,2,0,0,100">
                      <StylePriority Ref="93" UseBackColor="false" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="94" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="95" ControlType="XRTable" Name="XrTable5" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="11.99995, 181.25" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="96" ControlType="XRTableRow" Name="XrTableRow7" Weight="1">
                  <Cells>
                    <Item1 Ref="97" ControlType="XRTableCell" Name="XrTableCell21" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="98" Expression="[money_plus]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="99" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="100" ControlType="XRTableCell" Name="XrTableCell22" Weight="1" Text="المطلوب" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="101" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item1>
                <Item2 Ref="102" ControlType="XRTableRow" Name="XrTableRow8" Weight="1">
                  <Cells>
                    <Item1 Ref="103" ControlType="XRTableCell" Name="XrTableCell23" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                      <ExpressionBindings>
                        <Item1 Ref="104" Expression="[pay]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="105" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="106" ControlType="XRTableCell" Name="XrTableCell24" Weight="1" Text="المدفوع" TextAlignment="MiddleRight" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" Borders="None">
                      <StylePriority Ref="107" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item2>
              </Rows>
              <StylePriority Ref="108" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="109" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="200,100" LocationFloat="9.999998, 81.25" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="110" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
                  <Cells>
                    <Item1 Ref="111" ControlType="XRTableCell" Name="XrTableCell17" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="112" Expression="[invoice_descound]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="113" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="114" ControlType="XRTableCell" Name="XrTableCell18" Weight="1" Text="الخصم" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="115" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item1>
                <Item2 Ref="116" ControlType="XRTableRow" Name="XrTableRow6" Weight="1">
                  <Cells>
                    <Item1 Ref="117" ControlType="XRTableCell" Name="XrTableCell19" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="118" Expression="[invoice_tax]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="119" UsePadding="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="120" ControlType="XRTableCell" Name="XrTableCell20" Weight="1" Text="الضريبة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="121" UsePadding="false" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item2>
                <Item3 Ref="122" ControlType="XRTableRow" Name="XrTableRow10" Weight="1">
                  <Cells>
                    <Item1 Ref="123" ControlType="XRTableCell" Name="XrTableCell27" Weight="1" Text="XrTableCell27" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="124" Expression="[total_invoice]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="125" UsePadding="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="126" ControlType="XRTableCell" Name="XrTableCell28" Weight="1" Text="صافي الفاتورة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="127" UsePadding="false" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item3>
                <Item4 Ref="128" ControlType="XRTableRow" Name="XrTableRow11" Weight="1">
                  <Cells>
                    <Item1 Ref="129" ControlType="XRTableCell" Name="XrTableCell29" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell29" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="130" Expression="[invoice_back_print].[past_balace]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="131" UsePadding="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="132" ControlType="XRTableCell" Name="XrTableCell30" Weight="1" Text="السابق" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="133" UsePadding="false" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item4>
              </Rows>
              <StylePriority Ref="134" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item3>
            <Item4 Ref="135" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="491.2396, 368.625" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="136" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="137" UseFont="false" />
            </Item4>
            <Item5 Ref="138" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="90.1144, 266.4584" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="139" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="140" UseFont="false" UseTextAlignment="false" />
            </Item5>
            <Item6 Ref="141" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="90.1144, 293.6251" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="142" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="143" UseFont="false" UseTextAlignment="false" />
            </Item6>
            <Item7 Ref="144" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="508.8645, 266.4584" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="145" UseFont="false" UseTextAlignment="false" />
            </Item7>
            <Item8 Ref="146" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="508.8645, 291.5417" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="147" UseFont="false" UseTextAlignment="false" />
            </Item8>
            <Item9 Ref="148" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="11.99995, 368.625" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="149" UseFont="false" UseTextAlignment="false" />
            </Item9>
            <Item10 Ref="150" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="11.99995, 333.1251" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="151" UseFont="false" UseTextAlignment="false" />
            </Item10>
            <Item11 Ref="152" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="9.999998, 31.25" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="153" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
                  <Cells>
                    <Item1 Ref="154" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="155" Expression="[invoice_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="156" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="157" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="158" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item1>
                <Item2 Ref="159" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
                  <Cells>
                    <Item1 Ref="160" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="161" Expression="[invoice_pound]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="162" UsePadding="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="163" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <StylePriority Ref="164" UsePadding="false" UseTextAlignment="false" />
                    </Item2>
                  </Cells>
                </Item2>
              </Rows>
              <StylePriority Ref="165" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item11>
            <Item12 Ref="166" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="526.5314,25" LocationFloat="290.4685, 224.375" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="167" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="168" UseFont="false" UseTextAlignment="false" />
            </Item12>
            <Item13 Ref="169" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="526.5315,25" LocationFloat="290.4685, 199.375" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="170" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="171" UseFont="false" UseTextAlignment="false" />
            </Item13>
          </Controls>
        </Item2>
      </Bands>
    </Item6>
  </Bands>
  <StyleSheet>
    <Item1 Ref="172" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="173" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="174" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="175" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="176" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="177" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>