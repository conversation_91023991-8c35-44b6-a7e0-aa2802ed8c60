Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraReports.UI

Public Class CostCenterReport
    Inherits XtraForm

    Private GridControl1 As DevExpress.XtraGrid.GridControl
    Private GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Private DateFrom As DevExpress.XtraEditors.DateEdit
    Private DateTo As DevExpress.XtraEditors.DateEdit
    Private CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Private ShowReportBtn As DevExpress.XtraEditors.SimpleButton
    Private PrintBtn As DevExpress.XtraEditors.SimpleButton
    Private CloseBtn As DevExpress.XtraEditors.SimpleButton

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.DateFrom = New DevExpress.XtraEditors.DateEdit()
        Me.DateTo = New DevExpress.XtraEditors.DateEdit()
        Me.CostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        Me.ShowReportBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.PrintBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.CloseBtn = New DevExpress.XtraEditors.SimpleButton()

        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateFrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DateTo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()

        ' Form
        Me.Text = "تقرير مراكز التكلفة"
        Me.Size = New System.Drawing.Size(1000, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes

        ' DateFrom
        Me.DateFrom.Location = New System.Drawing.Point(750, 20)
        Me.DateFrom.Size = New System.Drawing.Size(120, 20)
        Me.DateFrom.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateFrom.EditValue = DateTime.Now.AddMonths(-1)

        ' DateTo
        Me.DateTo.Location = New System.Drawing.Point(600, 20)
        Me.DateTo.Size = New System.Drawing.Size(120, 20)
        Me.DateTo.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.DateTo.EditValue = DateTime.Now

        ' CostCenterCombo
        Me.CostCenterCombo.Location = New System.Drawing.Point(450, 20)
        Me.CostCenterCombo.Size = New System.Drawing.Size(120, 20)
        Me.CostCenterCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CostCenterCombo.Properties.DisplayMember = "display_name"
        Me.CostCenterCombo.Properties.ValueMember = "cost_center_id"

        ' ShowReportBtn
        Me.ShowReportBtn.Location = New System.Drawing.Point(350, 18)
        Me.ShowReportBtn.Size = New System.Drawing.Size(80, 25)
        Me.ShowReportBtn.Text = "عرض التقرير"
        AddHandler Me.ShowReportBtn.Click, AddressOf ShowReportBtn_Click

        ' PrintBtn
        Me.PrintBtn.Location = New System.Drawing.Point(260, 18)
        Me.PrintBtn.Size = New System.Drawing.Size(80, 25)
        Me.PrintBtn.Text = "طباعة"
        AddHandler Me.PrintBtn.Click, AddressOf PrintBtn_Click

        ' CloseBtn
        Me.CloseBtn.Location = New System.Drawing.Point(170, 18)
        Me.CloseBtn.Size = New System.Drawing.Size(80, 25)
        Me.CloseBtn.Text = "إغلاق"
        AddHandler Me.CloseBtn.Click, AddressOf CloseBtn_Click

        ' GridControl1
        Me.GridControl1.Location = New System.Drawing.Point(12, 60)
        Me.GridControl1.Size = New System.Drawing.Size(976, 500)
        Me.GridControl1.MainView = Me.GridView1

        ' GridView1
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowFooter = True

        ' Add controls to form
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.DateFrom)
        Me.Controls.Add(Me.DateTo)
        Me.Controls.Add(Me.CostCenterCombo)
        Me.Controls.Add(Me.ShowReportBtn)
        Me.Controls.Add(Me.PrintBtn)
        Me.Controls.Add(Me.CloseBtn)

        ' Add labels
        Dim lblFrom As New Label()
        lblFrom.Text = "من تاريخ:"
        lblFrom.Location = New System.Drawing.Point(880, 23)
        lblFrom.Size = New System.Drawing.Size(50, 20)
        Me.Controls.Add(lblFrom)

        Dim lblTo As New Label()
        lblTo.Text = "إلى تاريخ:"
        lblTo.Location = New System.Drawing.Point(730, 23)
        lblTo.Size = New System.Drawing.Size(50, 20)
        Me.Controls.Add(lblTo)

        Dim lblCostCenter As New Label()
        lblCostCenter.Text = "مركز التكلفة:"
        lblCostCenter.Location = New System.Drawing.Point(580, 23)
        lblCostCenter.Size = New System.Drawing.Size(70, 20)
        Me.Controls.Add(lblCostCenter)

        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateFrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DateTo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
    End Sub

    Private Sub CostCenterReport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تحميل مراكز التكلفة
            CostCenterHelper.LoadActiveCostCenters(CostCenterCombo, True)
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowReportBtn_Click(sender As Object, e As EventArgs)
        Try
            LoadCostCenterReport()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCostCenterReport()
        Dim sql As String = BuildReportQuery()
        
        Using adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet()
            adp.Fill(ds, "CostCenterReport")
            
            GridControl1.DataSource = ds.Tables("CostCenterReport")
            
            ' تكوين الأعمدة
            ConfigureGridColumns()
        End Using
    End Sub

    Private Function BuildReportQuery() As String
        Dim sql As String = "
            SELECT 
                cc.cost_center_name AS 'مركز التكلفة',
                'فاتورة مبيعات' AS 'نوع العملية',
                ia.invoice_number AS 'رقم الفاتورة',
                ia.invoice_date AS 'التاريخ',
                ia.Accounts_name AS 'العميل',
                ia.total_invoice AS 'المبلغ'
            FROM invoice_add ia
            INNER JOIN cost_centers cc ON ia.cost_center_id = cc.cost_center_id
            WHERE ia.invoice_date BETWEEN @DateFrom AND @DateTo"
        
        If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
            sql += " AND ia.cost_center_id = " & CostCenterCombo.EditValue.ToString()
        End If
        
        sql += "
            UNION ALL
            SELECT 
                cc.cost_center_name AS 'مركز التكلفة',
                'مصروفات' AS 'نوع العملية',
                ea.Expenses_number AS 'رقم الفاتورة',
                ea.Expenses_date AS 'التاريخ',
                'مصروفات' AS 'العميل',
                ea.total_Expenses AS 'المبلغ'
            FROM Expenses_add ea
            INNER JOIN cost_centers cc ON ea.cost_center_id = cc.cost_center_id
            WHERE ea.Expenses_date BETWEEN @DateFrom AND @DateTo"
        
        If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
            sql += " AND ea.cost_center_id = " & CostCenterCombo.EditValue.ToString()
        End If
        
        sql += " ORDER BY 'التاريخ' DESC"
        
        ' استبدال المعاملات
        sql = sql.Replace("@DateFrom", "'" & DateFrom.DateTime.ToString("yyyy-MM-dd") & "'")
        sql = sql.Replace("@DateTo", "'" & DateTo.DateTime.ToString("yyyy-MM-dd") & "'")
        
        Return sql
    End Function

    Private Sub ConfigureGridColumns()
        With GridView1
            .Columns("مركز التكلفة").Width = 150
            .Columns("نوع العملية").Width = 100
            .Columns("رقم الفاتورة").Width = 100
            .Columns("التاريخ").Width = 100
            .Columns("العميل").Width = 200
            .Columns("المبلغ").Width = 100
            .Columns("المبلغ").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            .Columns("المبلغ").DisplayFormat.FormatString = "N2"
            .Columns("المبلغ").SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum
            .Columns("المبلغ").SummaryItem.DisplayFormat = "الإجمالي: {0:N2}"
        End With
    End Sub

    Private Sub PrintBtn_Click(sender As Object, e As EventArgs)
        Try
            GridView1.ShowPrintPreview()
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في الطباعة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CloseBtn_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub
End Class
