﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v17.2, Version=17.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="tax_cus_a4" Margins="4, 0, 2, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="tax_cus_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="360.416718" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="label16" RightToLeft="Yes" Text="المشتريات" TextAlignment="TopCenter" SizeF="712.5,31.25" LocationFloat="81.3439, 180.208359" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="4" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="5" ControlType="XRLabel" Name="label15" RightToLeft="Yes" Text="المبيعات" TextAlignment="TopCenter" SizeF="712.5,31.25" LocationFloat="81.3439, 34.47917" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="6" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="7" ControlType="XRLabel" Name="label7" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="615.718933, 242.708359" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="8" Expression="[t5]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="9" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="10" ControlType="XRLabel" Name="label8" RightToLeft="Yes" Text="المشتريات" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="615.718933, 211.458359" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="11" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="12" ControlType="XRLabel" Name="label9" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="437.5939, 242.708359" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[t6]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="14" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="15" ControlType="XRLabel" Name="label10" RightToLeft="Yes" Text="المرتجع" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="437.5939, 211.458359" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="16" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="17" ControlType="XRLabel" Name="label11" RightToLeft="Yes" Text="ضريبة المرتجه" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="81.3439, 211.458359" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="18" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="19" ControlType="XRLabel" Name="label12" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="81.3439, 242.708359" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="20" Expression="[t8]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="21" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="22" ControlType="XRLabel" Name="label13" RightToLeft="Yes" Text="ضريبة المشتريات" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="259.4689, 211.458359" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="23" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="24" ControlType="XRLabel" Name="label14" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="259.4689, 242.708359" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="25" Expression="[t7]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="26" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="27" ControlType="XRLabel" Name="label3" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="259.4689, 96.97917" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="28" Expression="[t3]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="29" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="30" ControlType="XRLabel" Name="label4" RightToLeft="Yes" Text="ضريبة المبيعات" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="259.4689, 65.72917" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="31" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="32" ControlType="XRLabel" Name="label5" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="81.3439, 96.97917" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="33" Expression="[t4]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="34" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="35" ControlType="XRLabel" Name="label6" RightToLeft="Yes" Text="ضريبة المرتجع" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="81.3439, 65.72917" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="36" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="37" ControlType="XRLabel" Name="label1" RightToLeft="Yes" Text="المرتجع" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="437.5939, 65.72917" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="38" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="39" ControlType="XRLabel" Name="label2" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="437.5939, 96.97917" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="40" Expression="[t2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="41" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="42" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="المبيعات" TextAlignment="TopCenter" SizeF="178.125,31.2500019" LocationFloat="615.718933, 65.72917" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="43" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="44" ControlType="XRLabel" Name="XrLabel9" RightToLeft="Yes" Text="XrLabel9" TextAlignment="TopCenter" SizeF="178.125,31.25" LocationFloat="615.718933, 96.97917" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[t1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="46" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item18>
      </Controls>
    </Item1>
    <Item2 Ref="47" ControlType="TopMarginBand" Name="TopMargin" HeightF="2.083333" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="48" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
  </Bands>
  <StyleSheet>
    <Item1 Ref="49" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="50" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="51" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="52" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="53" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="54" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>