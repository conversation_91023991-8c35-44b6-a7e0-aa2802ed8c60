﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI

Public Class cashing_show

    Private Sub searsh_btn_Click(sender As Object, e As EventArgs) Handles searsh_btn.Click

        Dim adp As New SqlDataAdapter("select * from customer_cashing where chashingdate>='" & Format(Datefrom.Value, "yyy/MM/dd") & "'  and chashingdate<='" & Format(Dateto.Value, "yyy/MM/dd") & "' order by chashingdate", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt
    End Sub

    Private Sub cashing_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        day_option.SelectedIndex = 6
        searsh_btn_Click(Nothing, Nothing)

    End Sub
    Sub fill_report()
        Try
            Dim adp As New SqlDataAdapter("select * from pay_invoice where code_cash=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            DataGridView1.AutoGenerateColumns = False
            DataGridView1.DataSource = dt
            For i = 0 To DataGridView1.Rows.Count - 1
                Dim sql1 = "select * from invoice_add where invoice_number=N'" & (DataGridView1.Rows(i).Cells(1).Value.ToString) & "'"
                Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
                Dim ds1 As New DataSet
                adp1.Fill(ds1)
                Dim dt1 = ds1.Tables(0)
                If dt1.Rows.Count > 0 Then
                    Dim dr1 = dt1.Rows(0)
                    '========= بيانات اساسية============
                    dr1!pay_money = Val(dr1!pay_money) - Val(DataGridView1.Rows(i).Cells(2).Value.ToString)
                    Dim cmd25 As New SqlCommandBuilder(adp1)
                    adp1.Update(dt1)
                End If
            Next
        Catch ex As Exception

        End Try
      


    End Sub

    Private Sub day_option_SelectedIndexChanged(sender As Object, e As EventArgs) Handles day_option.SelectedIndexChanged
        If day_option.SelectedIndex = 0 Then
            Datefrom.Value = Now.Date
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 1 Then
            Datefrom.Value = DateAdd("d", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 2 Then
            Datefrom.Value = DateAdd("ww", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 3 Then
            Datefrom.Value = DateAdd("ww", -2, Now.Date)
            Dateto.Value = DateAdd("ww", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 4 Then
            Datefrom.Value = DateAdd("m", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 5 Then
            Datefrom.Value = DateAdd("m", -2, Now.Date)
            Dateto.Value = DateAdd("m", -1, Now.Date)
        ElseIf day_option.SelectedIndex = 6 Then
            Datefrom.Value = DateAdd("m", -3, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 7 Then
            Datefrom.Value = DateAdd("m", -6, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 8 Then
            Datefrom.Value = DateAdd("yyyy", -1, Now.Date)
            Dateto.Value = Now.Date
        ElseIf day_option.SelectedIndex = 9 Then
            Datefrom.Value = DateAdd("yyyy", -2, Now.Date)
            Dateto.Value = DateAdd("yyyy", -1, Now.Date)
        End If
    End Sub

    Private Sub delete_btn_Click(sender As Object, e As EventArgs) Handles delete_btn.Click
        If XtraForm1.m25.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد سند لحذفة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If XtraMessageBox.Show("هل تريد حذف هذا السند ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
            Dim sql = "select * from customer_cashing where Cashingcode=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then

            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                delet_custrans()
                delet_treausy()
                fill_report()
                delete()
                searsh_btn_Click(Nothing, Nothing)

                XtraMessageBox.Show("تم حذف السند بنجاح")
            End If
        End If
    End Sub
    Sub delete()
        Try
            For i2 = 0 To 1000
                Dim sql = "select * from pay_invoice where code_cash=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                    Exit Sub
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
        Catch ex As Exception

        End Try
      
    End Sub
    Sub delet_custrans()
        Try
            Dim sql = "select * from customer_trans where custrans_type= 'تحصيل فواتبر' and custrans_code=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
            adp = New SqlDataAdapter("select * from customer where cusname=N'" & (GridView2.GetFocusedRowCellValue("Accounts_name")) & "'", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            Dim dr5 = dt.Rows(0)
            dr5!Accounts_balace = Format(Val(dr5!Accounts_balace) + Val(GridView2.GetFocusedRowCellValue("Amount")), "0.00")
            Dim cmd5 As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Catch ex As Exception

        End Try
      
    End Sub
    Sub delet_treausy()
        Try
            If GridView2.GetFocusedRowCellValue("cash_pay") = "الخزينة" Then
                Dim sql = "select * from treasury_pay where movement= 'تحصيل فواتبر' and code_trans=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
                adp.Dispose()
                ds.Dispose()
                dt.Dispose()
                adp = New SqlDataAdapter("select * from treasury_name where treasury_name=N'" & (GridView2.GetFocusedRowCellValue("type_pay")) & "'", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                Dim dr6 = dt.Rows(0)
                dr6!balace = Format(Val(dr6!balace) - Val(GridView2.GetFocusedRowCellValue("Amount")), "0.00")
                Dim cmd6 As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Catch ex As Exception

        End Try
       

    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub
    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If XtraForm1.m24.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد سند للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_customer = New cashing_customer()
        f.Text = "سند قبض"
        f.MdiParent = XtraForm1
        f.Show()
        f.show_data(GridView2.GetFocusedRowCellValue("Cashingcode"))

        Dim sql = "select * from customer_cashing where Cashingcode=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("فشل في جلب البيانات")
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            delet_custrans()
            delet_treausy()
            delete()
            searsh_btn_Click(Nothing, Nothing)
        End If
        f.refresh_cash()
    End Sub

    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub

    Private Sub تحديثالبياناتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تحديثالبياناتToolStripMenuItem.Click
        searsh_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        Me.Dispose()
    End Sub

    Private Sub طباعةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles طباعةToolStripMenuItem.Click
        print_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub حذفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حذفToolStripMenuItem.Click
        delete_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub تعديلToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تعديلToolStripMenuItem.Click
        edit_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub cashing_show_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            edit_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            delete_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            print_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            searsh_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim adp As New SqlDataAdapter("select * from cash_cus_print where Cashingcode=N'" & GridView2.GetFocusedRowCellValue("Cashingcode") & "'", sqlconn)
        Dim ds As New DataSet

        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "cashing_cus_a4.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub
End Class