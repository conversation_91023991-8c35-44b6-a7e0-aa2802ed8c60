# دليل استكشاف الأخطاء - نظام مراكز التكلفة

## الأخطاء الشائعة وحلولها

### 1. خطأ: 'XtraMessageBox' is not declared
**السبب**: عدم استيراد مكتبة DevExpress.XtraEditors
**الحل**: تم إصلاحه بإضافة `Imports DevExpress.XtraEditors` في بداية الملف

### 2. خطأ: Type 'CostCenterReport' is not defined
**السبب**: عدم إضافة ملف التقرير للمشروع بشكل صحيح
**الحل**: تم دمج التقرير مباشرة في ملف `report_show.vb` لتجنب مشاكل المراجع

### 3. خطأ: 'sqlconn' is not declared
**السبب**: عدم الوصول لمتغير الاتصال بقاعدة البيانات
**الحل**: متغير sqlconn متاح في Module1 ويمكن الوصول إليه مباشرة

### 4. خطأ: 'XtraForm1' is not declared
**السبب**: عدم الوصول للنموذج الرئيسي
**الحل**: XtraForm1 متاح كفئة عامة في النظام

## التحقق من صحة التثبيت

### 1. التحقق من وجود الملفات:
- ✅ `CostCenter/CostCenterHelper.vb`
- ✅ تحديث `report/report_show.vb` (يحتوي على التقرير مدمج)
- ✅ تحديث `report/report_show.Designer.vb`
- ✅ لا حاجة لملفات منفصلة للتقرير

### 2. التحقق من الزر في شاشة التقارير:
- افتح شاشة التقارير
- انتقل إلى تبويب "الحسابات"
- ابحث عن زر "تقرير مراكز التكلفة"

### 3. التحقق من قاعدة البيانات:
عند فتح التقرير لأول مرة، سيتم:
- إنشاء جدول `cost_centers` تلقائياً
- إضافة أعمدة `cost_center_id` للجداول الموجودة
- إدراج بيانات تجريبية

## خطوات التشغيل الأولى

### 1. فتح التقرير لأول مرة:
```
التقارير → تبويب الحسابات → تقرير مراكز التكلفة
```

### 2. إذا ظهرت رسالة "جدول مراكز التكلفة غير موجود":
- اضغط "نعم" لإنشاء الجدول
- سيتم إنشاء الجدول مع بيانات تجريبية

### 3. إدارة مراكز التكلفة:
- يمكن إضافة شاشة إدارة مراكز التكلفة للقائمة الرئيسية
- أو استخدام البيانات التجريبية المدرجة

## البيانات التجريبية المدرجة

عند إنشاء الجدول، سيتم إدراج:
1. **CC001** - قسم المبيعات
2. **CC002** - قسم الإنتاج  
3. **CC003** - قسم الإدارة

## استخدام مراكز التكلفة

### في فواتير المبيعات:
- ستجد حقل "مركز التكلفة" في أسفل الشاشة
- اختر مركز التكلفة المناسب قبل الحفظ

### في شاشة المصروفات:
- ستجد حقل "مركز التكلفة" في أسفل الشاشة
- اختر مركز التكلفة المناسب قبل الحفظ

## ميزات التقرير

### 1. الفلاتر:
- **من تاريخ / إلى تاريخ**: لتحديد الفترة
- **مركز التكلفة**: لاختيار مركز محدد أو الكل

### 2. الأزرار:
- **عرض التقرير**: لعرض البيانات
- **ملخص**: لعرض ملخص إجمالي
- **تصدير**: لتصدير إلى Excel
- **طباعة**: لطباعة التقرير
- **تحديث**: لتحديث البيانات
- **إغلاق**: لإغلاق النافذة

### 3. البيانات المعروضة:
- مركز التكلفة
- نوع العملية (فاتورة مبيعات / مصروفات)
- رقم الفاتورة والتاريخ
- العميل أو نوع المصروف
- المبلغ والربح
- المستخدم ونوع الدفع
- الملاحظات

## في حالة استمرار المشاكل

### 1. تحقق من:
- صحة الاتصال بقاعدة البيانات
- صلاحيات المستخدم في قاعدة البيانات
- وجود جميع مراجع DevExpress

### 2. رسائل الخطأ:
- اقرأ رسالة الخطأ بعناية
- تحقق من سجل الأحداث
- راجع ملف التوثيق الشامل

### 3. الدعم:
- راجع ملف `COST_CENTER_INTEGRATION_GUIDE.md`
- راجع ملف `QUICK_ACCESS_GUIDE.md`
- تحقق من ملف `README.md`

## ملاحظات مهمة

1. **الأمان**: النظام يتعامل بأمان مع عدم وجود الجداول
2. **التوافق**: لا يؤثر على العمليات الموجودة
3. **المرونة**: يمكن ترك مركز التكلفة فارغاً
4. **الاستقرار**: تم اختبار النظام للتأكد من عدم وجود أخطاء

## الخلاصة

تم إصلاح جميع الأخطاء المعروفة:
- ✅ إصلاح خطأ XtraMessageBox
- ✅ إصلاح خطأ CostCenterReport (دمج التقرير في الملف الرئيسي)
- ✅ التأكد من وجود sqlconn
- ✅ التأكد من وجود XtraForm1
- ✅ اختبار عدم وجود أخطاء تشخيصية
- ✅ حل مشكلة مراجع المشروع

النظام جاهز للاستخدام! 🎉
