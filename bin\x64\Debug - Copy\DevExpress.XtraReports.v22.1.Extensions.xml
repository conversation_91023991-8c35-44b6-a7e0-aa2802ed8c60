<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <assembly>
    <name>DevExpress.XtraReports.v22.1.Extensions</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraReports.Configuration">
      <summary>
        <para>Contains classes that enable additional customization of the End-User Report Designer for WinForms.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Configuration.ChartDesignOptions">
      <summary>
        <para>Enables you to specify the default behavior of an <see cref="T:DevExpress.XtraReports.UI.XRChart"/> on adding it to a report.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Configuration.ChartDesignOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Configuration.ChartDesignOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.ChartDesignOptions.ShowAnyEditor">
      <summary>
        <para>Indicates whether or not any dialog (Chart Wizard or Chart Designer) is automatically invoked on adding an <see cref="T:DevExpress.XtraReports.UI.XRChart"/> to a report.</para>
      </summary>
      <value>true, if the Chart Wizard or Chart Designer is invoked on adding a chart; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.ChartDesignOptions.ShowDesigner">
      <summary>
        <para>Specifies whether or not the Chart Designer should be automatically invoked on adding an <see cref="T:DevExpress.XtraReports.UI.XRChart"/> to a report.</para>
      </summary>
      <value>true, to invoke the Chart Designer on adding a chart; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.ChartDesignOptions.ShowWizard">
      <summary>
        <para>Specifies whether or not the Chart Wizard should be automatically invoked on adding an <see cref="T:DevExpress.XtraReports.UI.XRChart"/> to a report.</para>
      </summary>
      <value>true, to invoke the Chart Wizard on adding a chart; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Configuration.DesignSettings">
      <summary>
        <para>Provides additional settings to the End-User Report Designer for WinForms.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Configuration.DesignSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Configuration.DesignSettings"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.DesignSettings.ChartOptions">
      <summary>
        <para>Enables you to customize the default behavior of an <see cref="T:DevExpress.XtraReports.UI.XRChart"/> in a reporting application for WinForms.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Configuration.ChartDesignOptions"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.DesignSettings.Default">
      <summary>
        <para>Provides access to the default <see cref="T:DevExpress.XtraReports.Configuration.DesignSettings"/> instance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Configuration.DesignSettings"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.DesignSettings.PivotGridOptions">
      <summary>
        <para>Enables you to customize the Pivot Grid’s behavior in a reporting application for WinForms.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Configuration.PivotGridDesignOptions"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.DesignSettings.UseOfficeInspiredRibbonStyle">
      <summary>
        <para>Specifies whether the End-User Report Designer uses an MS Office-inspired Ribbon toolbar.</para>
      </summary>
      <value>true, to make the Report Designer use an MS Office-inspired Ribbon; false, to use the previous Ribbon version.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Configuration.PivotGridDesignOptions">
      <summary>
        <para>Enables you to specify the settings related to the Pivot Grid behavior.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Configuration.PivotGridDesignOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Configuration.PivotGridDesignOptions"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Configuration.PivotGridDesignOptions.ShowLoadXmlDataDesignerTab">
      <summary>
        <para>Specifies whether to show the Load Data from XML tab in the Pivot Grid Designer.</para>
      </summary>
      <value>true, to show the Load Data from XML tab; otherwise, false.</value>
    </member>
    <member name="N:DevExpress.XtraReports.Design">
      <summary>
        <para>Provides the functionality that is necessary for deep customization of the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Design.ComponentVisibility">
      <summary>
        <para>Lists the available visibility types for report components.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.Design.ComponentVisibility.ComponentTray">
      <summary>
        <para>This field is obsolete.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.Design.ComponentVisibility.None">
      <summary>
        <para>The components are not displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.Design.ComponentVisibility.ReportExplorer">
      <summary>
        <para>The components are displayed in the Report Explorer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Design.SyntaxEditor">
      <summary>
        <para>Enables the Script Editor to implement intelligent code completion.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Design.SyntaxEditor.#ctor(DevExpress.XtraRichEdit.SyntaxEdit.ISyntaxColors,System.Action,DevExpress.XtraReports.Design.IScriptSource)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Design.SyntaxEditor"/> class with specified settings.</para>
      </summary>
      <param name="syntaxColors"></param>
      <param name="scriptValidator"></param>
      <param name="scriptSource"></param>
    </member>
    <member name="M:DevExpress.XtraReports.Design.SyntaxEditor.BeginInit">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Design.SyntaxEditor.EnableCodeCompletion">
      <summary>
        <para>Specifies whether the Report Designer’s Script Editor supports intelligent code completion.</para>
      </summary>
      <value>true, to enable intelligent code completion; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Design.SyntaxEditor.EndInit">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Design.SyntaxEditor.IsInitializing">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="N:DevExpress.XtraReports.Extensions">
      <summary>
        <para>Contains design-time extensions to provide advanced customization of the XtraReports End-User Designer.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.GetTemplatesHandler">
      <summary>
        <para>Provides functionality to implement a custom template gallery.</para>
      </summary>
      <param name="templatesInfo">A <see cref="T:DevExpress.XtraReports.Extensions.TemplatesInfo"/> object.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.ITemplateProvider">
      <summary>
        <para>When implemented by a class, provides the functionality to implement a custom template gallery.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ITemplateProvider.GetIconImage(System.Guid)">
      <summary>
        <para>Returns an icon for the specified report template.</para>
      </summary>
      <param name="templateID">A <see cref="T:System.Guid"/> structure.</param>
      <returns>A <see cref="T:System.Byte"/> array.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ITemplateProvider.GetPreviewImageAsync(System.Guid,System.Action{System.Guid,System.Byte[]})">
      <summary>
        <para>Obtains the preview image of a report template, asynchronously.</para>
      </summary>
      <param name="templateID">A <see cref="T:System.Guid"/> structure.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ITemplateProvider.GetTemplateLayout(System.Guid)">
      <summary>
        <para>Returns the layout of a specified report template.</para>
      </summary>
      <param name="templateID">A <see cref="T:System.Byte"/> array, storing the template layout.</param>
      <returns>A <see cref="T:System.Guid"/> structure.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ITemplateProvider.GetTemplates(System.String,DevExpress.XtraReports.Extensions.GetTemplatesHandler)">
      <summary>
        <para>Returns the collection of report templates.</para>
      </summary>
      <param name="searchString">A <see cref="T:System.String"/> value.</param>
      <param name="getTemplates">A <see cref="T:DevExpress.XtraReports.Extensions.GetTemplatesHandler"/> delegate.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ITemplateProvider.SendTemplate(System.String,System.String,System.Byte[],System.Drawing.Image,System.Drawing.Image)">
      <summary>
        <para>Sends the specified report template to a storage.</para>
      </summary>
      <param name="templateName">A <see cref="T:System.String"/> value, specifying the template name.</param>
      <param name="description">A <see cref="T:System.String"/> value, specifying the template description.</param>
      <param name="layout">A <see cref="T:System.Byte"/> array, storing the template layout.</param>
      <param name="preview">A <see cref="T:System.Drawing.Image"/> object, specifying the template’s preview image.</param>
      <param name="icon">A <see cref="T:System.Drawing.Image"/> object, specifying the template’s icon.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.ParametersRequestExtension">
      <summary>
        <para>Supports the creation of custom parameter editors in a Print Preview.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ParametersRequestExtension.AssociateReportWithExtension(DevExpress.XtraReports.UI.XtraReport,System.String)">
      <summary>
        <para>Associates the report with the custom parameter extension, to implement custom parameter editors in a print preview.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</param>
      <param name="contextName">A <see cref="T:System.String"/> value, identifying the context.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ParametersRequestExtension.RegisterExtension(DevExpress.XtraReports.Extensions.ParametersRequestExtension,System.String)">
      <summary>
        <para>Registers the custom parameters request extension, to implement custom parameter editors in a print preview.</para>
      </summary>
      <param name="extension">A <see cref="T:DevExpress.XtraReports.Extensions.ParametersRequestExtension"/> object.</param>
      <param name="contextName">A <see cref="T:System.String"/> value, identifying the context.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.ReportDesignExtension">
      <summary>
        <para>Enables you to serialize custom parameter types and other custom objects along with a report’s definition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportDesignExtension.AddParameterTypes(System.Collections.Generic.IDictionary{System.Type,System.String})">
      <summary>
        <para>Adds custom parameter types to the <see cref="T:DevExpress.XtraReports.Extensions.ReportDesignExtension"/>.</para>
      </summary>
      <param name="dictionary">A collection of custom parameter types.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportDesignExtension.AssociateReportWithExtension(DevExpress.XtraReports.UI.XtraReport,System.String)">
      <summary>
        <para>Associates the report with the custom design extension, to employ parameters of custom types.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class descendant.</param>
      <param name="contextName">A <see cref="T:System.String"/> value, identifying the context.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportDesignExtension.GetEditableDataTypes">
      <summary>
        <para>Gets the editable custom parameter types.</para>
      </summary>
      <returns>A <see cref="T:System.Type"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportDesignExtension.GetSerializableDataTypes">
      <summary>
        <para>Use the CanSerialize and CanDeserialize methods of the IDataSerializer interface instead.</para>
      </summary>
      <returns>A <see cref="T:System.Type"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportDesignExtension.RegisterExtension(DevExpress.XtraReports.Extensions.ReportDesignExtension,System.String)">
      <summary>
        <para>Registers the custom report design extension to serialize custom parameter types.</para>
      </summary>
      <param name="extension">A <see cref="T:DevExpress.XtraReports.Extensions.ReportDesignExtension"/> object.</param>
      <param name="contextName">A <see cref="T:System.String"/> value, identifying the context.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.ReportStorageExtension">
      <summary>
        <para>Carries the functionality that is required to implement custom serialization of report definitions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.AfterGetData(System.String,DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>When overridden, performs custom actions after the <see cref="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.GetData(System.String)"/> method is performed.</para>
      </summary>
      <param name="url">A <see cref="T:System.String"/> that specifies the URL used to store a report.</param>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object that is associated with the passed report url.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.CanSetData(System.String)">
      <summary>
        <para>Determines whether it is allowed to store a report in a Report Storage, using the specified URL.</para>
      </summary>
      <param name="url">A <see cref="T:System.String"/> specifying the URL to store a report.</param>
      <returns>true if it is allowed to store a report using the specified URL; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.GetData(System.String)">
      <summary>
        <para>Returns a report definition stored in a Report Storage using the specified URL.</para>
      </summary>
      <param name="url">A <see cref="T:System.String"/> specifying a URL, which was used to store a report.</param>
      <returns>An array of <see cref="T:System.Byte"/> values, representing a stream containing a report definition.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.GetNewUrl">
      <summary>
        <para>Returns a unique URL for a report being restored from a Report Storage.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value, specifying the URL.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.GetStandardUrls(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
        <para>Returns the array of the existing report URLs.</para>
      </summary>
      <param name="context">An object implementing the <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> interface.</param>
      <returns>An array of <see cref="T:System.String"/> values specifying the report URLs.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.GetStandardUrlsSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
        <para>Determines whether standard URLs are supported.</para>
      </summary>
      <param name="context">An object implementing the <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> interface.</param>
      <returns>true if standard URLs are supported; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.IsValidUrl(System.String)">
      <summary>
        <para>Determines whether the specified URL is valid in the current Report Storage.</para>
      </summary>
      <param name="url">A <see cref="T:System.String"/> specifying a URL value.</param>
      <returns>true if a report exists in a storage for the specified URL; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.RegisterExtensionGlobal(DevExpress.XtraReports.Extensions.ReportStorageExtension)">
      <summary>
        <para>Registers the specified Report Storage extension to be used by all End-User Designers within the current application.</para>
      </summary>
      <param name="extension">A <see cref="T:DevExpress.XtraReports.Extensions.ReportStorageExtension"/> class descendant.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.SetData(DevExpress.XtraReports.UI.XtraReport,System.IO.Stream)">
      <summary>
        <para>Stores the specified report to a Report Storage using the specified stream.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class descendant, specifying a report object to be saved.</param>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, which should be used to save a report.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.SetData(DevExpress.XtraReports.UI.XtraReport,System.String)">
      <summary>
        <para>Stores the specified report to a Report Storage using the specified URL.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class descendant, specifying a report object to be saved.</param>
      <param name="url">A <see cref="T:System.String"/> specifying the URL, which should be used to save a report.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportStorageExtension.SetNewData(DevExpress.XtraReports.UI.XtraReport,System.String)">
      <summary>
        <para>Stores the specified report object in a Report Storage as a new report using the default URL.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</param>
      <param name="defaultUrl">A <see cref="T:System.String"/> value specifying the new URL.</param>
      <returns>A <see cref="T:System.String"/> value specifying the report’s URL.</returns>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.ReportTemplateDirectoryExtension">
      <summary>
        <para>Assists in the deployment of a Custom Report Gallery.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateDirectoryExtension.#ctor(System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Extensions.ReportTemplateDirectoryExtension"/> class with the specified settings.</para>
      </summary>
      <param name="templatesPaths">An array of <see cref="T:System.String"/> values, specifying the paths to report template files.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateDirectoryExtension.GetTemplates">
      <summary>
        <para>Returns the collection of available templates.</para>
      </summary>
      <returns>A collection of <see cref="T:DevExpress.XtraReports.Templates.Template"/> objects.</returns>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.ReportTemplateExtension">
      <summary>
        <para>Provides functionality to implement a custom template gallery.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateExtension.GetIconImage(System.Guid)">
      <summary>
        <para>Returns an icon for the specified report template.</para>
      </summary>
      <param name="templateID">A <see cref="T:System.Guid"/> structure.</param>
      <returns>A <see cref="T:System.Byte"/> array.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateExtension.GetPreviewImageAsync(System.Guid,System.Action{System.Guid,System.Byte[]})">
      <summary>
        <para>Obtains the preview image of a report template, asynchronously.</para>
      </summary>
      <param name="templateID">A <see cref="T:System.Guid"/> structure.</param>
      <param name="callback">A <see cref="T:System.Action"/> delegate.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateExtension.GetTemplates">
      <summary>
        <para>Provides access to the collection of report templates.</para>
      </summary>
      <returns>A collection of <see cref="T:DevExpress.XtraReports.Templates.Template"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateExtension.RegisterExtensionGlobal(DevExpress.XtraReports.Extensions.ITemplateProvider)">
      <summary>
        <para>Registers the extension on the server.</para>
      </summary>
      <param name="extension">An object implementing the <see cref="T:DevExpress.XtraReports.Extensions.ITemplateProvider"/> interface (typically, a <see cref="T:DevExpress.XtraReports.Extensions.ReportTemplateExtension"/> object).</param>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.ReportTemplateExtension.ResetTemplates">
      <summary>
        <para>Updates the Template Gallery to reflect changes made to it.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Extensions.TemplatesInfo">
      <summary>
        <para>Provides functionality to implement a custom template gallery.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Extensions.TemplatesInfo.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Extensions.TemplatesInfo"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Extensions.TemplatesInfo.ExceptionMessage">
      <summary>
        <para>Specifies the exception message shown if an error occurs when loading templates.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the exception message.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Extensions.TemplatesInfo.Templates">
      <summary>
        <para>Specifies the collection of report templates.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraReports.Templates.Template"/> objects.</value>
    </member>
    <member name="N:DevExpress.XtraReports.ReportGeneration">
      <summary>
        <para>Contains classes that enable you to convert a <see cref="T:DevExpress.XtraGrid.GridControl"/> to an <see cref="T:DevExpress.XtraReports.UI.XtraReport"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.ReportGeneration.ReportGenerator">
      <summary>
        <para>Enables you to generate an <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> based on a <see cref="T:DevExpress.XtraGrid.GridControl"/>‘s data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerator"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerator"/> class with the specified container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="P:DevExpress.XtraReports.ReportGeneration.ReportGenerator.ContainerForm">
      <summary>
        <para>Provides access to the form containing the <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerator"/> component.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Form"/> object, specifying the <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerator"/> container.</value>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.GenerateReport(DevExpress.XtraGrid.Views.Base.BaseView)">
      <summary>
        <para>Generates a new report using the specified grid view.</para>
      </summary>
      <param name="view">A <see cref="T:DevExpress.XtraGrid.Views.Base.BaseView"/> descendant.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.GenerateReport(DevExpress.XtraGrid.Views.Base.BaseView,DevExpress.XtraReports.ReportGeneration.ReportGenerationOptions)">
      <summary>
        <para>Generates a new report using the specified grid view and generation options.</para>
      </summary>
      <param name="view">A <see cref="T:DevExpress.XtraGrid.Views.Base.BaseView"/> descendant.</param>
      <param name="options">A <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerationOptions"/> object.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.GenerateReport(DevExpress.XtraGrid.Views.Base.BaseView,DevExpress.XtraReports.ReportGeneration.ReportGenerationOptions,System.Boolean)">
      <summary>
        <para>Generates a new report using the specified grid view.</para>
      </summary>
      <param name="view">A <see cref="T:DevExpress.XtraGrid.Views.Base.BaseView"/> descendant.</param>
      <param name="options">A <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerationOptions"/> object.</param>
      <param name="useExpressionBindings">true, to use expression bindings; false, to use the legacy binding mode.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.GenerateReport(DevExpress.XtraReports.UI.XtraReport,DevExpress.XtraGrid.Views.Base.BaseView)">
      <summary>
        <para>Generates a new report using the specified source report and grid view. Only the settings related to the document layout of the source report are preserved in the resulting report (such as <see cref="P:DevExpress.XtraReports.UI.XtraReport.Landscape"/> and <see cref="P:DevExpress.XtraReports.UI.XtraReport.Margins"/>).</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</param>
      <param name="view">A <see cref="T:DevExpress.XtraGrid.Views.Base.BaseView"/> descendant.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.ReportGeneration.ReportGenerator.GenerateReport(DevExpress.XtraReports.UI.XtraReport,DevExpress.XtraGrid.Views.Base.BaseView,DevExpress.XtraReports.ReportGeneration.ReportGenerationOptions,System.Boolean)">
      <summary>
        <para>Generates a new report using the specified source report and grid view. Only the settings related to the document layout of the source report are preserved in the resulting report (such as <see cref="P:DevExpress.XtraReports.UI.XtraReport.Landscape"/> and <see cref="P:DevExpress.XtraReports.UI.XtraReport.Margins"/>).</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</param>
      <param name="view">A <see cref="T:DevExpress.XtraGrid.Views.Base.BaseView"/> descendant.</param>
      <param name="options">A <see cref="T:DevExpress.XtraReports.ReportGeneration.ReportGenerationOptions"/> object.</param>
      <param name="useExpressionBindings">true, to use expression bindings; false, to use the legacy binding mode.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</returns>
    </member>
    <member name="N:DevExpress.XtraReports.UI">
      <summary>
        <para>Contains classes that implement the basic functionality of XtraReports.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UI.IReportDesignTool">
      <summary>
        <para>Provides methods for showing both the standard and ribbon-based End-User Report Designer forms.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowDesigner">
      <summary>
        <para>When implemented by a class, invokes the standard End-User Report Designer form which allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowDesigner(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>When implemented by a class, invokes the standard End-User Report Designer form which allows a report to be edited by end-users using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowDesigner(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>When implemented by a class, invokes the standard End-User Report Designer form using the specified look and feel settings, and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowDesignerDialog">
      <summary>
        <para>When implemented by a class, invokes the standard End-User Report Designer form modally. This form allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>When implemented by a class, invokes the standard End-User Report Designer form which is shown modally and allows a report to be edited by end-users using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>When implemented by a class, invokes the standard End-User Report Designer form modally using the specified look and feel settings and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowRibbonDesigner">
      <summary>
        <para>When implemented by a class, invokes the ribbon-based End-User Report Designer form which allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowRibbonDesigner(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>When implemented by a class, invokes the ribbon-based End-User Report Designer form which allows a report to be edited by end-users using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowRibbonDesigner(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>When implemented by a class, invokes the ribbon-based End-User Report Designer form using the specified look and feel settings, and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowRibbonDesignerDialog">
      <summary>
        <para>When implemented by a class, invokes the ribbon-based End-User Report Designer form modally. This form allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowRibbonDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>When implemented by a class, invokes the ribbon-based End-User Report Designer form modally, using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.IReportDesignTool.ShowRibbonDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>When implemented by a class, invokes the ribbon-based End-User Report Designer form modally, using the specified look and feel settings, and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UI.ReportDesignTool">
      <summary>
        <para>An instrument for editing reports in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.#ctor(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UI.ReportDesignTool"/> class with the specified report.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UI.ReportDesignTool.DesignForm">
      <summary>
        <para>Provides access to the standard End-User Report Designer form of the <see cref="T:DevExpress.XtraReports.UI.ReportDesignTool"/>.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.IDesignForm"/> interface (typically, an <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/> instance).</value>
    </member>
    <member name="P:DevExpress.XtraReports.UI.ReportDesignTool.DesignRibbonForm">
      <summary>
        <para>Provides access to the ribbon-based End-User Report Designer form of the <see cref="T:DevExpress.XtraReports.UI.ReportDesignTool"/>.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.IDesignForm"/> interface (typically, an <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/> instance).</value>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.Dispose">
      <summary>
        <para>Disposes of the <see cref="T:DevExpress.XtraReports.UI.ReportDesignTool"/> object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UI.ReportDesignTool.Report">
      <summary>
        <para>Provides access to the report assigned to the <see cref="T:DevExpress.XtraReports.UI.ReportDesignTool"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowDesigner">
      <summary>
        <para>Invokes the standard End-User Report Designer form which allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowDesigner(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the standard End-User Report Designer form which allows a report to be edited by end-users using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowDesigner(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the standard End-User Report Designer form using the specified look and feel settings, and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Designer form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowDesignerDialog">
      <summary>
        <para>Invokes the standard End-User Report Designer form modally. This form allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the standard End-User Report Designer form which is shown modally and allows a report to be edited by end-users using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the standard End-User Report Designer form modally using the specified look and feel settings and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowRibbonDesigner">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form which allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowRibbonDesigner(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form which allows a report to be edited by end-users using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowRibbonDesigner(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form using the specified look and feel settings, and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowRibbonDesignerDialog">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form modally. This form allows a report to be edited by end-users.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowRibbonDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form modally, using the specified look and feel settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.ReportDesignTool.ShowRibbonDesignerDialog(DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form modally, using the specified look and feel settings, and with the specified design panels hidden.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer form.</param>
      <param name="hiddenPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value identifying the End-User Designer dock panels to be hidden.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UI.XtraReportDesignerExtensions">
      <summary>
        <para>Provides extension methods that enable you to load a report in an End-User WinForms Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.About(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="report"></param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowDesigner(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Invokes the standard End-User Report Designer form which allows end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowDesigner(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the standard End-User Report Designer form with the specified look and feel settings to allow end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowDesigner(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the standard End-User Report Designer form with the specified look and feel settings as well as design panel visibility settings.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
      <param name="hiddenPanels">One or more <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration values identifying the Report Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowDesignerDialog(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Invokes the standard End-User Report Designer form modally to allow end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowDesignerDialog(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the standard End-User Report Designer form modally with the specified look and feel settings to allow end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowDesignerDialog(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the standard End-User Report Designer form modally with the specified look and feel settings and design panel visibility settings.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
      <param name="hiddenPanels">One or more <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration values identifying the Report Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowRibbonDesigner(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form which allows end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowRibbonDesigner(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form with the specified look and feel settings to allow end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowRibbonDesigner(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form with the specified look and feel settings and design panel visibility settings.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
      <param name="hiddenPanels">One or more <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration values identifying the Report Designer dock panels to be hidden.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowRibbonDesignerDialog(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form modally to allow end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowRibbonDesignerDialog(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form modally with the specified look and feel settings to allow end-users to edit the report.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UI.XtraReportDesignerExtensions.ShowRibbonDesignerDialog(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Invokes the ribbon-based End-User Report Designer form modally with the specified look and feel settings and design panel visibility settings.</para>
      </summary>
      <param name="report">A report to open in the End-User Report Designer.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Report Designer.</param>
      <param name="hiddenPanels">One or more <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration values identifying the Report Designer dock panels to be hidden.</param>
    </member>
    <member name="N:DevExpress.XtraReports.UserDesigner">
      <summary>
        <para>Contains classes that implement the functionality of the End-User Designer for XtraReports.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.CommandVisibility">
      <summary>
        <para>Specifies the visibility levels for the report commands in the End-User Designer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.CommandVisibility.All">
      <summary>
        <para>The command is visible wherever it can be available.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.CommandVisibility.ContextMenu">
      <summary>
        <para>The command is visible in the context menu only. If this command doesn’t have an associated context menu item, the SetCommandVisibility method doesn’t change its visibility.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.CommandVisibility.None">
      <summary>
        <para>The command is inaccessible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.CommandVisibility.Toolbar">
      <summary>
        <para>The command is visible in the toolbar and menu only. If this command doesn’t have an associated bar or menu item, the SetCommandVisibility method doesn’t change its visibility.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.CommandVisibility.Verb">
      <summary>
        <para>The command is available as a context link only. If this command doesn’t have an associated verb, the SetCommandVisibility method doesn’t change its visibility.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel">
      <summary>
        <para>The base for classes that provide dock panels to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> of the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.DesignDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.DesignDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> that is the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value that specifies the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.DesignDockPanel.Text">
      <summary>
        <para>Overrides the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Text"/> property to hide it.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the dock panel caption.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.DesignDockPanel.XRDesignPanel">
      <summary>
        <para>Associates the <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.DockPanelCollection">
      <summary>
        <para>For internal use. Lists the auto-hidden dock panels in the Ribbon End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.DockPanelCollection.AddRange(DevExpress.XtraBars.Docking.DockPanel[])">
      <summary>
        <para>Appends an array of dock panels to the collection.</para>
      </summary>
      <param name="panels">An array of <see cref="T:DevExpress.XtraBars.Docking.DockPanel"/> objects to append to the collection.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ErrorListDockPanel">
      <summary>
        <para>The Scripts Errors dock panel that displays the results of script validation in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ErrorListDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ErrorListDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ErrorListDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ErrorListDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object which represents the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value which represents the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ErrorListUserControl">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ErrorListUserControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ErrorListUserControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ErrorListUserControl.Model">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.FieldListDockPanel">
      <summary>
        <para>The Field List dock panel that displays the structure of a report’s data source in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.FieldListDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.FieldListDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.FieldListDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.FieldListDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object which represents the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value which represents the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.FieldListDockPanel.ShowNodeToolTips">
      <summary>
        <para>Gets or sets a value indicating whether to show node tooltips for end-users, for the purpose of  teaching them to use the Field List.</para>
      </summary>
      <value>true, to show node tooltips; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.FieldListDockPanel.ShowParametersNode">
      <summary>
        <para>Gets or sets a value indicating whether to show the Parameters node for end-users in the Field List.</para>
      </summary>
      <value>true to show the Parameters node; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.FieldListDockPanel.UpdateDataSource(System.ComponentModel.Design.IDesignerHost)">
      <summary>
        <para>Forcibly updates the structure of data sources currently displayed within this <see cref="T:DevExpress.XtraReports.UserDesigner.FieldListDockPanel"/> object.</para>
      </summary>
      <param name="designerHost">An object implementing the <see cref="T:System.ComponentModel.Design.IDesignerHost"/> interface that is used to manage designer transactions and components.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.GroupAndSortDockPanel">
      <summary>
        <para>The Group and Sort dock panel that enables managing groups and sorting data in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.GroupAndSortDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.GroupAndSortDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.GroupAndSortDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.GroupAndSortDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object which represents the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value which represents the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ICommandHandler">
      <summary>
        <para>If implemented by a class, provides methods for handling commands in an End-User Designer for Windows Forms.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ICommandHandler.CanHandleCommand(DevExpress.XtraReports.UserDesigner.ReportCommand,System.Boolean@)">
      <summary>
        <para>Indicates whether or not the specified End-User Designer command can be handled.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value that specifies the command.</param>
      <param name="useNextHandler">true to allow calling the next handler registered for this command; otherwise false.</param>
      <returns>true, if the command can be handled; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ICommandHandler.HandleCommand(DevExpress.XtraReports.UserDesigner.ReportCommand,System.Object[])">
      <summary>
        <para>Handles the specified End-User Designer command.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value which specifies the command to be handled.</param>
      <param name="args">A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.IDesignControl">
      <summary>
        <para>If implemented, provides the Design Panel functionality.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.IDesignControl.XRDesignPanel">
      <summary>
        <para>Specifies the current Design Panel instance.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.IDesignForm">
      <summary>
        <para>When implemented by a class, provides main methods for the End-User Report Designer form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.IDesignForm.DesignDockManager">
      <summary>
        <para>When implemented by a class, gets the docking panels’ container of the End-User Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.IDesignForm.DesignMdiController">
      <summary>
        <para>When implemented by a class, gets the MDI (multi-document interface) Controller associated with the design form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/> object that is the MDI Controller associated with the form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.IDesignForm.IsDisposed">
      <summary>
        <para>For internal use. Gets a value indicating whether the form has been disposed of.</para>
      </summary>
      <value>true if the form has been disposed of; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.IDesignForm.OpenReport(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Loads the specified report instance to the <see cref="T:DevExpress.XtraReports.UserDesigner.IDesignForm"/>.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.IDesignForm.OpenReport(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Loads the specified report instance to the <see cref="T:DevExpress.XtraReports.UserDesigner.IDesignForm"/> with the specified look and feel settings.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, which specifies the look and feel settings applied to the End-User Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.IDesignForm.SetWindowVisibility(DevExpress.XtraReports.UserDesigner.DesignDockPanelType,System.Boolean)">
      <summary>
        <para>If implemented by a class, sets the visibility of dock panels in the End-User Report Designer.</para>
      </summary>
      <param name="designDockPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value, identifying the dock panel(s) of the End-User Designer.</param>
      <param name="visible">true, to display the dock panel(s); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.IDesignForm.Show">
      <summary>
        <para>Shows the End-User Designer form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.IDesignForm.ShowDialog">
      <summary>
        <para>Shows the End-User Designer form, modally.</para>
      </summary>
      <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value.</returns>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.IPropertyGridIconsProvider">
      <summary>
        <para>When implemented, provides access to the Properties window’s tab icons collection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.IPropertyGridIconsProvider.Icons">
      <summary>
        <para>Provides access to the Properties window’s tab icons collection.</para>
      </summary>
      <value>A dictionary of IconName-IconImage items.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel">
      <summary>
        <para>The Properties window dock panel that enables modifying the properties of a report, its bands and controls in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object which represents the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value which represents the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel.ShowCategories">
      <summary>
        <para>Gets or sets a value indicating whether to show categories for the items in the Properties window, or show them in alphabetical order.</para>
      </summary>
      <value>true to show categories; false to sort alphabetically.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel.ShowDescription">
      <summary>
        <para>Gets or sets a value indicating whether to show the Description bar at the bottom of the Properties window.</para>
      </summary>
      <value>true to show the Description bar; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel.UseOfficeInspiredView">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.PropertyGridDockPanel.UseTabbedView">
      <summary>
        <para>Gets or sets whether the End-User Designer‘s Properties window shows properties organized in tabs.</para>
      </summary>
      <value>true, if the End-User Designer’s Properties window shows properties organized in tabs; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportCommand">
      <summary>
        <para>Specifies the commands which can be executed in the report’s End-User Designer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddCalculatedField">
      <summary>
        <para>Invokes the editor to add a new calculated field. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddGalleryFolder">
      <summary>
        <para>Adds a new folder to the selected category of the Report Gallery. This command is available in a category’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddNewDataSource">
      <summary>
        <para>Invokes the data source Wizard which allows end-users to provide data for a report, as it has been invoked by the Add New data source verb available in the XtraReport.DataSource property editor. Note that if you hide this command, an end-user will be unable to create a new data source for a report.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddParameter">
      <summary>
        <para>Invokes the Add New Parameter dialog that allows you to create a report parameter. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddToGallery">
      <summary>
        <para>Adds the selected element(s) to the Report Gallery. This command is available in the context menu of report controls, styles, data sources and a report.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddToReportComponents">
      <summary>
        <para>Adds the data source selected in the Report Gallery to the current report. This command is available in a data source’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AddToReportStyleSheet">
      <summary>
        <para>Adds a style selected in the Report Gallery to the current report’s style sheet. This command is available in a style’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignBottom">
      <summary>
        <para>Aligns the bottoms of the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignHorizontalCenters">
      <summary>
        <para>Aligns the centers of the selected controls horizontally. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignLeft">
      <summary>
        <para>Aligns the selected controls to the left. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignRight">
      <summary>
        <para>Aligns the selected controls to the right. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignToGrid">
      <summary>
        <para>Aligns the positions of the selected controls to the grid. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignTop">
      <summary>
        <para>Aligns the tops of the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.AlignVerticalCenters">
      <summary>
        <para>Aligns the centers of the selected controls vertically. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ApplyDataSource">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ApplyLayoutToBand">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ApplyLayoutToReport">
      <summary>
        <para>Applies a report layout selected in the Report Gallery to the current report. This command is available in a report layout’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BackColor">
      <summary>
        <para>Sets the background color of the selected control(s). This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BandCollapseAll">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BandCollapseAllButSelected">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BandExpandAll">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BandMoveDown">
      <summary>
        <para>Identifies the Move Down item of the context menu. This menu is invoked if an end-user right-clicks a band  of either the <see cref="F:DevExpress.XtraReports.UI.BandKind.GroupHeader"/>, <see cref="F:DevExpress.XtraReports.UI.BandKind.GroupFooter"/> or <see cref="F:DevExpress.XtraReports.UI.BandKind.DetailReport"/> kind. If you hide this command, then the Move Down menu item will be hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BandMoveUp">
      <summary>
        <para>Identifies the Move Up item of the context menu. This menu is invoked if an end-user right-clicks a band  of either the <see cref="F:DevExpress.XtraReports.UI.BandKind.GroupHeader"/>, <see cref="F:DevExpress.XtraReports.UI.BandKind.GroupFooter"/> or <see cref="F:DevExpress.XtraReports.UI.BandKind.DetailReport"/> kind. If you hide this command, then the Move Up menu item will be hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BarCodeAutoModule">
      <summary>
        <para>Enables/disables automatic calculation of the bar width depending on the bar code’s size. This command is available in the Bar Code‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BarCodeShowText">
      <summary>
        <para>Displays/hides accompanying text in a bar code. This command is available in the Bar Code‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BarCodeSymbology">
      <summary>
        <para>Sets the specified bar code symbology. This command is available in the Bar Code‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BindFieldToXRBarCode">
      <summary>
        <para>Identifies the XRBarCode item of the context menu. This menu is invoked after drag-and-dropping any Field List item with the right mouse click. If you hide this command, then the XRBarCode menu item will be hidden. Note that if you execute this command, it does nothing.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BindFieldToXRCheckBox">
      <summary>
        <para>Identifies the XRCheckBox item of the context menu. This menu is invoked after drag-and-dropping any Field List item with the right mouse click. If you hide this command, then the XRCheckBox menu item will be hidden. Note that if you execute this command, it does nothing.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BindFieldToXRLabel">
      <summary>
        <para>Identifies the XRLabel item of the context menu. This menu is invoked after drag-and-dropping any Field List item with the right mouse click. If you hide this command, then the XRLabel menu item will be hidden. Note that if you execute this command, it does nothing.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BindFieldToXRPictureBox">
      <summary>
        <para>Identifies the XRPictureBox item of the context menu. This menu is invoked after drag-and-dropping any Field List item with the right mouse click. If you hide this command, then the XRPictureBox menu item will be hidden. Note that if you execute this command, it does nothing.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BindFieldToXRRichText">
      <summary>
        <para>Identifies the XRRichText item of the context menu. This menu is invoked after drag-and-dropping any Field List item with the right mouse click. If you hide this command, then the XRRichText menu item will be hidden. Note that if you execute this command, it does nothing.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BindFieldToXRZipCode">
      <summary>
        <para>Identifies the XRZipCode item of the context menu. This menu is invoked after drag-and-dropping any Field List item with the right mouse click. If you hide this command, then the XRZipCode menu item will be hidden. Note that if you execute this command, it does nothing.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderBottom">
      <summary>
        <para>Adds the bottom border to the selected control(s). This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderColor">
      <summary>
        <para>Sets the specified border color. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderDashStyle">
      <summary>
        <para>Sets the specified border dash style. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderLeft">
      <summary>
        <para>Adds the left border to the selected control(s). This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderRight">
      <summary>
        <para>Adds the right border to the selected control(s). This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BordersAll">
      <summary>
        <para>Adds all borders to the selected control(s). This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BordersNone">
      <summary>
        <para>Removes borders from the selected control(s). This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderTop">
      <summary>
        <para>Adds the top border to the selected control(s). This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BorderWidth">
      <summary>
        <para>Sets the specified border width. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.BringToFront">
      <summary>
        <para>Brings the selected control(s) to the front. This command is represented via the menu item, toolbar button and context menu item.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CenterHorizontally">
      <summary>
        <para>Horizontally centers the selected controls within a band. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CenterVertically">
      <summary>
        <para>Vertically centers the selected controls within a band. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CharacterCombCellAutoHeight">
      <summary>
        <para>Enables/disables automatic adjusting of the character comb cell height depending on the current font size. This command is available in the Character Comb‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CharacterCombCellAutoWidth">
      <summary>
        <para>Enables/disables automatic adjusting of the character comb cell width depending on the current font size. This command is available in the Character Comb‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CharacterCombCellHeight">
      <summary>
        <para>Sets the specified character comb cell height. This command is available in the Character Comb‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CharacterCombCellHorizontalSpacing">
      <summary>
        <para>Sets the specified horizontal spacing between adjacent character comb cells. This command is available in the Character Comb‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CharacterCombCellVerticalSpacing">
      <summary>
        <para>Sets the specified vertical spacing between adjacent character comb cells. This command is available in the Character Comb‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CharacterCombCellWidth">
      <summary>
        <para>Sets the specified character comb cell width. This command is available in the Character Comb‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddDataSource">
      <summary>
        <para>Invokes the Data Source Wizard to set up a data source for a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddImageAnnotation">
      <summary>
        <para>Adds an image annotation to a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewArea">
      <summary>
        <para>Adds an area series to a chart to display values as a filled area with peaks and hollows. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewBar">
      <summary>
        <para>Adds a bar series to a chart to display values as vertical columns grouped by categories. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewLine">
      <summary>
        <para>Adds a line series to a chart to show line trends over time or categories. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewOther">
      <summary>
        <para>Adds the specified series to a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewPieAndDoughnut">
      <summary>
        <para>Adds a series to a chart to display the percentage values of different point arguments to compare their significance. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewRadarAndPolar">
      <summary>
        <para>Adds a series to a chart to display values as a circular graph. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddSeriesViewRange">
      <summary>
        <para>Adds a series to a chart to display a range of values with the minimum and maximum limits. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAddTextAnnotation">
      <summary>
        <para>Adds a text annotation to a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartAppearanceName">
      <summary>
        <para>Sets the specified appearance name for a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartEditAnnotations">
      <summary>
        <para>Invokes the Annotation Collection Editor that allows managing a chart’s text and image annotations. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartEditPalettes">
      <summary>
        <para>Invokes the Palettes Collection Editor that allows managing chart palettes. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartEditSeries">
      <summary>
        <para>Invokes the Series Collection Editor that allows managing chart series. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartLoad">
      <summary>
        <para>Invokes the Open dialog that allows you to load a chart from an XML file. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartPaletteName">
      <summary>
        <para>Sets the specified palette for painting a chart’s series. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartRemoveAnnotation">
      <summary>
        <para>Removes the specified annotation from a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartRemoveSeries">
      <summary>
        <para>Removes the specified series from a chart. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartRunDesigner">
      <summary>
        <para>Runs the Chart Designer that allows creating and editing properties of a chart and its elements. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ChartSave">
      <summary>
        <para>Invokes the Save dialog that allows you to save a chart to an XML file. This command is available in the Chart‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CheckIn">
      <summary>
        <para>For use only by Report and Dashboard Server.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Close">
      <summary>
        <para>Closes the active Design Panel in an MDI End-User Designer form.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Closing">
      <summary>
        <para>This command should be handled when overriding saving in the End-User Designer. Note that you don’t need to either change its visibility or execute it, otherwise the result may be unpredictable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Copy">
      <summary>
        <para>Copies the selected control(s) to the clipboard. This command is represented via the menu item, toolbar button and context menu item.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CopyGalleryItem">
      <summary>
        <para>Copies the selected Report Gallery item to the clipboard. This command is available in the item’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Cut">
      <summary>
        <para>Deletes the currently selected control(s) and copies it to the clipboard. This command is represented via the menu item, toolbar button and context menu item.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.CutGalleryItem">
      <summary>
        <para>Deletes the selected Report Gallery item and copies it to the clipboard. This command is available in the item’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Delete">
      <summary>
        <para>Deletes the currently selected control(s). This command is represented via the menu item and context menu item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.DeleteGalleryItem">
      <summary>
        <para>Deletes the selected Report Gallery item. This command is available in the item’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.DrawGridLines">
      <summary>
        <para>Shows/hides gridlines on the report surface. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.DrawWatermark">
      <summary>
        <para>Shows/hides the document’s watermark on the report surface. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.EditCalculatedFields">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.EditFavoriteProperties">
      <summary>
        <para>Invokes the Favorite Properties Editor that allows editing favorite properties. This command is available in the Properties window’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.EditParameters">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.EditStyles">
      <summary>
        <para>Invokes the Styles Editor that allows managing report styles. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Exit">
      <summary>
        <para>Closes the form containing the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> control which executes this command. Note that if you hide this command, then all menu items and toolbar buttons associated with it will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ExtractStyle">
      <summary>
        <para>Creates a new style based on the specified control’s appearance settings. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FitBoundsToContainer">
      <summary>
        <para>Adjusts the control’s size to fit its parent container. This command is available in the control context menu and in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FitBoundsToText">
      <summary>
        <para>Adjusts the control’s size to fit its text. This command is available in the context menu of <see cref="T:DevExpress.XtraReports.UI.XRLabel"/> and <see cref="T:DevExpress.XtraReports.UI.XRCharacterComb"/> and in the ribbon’s Text contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FitTextToBounds">
      <summary>
        <para>Adjusts the control’s font size to fit the control boundaries. This command is available in the context menu of <see cref="T:DevExpress.XtraReports.UI.XRLabel"/> and <see cref="T:DevExpress.XtraReports.UI.XRTableCell"/> and in the ribbon’s Text contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FontBold">
      <summary>
        <para>Makes the font bold. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FontItalic">
      <summary>
        <para>Makes the font italic. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FontName">
      <summary>
        <para>Sets the specified font name to the <see cref="P:DevExpress.XtraReports.UI.XRControl.Font"/> property of the currently selected control(s), as it has been changed by the Font Name edit box which is shown in the Formatting Toolbar. Note that if you hide this command, then the Font Name edit box will be unavailable.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FontSize">
      <summary>
        <para>Sets the specified font size to the <see cref="P:DevExpress.XtraReports.UI.XRControl.Font"/> property of the currently selected control(s), as it has been changed by the Font Size edit box which is shown in the Formatting Toolbar. Note that if you hide this command, then the Font Size edit box will be unavailable.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FontStrikeout">
      <summary>
        <para>Crosses the text out by drawing a line through it. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.FontUnderline">
      <summary>
        <para>Underlines the font. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ForeColor">
      <summary>
        <para>Sets the foreground color of the selected control(s). This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.GaugeViewThemeDark">
      <summary>
        <para>Sets the Flat Dark color theme for a gauge. This command is available in the Gauge‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.GaugeViewThemeLight">
      <summary>
        <para>Sets the Flat Light color theme for a gauge. This command is available in the Gauge‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.GaugeViewTypeStyle">
      <summary>
        <para>Sets the specified view style for a gauge. This command is available in the Gauge‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HorizSpaceConcatenate">
      <summary>
        <para>Removes the horizontal spacing between the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HorizSpaceDecrease">
      <summary>
        <para>Decreases the horizontal spacing between the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HorizSpaceIncrease">
      <summary>
        <para>Increases the horizontal spacing between the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HorizSpaceMakeEqual">
      <summary>
        <para>Makes the horizontal spacing between the selected controls equal. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HtmlBackward">
      <summary>
        <para>Moves the HTML browser backward to the previous page. This command is represented on the HTML View Ribbon page of the End-User Designer with a Ribbon only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HtmlFind">
      <summary>
        <para>Finds the text on the HTML page. This command is represented on the HTML View Ribbon page of the End-User Designer with a Ribbon only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HtmlForward">
      <summary>
        <para>Moves the HTML browser forward to the previous page. This command is represented on the HTML View Ribbon page of the End-User Designer with a Ribbon only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HtmlHome">
      <summary>
        <para>Displays the Home page in the HTML browser. This command is represented on the HTML View Ribbon page of the End-User Designer with a Ribbon only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.HtmlRefresh">
      <summary>
        <para>Refreshes the current page in the HTML browser. This command is represented on the HTML View Ribbon page of the End-User Designer with a Ribbon only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertBottomMarginBand">
      <summary>
        <para>Inserts the Bottom Margin band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertDetailBand">
      <summary>
        <para>Inserts the Detail band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertDetailReport">
      <summary>
        <para>Inserts the Detail Report band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertGroupFooterBand">
      <summary>
        <para>Inserts the Group Footer band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertGroupHeaderBand">
      <summary>
        <para>Inserts the Group Header band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertPageFooterBand">
      <summary>
        <para>Inserts the Page Footer band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertPageHeaderBand">
      <summary>
        <para>Inserts the Page Header band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertReportFooterBand">
      <summary>
        <para>Inserts the Report Footer band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertReportHeaderBand">
      <summary>
        <para>Inserts the Report Header band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertSubBand">
      <summary>
        <para>Inserts a SubBand into a report. This command is available from the context menu only. It is not available in <see cref="T:DevExpress.XtraReports.UI.TopMarginBand"/>, <see cref="T:DevExpress.XtraReports.UI.BottomMarginBand"/> and <see cref="T:DevExpress.XtraReports.UI.VerticalBand"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertTopMarginBand">
      <summary>
        <para>Inserts the Top Margin band into a report. This command is represented via the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertVerticalDetailBand">
      <summary>
        <para>Inserts the Vertical Detail band into a report. This command is available in the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertVerticalHeaderBand">
      <summary>
        <para>Inserts the Vertical Header band into a report. This command is available in the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.InsertVerticalTotalBand">
      <summary>
        <para>Inserts the Vertical Total band into a report. This command is available in the context menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.JustifyCenter">
      <summary>
        <para>Aligns the control’s text to the center. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.JustifyJustify">
      <summary>
        <para>Justifies the control’s text. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.JustifyLeft">
      <summary>
        <para>Aligns the control’s text to the left. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.JustifyRight">
      <summary>
        <para>Aligns the control’s text to the right. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyCancel">
      <summary>
        <para>Performs the Cancel action, the same as the one which should be performed if an end-user has pressed the Cancel key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyDefaultAction">
      <summary>
        <para>Performs the Default action, the same as the one which should be performed if an end-user has pressed the Enter key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyMoveDown">
      <summary>
        <para>Moves the selected control(s) to the bottom as they were moved by the Down Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Down Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyMoveLeft">
      <summary>
        <para>Moves the selected control(s) to the left as they were moved by the Left Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Left Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyMoveRight">
      <summary>
        <para>Moves the selected control(s) to the right as they were moved by the Right Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Right Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyMoveUp">
      <summary>
        <para>Moves the selected control(s) to the top as they were moved by the Up Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Up Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeDown">
      <summary>
        <para>Moves the selected control(s) to the bottom by one report unit as they have been moved using the Ctrl+Down Arrow keys. Note that if you hide this command, an end-user will still be able to move controls by the Ctrl+Down Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeHeightDecrease">
      <summary>
        <para>Decreases the height of the selected control(s) by one report unit as it has been decreased using the Ctrl+Shift+Up Arrow keys. Note that if you hide this command, an end-user will still be able to decrease the controls height by the Ctrl+Shift+Up Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeHeightIncrease">
      <summary>
        <para>Increases the height of the selected control(s) by one report unit as it has been increased using the Ctrl+Shift+Down Arrow keys. Note that if you hide this command, an end-user will still be able to increase the controls height by the Ctrl+Shift+Down Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeLeft">
      <summary>
        <para>Moves the selected control(s) to the left by one report unit as they have been moved using the Ctrl+Left Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Ctrl+Left Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeRight">
      <summary>
        <para>Moves the selected control(s) to the right by one report unit as they have been moved using the Ctrl+Right Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Ctrl+Right Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeUp">
      <summary>
        <para>Moves the selected control(s) to the top by one report unit as they have been moved using the Ctrl+Up Arrow key. Note that if you hide this command, an end-user will still be able to move controls by the Ctrl+Up Arrow key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeWidthDecrease">
      <summary>
        <para>Decreases the width of the selected control(s) by one report unit as it has been decreased using the Ctrl+Shift+Left Arrow keys. Note that if you hide this command, an end-user will still be able to decrease the controls width by the Ctrl+Shift+Left Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeyNudgeWidthIncrease">
      <summary>
        <para>Increases the width of the selected control(s) by one report unit as it has been increased using the Ctrl+Shift+Right Arrow keys. Note that if you hide this command, an end-user will still be able to increase the controls width by the Ctrl+Shift+Right Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeySelectNext">
      <summary>
        <para>Selects a control which is next to the currently selected control in the tab order, as it was selected by the Tab key. Note that if you hide this command, an end-user will still be able to move the controls selection by the Tab key.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeySelectPrevious">
      <summary>
        <para>Selects a control which is previous to the currently selected control in the tab order, as it was selected by the Shift+Tab keys. Note that if you hide this command, an end-user will still be able to move the controls selection by the Shift+Tab keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeySizeHeightDecrease">
      <summary>
        <para>Decreases the selected control’s height when users press the Shift+Up Arrow keys. The decrease depends on the report’s <see cref="P:DevExpress.XtraReports.UI.XtraReport.SnappingMode"/> property value: * None - decreases the height by the <see cref="P:DevExpress.XtraReports.UI.XtraReport.ReportUnit"/> value; * SnapLines - decreases the height to the nearest snap position; * SnapGrid - decreases the height to the nearest grid line; * SnapToGridAndSnapLines - decreases the height to the nearest grid line or snap position. If you hide this command, users can still decrease the controls height with the Shift+Up Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeySizeHeightIncrease">
      <summary>
        <para>Increases the selected control’s height when users press the Shift+Down Arrow keys. The increase depends on the report’s <see cref="P:DevExpress.XtraReports.UI.XtraReport.SnappingMode"/> property value: * None - increases the height by the <see cref="P:DevExpress.XtraReports.UI.XtraReport.ReportUnit"/> value; * SnapLines - increases the height to the nearest snap position; * SnapGrid - increases the height to the nearest grid line; * SnapToGridAndSnapLines - increases the height to the nearest grid line or snap position. If you hide this command, users can still increase the controls height with the Shift+Down Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeySizeWidthDecrease">
      <summary>
        <para>Decreases the selected control’s width when users press the Shift+Left Arrow keys. The decrease depends on the report’s <see cref="P:DevExpress.XtraReports.UI.XtraReport.SnappingMode"/> property value: * None - decreases the width by the <see cref="P:DevExpress.XtraReports.UI.XtraReport.ReportUnit"/> value; * SnapLines - decreases the width to the nearest snap position; * SnapGrid - decreases the width to the nearest grid line; * SnapToGridAndSnapLines - decreases the width to the nearest grid line or snap position. If you hide this command, users can still decrease the controls’ width with the Shift+Left Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.KeySizeWidthIncrease">
      <summary>
        <para>Increases the selected control’s width when users press the Shift+Right Arrow keys. The increase depends on the report’s <see cref="P:DevExpress.XtraReports.UI.XtraReport.SnappingMode"/> property value: * None - increases the width by the <see cref="P:DevExpress.XtraReports.UI.XtraReport.ReportUnit"/> value; * SnapLines - increases the width to the nearest snap position; * SnapGrid - increases the width to the nearest grid line; * SnapToGridAndSnapLines - increases the width to the nearest grid line or snap position. If you hide this command, users can still increase the controls’ width with the Shift+Right Arrow keys.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.LabelAutoWidth">
      <summary>
        <para>Enables/disables the selected control(s) to adjust their width to fit their content. This command is available in the ribbon’s Text contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.LabelCanGrow">
      <summary>
        <para>Enables/disables the selected controls to increase their height to fit their content. This command is available in the ribbon’s Text contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.LabelCanShrink">
      <summary>
        <para>Enables/disables the selected control(s) to decrease their height to fit their content. This command is available in the ribbon’s Text contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.LabelsConvertToTable">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.LabelWordWrap">
      <summary>
        <para>Enables/disables the selected control(s) to wrap their text if it does not fit a line. This command is available in the ribbon’s Text contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Language">
      <summary>
        <para>Sets the report locale (language) for localization.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.LoadGallery">
      <summary>
        <para>Loads Report Gallery templates from the selected file. This command is available in a Report Gallery’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Logout">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.MdiCascade">
      <summary>
        <para>When the window interface is enabled in an MDI End-User Designer form, arranges Design Panel windows in cascade.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.MdiTileHorizontal">
      <summary>
        <para>When the window interface is enabled in an MDI End-User Designer form, tiles Design Panel windows horizontally.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.MdiTileVertical">
      <summary>
        <para>When the window interface is enabled in an MDI End-User Designer form, tiles Design Panel windows vertically.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.MoveGalleryItem">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.NavigateToControl">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.NewReport">
      <summary>
        <para>Creates a new blank report. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.NewReportWizard">
      <summary>
        <para>Creates a new blank report and runs the XtraReports Wizard to customize it. This command is represented via the menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.None">
      <summary>
        <para>Doesn’t identify any command. This member is intended for internal use only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.OpenFile">
      <summary>
        <para>Opens a report. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.OpenRemoteReport">
      <summary>
        <para>For use only by Report and Dashboard Server.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.OpenSubreport">
      <summary>
        <para>For use only by Report and Dashboard Server.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PageColor">
      <summary>
        <para>Sets the specified background color for report pages. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PageMargins">
      <summary>
        <para>Sets the specified margin sizes for a report. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PageOrientation">
      <summary>
        <para>Sets the portrait or landscape page layout. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PageSetup">
      <summary>
        <para>Invokes the Page Setup dialog that allows customizing the paper size and page margins. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PageSize">
      <summary>
        <para>Sets the specified paper size for a report. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PageWatermark">
      <summary>
        <para>Inserts a ghost text or image behind the page content. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Paste">
      <summary>
        <para>Adds the control(s) from the clipboard to the report. This command is represented via the menu item, toolbar button and context menu item.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PasteGalleryItem">
      <summary>
        <para>Adds the item from the clipboard to the Report Gallery. This command is available in a node’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridAddDataSource">
      <summary>
        <para>Runs the Data Source Wizard that allows you to set up a data source for a Pivot Grid. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridAddField">
      <summary>
        <para>Adds a new Pivot Grid field to the specified header area. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridColumnAreaOnEveryPage">
      <summary>
        <para>Prints column headers on every page. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridColumnHeaders">
      <summary>
        <para>Prints/hides column field headers. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridDataHeaders">
      <summary>
        <para>Prints/hides data field headers. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridHorizontalLines">
      <summary>
        <para>Prints/hides horizontal grid lines. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridRemoveField">
      <summary>
        <para>Removes the selected Pivot Grid field. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridRowAreaOnEveryPage">
      <summary>
        <para>Prints row headers on every page. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridRowHeaders">
      <summary>
        <para>Prints/hides row field headers. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridRunDesigner">
      <summary>
        <para>Runs the Pivot Grid Designer that allows customizing fields, the control’s layout, appearance settings and printing options. This command is available in the Pivot Grid)’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PivotGridVerticalLines">
      <summary>
        <para>Prints/hides vertical grid lines. This command is available in the Pivot Grid’s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.PropertiesWindow">
      <summary>
        <para>Invokes the Properties window for the currently selected control(s). This command is represented via the context menu item only.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Redo">
      <summary>
        <para>Redoes the last operation which has been previously undone by the Undo command. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.RefreshGallery">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.RemoveAllCalculatedFields">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.RemoveAllParameters">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.RenameGalleryItem">
      <summary>
        <para>Renames the selected Report Gallery item. This command is available in the item’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.RevertToRevision">
      <summary>
        <para>For use only by Report and Dashboard Server.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SaveAll">
      <summary>
        <para>Saves all existing reports (which are shown in all Design Panels) to files. This command is represented via both the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SaveFile">
      <summary>
        <para>Saves the report (which is shown in the currently active Design Panel) to a file. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SaveFileAs">
      <summary>
        <para>Invokes the Save As dialog to save a report with a new name. This command is represented via the menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SaveGalleryAs">
      <summary>
        <para>Saves Report Gallery templates to the specified file. This command is available in a Report Gallery’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ScriptsSelectControl">
      <summary>
        <para>Selects a control for specifying an event. This command is available in the Script Editor‘s ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ScriptsSelectEvent">
      <summary>
        <para>Selects the specified events. This command is available in the Script Editor‘s ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ScriptsValidate">
      <summary>
        <para>Checks whether report scripts contain errors. This command is available in the Script Editor‘s ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SelectAll">
      <summary>
        <para>Selects all the controls in the report. This command is represented via the menu item only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SendToBack">
      <summary>
        <para>Moves the selected control(s) to the back. This command is represented via the menu item, toolbar button and context menu item.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShapeStretch">
      <summary>
        <para>Stretches a shape to fill its entire area when it is rotated. This command is available in the Shape‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShapeType">
      <summary>
        <para>Sets the specified shape type. This command is available in the Shape‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowDesignerTab">
      <summary>
        <para>Switches the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> to its Designer tab, as it has been switched via the Designer button at the bottom of the design surface. Note that if you hide this command, this button will be unavailable.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowExportWarnings">
      <summary>
        <para>Highlights intersecting report controls to warn you about the possibility of corrupting the document layout when exporting the document to specific formats.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowHTMLViewTab">
      <summary>
        <para>Switches the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> to its HTML View tab, as it has been switched via the HTML View button at the bottom of the design surface. Note that if you hide this command, this button will be unavailable.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowLocalizableProperties">
      <summary>
        <para>Filters the Properties panel to display the localizable properties.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowPreviewTab">
      <summary>
        <para>Switches the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> to its Preview tab, as it has been switched via the Preview button at the bottom of the design surface. Note that if you hide this command, this button will be unavailable.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowPrintingWarnings">
      <summary>
        <para>Highlights report controls that overrun the right page margin to warn you about extra pages when printing the document.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowReportDesignAnalyzer">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowScriptsTab">
      <summary>
        <para>Switches the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> to its Scripts tab, as it has been switched via the Designer button at the bottom of the design surface. Note that if you hide this command, this button will be unavailable.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowTabbedInterface">
      <summary>
        <para>Enables the tabbed interface for Design Panels in an MDI End-User Designer form.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ShowWindowInterface">
      <summary>
        <para>Enables the window interface for Design Panels in an MDI End-User Designer form.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SizeToControl">
      <summary>
        <para>Makes the selected controls to be of the same size. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SizeToControlHeight">
      <summary>
        <para>Makes the selected controls have the same height. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SizeToControlWidth">
      <summary>
        <para>Makes the selected controls have the same width. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SizeToGrid">
      <summary>
        <para>Sizes the selected controls to the grid. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SnapLines">
      <summary>
        <para>Enables/disables snapping using snap lines. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SnapToGrid">
      <summary>
        <para>Enables/disables snapping to the snap grid. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SparklineAddDataSource">
      <summary>
        <para>Runs the Data Source Wizard that allows you to set up a data source for a sparkline. This command is available in the Sparkline‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.SparklineView">
      <summary>
        <para>Sets the specified sprakline view. This command is available in the Sparkline‘s ribbon contextual tab.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.StyleName">
      <summary>
        <para>Sets the specified style name. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableConvertToLabels">
      <summary>
        <para>Converts the selected <see cref="T:DevExpress.XtraReports.UI.XRTableCell"/> objects to <see cref="T:DevExpress.XtraReports.UI.XRLabel"/> controls.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableDeleteCell">
      <summary>
        <para>Deletes the currently selected table cell. This command is available in the table cell’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableDeleteColumn">
      <summary>
        <para>Deletes the currently selected table column. This command is available in the table element’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableDeleteRow">
      <summary>
        <para>Deletes the currently selected table row. This command is available in the table element’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableDeleteTable">
      <summary>
        <para>Deletes the entire table. This command is available in the table cell’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableDistributeColumnsEvenly">
      <summary>
        <para>Distributes selected table columns so that they have the same size. This command is available in the context menu of a table, rows and multiple selected table elements as well as in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableDistributeRowsEvenly">
      <summary>
        <para>Distributes selected table rows so that they have the same size. This command is available in the context menu of a table, rows and multiple selected table elements as well as in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableInsertCell">
      <summary>
        <para>Inserts a table cell to the right of the currently selected cell. This command is available in the table cell’s context menu.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableInsertColumnToLeft">
      <summary>
        <para>Adds a new column directly to the left of the current column. This command is available in the table element’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableInsertColumnToRight">
      <summary>
        <para>Adds a new column directly to the right of the current column. This command is available in the table element’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableInsertRowAbove">
      <summary>
        <para>Adds a new row directly above the current row. This command is available in the table element’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableInsertRowBelow">
      <summary>
        <para>Adds a new row directly below the current row. This command is available in the table element’s context menu and in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableMergeCells">
      <summary>
        <para>Merges multiple selected cells into one cell. This command is available in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableSelectColumn">
      <summary>
        <para>Selects the current column. This command is available in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableSelectRow">
      <summary>
        <para>Selects the current row. This command is available in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableSelectTable">
      <summary>
        <para>Selects the entire table. This command is available in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.TableSplitCells">
      <summary>
        <para>Splits the selected cells into the specified number of rows or columns. This command is available in the Table‘s ribbon contextual tabs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Undo">
      <summary>
        <para>Undoes the last operation. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.UndoCheckOut">
      <summary>
        <para>For use only by Report and Dashboard Server.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.UploadNewRemoteReport">
      <summary>
        <para>For use only by Report and Dashboard Server.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbConvertToExpressions">
      <summary>
        <para>Invokes the dialog that converts a report to use expression bindings instead of the legacy data bindings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbEditBands">
      <summary>
        <para>Invokes the Report Editor dialog, as though it has been invoked by the “Edit and Reorder Bands” context link. Note that if you hide this command, then this verb will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbEditBindings">
      <summary>
        <para>Invokes the Edit Bindings dialog, as though it has been invoked by the “Edit Bindings…” context link. Note that if you hide this command, then this verb will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbEditLocalization">
      <summary>
        <para>Invokes the Localization Editor.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbEditText">
      <summary>
        <para>Invokes the in-place editor for the currently selected control, as though it has been invoked by the “Edit Text” context link. Note that if you hide this command, then this verb will become unavailable, but an end-user will still be able to invoke the in-place editor by double-clicking the control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbExecute">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbExport">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbImport">
      <summary>
        <para>Runs the Import dialog.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbLoadReportTemplate">
      <summary>
        <para>Invokes the Report Templates dialog, as though it has been invoked by the “Load Report Template…” context link. Note that if you hide this command, then this verb will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbPivotGridDesigner">
      <summary>
        <para>Invokes the Designer for the currently selected <see cref="T:DevExpress.XtraReports.UI.XRPivotGrid"/> control, as though it has been invoked by the “Run Designer…” context link. Note that if you hide this command, then this verb will become unavailable for all pivot grid controls.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbReportWizard">
      <summary>
        <para>Invokes the XtraReports Wizard for the current report, as though it has been invoked by the “Design in Report Wizard…” context link. Note that if you hide this command, then this verb will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbRtfClear">
      <summary>
        <para>Clears the RTF contents of the currently selected control (if it is the <see cref="T:DevExpress.XtraReports.UI.XRRichText"/> one), as though it has been cleared by the “Clear” context link. Note that if you hide this command, then this verb will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbRtfLoadFile">
      <summary>
        <para>Invokes the Open File dialog for the currently selected control (if it is the <see cref="T:DevExpress.XtraReports.UI.XRRichText"/> one) to load RTF contents, as though it has been invoked by the “Load File…” context link. Note that if you hide this command, then this verb will become unavailable.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VerbRtfSaveFile">
      <summary />
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertAlignBottom">
      <summary>
        <para>Aligns text to the bottom. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertAlignMiddle">
      <summary>
        <para>Centers text between the top and bottom. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertAlignTop">
      <summary>
        <para>Aligns text to the top. This command is available in the ribbon toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertSpaceConcatenate">
      <summary>
        <para>Removes the vertical spacing between the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertSpaceDecrease">
      <summary>
        <para>Decreases the vertical spacing between the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertSpaceIncrease">
      <summary>
        <para>Increases the vertical spacing between the selected controls. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.VertSpaceMakeEqual">
      <summary>
        <para>Makes the vertical spacing between the selected controls equal. This command is represented both via the menu item and toolbar button.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.Zoom">
      <summary>
        <para>Sets the specified zoom value and zooms the report’s design surface in or out. The predefined zoom factors are: 50%, 100%, 150%, 200%, 300%, 400% and 800%. The maximum value is 800%.</para>
        <para>Note, if you hide this command, the Zoom edit box will be invisible.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ZoomIn">
      <summary>
        <para>Zooms the report’s design surface in. Each time this command is performed, the preview gets zoomed in by 10%.</para>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportCommand.ZoomOut">
      <summary>
        <para>Zooms the report’s design surface out. Each time this command is performed, the preview gets zoomed out by 10%.</para>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportExplorerDockPanel">
      <summary>
        <para>The Report Explorer dock panel that displays the structure of a report in a tree-like form in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportExplorerDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportExplorerDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportExplorerDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportExplorerDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object which represents the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value which represents the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportExpressionEditorCustomizationService">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportExpressionEditorCustomizationService.BeforeFilterEditorRun(System.String,DevExpress.Data.Controls.ExpressionEditor.ExpressionEditorContext)">
      <summary />
      <param name="expressionString"></param>
      <param name="expressionEditorContext"></param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportExpressionEditorCustomizationService.BeforeRun(System.String,DevExpress.Data.Controls.ExpressionEditor.IExpressionEditorView,DevExpress.Data.Controls.ExpressionEditor.ExpressionEditorContext)">
      <summary />
      <param name="expressionString"></param>
      <param name="expressionEditorView"></param>
      <param name="expressionEditorContext"></param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportGalleryDockPanel">
      <summary>
        <para>The Report Gallery dock panel that allows you to store common report controls, styles, data sources and full report layouts, and re-use them in different reports.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportGalleryDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportGalleryDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportGalleryDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportGalleryDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object that specifies the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value that specifies the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ReportGalleryDockPanel.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object to which the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> that owns the current dock panel belongs.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object to which the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> belongs.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportSettings">
      <summary>
        <para>Contains report page settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportSettings.#ctor(System.Func{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportSettings"/> class with the specified MDI controller’s loading state.</para>
      </summary>
      <param name="isLoading">true if the MDI controller is loading; otherwise false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportState">
      <summary>
        <para>Specifies the state of the report in the End-User Designer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportState.Changed">
      <summary>
        <para>At least one component in the report has been modified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportState.Closing">
      <summary>
        <para>The report editing session is being closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportState.None">
      <summary>
        <para>The report’s state is not specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportState.Opened">
      <summary>
        <para>The report is open in the End-User Designer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportState.Opening">
      <summary>
        <para>The report is in the process of opening.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ReportState.Saved">
      <summary>
        <para>The report has been saved to a file.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportStateEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.ReportStateChanged"/> and <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportStateChanged"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ReportStateEventArgs.#ctor(DevExpress.XtraReports.UserDesigner.ReportState)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportStateEventArgs"/> class with the specified report state.</para>
      </summary>
      <param name="reportState">One of the <see cref="T:DevExpress.XtraReports.UserDesigner.ReportState"/> enumeration’s values specifying the state of the report.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ReportStateEventArgs.ReportState">
      <summary>
        <para>Gets the report’s state for the event handler.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportState"/> value representing the report’s state for this event handler.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ReportStateEventHandler">
      <summary>
        <para>Represents the method that will handle the <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.ReportStateChanged"/> and <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportStateChanged"/> events.</para>
      </summary>
      <param name="sender">An object of any type that triggers the <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.ReportStateChanged"/> or <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportStateChanged"/> event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportStateEventArgs"/> object that provides data for the <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.ReportStateChanged"/> and <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportStateChanged"/> events.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel">
      <summary>
        <para>The Toolbox dock panel in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel.#ctor(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraBars.Docking.DockingStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel"/> class with the specified docking style and owner.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object which represents the owner of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.DockManager"/> property.</param>
      <param name="dock">A <see cref="T:DevExpress.XtraBars.Docking.DockingStyle"/> enumeration value which represents the docking style of the created object. This value is assigned to the <see cref="P:DevExpress.XtraBars.Docking.DockPanel.Dock"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel.GroupsStyle">
      <summary>
        <para>Gets or sets the style for presenting the groups’ contents.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraNavBar.NavBarGroupStyle"/> value specifying how the groups’ contents are presented.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ToolBoxDockPanel.PaintStyleName">
      <summary>
        <para>Gets or sets the name of the paint style applied to the Tool Box.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the name of the paint style applied to the Tool Box.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ToolBoxExpandButton">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ToolBoxExpandButton.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ToolBoxExpandButton"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ToolBoxExpandButton.IsCollapsed">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ToolboxType">
      <summary>
        <para>Specifies the Toolbox type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ToolboxType.Custom">
      <summary>
        <para>A custom Toolbox.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ToolboxType.None">
      <summary>
        <para>The object is not used as a Toolbox.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraReports.UserDesigner.ToolboxType.Standard">
      <summary>
        <para>The standard Toolbox.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.TreeViewDesignDockPanel">
      <summary>
        <para>The base class for tree-like dock panels of the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.TreeViewDesignDockPanel.CollapseAll">
      <summary>
        <para>Collapses all the nodes in the tree view dock panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.TreeViewDesignDockPanel.ExpandAll">
      <summary>
        <para>Expands all the nodes in the tree view dock panel.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.TypedDesignDockPanel">
      <summary>
        <para>The base class for the dock panels of the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.TypedDesignDockPanel.DesignControl">
      <summary>
        <para>Provides access to the Design Panel associated with the current dock panel.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.IDesignControl"/> interface.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.TypedDesignDockPanel.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, to which the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> that owns the current dock panel belongs.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, to which the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> belongs.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.ValidateCustomSql"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs.#ctor(DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="connectionParameters">A <see cref="T:DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase"/> object containing settings used to establish a data connection.</param>
      <param name="sql">A string containing the SQL query to validate.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs.ConnectionParameters">
      <summary>
        <para>Provides access to settings used to establish the current data connection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.ConnectionParameters.DataConnectionParametersBase"/> object containing settings used to establish a data connection.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs.Message">
      <summary>
        <para>Specifies the error message to display if query validation fails.</para>
      </summary>
      <value>A string specifying the text of the error message.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs.Sql">
      <summary>
        <para>Contains the SQL query to validate.</para>
      </summary>
      <value>A string containing the SQL query to validate.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.ValidateSqlEventArgs.Valid">
      <summary>
        <para>Specifies whether the query is valid.</para>
      </summary>
      <value>true, if the query is valid; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager">
      <summary>
        <para>Provides bars to the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/> class with the specified container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.BarInfos">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.EndInit">
      <summary>
        <para>Notifies the control that initialization has been completed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.FontNameBox">
      <summary>
        <para>Gets or sets the pop-up box which is used to specify a font name.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemComboBox"/> object which represents the pop-up box used to select a font name.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.FontNameEdit">
      <summary>
        <para>Gets or sets the editor which is used to specify a font name.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.BarEditItem"/> object which represents the edtitor used to select a font name.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.FontSizeBox">
      <summary>
        <para>Gets or sets the pop-up box which is used to specify a font size.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemComboBox"/> object which represents the pop-up box used to select a font size.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.FontSizeEdit">
      <summary>
        <para>Gets or sets the editor which is used to specify a font size.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.BarEditItem"/> object which represents the edtitor used to select a font size.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.FormattingToolbar">
      <summary>
        <para>For internal use. Gets or sets the Formatting Toolbar controlled by this bar manager.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Bar"/> object, which specifies the Formatting Toolbar.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.GetBarItemByCommand(System.ComponentModel.Design.CommandID)">
      <summary>
        <para>Gets a bar item within the bar manager by its command.</para>
      </summary>
      <param name="commandID">A <see cref="T:System.ComponentModel.Design.CommandID"/> object which specifies the command to be executed by the bar item to find.</param>
      <returns>A DevExpress.XtraReports.UserDesigner.CommandBarItem object that represents the bar item which executes the specified command.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.GetBarItemsByReportCommand(DevExpress.XtraReports.UserDesigner.ReportCommand)">
      <summary>
        <para>Gets an array of bar items within the bar manager by their report command.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value that specifies the command assigned to the bar items being sought.</param>
      <returns>An array of <see cref="T:DevExpress.XtraBars.BarItem"/> objects that represent the bar items which are associated with the specified report command.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.GetToolboxType(DevExpress.XtraBars.Bar)">
      <summary>
        <para>Gets the Toolbox type used by the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/>.</para>
      </summary>
      <param name="bar">A <see cref="T:DevExpress.XtraReports.UserDesigner.ToolboxType"/> enumeration value.</param>
      <returns>A <see cref="T:DevExpress.XtraBars.Bar"/> object, representing the Toolbox.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.HintStaticItem">
      <summary>
        <para>Gets or sets the object which is used to show hints in the design bar manager.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.BarStaticItem"/> object which represents the static hint item.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.Images">
      <summary>
        <para>Gets the source of images that can be displayed within items.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Images"/> object which provides images for bar items.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.ImageStream">
      <summary>
        <para>Gets or sets a stream which contains the images used to display Design Bar Manager items.</para>
      </summary>
      <value>A DevExpress.Utils.ImageCollectionStreamer object which represents the stream of images.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.Initialize(DevExpress.XtraReports.UserDesigner.XRDesignPanel)">
      <summary>
        <para>Performs basic initialization of the created <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/> object, and assigns the specified Design Panel to it.</para>
      </summary>
      <param name="designPanel">An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object which specifies the Design Panel to assign.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.LargeImages">
      <summary>
        <para>Overrides the <see cref="P:DevExpress.XtraBars.BarManager.LargeImages"/> property.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> which provides large images for bar button items.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.LayoutToolbar">
      <summary>
        <para>For internal use. Gets or sets the Layout Toolbar controlled by this bar manager.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Bar"/> object, which specifies the Layout Toolbar.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.RegisterCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Registers the specified command handler.</para>
      </summary>
      <param name="handler">An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface, that represents the command handler.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.SetCommandVisibility(System.ComponentModel.Design.CommandID,DevExpress.XtraBars.BarItemVisibility)">
      <summary>
        <para>Changes the visibility of any bar item which executes the specified command.</para>
      </summary>
      <param name="commandID">A <see cref="T:System.ComponentModel.Design.CommandID"/> object specifying the command whose visibility needs to be changed.</param>
      <param name="visibility">One of the <see cref="T:DevExpress.XtraBars.BarItemVisibility"/> enumeration’s values that specifies the new visibility state.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.SetCommandVisibility(System.ComponentModel.Design.CommandID[],DevExpress.XtraBars.BarItemVisibility)">
      <summary>
        <para>Changes the visibility of any bar item which executes any of the specified commands.</para>
      </summary>
      <param name="commands">An array of <see cref="T:System.ComponentModel.Design.CommandID"/> objects specifying the commands whose visibility needs to be changed.</param>
      <param name="visibility">One of the <see cref="T:DevExpress.XtraBars.BarItemVisibility"/> enumeration’s values that specifies the new visibility state.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.SetToolboxType(DevExpress.XtraBars.Bar,DevExpress.XtraReports.UserDesigner.ToolboxType)">
      <summary>
        <para>Sets the Toolbox type used by the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/>.</para>
      </summary>
      <param name="bar">A <see cref="T:DevExpress.XtraBars.Bar"/> object, representing the Toolbox.</param>
      <param name="value">A <see cref="T:DevExpress.XtraReports.UserDesigner.ToolboxType"/> enumeration value.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.Toolbar">
      <summary>
        <para>For internal use. Gets or sets the Main Toolbar controlled by this bar manager.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Bar"/> object, which specifies the Main Toolbar.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.UnregisterCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Unregisters the specified command handler.</para>
      </summary>
      <param name="handler">An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface, that represents the command handler.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.Updates">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.XRDesignPanel">
      <summary>
        <para>Associates the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/> with the default <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignBarManager.ZoomItem">
      <summary>
        <para>Gets or sets the editor which is used to specify a zoom value for report design surface.</para>
      </summary>
      <value>A DevExpress.XtraReports.UserDesigner.XRZoomBarEditItem object which represents the edtitor used to specify the zoom value.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager">
      <summary>
        <para>Provides dock panels to the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> class with the specified tab container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.DesignDockPanels">
      <summary>
        <para>Maintains the collection of dock panels in the End-User Report Designer.</para>
        <para>This member is now obsolete. To access dock panels, use the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.Item(DevExpress.XtraReports.UserDesigner.DesignDockPanelType)"/> property.</para>
      </summary>
      <value>An array of <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> objects.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.EndInit">
      <summary>
        <para>Ends the initialization of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.Images">
      <summary>
        <para>Returns the collection of image resources that are required by the user interface of dock panels.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Images"/> collection that supplies images to dock panels.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.ImageStream">
      <summary>
        <para>For internal use. Specifies a stream that stores the images associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/>‘s items.</para>
      </summary>
      <value>A DevExpress.Utils.ImageCollectionStreamer object that stores the image resources.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.Initialize(DevExpress.XtraReports.UserDesigner.XRDesignPanel,DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Initializes the specified <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object.</para>
      </summary>
      <param name="designPanel">An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object.</param>
      <param name="panelTypes">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.Item(DevExpress.XtraReports.UserDesigner.DesignDockPanelType)">
      <summary>
        <para>Provides access to a dock panel of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/>, by specifying its type.</para>
      </summary>
      <param name="panelType">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value that specifies the dock panel type in question.</param>
      <value>A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> object that corresponds to the specified dock panel type.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.SavedAutoHidePanels">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Docking.DockPanelCollection"/> object, storing the hidden <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> objects.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.SavedVisiblePanels">
      <summary>
        <para>For internal use. Maintains the visibility state of the auto-hidden dock panels when switching the associated tabs in the Ribbon End-User Report Designer.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Docking.DockPanelCollection"/> object, storing the visible <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanel"/> objects.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.SetWindowVisibility(DevExpress.XtraReports.UserDesigner.DesignDockPanelType,System.Boolean)">
      <summary>
        <para>Sets the specified dock panels’ visibility in the custom End-User Report Designer form.</para>
      </summary>
      <param name="designDockPanels">A DesignDockPanelType enumeration value identifying the End-User Designer’s dock panel(s).</param>
      <param name="visible">true, to display the dock panel(s); otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.Style">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.ToolboxState">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignDockManager.XRDesignPanel">
      <summary>
        <para>Associates the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> with the default <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignErrorList">
      <summary>
        <para>The Scripts navigation tab that enables the assessment and validation of report scripts in the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignErrorList.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignErrorList"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignErrorList.XRDesignPanel">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignFieldList">
      <summary>
        <para>The Field List panel within an End-User Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFieldList.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignFieldList"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFieldList.ShowComplexNodesFirst">
      <summary>
        <para>Specifies whether or not complex nodes should be displayed atop simple nodes in the Field List.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFieldList.ShowComplexProperties">
      <summary>
        <para>Specifies the order in which complex properties are shown in the Field List.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Design.ShowComplexProperties"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFieldList.SortOrder">
      <summary>
        <para>Specifies the sorting order of the Field List nodes.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.SortOrder"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFieldList.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignFieldList"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object assigned to the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignFieldList"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignForm">
      <summary>
        <para>The standard End-User Report Designer form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignForm.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignForm.ActiveDesignPanel">
      <summary>
        <para>Provides access to the currently active Design Panel of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, representing the active Design Panel.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignForm.DesignBarManager">
      <summary>
        <para>Gets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/> object of the End-User Report Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignForm.DesignDockManager">
      <summary>
        <para>Specifies the options of an <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object of a standard End-User Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object, containing dock panels of the End-User Designer.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignForm.DesignMdiController">
      <summary>
        <para>Gets the MDI (multi-document interface) Controller associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/> instance.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/> object, which represents the MDI Controller associated with the form.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignForm.OpenReport(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Loads the specified report instance to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/>.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants, representing the report to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignForm.OpenReport(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Loads the specified report instance to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/> with the specified look and feel settings.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignForm.OpenReport(System.String)">
      <summary>
        <para>Loads the report definition from the specified REPX file and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/>.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignForm.OpenReport(System.String,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Loads the report definition from the specified REPX file and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignForm"/> with the specified look and feel settings.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the End-User Designer form.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignForm.SaveState">
      <summary>
        <para>Specifies whether the form’s window and panel size, position and zoom settings are saved to the Windows registry.</para>
      </summary>
      <value>true, if the Design Form settings are saved in the Windows registry; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignForm.SetWindowVisibility(DevExpress.XtraReports.UserDesigner.DesignDockPanelType,System.Boolean)">
      <summary>
        <para>Sets the visibility of dock panels in the standard End-User Report Designer form.</para>
      </summary>
      <param name="designDockPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value, identifying the dock panel(s) of the End-User Designer.</param>
      <param name="visible">true, to display the dock panel(s); otherwise, false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignFormEx">
      <summary>
        <para>For internal use. The standard End-User Designer form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignFormEx"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFormEx.DesignBarManager">
      <summary>
        <para>Gets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignFormEx"/> object of the End-User Report Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignBarManager"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase">
      <summary>
        <para>The base class for the form used to show the End-User Designer for a particular report.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.DesignDockManager">
      <summary>
        <para>Specifies the options of an <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object of a standard End-User Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object that contains dock panels of the End-User Designer.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.DesignPanel">
      <summary>
        <para>Gets or sets the Design Panel used to edit a report in the current End-User Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, which represents the Design Panel associated with the form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.FileName">
      <summary>
        <para>Gets or sets the path (including the file name) where the report currently being edited in the End-User Designer will be saved. A report definition is usually saved to a REPX file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> representing the path (including the filename) to a REPX file.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.OpenReport(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Opens the specified report in the form’s active Design Panel.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.OpenReport(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Opens the specified report in the form’s active Design Panel, and applies the specified look-and-feel settings to the current End-User Designer form.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look-and-feel settings applied to the End-User Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.OpenReport(System.String)">
      <summary>
        <para>Loads the report from the specified REPX file, and opens it in the form’s active Design Panel.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.OpenReport(System.String,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Loads a report definition from the specified REPX file, opens it into the form’s active Design Panel, and applies the specified look-and-feel settings to the current End-User Designer form.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look-and-feel settings applied to the End-User Designer form.</param>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.ReportStateChanged">
      <summary>
        <para>Occurs when the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportState"/> property has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.SaveReport(System.String)">
      <summary>
        <para>Saves the definition (layout and configuration) of the report currently being edited in the End-User Designer form to the specified path.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> containing the full path (including the file name) specifying where the report’s definition will be saved.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.SaveReportAs">
      <summary>
        <para>Saves the configuration of the report which is currently open in the End-User Report Designer to a file. The folder it is saved to, is specified in the Save As window.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.SaveState">
      <summary>
        <para>Specifies whether the form’s window and panel size, position and zoom settings are saved to the Windows registry.</para>
      </summary>
      <value>true, if the Design Form settings are saved in the Windows registry; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignFormExBase.SetWindowVisibility(DevExpress.XtraReports.UserDesigner.DesignDockPanelType,System.Boolean)">
      <summary>
        <para>Sets the visibility of design dock panels in the End-User Report Designer.</para>
      </summary>
      <param name="designDockPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value, identifying the dock panel(s) of the End-User Report Designer.</param>
      <param name="visible">true, to display the dock panel(s); otherwise, false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignGroupAndSort">
      <summary>
        <para>The Group and Sort panel within an End-User Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignGroupAndSort.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignGroupAndSort"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignGroupAndSort.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignGroupAndSort"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object assigned to the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignGroupAndSort"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController">
      <summary>
        <para>Provides a multi-document interface to the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/> class with the specified container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.ActiveDesignPanel">
      <summary>
        <para>Provides access to the currently active Design Panel of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, representing the active Design Panel.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AddCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Adds the specified command handler to the command handlers list for one or several of the report commands.</para>
      </summary>
      <param name="handler">An object which implements the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
      <summary>
        <para>Adds the specified service to <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> value, specifying the service type.</param>
      <param name="callback">A callback object that can create the service. This allows a service to be declared as available, but delays creation of the object until the service is requested.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
      <summary>
        <para>Adds the specified service to <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> value, specifying the service type.</param>
      <param name="callback">A callback object that can create the service. This allows a service to be declared as available, but delays creation of the object until the service is requested.</param>
      <param name="promote">true to promote these changes to all available Design Panels; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AddService(System.Type,System.Object)">
      <summary>
        <para>Adds the specified service to <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> value, specifying the service type.</param>
      <param name="serviceInstance">A <see cref="T:System.Object"/>, specifying the service instance.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AddService(System.Type,System.Object,System.Boolean)">
      <summary>
        <para>Adds the specified service to <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> value, specifying the service type.</param>
      <param name="serviceInstance">A <see cref="T:System.Object"/>, specifying the service instance.</param>
      <param name="promote">true to promote these changes to all available Design Panels; otherwise false.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AllowDefaultSvgImages">
      <summary>
        <para>Specifies whether to use bitmap or vector icons for the End-User Report Designer.</para>
      </summary>
      <value>true, to use vector images; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AnyDocumentActivated">
      <summary>
        <para>Occurs after any of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>‘s design panels showing an edited document has been activated during the design session.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.BeginInit">
      <summary />
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.ContainerControl">
      <summary>
        <para>Specifies the control containing the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.ContainerControl"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.Controller">
      <summary>
        <para>Specifies the Bar and Docking Controllers assigned to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.BarAndDockingController"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.CreateNewReport">
      <summary>
        <para>Creates a new report, and loads it into a new <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.CreateNewReportWizard">
      <summary>
        <para>Invokes the Report Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.CreateNewReportWizard(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="report"></param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.DataSourceWizardSettings">
      <summary>
        <para>Provides access to the Data Source Wizard settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.DataAccess.UI.Wizard.DataSourceWizardSettings"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.DefaultReportSettings">
      <summary>
        <para>Provides access to the default report page settings.</para>
      </summary>
      <value>Report page settings.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.DesignPanelListeners">
      <summary>
        <para>For internal use. Provides access to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection"/> class.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.DesignPanelLoaded">
      <summary>
        <para>Occurs when an <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object has been loaded.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.DesignSettings">
      <summary>
        <para>Provides access to an object that defines additional settings of an End-User Report Designer for WinForms.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.EndInit">
      <summary />
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.Form">
      <summary>
        <para>Specifies the form to associate with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Form"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.GetCommandVisibility(DevExpress.XtraReports.UserDesigner.ReportCommand)">
      <summary>
        <para>Gets the visibility state of the specified report command.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value, which specifies the command whose visibility is to be determined.</param>
      <returns>A <see cref="T:DevExpress.XtraReports.UserDesigner.CommandVisibility"/> enumeration value, which represents the visibility state of the report command.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.OpenReport">
      <summary>
        <para>Loads the specified report instance to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.OpenReport(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Loads the specified report instance to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.OpenReport(System.String)">
      <summary>
        <para>Loads the report definition from the specified REPX file and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.RemoveCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Removes the specified command handler for one or several of the report commands.</para>
      </summary>
      <param name="handler">An object which implements the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.RemoveService(System.Type)">
      <summary>
        <para>Removes the specified service from <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> value, specifying the service type.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.RemoveService(System.Type,System.Boolean)">
      <summary>
        <para>Removes the specified service from <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> value, specifying the service type.</param>
      <param name="promote">true to promote these changes to all available Design Panels; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.SetCommandVisibility(DevExpress.XtraReports.UserDesigner.ReportCommand,DevExpress.XtraReports.UserDesigner.CommandVisibility)">
      <summary>
        <para>Changes the visibility of the specified report command.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value, which specifies the command whose visibility needs to be changed.</param>
      <param name="visibility">A <see cref="T:DevExpress.XtraReports.UserDesigner.CommandVisibility"/> enumeration value, which specifies the visibility state of the report command.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.SetCommandVisibility(DevExpress.XtraReports.UserDesigner.ReportCommand[],DevExpress.XtraReports.UserDesigner.CommandVisibility)">
      <summary>
        <para>Changes the visibility of the specified report commands.</para>
      </summary>
      <param name="commands">An array of <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration values, which specify the commands whose visibility needs to be changed.</param>
      <param name="visibility">A <see cref="T:DevExpress.XtraReports.UserDesigner.CommandVisibility"/> enumeration value, which specifies the visibility state of the report command.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.SqlWizardSettings">
      <summary>
        <para>Provides access to settings of the Data Source Wizard associated with the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.DataAccess.UI.Wizard.SqlWizardSettings"/> object containing Data Source Wizard settings.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.UseDefaultSvgImages">
      <summary>
        <para>Use the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.AllowDefaultSvgImages"/> property instead.</para>
      </summary>
      <value>true, to use vector images; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.ValidateCustomSql">
      <summary>
        <para>Occurs when a custom SQL query is validated.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiController.XtraTabbedMdiManager">
      <summary>
        <para>Gets or sets the MDI Manager that provides the multi-document interface for the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings">
      <summary>
        <para>Provides additional settings to the End-User Report Designer for WinForms.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings.DockingViewStyle">
      <summary>
        <para>Specifies the View style for the Report Designer‘s dock panels and tabs.</para>
      </summary>
      <value>A DevExpress.XtraBars.Docking2010.Views.DockingViewStyle enumeration value that specifies the View style.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings.FilterCriteriaDisplayStyle">
      <summary>
        <para>Gets or sets whether Filter Editors render filter criteria in the text format or in the visual format.</para>
      </summary>
      <value>Text, to render filter criteria in the text format; Visual, to render filter criteria in the visual format; Default, to render filter criteria per the <see cref="P:DevExpress.XtraEditors.WindowsFormsSettings.FilterCriteriaDisplayStyle"/> global property value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings.UseDirectXPaint">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings.UseLegacyExpressionEditor">
      <summary>
        <para>Enables you to switch back a WinForms reporting application to using the legacy Expression Editor that does not support syntax highlighting and intelligent code completion.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/>, to use the legacy Expression Editor; <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>, to use a newer Expression Editor version; <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> to use the <see cref="F:DevExpress.XtraEditors.WindowsFormsSettings.DefaultSettingsCompatibilityMode"/> setting.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignMdiControllerDesignSettings.UseLegacyFilterEditor">
      <summary>
        <para>Indicates whether to use a legacy Filter Editor or a new one in reporting applications.</para>
      </summary>
      <value>true if a legacy Filter Editor is used. By default, this property is set to false.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel">
      <summary>
        <para>A panel that is used to edit a report in the End-User Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.Activate">
      <summary>
        <para>For internal use. Activates the host of the given <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.Activated">
      <summary>
        <para>For internal use. Occurs when the host of the given <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance has been activated.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.AddCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Adds the specified command handler to the command handlers list for one or several of the report commands.</para>
      </summary>
      <param name="handler">An object which implements the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface, representing the command handler to be added.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.AddService(System.Type,System.Object)">
      <summary>
        <para>Adds the specified service to the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance.</para>
      </summary>
      <param name="type">The type of service to add.</param>
      <param name="value">An <see cref="T:System.Object"/>, representing an instance of the service type to be added. This object must implement or inherit from the type indicated by the type parameter.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.BorderStyle">
      <summary>
        <para>Gets or sets the Design Panel’s border style.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value which specifies the border style for the Design Panel.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.CloseReport">
      <summary>
        <para>Closes the report currently being edited in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.CommandStatusChanged">
      <summary>
        <para>Occurs if the status of a menu command has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentAdded">
      <summary>
        <para>Occurs after a component has been added to the designer host of the report currently being edited in the Design Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentAdding">
      <summary>
        <para>Occurs before a component can be added to the designer host of the report currently being edited in the Design Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentChanged">
      <summary>
        <para>Occurs when altering a property that belongs to any component in the designer host of the report currently being edited.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentRemoved">
      <summary>
        <para>Occurs after any component has been removed from the designer host of the report being currently edited in the Design Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentRemoving">
      <summary>
        <para>Occurs before any component can be removed from the designer host of the report being currently edited in the Design Panel.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentVisibility">
      <summary>
        <para>Specifies whether the Components node is visible in the Report Explorer.</para>
      </summary>
      <value>The Components node visibility type.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.CreateSaveFileDialog(DevExpress.XtraReports.UI.XtraReport,System.String)">
      <summary>
        <para>Creates the SaveFile dialog window to save the current report’s configuration to a REPX file.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object to be saved to a REPX file.</param>
      <param name="fileName">A <see cref="T:System.String"/> representing the path (including the filename), specifying where to save the report.</param>
      <returns>A <see cref="T:System.Windows.Forms.SaveFileDialog"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.CreateSaveFileDialog(DevExpress.XtraReports.UI.XtraReport,System.String,System.String)">
      <summary>
        <para>Creates the SaveFile dialog window to save the current report’s configuration to a REPX file.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object to be saved to a REPX file.</param>
      <param name="defaultDirectory">A <see cref="T:System.String"/>, specifying the path to the directory that is opened by default.</param>
      <param name="fileName">A <see cref="T:System.String"/>, specifying the file name.</param>
      <returns>A <see cref="T:System.Windows.Forms.SaveFileDialog"/> object.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.DataSourcesNodeVisibility">
      <summary>
        <para>Specifies whether the Data Sources node is visible in the Report Explorer.</para>
      </summary>
      <value>The Data Sources node visibility type.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.Deactivate">
      <summary>
        <para>For internal use. Deactivates the host of the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.Deactivated">
      <summary>
        <para>For internal use. Occurs when the host of the given <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance has been deactivated.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.DesignerHostLoaded">
      <summary>
        <para>Occurs after the designer host is activated for a report currently being edited in the current Design Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.DesignerHostLoading">
      <summary>
        <para>For internal use. Occurs when the host of the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> instance starts loading.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ExecCommand(DevExpress.XtraReports.UserDesigner.ReportCommand)">
      <summary>
        <para>Executes the specified report command without any parameters passed to it.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value, specifying the report command to execute.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ExecCommand(DevExpress.XtraReports.UserDesigner.ReportCommand,System.Object[])">
      <summary>
        <para>Executes the specified report command and passes the specified parameters to it.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value, specifying the report command to execute.</param>
      <param name="args">An array of <see cref="T:System.Object"/> values, specifying the parameters to be passed to the report command.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ExecuteCommand(System.ComponentModel.Design.CommandID)">
      <summary>
        <para>Executes the specified command.</para>
      </summary>
      <param name="cmdID">A <see cref="T:System.ComponentModel.Design.CommandID"/> object.</param>
      <returns>true if the specified command is executed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ExecuteCommand(System.ComponentModel.Design.CommandID,System.Object[])">
      <summary>
        <para>Executes the specified command with the specified set of parameters.</para>
      </summary>
      <param name="cmdID">A <see cref="T:System.ComponentModel.Design.CommandID"/> object.</param>
      <param name="parameters">An array of <see cref="T:System.Object"/> values that specify the parameters for the command to be executed.</param>
      <returns>true if the specified command is executed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.FileName">
      <summary>
        <para>Gets or sets the path (including the file name) where the report currently being edited in the Design Panel will be saved. A report definition is usually saved to a REPX file.</para>
      </summary>
      <value>A <see cref="T:System.String"/> representing the path (including the filename) to a REPX file.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.FindMenuCommand(System.ComponentModel.Design.CommandID)">
      <summary>
        <para>Searches for the specified command ID.</para>
      </summary>
      <param name="cmdID">An object of the <see cref="T:System.ComponentModel.Design.CommandID"/> type, identifying the command to be found.</param>
      <returns>A <see cref="T:System.ComponentModel.Design.MenuCommand"/> object, representing the command associated with the specified ID. Returns null (Nothing in Visual Basic) if no command is found.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.FormattingRulesNodeVisibility">
      <summary>
        <para>Specifies whether the Formatting Rules node is visible in the Report Explorer.</para>
      </summary>
      <value>The Formatting Rules node visibility type.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.GetCommandEnabled(DevExpress.XtraReports.UserDesigner.ReportCommand)">
      <summary>
        <para>Determines whether the specified report command is currently enabled in the End-User Designer.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value, specifying the command whose enabled state is to be determined.</param>
      <returns>true if the report command is enabled; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.GetCommandVisibility(DevExpress.XtraReports.UserDesigner.ReportCommand)">
      <summary>
        <para>Gets the visibility state of the specified report command in the Design Panel.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value, which specifies the command whose visibility is to be determined.</param>
      <returns>A <see cref="T:DevExpress.XtraReports.UserDesigner.CommandVisibility"/> enumeration value, which represents the visibility state of the report command.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.GetSelectedComponents">
      <summary>
        <para>Gets the collection of components that are currently selected in the Design Panel.</para>
      </summary>
      <returns>A collection that represents the current set of selected components.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.GetService(System.Type)">
      <summary>
        <para>Obtains the service object of the specified type from the designer host used by the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <param name="serviceType">A <see cref="T:System.Type"/> object that specifies the type of the service object to obtain.</param>
      <returns>A service object of the specified type, or null (Nothing in Visual Basic) if there is no service object of this type.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.GetService``1">
      <summary>
        <para>Obtains the service object of the specified type from the designer host used by the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <typeparam name="T"></typeparam>
      <returns>A service object of the specified generic type, or a null reference (Nothing in Visual Basic) if there is no service object of this type.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.OpenReport(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Loads the specified report and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.OpenReport(System.IO.Stream)">
      <summary>
        <para>Loads a report definition from the specified stream and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which contains a report to be opened in the design panel.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.OpenReport(System.String)">
      <summary>
        <para>Loads a report definition from the specified file and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.OpenReportFile">
      <summary>
        <para>Invokes the <see cref="T:System.Windows.Forms.OpenFileDialog"/>, which allow your end-users to locate and choose a report, and load it into the Design Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.RemoveCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Removes the specified command handler.</para>
      </summary>
      <param name="handler">The command handler to be removed.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.RemoveService(System.Type)">
      <summary>
        <para>Removes the specified service from the Design Panel.</para>
      </summary>
      <param name="type">A <see cref="T:System.Type"/> value, specifying the type of service to remove.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.Report">
      <summary>
        <para>Gets the report currently opened in the Design Panel.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object currently opened in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportState">
      <summary>
        <para>Gets the state of the report currently being edited in the Design Panel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportState"/> enumeration value which represents the current state of a report.</value>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportStateChanged">
      <summary>
        <para>Occurs when the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ReportState"/> property has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SaveChangedReport">
      <summary>
        <para>Saves the configuration of the report currently opened in the Design Panel to a file, only if changes have been made to the report.</para>
      </summary>
      <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SaveReport">
      <summary>
        <para>Saves the definition of the report currently open in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/>, to the file specified by the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.FileName"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SaveReport(System.String)">
      <summary>
        <para>Saves the definition of the report currently open in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> to the specified file.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> value specifying the file name.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SaveReportAs">
      <summary>
        <para>Saves the configuration of the report which is currently open in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> to a file. The folder it is saved to, is specified in the Save As window.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SelectedTabIndex">
      <summary>
        <para>Gets or sets the index of the tab currently selected in the Design Panel.</para>
      </summary>
      <value>An integer value which represents the index of the currently selected tab.</value>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SelectedTabIndexChanged">
      <summary>
        <para>Occurs after the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SelectedTabIndex"/> property’s value has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SelectedToolboxItemChanged">
      <summary>
        <para>Occurs if the currently selected Toolbox item has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SelectedToolboxItemUsed">
      <summary>
        <para>Occurs when a control represented by the currently selected Toolbox item has been dropped onto a report.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SelectionChanged">
      <summary>
        <para>Occurs if the collection of currently selected components in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SetCommandVisibility(DevExpress.XtraReports.UserDesigner.ReportCommand,DevExpress.XtraReports.UserDesigner.CommandVisibility)">
      <summary>
        <para>Changes the visibility state of the specified report command in the End-User Designer.</para>
      </summary>
      <param name="command">A <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration value which specifies the command whose visibility needs to be changed.</param>
      <param name="visibility">A <see cref="T:DevExpress.XtraReports.UserDesigner.CommandVisibility"/> enumeration value which specifies the new visibility state for the command.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanel.SetCommandVisibility(DevExpress.XtraReports.UserDesigner.ReportCommand[],DevExpress.XtraReports.UserDesigner.CommandVisibility)">
      <summary>
        <para>Changes the visibility state of the specified report commands in the End-User Designer.</para>
      </summary>
      <param name="commands">An array of <see cref="T:DevExpress.XtraReports.UserDesigner.ReportCommand"/> enumeration values which specify the commands whose visibility needs to be changed.</param>
      <param name="visibility">A <see cref="T:DevExpress.XtraReports.UserDesigner.CommandVisibility"/> enumeration value which specifies the new visibility state for the commands.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ShowComponentTray">
      <summary>
        <para>Use the <see cref="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.ComponentVisibility"/> property instead.</para>
      </summary>
      <value>true, to show the Component Tray; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanel.StylesNodeVisibility">
      <summary>
        <para>Specifies whether the Styles node is visible in the Report Explorer.</para>
      </summary>
      <value>The Styles node visibility type.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener">
      <summary>
        <para>For internal use. Provides the functionality for adjusting <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener.#ctor(DevExpress.XtraReports.UserDesigner.IDesignPanelListener)">
      <summary>
        <para>For internal use. Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UI.XRWatermark"/> class with the specified settings.</para>
      </summary>
      <param name="designControl"></param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener.DesignControl">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection">
      <summary>
        <para>For internal use. Represents the collection of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.#ctor(DevExpress.XtraReports.UserDesigner.XRDesignMdiController)">
      <summary />
      <param name="mdiController"></param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Add(DevExpress.XtraReports.UserDesigner.IDesignPanelListener)">
      <summary>
        <para>Appends the specified item to the current collection.</para>
      </summary>
      <param name="item">An object implementing the DevExpress.XtraReports.UserDesigner.IDesignPanelListener interface to append to the collection.</param>
      <returns>An integer value indicating the position into which a new element was inserted.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Add(DevExpress.XtraReports.UserDesigner.XRDesignPanelListener)">
      <summary>
        <para>Appends the specified item to the current collection.</para>
      </summary>
      <param name="item">An DevExpress.XtraReports.UserDesigner.IDesignPanelListener object to append to the collection.</param>
      <returns>An integer value indicating the position into which a new element was inserted.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.AddRange(DevExpress.XtraReports.UserDesigner.XRDesignPanelListener[])">
      <summary>
        <para>Appends an array of listeners to the collection.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> objects to append to the collection.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Contains(DevExpress.XtraReports.UserDesigner.IDesignPanelListener)">
      <summary>
        <para>For internal use. Determines whether the collection contains the specified object.</para>
      </summary>
      <param name="item"></param>
      <returns>A Boolean value.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Contains(DevExpress.XtraReports.UserDesigner.XRDesignPanelListener)">
      <summary>
        <para>For internal use. Determines whether the collection contains the specified <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> object.</para>
      </summary>
      <param name="item">An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> object.</param>
      <returns>A Boolean value.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Item(System.Int32)">
      <summary>
        <para>For internal use. Provides access to individual items in the collection.</para>
      </summary>
      <param name="index">A zero-based integer specifying the desired item’s position within the collection. If it’s negative or exceeds the last available index, an exception is raised.</param>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Remove(DevExpress.XtraReports.UserDesigner.IDesignPanelListener)">
      <summary>
        <para>For internal use. Removes the specified object from the collection.</para>
      </summary>
      <param name="item"></param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPanelListenersCollection.Remove(DevExpress.XtraReports.UserDesigner.XRDesignPanelListener)">
      <summary>
        <para>For internal use. Removes the specified <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> object from the collection.</para>
      </summary>
      <param name="item">An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanelListener"/> object.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignPropertyGrid">
      <summary>
        <para>The Properties Grid within an End-User Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignPropertyGrid.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPropertyGrid"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignPropertyGrid.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPropertyGrid"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object assigned to the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPropertyGrid"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignReportExplorer">
      <summary>
        <para>The Report Explorer within an End-User Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignReportExplorer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignReportExplorer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignReportExplorer.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignReportExplorer"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object assigned to the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignReportExplorer"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController">
      <summary>
        <para>Provides Ribbon pages to the End-User Report Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/> class with the specified container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.BeginInit">
      <summary>
        <para>For internal use. Starts the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/>‘s initialization. Initialization occurs at runtime.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.EndInit">
      <summary>
        <para>For internal use. Ends the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/>‘s runtime initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.ImageCollection">
      <summary>
        <para>Gets the collection of images used in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/> UI.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraPrinting.Preview.RibbonImageCollection"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.RegisterCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Registers the specified command handler.</para>
      </summary>
      <param name="commandHandler">An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface, that represents the command handler.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.RibbonControl">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> into which the Design Ribbon Controller embeds its tabs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.UnregisterCommandHandler(DevExpress.XtraReports.UserDesigner.ICommandHandler)">
      <summary>
        <para>Unregisters the specified command handler.</para>
      </summary>
      <param name="commandHandler">An object implementing the <see cref="T:DevExpress.XtraReports.UserDesigner.ICommandHandler"/> interface, that represents the command handler.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.XRDesignDockManager">
      <summary>
        <para>Gets or sets the component used to create dock panels in the End-User Report Designer.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object assigned to the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm">
      <summary>
        <para>The ribbon-based End-User Report Designer form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.ActiveDesignPanel">
      <summary>
        <para>Provides access to the currently active Design Panel of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, representing the active Design Panel.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.DesignDockManager">
      <summary>
        <para>Specifies the options of an <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object of a ribbon-based End-User Report Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignDockManager"/> object, containing dock panels of the End-User Designer.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.DesignMdiController">
      <summary>
        <para>Gets the MDI (multi-document interface) Controller associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/> instance.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignMdiController"/> object, which represents the MDI Controller associated with the form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.DesignRibbonController">
      <summary>
        <para>Gets the DesignRibbonController of the ribbon-based End-User Report Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/> object, which represents the Design Ribbon Controller of the Ribbon End-User Designer form.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.OpenReport(DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Loads the specified report instance into the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/>.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.OpenReport(DevExpress.XtraReports.UI.XtraReport,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Loads the specified report instance into the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/> with the specified look and feel settings.</para>
      </summary>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object, or one of its descendants representing the report to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon End-User Designer form.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.OpenReport(System.String)">
      <summary>
        <para>Loads the report definition from the specified REPX file and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/>.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.OpenReport(System.String,DevExpress.LookAndFeel.UserLookAndFeel)">
      <summary>
        <para>Loads the report definition from the specified REPX file and opens it in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm"/> with the specified look and feel settings.</para>
      </summary>
      <param name="fileName">A <see cref="T:System.String"/> representing the full path to the report file (.REPX) to be opened.</param>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, which specifies the look and feel settings applied to the Ribbon End-User Designer form.</param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.RibbonControl">
      <summary>
        <para>Gets the RibbonControl of the ribbon-based End-User Report Designer form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object, which represents the Ribbon Control of the Ribbon End-User Designer form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.RibbonStatusBar">
      <summary>
        <para>Gets the RibbonStatusBar of the ribbon-based End-User Report Designer form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> object, which represents the Ribbon Status Bar of the Ribbon End-User Designer form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.SaveState">
      <summary>
        <para>Specifies whether the form’s window and panel size, position and zoom settings are saved to the Windows registry.</para>
      </summary>
      <value>true, if the Design Form settings are saved in the Windows registry; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonForm.SetWindowVisibility(DevExpress.XtraReports.UserDesigner.DesignDockPanelType,System.Boolean)">
      <summary>
        <para>Sets the visibility of dock panels in the ribbon-based End-User Report Designer form.</para>
      </summary>
      <param name="designDockPanels">A <see cref="T:DevExpress.XtraReports.UserDesigner.DesignDockPanelType"/> enumeration value, identifying the dock panel(s) of the End-User Designer.</param>
      <param name="visible">true to display the dock panel(s); otherwise, false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonFormEx">
      <summary>
        <para>For internal use. Represents a Ribbon End-User Designer form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignRibbonFormEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonFormEx"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonFormEx.DesignRibbonController">
      <summary>
        <para>Gets the DesignRibbonController of the Ribbon End-User Designer form.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignRibbonController"/> object, which represents the Design Ribbon Controller of the Ribbon End-User Designer form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonFormEx.RibbonControl">
      <summary>
        <para>Gets the RibbonControl of the ribbon-based End-User Report Designer form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object, which represents the Ribbon Control of the Ribbon End-User Designer form.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignRibbonFormEx.RibbonStatusBar">
      <summary>
        <para>Gets the RibbonStatusBar of the ribbon-based End-User Report Designer form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> object, which represents the Ribbon Status Bar of the Ribbon End-User Designer form.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignToolBox">
      <summary>
        <para>The Toolbox within an End-User Designer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignToolBox.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignToolBox"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolBox.GroupsStyle">
      <summary>
        <para>Gets or sets the style for presenting the groups’ contents.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraNavBar.NavBarGroupStyle"/> value specifying how the groups’ contents are presented.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolBox.SmallImages">
      <summary>
        <para>Gets or sets an object which serves as the source of the small images used in the toolbox.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.ImageList"/> object which represents the source of the small images.</value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolBox.XRDesignPanel">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object associated with the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignToolBox"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignPanel"/> object, specifying the Design Panel assigned to the current <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignToolBox"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignToolBoxControl">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignToolBoxControl.#ctor(System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignToolBoxControl"/> class with specified settings.</para>
      </summary>
      <param name="columnsCount"></param>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRDesignToolBoxControl.DpiChanged">
      <summary />
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolBoxControl.XRDesignPanel">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignToolboxItemsViewInfo">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignToolboxItemsViewInfo.#ctor(DevExpress.XtraToolbox.ToolboxViewInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignToolboxItemsViewInfo"/> class with specified settings.</para>
      </summary>
      <param name="info"></param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolboxItemsViewInfo.CanClearSelectedElements">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolboxItemsViewInfo.ViewInfo">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDesignToolboxViewInfo">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDesignToolboxViewInfo.#ctor(DevExpress.XtraToolbox.ToolboxControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRDesignToolboxViewInfo"/> class with specified settings.</para>
      </summary>
      <param name="owner"></param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRDesignToolboxViewInfo.Toolbox">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRDialogRunner">
      <summary>
        <para>Enables you to show report-specific dialogs.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRDialogRunner.ShowFilterStringEditorDialog(DevExpress.XtraReports.UI.XtraReportBase,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Shows the FilterString Editor.</para>
      </summary>
      <param name="report">The report to apply the FilterString to</param>
      <param name="owner">The owner window</param>
      <returns>OK, to apply the resulting FilterString; Cancel otherwise</returns>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager">
      <summary>
        <para>A component that provides the multiple document interface (MDI) child forms that are parented to the form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager"/> class with the specified container.</para>
      </summary>
      <param name="container">An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.</param>
    </member>
    <member name="E:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager.AnyDocumentActivated">
      <summary>
        <para>Occurs after any of the documents contained in the <see cref="T:DevExpress.XtraReports.UserDesigner.XRTabbedMdiManager"/> is activated.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.Create(System.Windows.Forms.Control,DevExpress.XtraReports.UserDesigner.XRDesignPanel,DevExpress.XtraReports.UserDesigner.XRDesignDockManager)">
      <summary />
      <param name="parent"></param>
      <param name="designPanel"></param>
      <param name="xRDesignDockManager"></param>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.DesignPanel">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.ExpandButton">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.FindToolbox(System.Windows.Forms.Control)">
      <summary />
      <param name="parent"></param>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.ToolBoxControl">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.UserDesigner.XRToolBoxPanel.ToolBoxState">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.UserDesigner.XRToolBoxUserControl">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxUserControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.UserDesigner.XRToolBoxUserControl"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxUserControl.AddButton(System.Windows.Forms.Control)">
      <summary />
      <param name="button"></param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxUserControl.AddContent(System.Windows.Forms.Control)">
      <summary />
      <param name="content"></param>
    </member>
    <member name="M:DevExpress.XtraReports.UserDesigner.XRToolBoxUserControl.CalculateWidth(System.Int32)">
      <summary />
      <param name="width"></param>
      <returns></returns>
    </member>
    <member name="N:DevExpress.XtraReports.Wizards">
      <summary>
        <para>Contains classes and interfaces that enable Report Wizard customization.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraReports.Wizards.Builder">
      <summary>
        <para>Contains the <see cref="T:DevExpress.XtraReports.Wizards.Builder.XtraWizardReportBuilder"/> class that is used to generate a report based on the report model settings accumulated while running the Report Wizard.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Builder.XtraWizardReportBuilder">
      <summary>
        <para>Generates a report based on the report model settings accumulated while running the Report Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Builder.XtraWizardReportBuilder.#ctor(System.ComponentModel.Design.IDesignerHost,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Builder.XtraWizardReportBuilder"/> class with the specified settings.</para>
      </summary>
      <param name="designerHost">An object implementing the <see cref="T:System.ComponentModel.Design.IDesignerHost"/> interface that is used to manage designer transactions and components.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the report data source.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value, specifying the report data member.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Builder.XtraWizardReportBuilder.Build(DevExpress.XtraReports.UI.XtraReport,DevExpress.XtraReports.Wizards.ReportModel)">
      <summary />
      <param name="report"></param>
      <param name="model"></param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.IWizardCustomizationService">
      <summary>
        <para>If implemented, enables customization of the Report Wizard pages in WinForms and WPF applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.IWizardCustomizationService.CustomizeDataSourceWizard(DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.XtraReports.Wizards.XtraReportModel})">
      <summary>
        <para>Enables customization of the Data Source Wizard pages.</para>
      </summary>
      <param name="tool">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface, specifying the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/>.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.IWizardCustomizationService.CustomizeReportWizard(DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.XtraReports.Wizards.XtraReportModel})">
      <summary>
        <para>Enables customization of the Report Wizard pages.</para>
      </summary>
      <param name="tool">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface, specifying the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/>.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.IWizardCustomizationService.TryCreateDataSource(DevExpress.DataAccess.Wizard.Model.IDataSourceModel,System.Object@,System.String@)">
      <summary>
        <para>Creates a new report data source with the specified settings.</para>
      </summary>
      <param name="model">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Model.IDataSourceModel"/> interface.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the data source.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value, specifying the data member.</param>
      <returns>true, if the data source has been created; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.IWizardCustomizationService.TryCreateReport(System.ComponentModel.Design.IDesignerHost,DevExpress.XtraReports.Wizards.XtraReportModel,System.Object,System.String)">
      <summary>
        <para>Creates a new report with the specified settings.</para>
      </summary>
      <param name="designerHost">An object implementing the <see cref="T:System.ComponentModel.Design.IDesignerHost"/> interface that is used to manage designer transactions and components.</param>
      <param name="model">An <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/> object.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the data source.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value, specifying the data member.</param>
      <returns>true, if the report has been created; otherwise, false.</returns>
    </member>
    <member name="N:DevExpress.XtraReports.Wizards.Views">
      <summary>
        <para>Contains classes and interfaces that provide functionality to the Report Wizard page views.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView">
      <summary>
        <para>Provides a view for the Add Grouping Levels (Multi-Query Version) page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.ActiveAvailableColumnsChanged">
      <summary>
        <para>Occurs when the active record in the list of the available columns is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.ActiveGroupingLevelChanged">
      <summary>
        <para>Occurs when the active record in the list of grouping levels is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.AddGroupingLevelClicked">
      <summary>
        <para>Occurs when the Add Grouping Level button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.CombineGroupingLevelClicked">
      <summary>
        <para>Occurs when the Combine Grouping Level button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.EnableAddGroupingLevelButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Add Grouping Level button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.EnableCombineGroupingLevelButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Combine Grouping Level button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.EnableGroupingLevelDown(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Move Grouping Level Down button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.EnableGroupingLevelUp(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Move Grouping Level Up button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.EnableRemoveGroupingLevelButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Remove Grouping Level button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.FillAvailableColumns(DevExpress.Data.XtraReports.DataProviders.ColumnInfo[])">
      <summary>
        <para>Populates the list of available columns.</para>
      </summary>
      <param name="columns">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.FillGroupingLevels(DevExpress.XtraReports.Wizards.GroupingLevelInfo[])">
      <summary>
        <para>Populates the list of grouping levels.</para>
      </summary>
      <param name="groupingLevels">An array of <see cref="T:DevExpress.XtraReports.Wizards.GroupingLevelInfo"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.GetActiveAvailableColumns">
      <summary>
        <para>Gets an array of records selected in the list of available columns.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.GetActiveGroupingLevel">
      <summary>
        <para>Gets the record selected in the list of grouping levels.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraReports.Wizards.GroupingLevelInfo"/> object.</returns>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.GroupingLevelDownClicked">
      <summary>
        <para>Occurs when the Move Grouping Level Down button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.GroupingLevelUpClicked">
      <summary>
        <para>Occurs when the Move Grouping Level Up button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.RemoveGroupingLevelClicked">
      <summary>
        <para>Occurs when the Remove Grouping Level button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.SetActiveGroupingLevel(DevExpress.XtraReports.Wizards.GroupingLevelInfo)">
      <summary>
        <para>Changes the active record in the list of grouping levels.</para>
      </summary>
      <param name="groupingLevel">A <see cref="T:DevExpress.XtraReports.Wizards.GroupingLevelInfo"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView.ShowWaitIndicator(System.Boolean)">
      <summary>
        <para>Shows or hides the wait indicator on a wizard page.</para>
      </summary>
      <param name="show">true to show the wait indicator; otherwise false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseObjectConstructorPageViewEx">
      <summary>
        <para>Provides a view for the Select a Data Source Constructor page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseObjectConstructorPageViewEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseObjectConstructorPageViewEx"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView">
      <summary>
        <para>Provides a view for the Choose a Report Color Scheme page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView.ColorScheme">
      <summary>
        <para>Specifies the selected color scheme.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.ColorSchemes.ColorScheme"/> object that specifies the selected color scheme.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView.ColorSchemeChanged">
      <summary>
        <para>Occurs when the active color scheme is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView.FillColorSchemes(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.ColorSchemes.ColorScheme})">
      <summary>
        <para>Populates the list of available color schemes.</para>
      </summary>
      <param name="colorSchemes">A collection of <see cref="T:DevExpress.XtraReports.Wizards.ColorSchemes.ColorScheme"/> objects.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportColorSchemePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseReportDataSourceTypePageViewEx">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportDataSourceTypePageViewEx.#ctor">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportDataSourceTypePageViewEx.#ctor(DevExpress.DataAccess.Wizard.Services.DataSourceTypes)">
      <summary />
      <param name="dataSourceTypes"></param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView">
      <summary>
        <para>Provides a view for the Choose a Report Layout page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView.AdjustFieldWidth">
      <summary>
        <para>Specifies the state of the Adjust the field width check box.</para>
      </summary>
      <value>true, to activate the check box; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView.IsGroupedReport">
      <summary>
        <para>Specifies whether or not the report contains groups.</para>
      </summary>
      <value>true, if the report is grouped; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView.Portrait">
      <summary>
        <para>Specifies the state of the Portrait and Landscape radio buttons.</para>
      </summary>
      <value>true if the Portrait radio button is active; false if the Landscape radio button is active.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView.ReportLayout">
      <summary>
        <para>Specifies the selected report layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.ReportLayout"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseReportStylePageView">
      <summary>
        <para>Provides a view for the Choose a Report Style page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportStylePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportStylePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportStylePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportStylePageView.ReportStyleId">
      <summary>
        <para>Specifies the report visual style.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.ReportStyleId"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseReportTemplatePageView">
      <summary>
        <para>Provides a view for the Choose a Report Template page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportTemplatePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportTemplatePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportTemplatePageView.FillTemplates(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.Templates.TemplateInfo})">
      <summary>
        <para>Populates the list of available templates.</para>
      </summary>
      <param name="templates">A collection of <see cref="T:DevExpress.XtraReports.Wizards.Templates.TemplateInfo"/> objects.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportTemplatePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportTemplatePageView.TemplateReportType">
      <summary>
        <para>Specifies the selected template report type.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView">
      <summary>
        <para>Provides a view for the Choose a Report Type page of the Report Wizard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView.ReportType">
      <summary>
        <para>If implemented, specifies the selected report type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.ReportType"/> enumeration value.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView.ReportTypeChanged">
      <summary>
        <para>Occurs when the report type selected on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView">
      <summary>
        <para>Provides a view for the Specify Summary Options (Multi-Query Version) page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView.FillSummaryOptions(DevExpress.XtraReports.Wizards.ColumnInfoSummaryOptions[])">
      <summary>
        <para>Fills the summary options check boxes on the Specify Summary Options (Multi-Query Version) page of the Report Wizard.</para>
      </summary>
      <param name="summaryOptions">An array of <see cref="T:DevExpress.XtraReports.Wizards.ColumnInfoSummaryOptions"/> objects that contain information required to calculate summary functions.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView.IgnoreNullValues">
      <summary>
        <para>Specifies the state of the Ignore null values check box.</para>
      </summary>
      <value>true to activate the check box; otherwise false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView.ShowWaitIndicator(System.Boolean)">
      <summary>
        <para>Shows or hides the wait indicator on a wizard page.</para>
      </summary>
      <param name="show">true to show the wait indicator; otherwise false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView.#ctor">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView.GetColumnFieldInfo">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView.GetDataFieldInfo">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView.GetRowFieldInfo">
      <summary />
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView.HeaderDescription">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureCrossTabPageView.Initialize(System.Object,System.String,System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.CrossTab.CrossTabFieldInfoBase})">
      <summary />
      <param name="dataSource"></param>
      <param name="dataMember"></param>
      <param name="fieldInfo"></param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView">
      <summary>
        <para>Provides a view for the Specify Report Page Settings page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.BottomMargin">
      <summary>
        <para>Specifies the bottom margin of the report page, measured in the units defined by the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit"/> property.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the page’s bottom margin.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Initialize(System.Collections.Generic.IEnumerable{System.Drawing.Printing.PaperSize},System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.GraphicsUnitInfo})">
      <summary>
        <para>Initializes a wizard page with the specified options.</para>
      </summary>
      <param name="paperSizes">A collection of <see cref="T:System.Drawing.Printing.PaperSize"/> objects that contains predefined sizes of the report’s pages.</param>
      <param name="units">A collection of <see cref="T:DevExpress.XtraReports.Wizards.GraphicsUnitInfo"/> objects that contains available units of measurement.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Landscape">
      <summary>
        <para>Specifies whether or not the page orientation is landscape.</para>
      </summary>
      <value>true, if the page orientation is landscape; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.LeftMargin">
      <summary>
        <para>Specifies the left margin of the report page, measured in the units defined by the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit"/> property.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the page’s left margin.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.OrientationChanged">
      <summary>
        <para>Occurs when the page orientation is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperHeight">
      <summary>
        <para>Specifies the paper height (measured in report units defined by the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit"/> property). This can only be set if the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperKind"/> property is set to Custom.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the paper height.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperKind">
      <summary>
        <para>Specifies the standard paper sizes for the report.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Printing.PaperKind"/> enumeration value. By default, this property is set to PaperKind.Letter.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperKindChanged">
      <summary>
        <para>Occurs when the report’s paper type is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperSizeChanged">
      <summary>
        <para>Occurs when the width or height of the report’s paper is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperWidth">
      <summary>
        <para>Specifies the paper width (measured in report units defined by the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit"/> property). This can only be set if the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.PaperKind"/> property is set to Custom.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the paper width.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.RefreshViewer">
      <summary>
        <para>Refreshes content of the Specify Report Page Settings wizard page according to specified page settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.RightMargin">
      <summary>
        <para>Specifies the right margin of the report page, measured in the units defined by the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit"/> property.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the page’s right margin.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.SetDisplayFormat(System.String,System.String)">
      <summary>
        <para>Sets the format settings applied to the display text of editors on the Specify Report Page Settings wizard page.</para>
      </summary>
      <param name="mask">A string that specifies the editors’ mask.</param>
      <param name="formatString">A string that specifies the output format for the editors’ text.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.TopMargin">
      <summary>
        <para>Specifies the top margin of the report page, measured in the units defined by the <see cref="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit"/> property.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that specifies the page’s top margin.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.Unit">
      <summary>
        <para>Specifies the selected measure units of the report’s page.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.GraphicsUnit"/> value.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.ConfigureReportPageSettingsPageView.UnitChanged">
      <summary>
        <para>Occurs when the Unit property value is changed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView">
      <summary>
        <para>Provides a view for the Customize the Label Options page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.BaseLayout">
      <summary>
        <para>Gets the Layout Hierarchical Structure that is used to arrange the content of the current Report Wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutControl"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.BottomMargin">
      <summary>
        <para>Specifies the value of the Bottom Margin editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.FillPageSizeList(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.PaperKindViewInfo})">
      <summary>
        <para>Populates the list of the available page sizes.</para>
      </summary>
      <param name="paperKinds">A collection of <see cref="T:DevExpress.XtraReports.Wizards.PaperKindViewInfo"/> objects.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.Height">
      <summary>
        <para>Specifies the value of the Label Height editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.HorizontalPitch">
      <summary>
        <para>Specifies the value of the Horizontal Pitch editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.LabelInformationChanged">
      <summary>
        <para>Occurs when label information displayed in the dedicated editors is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.LabelsCountHorz">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.LabelsCountVert">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.LeftMargin">
      <summary>
        <para>Specifies the value of the Left Margin editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.PaperKindFormattedText">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.RightMargin">
      <summary>
        <para>Specifies the value of the Right Margin editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.SelectedPaperKindChanged">
      <summary>
        <para>Occurs when the entry selected in the Page Size drop-down list is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.SelectedPaperKindId">
      <summary>
        <para>Specifies the entry selected in the Page Size drop-down list.</para>
      </summary>
      <value>An integer value that is an index of the selected entry.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.TopMargin">
      <summary>
        <para>Specifies the value of the Top Margin editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.Unit">
      <summary>
        <para>Specifies the state of the Inch and Millimeter radio buttons.</para>
      </summary>
      <value>A value that specifies the state of the Inch and Millimeter radio buttons.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.UnitChanged">
      <summary>
        <para>Occurs when the state of the Inch and Millimeter radio buttons is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.VerticalPitch">
      <summary>
        <para>Specifies the value of the Vertical Pitch editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView.Width">
      <summary>
        <para>Specifies the value of the Label Width editor.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.MapReportTemplateFieldsPageView">
      <summary>
        <para>Provides a view for the Map Report Template Fields page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MapReportTemplateFieldsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.MapReportTemplateFieldsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MapReportTemplateFieldsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MapReportTemplateFieldsPageView.SetDataSource(System.Object,System.String,System.String)">
      <summary>
        <para>Sets the data source with the specified settings for providing data to template fields.</para>
      </summary>
      <param name="dataSource">An object that specifies the data source.</param>
      <param name="dataSourceName">A string that specifies the data source name.</param>
      <param name="dataMember">A string that specifies the data source member.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MapReportTemplateFieldsPageView.TemplateFields">
      <summary>
        <para>Specifies the collection of available template fields.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraReports.Wizards.Templates.TemplateField"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView">
      <summary>
        <para>Provides a view for the Add Grouping Levels (Multi-Query Version) page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.ActiveAvailableColumnsChanged">
      <summary>
        <para>Occurs when the active record in the list of the available fields is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.ActiveGroupingLevelChanged">
      <summary>
        <para>Occurs when the active record in the list of grouping levels is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.AddGroupingLevelClicked">
      <summary>
        <para>Occurs when the button for adding a grouping level is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.CombineGroupingLevelClicked">
      <summary>
        <para>Occurs when the button for combining grouping levels is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.EnableAddGroupingLevelButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the button that adds grouping levels.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.EnableCombineGroupingLevelButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the button that combines grouping levels.</para>
      </summary>
      <param name="enable">true, to activate the button; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.EnableGroupingLevelDown(System.Boolean)">
      <summary>
        <para>Activates or deactivates the button that moves a grouping level down.</para>
      </summary>
      <param name="enable">true, to activate the button; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.EnableGroupingLevelUp(System.Boolean)">
      <summary>
        <para>Activates or deactivates the button that moves a grouping level up.</para>
      </summary>
      <param name="enable">true, to activate the button; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.EnableRemoveGroupingLevelButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the button that removes a grouping level.</para>
      </summary>
      <param name="enable">true, to activate the button; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.FillAvailableColumns(DevExpress.Data.XtraReports.DataProviders.ColumnInfo[])">
      <summary>
        <para>Populates the list of available columns.</para>
      </summary>
      <param name="columns">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.FillGroupingLevels(DevExpress.XtraReports.Wizards.GroupingLevelInfo[])">
      <summary>
        <para>Populates the list of grouping levels.</para>
      </summary>
      <param name="groupingLevels">An array of <see cref="T:DevExpress.XtraReports.Wizards.GroupingLevelInfo"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.GetActiveAvailableColumns">
      <summary>
        <para>Gets an array of records selected in the list of available fields.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.GetActiveGroupingLevel">
      <summary>
        <para>Gets the record selected in the list of grouping levels.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraReports.Wizards.GroupingLevelInfo"/> object.</returns>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.GroupingLevelDownClicked">
      <summary>
        <para>Occurs when the button for moving a grouping level down is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.GroupingLevelUpClicked">
      <summary>
        <para>Occurs when the button for moving a grouping level up is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.MasterDetailQueryInfo})">
      <summary>
        <para>Initializes a wizard page with the specified options.</para>
      </summary>
      <param name="queries">A collection of <see cref="T:DevExpress.XtraReports.Wizards.MasterDetailQueryInfo"/> objects.</param>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.RemoveGroupingLevelClicked">
      <summary>
        <para>Occurs when the button for removing a grouping level is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.SelectedQuery">
      <summary>
        <para>Specifies a query selected on a wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.MasterDetailQueryInfo"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.SelectedQueryChanged">
      <summary>
        <para>Occurs when a query selected on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.SetActiveGroupingLevel(DevExpress.XtraReports.Wizards.GroupingLevelInfo)">
      <summary>
        <para>Changes the active record in the list of grouping levels.</para>
      </summary>
      <param name="groupingLevel">A <see cref="T:DevExpress.XtraReports.Wizards.GroupingLevelInfo"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailAddGroupingLevelPageView.ShowWaitIndicator(System.Boolean)">
      <summary>
        <para>Shows or hides the wait indicator on a wizard page.</para>
      </summary>
      <param name="show">true, to show the wait indicator; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView">
      <summary>
        <para>Provides a view for the Specify Summary Options (Multi-Query Version) page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.ColumnSummariesChanged">
      <summary>
        <para>Occurs when the collection of currently selected summaries is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.FillColumnSummaries(DevExpress.XtraReports.Wizards.ColumnSummariesSet)">
      <summary>
        <para>Fills the Summary functions drop-downs with available summary types.</para>
      </summary>
      <param name="summaries">An object that contains information about selected summary functions.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.IgnoreNullValues">
      <summary>
        <para>Specifies the state of the Ignore null values check box.</para>
      </summary>
      <value>true, to activate the check box; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.MasterDetailQueryInfo})">
      <summary>
        <para>Initializes a wizard page with the list of available queries.</para>
      </summary>
      <param name="queries">A collection of <see cref="T:DevExpress.XtraReports.Wizards.MasterDetailQueryInfo"/> objects.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.SelectedQuery">
      <summary>
        <para>Specifies a query selected on a wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.MasterDetailQueryInfo"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.SelectedQueryChanged">
      <summary>
        <para>Occurs when a query selected on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailChooseSummaryOptionsPageView.SetAvailableColumns(DevExpress.Data.XtraReports.DataProviders.ColumnInfo[])">
      <summary>
        <para>Fills the Fields drop-downs with available columns.</para>
      </summary>
      <param name="columns">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView">
      <summary>
        <para>Provides a view for the Choose Fields to Display in a Report (Multi-Query Version) page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView.Changed">
      <summary>
        <para>Occurs when the collection of data members and fields selected on a wizard page is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView.DoAddFieldsAction(System.Action,DevExpress.XtraReports.Wizards.Presenters.MasterDetailFieldModel)">
      <summary>
        <para>Performs actions with the view required before and after adding fields.</para>
      </summary>
      <param name="action">A <see cref="T:System.Action"/> delegate.</param>
      <param name="rootAddedField">A <see cref="T:DevExpress.XtraReports.Wizards.Presenters.MasterDetailFieldModel"/> object that specifies the root field.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView.DoRemoveFieldsAction(System.Action)">
      <summary>
        <para>Performs actions with the view required before and after removing fields.</para>
      </summary>
      <param name="action">A <see cref="T:System.Action"/> delegate.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MasterDetailSelectDataMembersPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.Presenters.MasterDetailQueryModel},System.Collections.ObjectModel.ObservableCollection{DevExpress.XtraReports.Wizards.Presenters.MasterDetailFieldModel},System.Boolean)">
      <summary />
      <param name="queriesModel"></param>
      <param name="fieldsModel"></param>
      <param name="singleDataMemberOnly"></param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView">
      <summary>
        <para>Provides a view for the Choose Fields to Display in a Report (Obsolete) page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.ActiveTableChanged">
      <summary>
        <para>Occurs when the active table in the list of the available tables is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.FillColumns(DevExpress.Data.XtraReports.DataProviders.ColumnInfo[])">
      <summary>
        <para>Populates the list of available data columns corresponding to a specific data member.</para>
      </summary>
      <param name="columns">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.FillTables(DevExpress.Data.XtraReports.DataProviders.TableInfo[])">
      <summary>
        <para>Populates the list of available data members displayed on this wizard page.</para>
      </summary>
      <param name="tables">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.TableInfo"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.GetSelectedColumns">
      <summary>
        <para>Returns the list of data columns that are selected on this wizard page.</para>
      </summary>
      <returns>An array of <see cref="T:System.String"/> value, specifying the column names.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.SelectedTable">
      <summary>
        <para>Specifies the report data member.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value, specifying the report data member.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView.SetSelectedColumns(System.String[])">
      <summary>
        <para>Selects the specified data columns</para>
      </summary>
      <param name="selectedColumns">An array of <see cref="T:System.String"/> values, specifying the column names.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView">
      <summary>
        <para>This page is no longer used in the current Report Wizard implementation. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.MultiQuerySelectFieldsPageView"/> class instead (corresponding to the Choose Fields to Display in a Report (Multi-Query Version) wizard page).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.ActiveColumnsChanged">
      <summary>
        <para>Occurs when the collection of the currently selected columns is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.AddAllColumnsClicked">
      <summary>
        <para>Occurs when the Add All Columns button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.AddColumnsClicked">
      <summary>
        <para>Occurs when the Add Columns button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.EnableAddAllColumnsButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Add All Columns button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.EnableAddColumnsButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Add Columns button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.EnableRemoveAllColumnsButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Remove All Columns button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button, false to deactivate the button.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.EnableRemoveColumnsButton(System.Boolean)">
      <summary>
        <para>Activates or deactivates the Remove Columns button on a wizard page.</para>
      </summary>
      <param name="enable">true to activate the button; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.FillAvailableColumns(DevExpress.Data.XtraReports.DataProviders.ColumnInfo[])">
      <summary>
        <para>Populates the list of available commands.</para>
      </summary>
      <param name="columns">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects, specifying the columns to be added to the list.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.FillSelectedColumns(DevExpress.Data.XtraReports.DataProviders.ColumnInfo[])">
      <summary>
        <para>Populates the list of selected columns.</para>
      </summary>
      <param name="columns">An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects, specifying the columns to be added to the list.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.GetActiveAvailableColumns">
      <summary>
        <para>Gets a collection of the items selected in the list of available columns.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.GetActiveSelectedColumns">
      <summary>
        <para>Gets a collection of the items selected in the list of selected columns.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Data.XtraReports.DataProviders.ColumnInfo"/> objects.</returns>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.RemoveAllColumnsClicked">
      <summary>
        <para>Occurs when the Remove All Columns button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.RemoveColumnsClicked">
      <summary>
        <para>Occurs when the Remove Columns button is clicked on a wizard page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView.ShowWaitIndicator(System.Boolean)">
      <summary>
        <para>Shows or hides the wait indicator on a wizard page.</para>
      </summary>
      <param name="visibility">true to show the wait indicator; otherwise false.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.SelectCrossTabDataMemberPageView">
      <summary />
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectCrossTabDataMemberPageView.#ctor">
      <summary />
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectCrossTabDataMemberPageView.HeaderDescription">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectCrossTabDataMemberPageView.Initialize(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.Presenters.MasterDetailFieldModel})">
      <summary />
      <param name="dataModel"></param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectCrossTabDataMemberPageView.SelectedDataMember">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView">
      <summary>
        <para>Provides a view for the Select the Label Type page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.BaseLayout">
      <summary>
        <para>Gets the Layout Hierarchical Structure that is used to arrange the content of the current Report Wizard page.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutControl"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.CurrentPaperKindData">
      <summary>
        <para>Specifies the settings of the current paper kind.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.Labels.PaperKindData"/> value.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.FillLabelDetails(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.Labels.LabelDetails})">
      <summary>
        <para>Populates the Product Number drop-down list.</para>
      </summary>
      <param name="details">A collection of <see cref="T:DevExpress.XtraReports.Wizards.Labels.LabelDetails"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.FillLabelProducts(System.Collections.Generic.IEnumerable{DevExpress.XtraReports.Wizards.Labels.LabelProduct})">
      <summary>
        <para>Populates the Label Products drop-down list.</para>
      </summary>
      <param name="products">A collection of <see cref="T:DevExpress.XtraReports.Wizards.Labels.LabelProduct"/> objects.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.SelectedDetails">
      <summary>
        <para>Specifies the entry selected in the Product Number drop-down list.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.Labels.LabelDetails"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.SelectedLabelProductChanged">
      <summary>
        <para>Occurs when the active record in the Label Products drop-down list is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.SelectedLabelProductDetailsChanged">
      <summary>
        <para>Occurs when the active record in the Product Number drop-down list is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.SelectedProduct">
      <summary>
        <para>Specifies the entry selected in the Label Products drop-down list.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.Labels.LabelProduct"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView.UpdateLabelInformation">
      <summary>
        <para>Updates label information displayed in the dedicated editors.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.SetReportTitlePageView">
      <summary>
        <para>Provides a view for the Set the Report Title page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SetReportTitlePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.SetReportTitlePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SetReportTitlePageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SetReportTitlePageView.Title">
      <summary>
        <para>If implemented, specifies the report title text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView">
      <summary>
        <para>Provides a view for the Specify Report Template Options page of the Report Wizard in WinForms applications.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.AllowDiscountEditing">
      <summary>
        <para>Specifies whether the discount value can be edited.</para>
      </summary>
      <value>true, if the discount value can be edited; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.AllowTaxEditing">
      <summary>
        <para>Specifies whether the tax value can be edited.</para>
      </summary>
      <value>true, if the tax value can be edited; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.Changed">
      <summary>
        <para>Occurs when any property on the current wizard page changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.CurrencyFormat">
      <summary>
        <para>Specifies the index of the selected currency format.</para>
      </summary>
      <value>An index of the selected currency format.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.CurrencySymbol">
      <summary>
        <para>Specifies the selected currency symbol.</para>
      </summary>
      <value>A string that specifies the currency symbol.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.DiscountValue">
      <summary>
        <para>Specifies the discount value.</para>
      </summary>
      <value>An object that specifies the discount value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.DiscountValueRange">
      <summary>
        <para>Specifies to which range a discount value should be applied.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.TemplateValueRange"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.DiscountValueType">
      <summary>
        <para>Specifies the selected discount value type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.TemplateValueType"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.FillAvailableDiscountValueTypes(System.Collections.ICollection)">
      <summary>
        <para>Populates the list of available types for discount values.</para>
      </summary>
      <param name="discountValueTypes">A collection of discount value types.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.FillAvailableTaxValueTypes(System.Collections.ICollection)">
      <summary>
        <para>Populates the list of available types for tax values.</para>
      </summary>
      <param name="taxValueTypes">A collection of tax value types.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.FillCurrencyFormats(System.Collections.ICollection)">
      <summary>
        <para>Populates the list of available currency formats.</para>
      </summary>
      <param name="currencyFormats">A collection of currency formats.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.FillCurrencySymbols(System.Collections.ICollection)">
      <summary>
        <para>Populates the list of available currency symbols.</para>
      </summary>
      <param name="currencySymbols">A collection of currency symbols.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.HeaderDescription">
      <summary>
        <para>Specifies the description of the current wizard page.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the page description.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.SetDataSource(System.Object,System.String,System.String)">
      <summary>
        <para>Sets the data source with the specified settings for providing data to template fields.</para>
      </summary>
      <param name="dataSource">An object that specifies the data source.</param>
      <param name="dataSourceName">A string that specifies the data source name.</param>
      <param name="dataMember">A string that specifies the data source member.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.TaxInclusive">
      <summary>
        <para>Specifies whether the tax value is included in the price values.</para>
      </summary>
      <value>true, if the tax is included in the price values; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.TaxValue">
      <summary>
        <para>Specifies the tax value.</para>
      </summary>
      <value>An object that specifies the tax value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.TaxValueRange">
      <summary>
        <para>Specifies to which range a tax value should be applied.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.TemplateValueRange"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.TaxValueType">
      <summary>
        <para>Specifies the selected tax value type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.TemplateValueType"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.UpdateDiscountEditorProperties(System.Boolean,System.String)">
      <summary>
        <para>Updates the discount editor according to the specified settings.</para>
      </summary>
      <param name="isNumeric">true, if the discount value is manually entered; false, if this value is not specified or obtained from a data source</param>
      <param name="mask">A string that specifies the mask applied to the discount value.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.Views.SpecifyReportTemplateOptionsPageView.UpdateTaxEditorProperties(System.Boolean,System.String)">
      <summary>
        <para>Updates the tax editor according to the specified settings.</para>
      </summary>
      <param name="isNumeric">true, if the tax value is manually entered; false, if this value is not specified or obtained from a data source.</param>
      <param name="mask">A string that specifies the mask applied to the tax value.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.XtraReportModelUI">
      <summary>
        <para>Enables you to run the Report Wizard and obtain the resulting ReportModel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportModelUI.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Initializes a new instance of the XtraReportModelUI class with the specified storage service.</para>
      </summary>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface that provides a service functionality to store the data connection settings.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportModelUI.CreateReportModel(System.IServiceProvider)">
      <summary>
        <para>Creates the report model that stores default report settings.</para>
      </summary>
      <param name="serviceProvider">An object that implements the <see cref="T:System.IServiceProvider"/> interface.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/> object that stores report settings.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportModelUI.CreateReportModel(System.IServiceProvider,DevExpress.XtraReports.UI.XtraReport)">
      <summary>
        <para>Creates the report model that stores settings specified on the Specify Report Page Settings wizard page.</para>
      </summary>
      <param name="serviceProvider">An object that implements the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="report">An <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> object.</param>
      <returns>An <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/> object that stores report page settings.</returns>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1">
      <summary>
        <para>Enables you to register wizard page views with the corresponding presenters for a specified <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/>.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="dataSourceNameCreationService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,System.IServiceProvider,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="dataSourceNameCreationService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,System.IServiceProvider,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.UI.Wizard.DataSourceWizardSettings,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Excel.IExcelSchemaProvider,DevExpress.DataAccess.Wizard.Services.DataSourceTypes,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.Data.Utils.IDataDirectoryPatchingService)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="dataSourceNameCreationService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <param name="wizardSettings">A <see cref="T:DevExpress.DataAccess.UI.Wizard.DataSourceWizardSettings"/> object.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="excelSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</param>
      <param name="dataSourceTypes">A <see cref="T:DevExpress.DataAccess.Wizard.Services.DataSourceTypes"/> object.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="dataDirectoryPatchingService">An object implementing the <see cref="T:DevExpress.Data.Utils.IDataDirectoryPatchingService"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,System.IServiceProvider,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.Data.IDisplayNameProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="dataSourceNameCreationService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <param name="sqlWizardOptions">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,System.IServiceProvider,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Excel.IExcelSchemaProvider,DevExpress.DataAccess.Wizard.Services.DataSourceTypes)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="dataSourceNameCreationService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <param name="sqlWizardOptions">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="excelSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</param>
      <param name="dataSourceTypes">A <see cref="T:DevExpress.DataAccess.Wizard.Services.DataSourceTypes"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,System.IServiceProvider,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Excel.IExcelSchemaProvider,DevExpress.DataAccess.Wizard.Services.DataSourceTypes,System.Collections.Generic.IEnumerable{DevExpress.DataAccess.UI.Wizard.ProviderLookupItem},DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the DevExpress.DataAccess.Sql.IDBSchemaProvider interface.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="dataSourceNameCreationService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService"/> interface.</param>
      <param name="propertyGridServices">An object implementing the <see cref="T:System.IServiceProvider"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
      <param name="sqlWizardOptions">One or more <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/> enumeration values.</param>
      <param name="displayNameProvider">An object implementing the <see cref="T:DevExpress.Data.IDisplayNameProvider"/> interface.</param>
      <param name="excelSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Excel.IExcelSchemaProvider"/> interface.</param>
      <param name="dataSourceTypes">A <see cref="T:DevExpress.DataAccess.Wizard.Services.DataSourceTypes"/> object.</param>
      <param name="dataProviders">A collection of <see cref="T:DevExpress.DataAccess.UI.Wizard.ProviderLookupItem"/> objects.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Sql.IDBSchemaProvider,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IDataSourceNameCreationService,System.IServiceProvider,DevExpress.DataAccess.Wizard.OperationMode,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Wizard.SensitiveInfoSavingBehavior,DevExpress.DataAccess.Wizard.MongoDBWizardOptions,DevExpress.DataAccess.Wizard.MongoDBSensitiveInfoSavingBehavior,DevExpress.Data.IDisplayNameProvider,DevExpress.DataAccess.Excel.IExcelSchemaProvider,DevExpress.DataAccess.Wizard.Services.DataSourceTypes,System.Collections.Generic.IEnumerable{DevExpress.DataAccess.UI.Wizard.ProviderLookupItem},DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.Data.Utils.IDataDirectoryPatchingService)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class with specified settings.</para>
      </summary>
      <param name="connectionStorage"></param>
      <param name="parameterService"></param>
      <param name="solutionTypesProvider"></param>
      <param name="connectionStringsProvider"></param>
      <param name="dbSchemaProvider"></param>
      <param name="dbSchemaProviderEx"></param>
      <param name="dataSourceNameCreationService"></param>
      <param name="propertyGridServices"></param>
      <param name="operationMode"></param>
      <param name="sqlWizardOptions"></param>
      <param name="databaseCredentialsSavingOption"></param>
      <param name="mongoDBWizardOptions"></param>
      <param name="mongoDBSensitiveInfoSavingBehavior"></param>
      <param name="displayNameProvider"></param>
      <param name="excelSchemaProvider"></param>
      <param name="dataSourceTypes"></param>
      <param name="dataProviders"></param>
      <param name="customQueryValidator"></param>
      <param name="dataDirectoryPatchingService"></param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.DataSourceFactory">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.XtraReports.Wizards.IDataSourceFactory"/> interface.</value>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.RegisterDependencies(DevExpress.Utils.IoC.IntegrityContainer)">
      <summary>
        <para>Registers wizard page views with the corresponding presenters for the specified <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/>.</para>
      </summary>
      <param name="container">A <see cref="T:DevExpress.Utils.IoC.IntegrityContainer"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1.ReportWizardSettings">
      <summary>
        <para>Provides access to the Report Wizard settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraReports.Wizards.ReportWizardSettings"/> object.</value>
    </member>
    <member name="N:DevExpress.XtraReports.Wizards3">
      <summary>
        <para>This namespace is no longer used. Use classes from the <see cref="N:DevExpress.XtraReports.Wizards"/> namespace instead.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.IWizardCustomizationService">
      <summary>
        <para>Obsolete. Use the DevExpress.XtraReports.Wizards.IWizardCustomizationService interface instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.IWizardCustomizationService.CustomizeDataSourceWizard(DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.XtraReports.Wizards3.XtraReportModel})">
      <summary>
        <para>Enables customization of the Data Source Wizard pages.</para>
      </summary>
      <param name="tool">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface, specifying the <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportModel"/>.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.IWizardCustomizationService.CustomizeReportWizard(DevExpress.DataAccess.UI.Wizard.IWizardCustomization{DevExpress.XtraReports.Wizards3.XtraReportModel})">
      <summary>
        <para>Enables customization of the Report Wizard pages.</para>
      </summary>
      <param name="tool">An object implementing the <see cref="T:DevExpress.DataAccess.UI.Wizard.IWizardCustomization`1"/> interface, specifying the <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportModel"/>.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.IWizardCustomizationService.TryCreateDataSource(DevExpress.DataAccess.Wizard.Model.IDataSourceModel,System.Object@,System.String@)">
      <summary>
        <para>Creates a new report data source with the specified settings.</para>
      </summary>
      <param name="model">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Model.IDataSourceModel"/> interface.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the data source.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value, specifying the data member.</param>
      <returns>true, if the data source has been created; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.IWizardCustomizationService.TryCreateReport(System.ComponentModel.Design.IDesignerHost,DevExpress.XtraReports.Wizards3.XtraReportModel,System.Object,System.String)">
      <summary>
        <para>Creates a new report with the specified settings.</para>
      </summary>
      <param name="designerHost">An object implementing the <see cref="T:System.ComponentModel.Design.IDesignerHost"/> interface that is used to manage designer transactions and components.</param>
      <param name="model">An <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportModel"/> object.</param>
      <param name="dataSource">A <see cref="T:System.Object"/> value, specifying the data source.</param>
      <param name="dataMember">A <see cref="T:System.String"/> value, specifying the data member.</param>
      <returns>true, if the report has been created; otherwise, false.</returns>
    </member>
    <member name="N:DevExpress.XtraReports.Wizards3.Presenters">
      <summary>
        <para>This namespace is no longer used. Use classes from the <see cref="N:DevExpress.XtraReports.Wizards.Presenters"/> namespace instead.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseEFConnectionStringPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ChooseEFConnectionStringPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ChooseEFConnectionStringPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IChooseEFConnectionStringPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.Data.Entity.IConnectionStringsProvider,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseEFConnectionStringPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IChooseEFConnectionStringPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface that provides access to the available connection strings.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface that provides a service functionality to store the data connection settings.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseEFDataMemberPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ChooseEFDataMemberPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ChooseEFDataMemberPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IChooseEFDataMemberPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseEFDataMemberPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IChooseEFDataMemberPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectBindingModePageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ChooseObjectBindingModePageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectBindingModePageEx`1.#ctor(DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Views.IChooseObjectBindingModePageView,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectBindingModePageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="wizardRunnerContext">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IChooseObjectBindingModePageView"/> interface.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectConstructorPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ChooseObjectConstructorPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectConstructorPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IChooseObjectConstructorPageView,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectConstructorPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IChooseObjectConstructorPageView"/> interface that provides a view for a wizard page.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value that specifies which modes of operation are available.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectMemberPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ChooseObjectMemberPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectMemberPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IChooseObjectMemberPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectMemberPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IChooseObjectMemberPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value that specifies which modes of operation are available.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectTypePageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ChooseObjectTypePageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectTypePageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IChooseObjectTypePageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ChooseObjectTypePageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IChooseObjectTypePageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value that specifies which modes of operation are available.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureEFConnectionStringPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ConfigureEFConnectionStringPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureEFConnectionStringPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureEFConnectionStringPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IConnectionStorageService)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureEFConnectionStringPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureEFConnectionStringPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="connectionStorageService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface that provides a service functionality to store the data connection settings.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureEFStoredProceduresPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ConfigureEFStoredProceduresPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureEFStoredProceduresPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureEFStoredProceduresPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureEFStoredProceduresPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureEFStoredProceduresPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureExcelFileColumnsPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ConfigureExcelFileColumnsPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureExcelFileColumnsPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureExcelFileColumnsPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureExcelFileColumnsPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureExcelFileColumnsPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureQueryPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ConfigureQueryPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureQueryPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureQueryPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.IResultSchemaProvider,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureQueryPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureQueryPageView"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="options">An object that specifies the wizard options.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="resultSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IResultSchemaProvider"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureQueryPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureQueryPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.SqlWizardOptions,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureQueryPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureQueryPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="options">An object that specifies the wizard options.</param>
      <param name="dbSchemaProviderEx">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface that provides custom validation logic for SQL queries.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureSqlParametersPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ConfigureSqlParametersPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureSqlParametersPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureParametersPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Sql.IResultSchemaProvider,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureSqlParametersPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureParametersPageView"/> interface.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="dbSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface.</param>
      <param name="resultSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IResultSchemaProvider"/> interface.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface.</param>
      <param name="options">An object that specifies the wizard options.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ConfigureSqlParametersPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IConfigureParametersPageView,DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Sql.IDBSchemaProviderEx,DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator,DevExpress.DataAccess.Wizard.SqlWizardOptions)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ConfigureSqlParametersPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IConfigureParametersPageView"/> interface that provides a view for a wizard page.</param>
      <param name="context">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage query parameters.</param>
      <param name="dbSchemaProvider">An object implementing the <see cref="T:DevExpress.DataAccess.Sql.IDBSchemaProviderEx"/> interface that provides the capability to customize the data source schema.</param>
      <param name="customQueryValidator">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.ICustomQueryValidator"/> interface that provides custom validation logic for SQL queries.</param>
      <param name="options">An object that specifies the wizard options.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ObjectConstructorParametersPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ObjectConstructorParametersPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ObjectConstructorParametersPageEx`1.#ctor(DevExpress.DataAccess.Wizard.IWizardRunnerContext,DevExpress.DataAccess.Wizard.Views.IObjectConstructorParametersPageView,DevExpress.DataAccess.Wizard.Services.IParameterService)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ObjectConstructorParametersPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="wizardRunnerContext">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.IWizardRunnerContext"/> interface that provides the functionality to run the wizard.</param>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IObjectConstructorParametersPageView"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.ObjectMemberParametersPageEx`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.ObjectMemberParametersPageEx`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.ObjectMemberParametersPageEx`1.#ctor(DevExpress.DataAccess.Wizard.Views.IObjectMemberParametersPageView,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.DataAccess.Wizard.OperationMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.ObjectMemberParametersPageEx`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Views.IObjectMemberParametersPageView"/> interface that provides a view for a wizard page.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface that provides a service functionality to manage parameters.</param>
      <param name="operationMode">An <see cref="T:DevExpress.DataAccess.Wizard.OperationMode"/> enumeration value that specifies which modes of operation are available.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Presenters.SelectColumnsPage`1">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Presenters.SelectColumnsPage`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.SelectColumnsPage`1.#ctor(DevExpress.XtraReports.Wizards.Views.ISelectColumnsPageView)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.SelectColumnsPage`1"/> class with the specified page view.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.XtraReports.Wizards.Views.ISelectColumnsPageView"/> interface.</param>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Presenters.SelectColumnsPage`1.#ctor(DevExpress.XtraReports.Wizards.Views.ISelectColumnsPageView,DevExpress.XtraReports.Wizards.IColumnInfoCache)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Presenters.SelectColumnsPage`1"/> class with the specified settings.</para>
      </summary>
      <param name="view">An object implementing the <see cref="T:DevExpress.XtraReports.Wizards.Views.ISelectColumnsPageView"/> interface.</param>
      <param name="columnInfoCache">An object implementing the <see cref="T:DevExpress.XtraReports.Wizards.IColumnInfoCache"/> interface. (Typically, it is the <see cref="T:DevExpress.XtraReports.Wizards.ColumnInfoCache"/> object.)</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.SqlWizardOptionsProvider">
      <summary>
        <para>Obsolete. This class is no longer used in the current Report Wizard implementation.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.SqlWizardOptionsProvider.#ctor(System.Func{DevExpress.DataAccess.Wizard.SqlWizardOptions})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.SqlWizardOptionsProvider"/> class with the specified wizard options.</para>
      </summary>
      <param name="getOptions">A function specifying the required <see cref="T:DevExpress.DataAccess.Wizard.SqlWizardOptions"/>.</param>
    </member>
    <member name="N:DevExpress.XtraReports.Wizards3.Views">
      <summary>
        <para>This namespace is no longer used. Use classes from the <see cref="N:DevExpress.XtraReports.Wizards.Views"/> namespace instead.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.AddGroupingLevelPageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.AddGroupingLevelPageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.AddGroupingLevelPageView.#ctor">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.AddGroupingLevelPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.ChooseObjectConstructorPageViewEx">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseObjectConstructorPageViewEx"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.ChooseObjectConstructorPageViewEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.ChooseObjectConstructorPageViewEx"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.ChooseReportLayoutPageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportLayoutPageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.ChooseReportLayoutPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.ChooseReportLayoutPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.ChooseReportStylePageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportStylePageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.ChooseReportStylePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.ChooseReportStylePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.ChooseReportTypePageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseReportTypePageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.ChooseReportTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.ChooseReportTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.ChooseSummaryOptionsPageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.ChooseSummaryOptionsPageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.ChooseSummaryOptionsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.ChooseSummaryOptionsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.CustomizeLabelPageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.CustomizeLabelPageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.CustomizeLabelPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.CustomizeLabelPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.IChooseReportTypePageViewExtended">
      <summary>
        <para>This interface is no longer used in the current Report Wizard implementation. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.IChooseReportTypePageView"/> interface instead.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.SelectColumnsPageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.SelectColumnsPageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.SelectColumnsPageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.SelectColumnsPageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.SelectLabelTypePageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.SelectLabelTypePageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.SelectLabelTypePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.SelectLabelTypePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.Views.SetReportTitlePageView">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.Views.SetReportTitlePageView"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.Views.SetReportTitlePageView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.Views.SetReportTitlePageView"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.XtraReportModel">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportModel"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.XtraReportModel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportModel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.XtraReportModel.#ctor(DevExpress.XtraReports.Wizards3.XtraReportModel)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportModel"/> class with the specified report model.</para>
      </summary>
      <param name="model">An <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportModel"/> object.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.XtraReportWizardPageFactory`2">
      <summary>
        <para>Obsolete. Use the <see cref="T:DevExpress.XtraReports.Wizards.XtraReportWizardClient`1"/> class instead.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
      <typeparam name="TClient"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.XtraReportWizardPageFactory`2.#ctor(DevExpress.DataAccess.Wizard.Services.IConnectionStorageService,DevExpress.DataAccess.Wizard.Services.IParameterService,DevExpress.Entity.ProjectModel.ISolutionTypesProvider,DevExpress.Data.Entity.IConnectionStringsProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportWizardPageFactory`2"/> class with the specified settings.</para>
      </summary>
      <param name="connectionStorage">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IConnectionStorageService"/> interface.</param>
      <param name="parameterService">An object implementing the <see cref="T:DevExpress.DataAccess.Wizard.Services.IParameterService"/> interface.</param>
      <param name="solutionTypesProvider">An object implementing the <see cref="T:DevExpress.Entity.ProjectModel.ISolutionTypesProvider"/> interface.</param>
      <param name="connectionStringsProvider">An object implementing the <see cref="T:DevExpress.Data.Entity.IConnectionStringsProvider"/> interface.</param>
    </member>
    <member name="T:DevExpress.XtraReports.Wizards3.XtraReportWizardRunner`2">
      <summary>
        <para>Obsolete. This class is no longer used in the current Report Wizard implementation.</para>
      </summary>
      <typeparam name="TModel"></typeparam>
      <typeparam name="TClient"></typeparam>
    </member>
    <member name="M:DevExpress.XtraReports.Wizards3.XtraReportWizardRunner`2.#ctor(DevExpress.LookAndFeel.UserLookAndFeel,System.Windows.Forms.IWin32Window)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraReports.Wizards3.XtraReportWizardRunner`2"/> class with the specified settings.</para>
      </summary>
      <param name="lookAndFeel">A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.</param>
      <param name="owner">An object implementing the <see cref="T:System.Windows.Forms.IWin32Window"/> interface.</param>
    </member>
  </members>
</doc>