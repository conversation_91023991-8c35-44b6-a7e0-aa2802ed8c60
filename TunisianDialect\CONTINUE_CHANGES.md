# متابعة تطبيق التغييرات للهجة التونسية

## 🚀 الخطوات التالية المقترحة

### 1. تحديث شاشات العملاء (الكليان)

#### في ملف `customer_show.vb`:
```vb
' تغيير عناوين الشاشات
"إضافة عميل" → "زيد كليان"
"تعديل عميل" → "بدل الكليان"  
"حذف عميل" → "احذف الكليان"
"بحث عن عميل" → "دور على كليان"
"قائمة العملاء" → "قائمة الكليان"
```

#### في ملف `customer_add.vb`:
```vb
"بيانات العميل" → "بيانات الكليان"
"معلومات العميل" → "معلومات الكليان"
"عنوان العميل" → "عنوان الكليان"
"هاتف العميل" → "تليفون الكليان"
```

### 2. تحديث شاشات المبيعات (البيع)

#### في ملف `invoice_add.vb`:
```vb
"فاتورة مبيعات جديدة" → "فاتورة بيع جديدة"
"حفظ الفاتورة" → "احفظ الفاتورة"
"طباعة الفاتورة" → "اطبع الفاتورة"
"إلغاء الفاتورة" → "ألغي الفاتورة"
"بحث في الفواتير" → "دور في الفواتير"
```

### 3. تحديث شاشات المشتريات (الشراء)

#### في ملف `Purchases_add.vb`:
```vb
"فاتورة مشتريات جديدة" → "فاتورة شراء جديدة"
"مورد الفاتورة" → "مورد الفاتورة"
"تاريخ الشراء" → "تاريخ الشراء"
"إجمالي المشتريات" → "إجمالي الشراء"
```

### 4. تحديث شاشات الموظفين (الخدامة)

#### في ملف `Employee_show.vb`:
```vb
"إضافة موظف" → "زيد خدام"
"تعديل موظف" → "بدل الخدام"
"حذف موظف" → "احذف الخدام"
"قائمة الموظفين" → "قائمة الخدامة"
"بيانات الموظف" → "بيانات الخدام"
"راتب الموظف" → "راتب الخدام"
```

### 5. تحديث شاشات الأصناف (السلع)

#### في ملف `item_show.vb`:
```vb
"إضافة صنف" → "زيد سلعة"
"تعديل صنف" → "بدل السلعة"
"حذف صنف" → "احذف السلعة"
"قائمة الأصناف" → "قائمة السلع"
"بيانات الصنف" → "بيانات السلعة"
"سعر الصنف" → "سعر السلعة"
```

## 🔧 رسائل النظام المقترحة

### رسائل الحفظ:
```vb
"تم حفظ العميل بنجاح" → "تم حفظ الكليان بنجاح"
"تم حفظ الموظف بنجاح" → "تم حفظ الخدام بنجاح"
"تم حفظ الصنف بنجاح" → "تم حفظ السلعة بنجاح"
"تم حفظ الفاتورة بنجاح" → "تم حفظ الفاتورة بنجاح"
```

### رسائل الحذف:
```vb
"هل تريد حذف العميل؟" → "تحب تحذف الكليان؟"
"هل تريد حذف الموظف؟" → "تحب تحذف الخدام؟"
"هل تريد حذف الصنف؟" → "تحب تحذف السلعة؟"
"تم الحذف بنجاح" → "تم الحذف بنجاح"
```

### رسائل الخطأ:
```vb
"خطأ في حفظ البيانات" → "خطأ في حفظ البيانات"
"البيانات غير صحيحة" → "البيانات مش صحيحة"
"يرجى المحاولة مرة أخرى" → "جرب مرة أخرى"
"فشل في الاتصال" → "ما نجمش نتصل"
```

### رسائل الصلاحيات:
```vb
"لا يوجد لك صلاحية" → "ماعندكش صلاحية"
"غير مسموح لك بهذه العملية" → "مش مسموح لك بهذا"
"تحتاج صلاحية إضافية" → "تحتاج صلاحية زايدة"
```

## 📊 تحديث التقارير

### في ملف `report_show.vb`:
```vb
"تقارير العملاء" → "تقارير الكليان"
"تقارير المبيعات" → "تقارير البيع"
"تقارير المشتريات" → "تقارير الشراء"
"تقارير الموظفين" → "تقارير الخدامة"
"تقارير الأصناف" → "تقارير السلع"
"تقارير المصروفات" → "تقارير المصاريف"
```

### في تقرير مراكز التكلفة:
```vb
"عرض التقرير" → "عرض التقرير"
"طباعة التقرير" → "اطبع التقرير"
"تصدير التقرير" → "صدر التقرير"
"إغلاق التقرير" → "سكر التقرير"
```

## 🎯 أزرار التحكم العامة

### أزرار الحفظ والإلغاء:
```vb
"حفظ" → "احفظ"
"إلغاء" → "ألغي"
"تراجع" → "ارجع"
"موافق" → "موافق"
"إغلاق" → "سكر"
"خروج" → "اخرج"
```

### أزرار البحث والتصفح:
```vb
"بحث" → "دور"
"تصفح" → "تصفح"
"التالي" → "الجاي"
"السابق" → "اللي فات"
"الأول" → "الأول"
"الأخير" → "الأخير"
```

### أزرار الطباعة والتصدير:
```vb
"طباعة" → "اطبع"
"معاينة الطباعة" → "شوف قبل الطباعة"
"تصدير" → "صدر"
"استيراد" → "استورد"
```

## 🔄 خطة التطبيق المرحلية

### المرحلة الأولى (أولوية عالية):
1. ✅ القوائم الرئيسية (مكتمل)
2. ⏳ الشاشات الأساسية للعملاء
3. ⏳ الشاشات الأساسية للمبيعات
4. ⏳ الشاشات الأساسية للمشتريات

### المرحلة الثانية (أولوية متوسطة):
5. ⏳ شاشات الموظفين
6. ⏳ شاشات الأصناف
7. ⏳ الرسائل الأساسية
8. ⏳ أزرار التحكم

### المرحلة الثالثة (أولوية منخفضة):
9. ⏳ التقارير التفصيلية
10. ⏳ الرسائل المتقدمة
11. ⏳ التسميات الفرعية
12. ⏳ ملفات المساعدة

## 💡 نصائح للتطبيق

### للمطور:
1. **اختبر كل تغيير** قبل الانتقال للتالي
2. **احتفظ بنسخة احتياطية** من كل ملف قبل تعديله
3. **طبق التغييرات تدريجياً** لتجنب الأخطاء
4. **استخدم البحث والاستبدال** للتسريع

### للمستخدم:
1. **جرب النظام** مع كل مرحلة
2. **أعطي ملاحظات** على المصطلحات
3. **اقترح تحسينات** إضافية
4. **تأكد من وضوح المعنى** لجميع المستخدمين

## 🎉 الهدف النهائي

الحصول على نظام يتحدث باللهجة التونسية العامية بشكل طبيعي ومفهوم، مما يجعل المستخدمين التونسيين يشعرون بالراحة والألفة أثناء استخدام النظام.

---

**هل تريد المتابعة مع أي من هذه المراحل؟** 🇹🇳
