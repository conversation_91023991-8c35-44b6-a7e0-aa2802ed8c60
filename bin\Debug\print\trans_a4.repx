﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.trans_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="trans_a4" Margins="0, 0, 25, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="trans_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="25" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="6" ControlType="DetailBand" Name="Detail1" HeightF="26.04167">
          <Controls>
            <Item1 Ref="7" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="827.0001,25" LocationFloat="0, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="8" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="9" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.1005432128906252" TextFormatString="{0:#,#}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="10" Expression="[trans_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="11" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="12" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.98549850463867172" TextFormatString="{0:#,#}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="13" Expression="[trans_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="14" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="15" ControlType="XRTableCell" Name="XrTableCell6" Weight="0.918644561767578" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="16" Expression="[trans_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="17" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.2216995239257815" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="18" Expression="[trans_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="19" ControlType="XRTableCell" Name="XrTableCell8" Weight="4.0436148071289058" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="20" Expression="[trans_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="21" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="22" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
        <Item2 Ref="23" ControlType="ReportFooterBand" Name="ReportFooter1" HeightF="221.4167">
          <Controls>
            <Item1 Ref="24" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="490.7396, 190.0834" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="25" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="26" UseFont="false" />
            </Item1>
            <Item2 Ref="27" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="89.61462, 87.91669" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="28" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="29" UseFont="false" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="30" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="89.61462, 115.0834" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="31" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
            </Item3>
            <Item4 Ref="33" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="508.3646, 87.91669" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="34" UseFont="false" UseTextAlignment="false" />
            </Item4>
            <Item5 Ref="35" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="508.3646, 113" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="36" UseFont="false" UseTextAlignment="false" />
            </Item5>
            <Item6 Ref="37" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="5.864525, 190.0834" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="38" UseFont="false" UseTextAlignment="false" />
            </Item6>
            <Item7 Ref="39" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="5.864525, 154.5834" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <StylePriority Ref="40" UseFont="false" UseTextAlignment="false" />
            </Item7>
            <Item8 Ref="41" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="15.50004, 20.83333" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="42" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
                  <Cells>
                    <Item1 Ref="43" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="44" Expression="[trans_print].[trans_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item1>
                    <Item2 Ref="45" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" Padding="2,2,0,0,100" />
                  </Cells>
                </Item1>
                <Item2 Ref="46" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
                  <Cells>
                    <Item1 Ref="47" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell11" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="48" Expression="[trans_print].[total_trans]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="49" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="50" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" Padding="2,2,0,0,100">
                      <StylePriority Ref="51" UsePadding="false" />
                    </Item2>
                  </Cells>
                </Item2>
              </Rows>
              <StylePriority Ref="52" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item8>
            <Item9 Ref="53" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="526.5314,25" LocationFloat="289.9686, 45.83333" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="54" Expression="[trans_print].[total_string]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="55" UseFont="false" UseTextAlignment="false" />
            </Item9>
            <Item10 Ref="56" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="526.5315,25" LocationFloat="289.9686, 20.83333" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="57" Expression="[trans_print].[trans_note]" PropertyName="Text" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="58" UseFont="false" UseTextAlignment="false" />
            </Item10>
          </Controls>
        </Item2>
      </Bands>
    </Item4>
    <Item5 Ref="59" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="112.5">
      <Controls>
        <Item1 Ref="60" ControlType="XRLine" Name="XrLine2" SizeF="813.0001,4.791673" LocationFloat="5.864525, 0" />
        <Item2 Ref="61" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="546.1459, 17.45833" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="62" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="63" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="64" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="546.1459, 48.79166" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="65" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="66" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="67" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="124.5602,98.16664" LocationFloat="15.50004, 4.791673">
          <ExpressionBindings>
            <Item1 Ref="68" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item4>
      </Controls>
    </Item5>
    <Item6 Ref="69" ControlType="PageHeaderBand" Name="PageHeader" HeightF="242.2084">
      <Controls>
        <Item1 Ref="70" ControlType="XRLine" Name="XrLine1" SizeF="813.0001,4.791673" LocationFloat="3.499858, 0" />
        <Item2 Ref="71" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="225.1042,31.25" LocationFloat="22.89585, 101.5625" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="72" Expression="[store2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="73" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="74" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="الي مخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="248, 101.5625" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="75" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="76" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="827,33.33334" LocationFloat="0, 208.2501" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="77" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="78" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.6964836904534818" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="79" UsePadding="false" />
                </Item1>
                <Item2 Ref="80" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.62367750488082152" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="81" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.58136839300753063" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="82" UsePadding="false" />
                </Item3>
                <Item4 Ref="83" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.77315790733206891" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item5 Ref="84" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5590196630162829" Text="اسم الصنف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="85" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="86" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="82.89587, 38.62495">
          <ExpressionBindings>
            <Item1 Ref="87" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item5>
        <Item6 Ref="88" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="672.9999, 7.291667">
          <ExpressionBindings>
            <Item1 Ref="89" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item6>
        <Item7 Ref="90" ControlType="XRLabel" Name="XrLabel19" Text="تحويلات مخازن" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="22.89585, 7.291667" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="91" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="92" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="22.89585, 71.37498" Padding="2,2,0,0,100">
          <StylePriority Ref="93" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="94" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="248, 155.7501" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="95" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="96" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="657.3751, 155.7501" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="97" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="98" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="225.1043,31.25004" LocationFloat="22.89585, 155.7501" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="99" Expression="[trans_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="100" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="101" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="277.0833,31.24999" LocationFloat="380.2917, 155.7501" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="102" Expression="[trans_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="103" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="104" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="من مخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="657.3751, 101.5625" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="105" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="106" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="277.0832,31.25" LocationFloat="380.2918, 101.5625" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="107" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="108" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
      </Controls>
    </Item6>
    <Item7 Ref="109" ControlType="PageFooterBand" Name="PageFooter" HeightF="38.54164">
      <Controls>
        <Item1 Ref="110" ControlType="XRLine" Name="XrLine4" SizeF="813.0001,4.791673" LocationFloat="9.999998, 0" />
        <Item2 Ref="111" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="22.89584, 13.45832" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="112" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="113" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="752.375, 13.45832" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="114" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="115" ControlType="XRPageInfo" Name="XrPageInfo3" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="703.4166, 13.45832" Padding="2,2,0,0,100">
          <StylePriority Ref="116" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="117" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="118" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="119" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="120" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="121" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="122" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>