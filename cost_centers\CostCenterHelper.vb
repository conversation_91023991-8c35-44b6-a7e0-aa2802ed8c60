Imports System.Data.SqlClient
Imports DevExpress.XtraEditors

''' <summary>
''' كلاس مساعد لإدارة مراكز التكلفة وربطها بالعمليات المختلفة
''' </summary>
Public Class CostCenterHelper
    
    ''' <summary>
    ''' تحميل مراكز التكلفة النشطة في ComboBox أو LookUpEdit
    ''' </summary>
    Public Shared Sub LoadActiveCostCenters(control As Object, Optional includeEmpty As Boolean = True)
        Try
            Dim sql = "SELECT cost_center_id, cost_center_code, cost_center_name, " &
                     "cost_center_code + ' - ' + cost_center_name as display_name " &
                     "FROM cost_centers WHERE is_active = 1 ORDER BY cost_center_code"
            
            Dim adapter As New SqlDataAdapter(sql, sqlconn)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            
            ' إضافة صف فارغ إذا طُلب ذلك
            If includeEmpty Then
                Dim emptyRow = dataTable.NewRow()
                emptyRow("cost_center_id") = DBNull.Value
                emptyRow("cost_center_code") = ""
                emptyRow("cost_center_name") = "-- اختر مركز التكلفة --"
                emptyRow("display_name") = "-- اختر مركز التكلفة --"
                dataTable.Rows.InsertAt(emptyRow, 0)
            End If
            
            ' تحديد نوع العنصر وتعيين البيانات
            If TypeOf control Is ComboBoxEdit Then
                Dim combo = CType(control, ComboBoxEdit)
                combo.Properties.Items.Clear()
                For Each row As DataRow In dataTable.Rows
                    combo.Properties.Items.Add(row("display_name").ToString())
                Next
                
            ElseIf TypeOf control Is LookUpEdit Then
                Dim lookup = CType(control, LookUpEdit)
                lookup.Properties.DataSource = dataTable
                lookup.Properties.DisplayMember = "display_name"
                lookup.Properties.ValueMember = "cost_center_id"
                
            End If
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ''' <summary>
    ''' الحصول على اسم مركز التكلفة بواسطة المعرف
    ''' </summary>
    Public Shared Function GetCostCenterName(costCenterId As Integer) As String
        Try
            If costCenterId <= 0 Then Return ""
            
            Dim sql = "SELECT cost_center_name FROM cost_centers WHERE cost_center_id = @id"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", costCenterId)
            
            Dim result = cmd.ExecuteScalar()
            Return If(result IsNot Nothing, result.ToString(), "")
            
        Catch ex As Exception
            Return ""
        End Try
    End Function
    
    ''' <summary>
    ''' الحصول على كود مركز التكلفة بواسطة المعرف
    ''' </summary>
    Public Shared Function GetCostCenterCode(costCenterId As Integer) As String
        Try
            If costCenterId <= 0 Then Return ""
            
            Dim sql = "SELECT cost_center_code FROM cost_centers WHERE cost_center_id = @id"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", costCenterId)
            
            Dim result = cmd.ExecuteScalar()
            Return If(result IsNot Nothing, result.ToString(), "")
            
        Catch ex As Exception
            Return ""
        End Try
    End Function
    
    ''' <summary>
    ''' الحصول على معرف مركز التكلفة بواسطة الكود
    ''' </summary>
    Public Shared Function GetCostCenterIdByCode(costCenterCode As String) As Integer
        Try
            If String.IsNullOrWhiteSpace(costCenterCode) Then Return 0
            
            Dim sql = "SELECT cost_center_id FROM cost_centers WHERE cost_center_code = @code AND is_active = 1"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@code", costCenterCode.Trim())
            
            Dim result = cmd.ExecuteScalar()
            Return If(result IsNot Nothing AndAlso Not IsDBNull(result), Convert.ToInt32(result), 0)
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    ''' <summary>
    ''' التحقق من وجود مركز التكلفة ونشاطه
    ''' </summary>
    Public Shared Function IsCostCenterActive(costCenterId As Integer) As Boolean
        Try
            If costCenterId <= 0 Then Return False
            
            Dim sql = "SELECT is_active FROM cost_centers WHERE cost_center_id = @id"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", costCenterId)
            
            Dim result = cmd.ExecuteScalar()
            Return If(result IsNot Nothing AndAlso Not IsDBNull(result), Convert.ToBoolean(result), False)
            
        Catch ex As Exception
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' حساب إجمالي التكاليف لمركز تكلفة معين في فترة محددة
    ''' </summary>
    Public Shared Function CalculateCostCenterExpenses(costCenterId As Integer, startDate As Date, endDate As Date) As Decimal
        Try
            If costCenterId <= 0 Then Return 0
            
            Dim sql = "SELECT ISNULL(SUM(total_Expenses), 0) FROM Expenses_add " &
                     "WHERE cost_center_id = @id AND Expenses_date BETWEEN @startDate AND @endDate"
            
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", costCenterId)
            cmd.Parameters.AddWithValue("@startDate", startDate)
            cmd.Parameters.AddWithValue("@endDate", endDate)
            
            Dim result = cmd.ExecuteScalar()
            Return If(result IsNot Nothing AndAlso Not IsDBNull(result), Convert.ToDecimal(result), 0)
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    ''' <summary>
    ''' حساب إجمالي الإيرادات لمركز تكلفة معين في فترة محددة
    ''' </summary>
    Public Shared Function CalculateCostCenterRevenues(costCenterId As Integer, startDate As Date, endDate As Date) As Decimal
        Try
            If costCenterId <= 0 Then Return 0
            
            Dim sql = "SELECT ISNULL(SUM(total_invoice), 0) FROM invoice_add " &
                     "WHERE cost_center_id = @id AND invoice_date BETWEEN @startDate AND @endDate"
            
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@id", costCenterId)
            cmd.Parameters.AddWithValue("@startDate", startDate)
            cmd.Parameters.AddWithValue("@endDate", endDate)
            
            Dim result = cmd.ExecuteScalar()
            Return If(result IsNot Nothing AndAlso Not IsDBNull(result), Convert.ToDecimal(result), 0)
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    ''' <summary>
    ''' حساب صافي الربح/الخسارة لمركز تكلفة معين في فترة محددة
    ''' </summary>
    Public Shared Function CalculateCostCenterNetProfit(costCenterId As Integer, startDate As Date, endDate As Date) As Decimal
        Try
            Dim revenues = CalculateCostCenterRevenues(costCenterId, startDate, endDate)
            Dim expenses = CalculateCostCenterExpenses(costCenterId, startDate, endDate)
            
            Return revenues - expenses
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    ''' <summary>
    ''' الحصول على ملخص شامل لمركز التكلفة
    ''' </summary>
    Public Shared Function GetCostCenterSummary(costCenterId As Integer, startDate As Date, endDate As Date) As CostCenterSummary
        Try
            Dim summary As New CostCenterSummary()
            summary.CostCenterId = costCenterId
            summary.CostCenterName = GetCostCenterName(costCenterId)
            summary.CostCenterCode = GetCostCenterCode(costCenterId)
            summary.TotalExpenses = CalculateCostCenterExpenses(costCenterId, startDate, endDate)
            summary.TotalRevenues = CalculateCostCenterRevenues(costCenterId, startDate, endDate)
            summary.NetProfit = summary.TotalRevenues - summary.TotalExpenses
            summary.StartDate = startDate
            summary.EndDate = endDate
            
            Return summary
            
        Catch ex As Exception
            Return New CostCenterSummary()
        End Try
    End Function
    
    ''' <summary>
    ''' تحديث مركز التكلفة في فاتورة معينة
    ''' </summary>
    Public Shared Sub UpdateInvoiceCostCenter(invoiceId As Integer, costCenterId As Integer?)
        Try
            Dim sql = "UPDATE invoice_add SET cost_center_id = @costCenterId WHERE invoice_id = @invoiceId"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@invoiceId", invoiceId)
            cmd.Parameters.AddWithValue("@costCenterId", If(costCenterId.HasValue, costCenterId.Value, DBNull.Value))
            
            cmd.ExecuteNonQuery()
            
        Catch ex As Exception
            Throw New Exception("خطأ في تحديث مركز التكلفة للفاتورة: " & ex.Message)
        End Try
    End Sub
    
    ''' <summary>
    ''' تحديث مركز التكلفة في مصروف معين
    ''' </summary>
    Public Shared Sub UpdateExpenseCostCenter(expenseId As Integer, costCenterId As Integer?)
        Try
            Dim sql = "UPDATE Expenses_add SET cost_center_id = @costCenterId WHERE Expenses_id = @expenseId"
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@expenseId", expenseId)
            cmd.Parameters.AddWithValue("@costCenterId", If(costCenterId.HasValue, costCenterId.Value, DBNull.Value))
            
            cmd.ExecuteNonQuery()
            
        Catch ex As Exception
            Throw New Exception("خطأ في تحديث مركز التكلفة للمصروف: " & ex.Message)
        End Try
    End Sub

End Class

''' <summary>
''' كلاس لتمثيل ملخص مركز التكلفة
''' </summary>
Public Class CostCenterSummary
    Public Property CostCenterId As Integer
    Public Property CostCenterCode As String
    Public Property CostCenterName As String
    Public Property TotalExpenses As Decimal
    Public Property TotalRevenues As Decimal
    Public Property NetProfit As Decimal
    Public Property StartDate As Date
    Public Property EndDate As Date
    
    Public ReadOnly Property ProfitMarginPercentage As Decimal
        Get
            If TotalRevenues > 0 Then
                Return (NetProfit / TotalRevenues) * 100
            Else
                Return 0
            End If
        End Get
    End Property
End Class
