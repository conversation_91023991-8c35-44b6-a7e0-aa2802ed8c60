﻿Imports System.Data.SqlClient

Public Class Destruction_ethol


    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        new_btn_Click(Nothing, Nothing)
      
    End Sub
    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Fixed_assets order by origin ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("origin"))
        Next

    End Sub

    Function get_id(subname) As String
        Dim sql = "select * from Fixed_assets where origin=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        origin.Text = ""
        Dim sql = "select * from Fixed_assets where id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)

            origin.Text = dr!origin
            amount.Text = dr!amount
            origin_date.Value = dr!origin_date
            Try
                amount2.Text = dr!amount2
            Catch ex As Exception

            End Try

        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub


    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        origin.Text = ""
        amount.Text = 0
        amount2.Text = 0
        origin_date.Value = Now.Date
        fill_user()
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If origin.Text = "" Then
            MsgBox("أدخل اسم الاصل")
            origin.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from Fixed_assets where origin=N'" & (origin.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم الاصل موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!origin = origin.Text
                dr!amount = amount.Text
                Try
                    dr!amount2 = amount2.Text
                Catch ex As Exception

                End Try

                dr!origin_date = origin_date.Value
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة الاصل")
                new_btn_Click(Nothing, Nothing)
                fill_user()
                customer_add.fill()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ الاصل اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from Fixed_assets where id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!origin = origin.Text
                dr!amount = amount.Text
                dr!origin_date = origin_date.Value
                Try
                    dr!amount2 = amount2.Text
                Catch ex As Exception

                End Try

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل الاصل", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل الاصل اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from Fixed_assets where id= N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الاصل")
            Else
                Dim dr = dt.Rows(0)

                dr.Delete()

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف الاصل", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الاصل اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub
End Class