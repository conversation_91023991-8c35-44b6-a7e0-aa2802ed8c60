﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors.XtraMessageBox

Public Class customer_show
    Private ctr As Integer = 0
    Sub fill_dgv()
        Dim adp As New SqlDataAdapter("select * from customer", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt

        Dim gridFormatRule As New GridFormatRule()
        Dim formatConditionRuleDataBar As New FormatConditionRuleDataBar()
        gridFormatRule.Column = Me.GridColumn7
        formatConditionRuleDataBar.PredefinedName = "Blue Gradient"
        gridFormatRule.Rule = formatConditionRuleDataBar
        Me.GridView2.FormatRules.Add(gridFormatRule)
    End Sub
    Private Sub customer_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_dgv()   

    End Sub
    Private Sub save_try()
        For i = 0 To GridView2.RowCount - 1
            Try
                Dim sql = "select (Cuscode),(cusname),(Accounts_balace) from customer where cusname=N'" & GridView2.GetRowCellValue(i, "cusname").ToString & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)

                Dim dr = dt.Rows(0)
                '========= بيانات اساسية============
                dr!Accounts_balace = Val(GridView2.GetRowCellValue(i, "Accounts_balace").ToString)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

            Catch ex As Exception

            End Try
        Next
       
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        Dim f As customer_add = New customer_add()
        f.Text = "تسجيل عميل"
        f.new_cus()
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub delete_btn_Click(sender As Object, e As EventArgs) Handles delete_btn.Click
        If XtraForm1.m23.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد عميل لحذفه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If XtraMessageBox.Show("يوجد تعاملات سابقة مع هذا العميل هل انت متاكد من حذفة ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then

            Dim sql = "select * from customer where Cuscode=N'" & GridView2.GetFocusedRowCellValue("Cuscode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                delete()

                XtraMessageBox.Show("تم حذف العميل بنجاح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                fill_dgv()
            End If
        End If
    End Sub
    Sub delete()
        For i2 = 0 To 1000
            Dim sql = "select * from customer_trans where customer_name=N'" & GridView2.GetFocusedRowCellValue("cusname") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If XtraForm1.m22.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد عميل للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_add = New customer_add()
        f.Text = "تسجيل عميل"
        f.new_cus()
        f.MdiParent = XtraForm1
        f.show_data(GridView2.GetFocusedRowCellValue("Cuscode"))
        f.Show()
    End Sub
    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub
    
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub
 
    Private Sub catch_btn_Click(sender As Object, e As EventArgs) Handles catch_btn.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد عميل لاستلام النقدية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_customer = New cashing_customer()
        f.Text = "سند قبض"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Text = GridView2.GetFocusedRowCellValue("cusname")
        f.Amount.Focus()
    End Sub

    Private Sub report_btn_Click(sender As Object, e As EventArgs) Handles report_btn.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد عميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_Detailed = New customer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = XtraForm1
        f.Show()
        f.customername.Text = GridView2.GetFocusedRowCellValue("cusname")
        f.search()
    End Sub

    Private Sub statistic_Click(sender As Object, e As EventArgs) Handles statistic.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد عميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Statistic_customer = New Statistic_customer()
        f.Text = "احصائية عميل"
        f.MdiParent = XtraForm1
        f.Show()
        f.customername.Text = GridView2.GetFocusedRowCellValue("cusname")
        f.search()
    End Sub
    Private Sub trans_btn_Click(sender As Object, e As EventArgs) Handles trans_btn.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد عميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        cus_trans_show.ShowDialog()
        cus_trans_show.Accounts_name.Text = GridView2.GetFocusedRowCellValue("cusname")

    End Sub
  
    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles w10.Click
        Me.Dispose()
    End Sub
    Private Sub customer_show_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            w1_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            w2_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            w3_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            w4_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F5 Then
            w5_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F6 Then
            w6_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F7 Then
            w7_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F8 Then
            w8_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F9 Then
            w9_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub w1_Click(sender As Object, e As EventArgs) Handles w1.Click
        new_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w2_Click(sender As Object, e As EventArgs) Handles w2.Click
        edit_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w3_Click(sender As Object, e As EventArgs) Handles w3.Click
        delete_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w4_Click(sender As Object, e As EventArgs) Handles w4.Click
        catch_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w5_Click(sender As Object, e As EventArgs) Handles w5.Click
        print_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w6_Click(sender As Object, e As EventArgs) Handles w6.Click
        trans_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w7_Click(sender As Object, e As EventArgs) Handles w7.Click
        report_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w8_Click(sender As Object, e As EventArgs) Handles w8.Click
        statistic_Click(Nothing, Nothing)
    End Sub

    Private Sub w9_Click(sender As Object, e As EventArgs) Handles w9.Click
        fill_dgv()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        Dim f As customer_edit = New customer_edit()
        f.Text = "التعديل السريع"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub Button11_Click(sender As Object, e As EventArgs) Handles Button11.Click
        GridView2.ShowCustomization()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Process.Start("https://web.whatsapp.com/send?phone=" + "+2" + GridView2.GetFocusedRowCellValue("cus_phone1") + "")
    End Sub
End Class