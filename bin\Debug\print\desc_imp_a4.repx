﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.desc_imp_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="desc_imp_a4" Margins="0, 3, 4, 6" PageWidth="850" PageHeight="1100" Version="17.2" DataMember="desc_imp_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="202.5" TextAlignment="TopLeft" Padding="0,0,0,0,100">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="XrLabel13" Text="XrLabel13" TextAlignment="MiddleRight" SizeF="649.052,31.25" LocationFloat="40.11459, 152.5" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="4" Expression="[cash_type]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="5" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="6" ControlType="XRLabel" Name="XrLabel12" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel12" TextAlignment="MiddleCenter" SizeF="218.8958,31.25" LocationFloat="41.66667, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[descdate]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="9" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleCenter" SizeF="296.3126,31.25" LocationFloat="392.8541, 10.00001" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="10" Expression="[desccode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="12" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="647.5,31.25" LocationFloat="41.66667, 57.8125" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[customername]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="14" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="15" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="460.2084,31.25" LocationFloat="41.66667, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="16" Expression="[amount_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="17" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="18" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:#,#}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="187.2916,31.25" LocationFloat="501.8751, 104.5833" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="19" Expression="[Amount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="20" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="21" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="كود الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="689.1667, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="22" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="23" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="أســـــــم العميل :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="689.1667, 57.8125" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="24" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="25" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="مـبلغ وقدره :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="689.1667, 104.5833" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="26" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="27" ControlType="XRLabel" Name="XrLabel15" RightToLeft="Yes" Text="وذلك عن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="689.1667, 152.5" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="28" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="29" ControlType="XRLabel" Name="XrLabel5" RightToLeft="Yes" Text="تـــاريخ الحركة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="260.5624, 10.00001" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1">
          <StylePriority Ref="30" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item1>
    <Item2 Ref="31" ControlType="TopMarginBand" Name="TopMargin" HeightF="3.666671" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="32" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="6.25" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="33" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="110.4167">
      <Controls>
        <Item1 Ref="34" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="551.1042, 28.04165" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="35" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="36" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="37" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="551.1042, 59.37498" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="38" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="40" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="128.7499,100.4167" LocationFloat="17, 10.00001">
          <ExpressionBindings>
            <Item1 Ref="41" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="42" ControlType="PageHeaderBand" Name="PageHeader" HeightF="96.74997">
      <Controls>
        <Item1 Ref="43" ControlType="XRBarCode" Name="barCode1" SizeF="140.625015,23.0000019" LocationFloat="142.708328, 31.3333187" Padding="10,10,0,0,96">
          <Symbology Ref="44" Name="Code128" />
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[desccode]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="46" ControlType="XRLabel" Name="XrLabel19" Text="إشعار بإستلام خصم" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="41.66667, 0" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="47" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="48" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="41.6666679, 63.74998" Padding="2,2,0,0,100">
          <StylePriority Ref="49" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item5>
    <Item6 Ref="50" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="127.25">
      <Controls>
        <Item1 Ref="51" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="501.8751, 102.1667" Font="Times New Roman, 11pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="52" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="53" UseFont="false" />
        </Item1>
        <Item2 Ref="54" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="100.7501, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="55" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="56" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="57" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="100.7501, 27.16665" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="58" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="60" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="519.5001, 0" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="62" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="519.5001, 25.08332" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="63" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="64" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="17, 102.1667" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="66" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="17, 66.66665" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item6>
    <Item7 Ref="68" ControlType="PageFooterBand" Name="PageFooter" HeightF="38.54167">
      <Controls>
        <Item1 Ref="69" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="706.9791, 10" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="71" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52081,25.08331" LocationFloat="755.9375, 5.541674" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="72" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="73" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="41.66667, 3.458341" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="74" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="75" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="76" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="77" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="78" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="79" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="80" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="PFNxbERhdGFTb3VyY2U+PE5hbWU+U3FsRGF0YVNvdXJjZTE8L05hbWU+PENvbm5lY3Rpb24gTmFtZT0iZGF0YSIgRnJvbUFwcENvbmZpZz0idHJ1ZSIgLz48UXVlcnkgVHlwZT0iU2VsZWN0UXVlcnkiIE5hbWU9ImRlc2NfaW1wX3ByaW50Ij48VGFibGVzPjxUYWJsZSBOYW1lPSJkZXNjX2ltcF9wcmludCIgWD0iMzAiIFk9IjMwIiBXaWR0aD0iMTI1IiBIZWlnaHQ9IjM4MSIgLz48L1RhYmxlcz48Q29sdW1ucz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0iY29kZV9wcmludCIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0iYW1vdW50X3N0cmluZyIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0iQW1vdW50IiAvPjxDb2x1bW4gVGFibGU9ImRlc2NfaW1wX3ByaW50IiBOYW1lPSJjdXN0b21lcm5hbWUiIC8+PENvbHVtbiBUYWJsZT0iZGVzY19pbXBfcHJpbnQiIE5hbWU9ImRlc2NkYXRlIiAvPjxDb2x1bW4gVGFibGU9ImRlc2NfaW1wX3ByaW50IiBOYW1lPSJkZXNjY29kZSIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0iY29kZSIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0ic2VjX25hbWUiIC8+PENvbHVtbiBUYWJsZT0iZGVzY19pbXBfcHJpbnQiIE5hbWU9InNlY19waG9uZSIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0ic2VjX2FkZHJlc3MiIC8+PENvbHVtbiBUYWJsZT0iZGVzY19pbXBfcHJpbnQiIE5hbWU9InNlY19lbWFpbCIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0ic2VjX3dlYiIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0ic2VjX251bWJlciIgLz48Q29sdW1uIFRhYmxlPSJkZXNjX2ltcF9wcmludCIgTmFtZT0ic2VjX3MxIiAvPjxDb2x1bW4gVGFibGU9ImRlc2NfaW1wX3ByaW50IiBOYW1lPSJzZWNfcGljIiAvPjxDb2x1bW4gVGFibGU9ImRlc2NfaW1wX3ByaW50IiBOYW1lPSJzZWNfczIiIC8+PENvbHVtbiBUYWJsZT0iZGVzY19pbXBfcHJpbnQiIE5hbWU9ImNhc2hfdHlwZSIgLz48L0NvbHVtbnM+PC9RdWVyeT48UmVzdWx0U2NoZW1hPjxEYXRhU2V0IE5hbWU9IlNxbERhdGFTb3VyY2UxIj48VmlldyBOYW1lPSJkZXNjX2ltcF9wcmludCI+PEZpZWxkIE5hbWU9ImNvZGVfcHJpbnQiIFR5cGU9IkludDMyIiAvPjxGaWVsZCBOYW1lPSJhbW91bnRfc3RyaW5nIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9IkFtb3VudCIgVHlwZT0iRGVjaW1hbCIgLz48RmllbGQgTmFtZT0iY3VzdG9tZXJuYW1lIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9ImRlc2NkYXRlIiBUeXBlPSJEYXRlVGltZSIgLz48RmllbGQgTmFtZT0iZGVzY2NvZGUiIFR5cGU9IkludDMyIiAvPjxGaWVsZCBOYW1lPSJjb2RlIiBUeXBlPSJJbnQzMiIgLz48RmllbGQgTmFtZT0ic2VjX25hbWUiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX3Bob25lIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19hZGRyZXNzIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19lbWFpbCIgVHlwZT0iU3RyaW5nIiAvPjxGaWVsZCBOYW1lPSJzZWNfd2ViIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19udW1iZXIiIFR5cGU9IlN0cmluZyIgLz48RmllbGQgTmFtZT0ic2VjX3MxIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9InNlY19waWMiIFR5cGU9IkJ5dGVBcnJheSIgLz48RmllbGQgTmFtZT0ic2VjX3MyIiBUeXBlPSJTdHJpbmciIC8+PEZpZWxkIE5hbWU9ImNhc2hfdHlwZSIgVHlwZT0iU3RyaW5nIiAvPjwvVmlldz48L0RhdGFTZXQ+PC9SZXN1bHRTY2hlbWE+PENvbm5lY3Rpb25PcHRpb25zIENsb3NlQ29ubmVjdGlvbj0idHJ1ZSIgLz48L1NxbERhdGFTb3VyY2U+" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>