﻿﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class shift_end
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(shift_end))
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.start_notes = New DevExpress.XtraEditors.MemoEdit()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.start_amount = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.start_date = New DevExpress.XtraEditors.DateEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.store_name = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.cashier_name = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.shift_code = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.total_items = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.total_profit = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl13 = New DevExpress.XtraEditors.LabelControl()
        Me.total_credit = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl12 = New DevExpress.XtraEditors.LabelControl()
        Me.total_cash = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.total_sales = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.total_invoices = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.cash_difference = New DevExpress.XtraEditors.SpinEdit()
        Me.difference_label = New DevExpress.XtraEditors.LabelControl()
        Me.expected_cash = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl17 = New DevExpress.XtraEditors.LabelControl()
        Me.end_notes = New DevExpress.XtraEditors.MemoEdit()
        Me.LabelControl16 = New DevExpress.XtraEditors.LabelControl()
        Me.end_amount = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl15 = New DevExpress.XtraEditors.LabelControl()
        Me.end_time = New DevExpress.XtraEditors.TimeEdit()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.end_date = New DevExpress.XtraEditors.DateEdit()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.end_shift_btn = New DevExpress.XtraEditors.SimpleButton()
        Me.cancel_btn = New DevExpress.XtraEditors.SimpleButton()
        Me.refresh_btn = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.start_notes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.start_amount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.start_date.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.store_name.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cashier_name.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.shift_code.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.total_items.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.total_profit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.total_credit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.total_cash.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.total_sales.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.total_invoices.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.cash_difference.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.expected_cash.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.end_notes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.end_amount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.end_time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.end_date.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.start_notes)
        Me.GroupControl1.Controls.Add(Me.LabelControl9)
        Me.GroupControl1.Controls.Add(Me.start_amount)
        Me.GroupControl1.Controls.Add(Me.LabelControl5)
        Me.GroupControl1.Controls.Add(Me.start_date)
        Me.GroupControl1.Controls.Add(Me.LabelControl4)
        Me.GroupControl1.Controls.Add(Me.store_name)
        Me.GroupControl1.Controls.Add(Me.LabelControl3)
        Me.GroupControl1.Controls.Add(Me.cashier_name)
        Me.GroupControl1.Controls.Add(Me.LabelControl2)
        Me.GroupControl1.Controls.Add(Me.shift_code)
        Me.GroupControl1.Controls.Add(Me.LabelControl1)
        Me.GroupControl1.Location = New System.Drawing.Point(12, 12)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupControl1.Size = New System.Drawing.Size(360, 220)
        Me.GroupControl1.TabIndex = 0
        Me.GroupControl1.Text = "بيانات الشيفت"
        '
        'start_notes
        '
        Me.start_notes.Location = New System.Drawing.Point(15, 150)
        Me.start_notes.Name = "start_notes"
        Me.start_notes.Properties.ReadOnly = True
        Me.start_notes.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.start_notes.Size = New System.Drawing.Size(250, 60)
        Me.start_notes.TabIndex = 11
        '
        'LabelControl9
        '
        Me.LabelControl9.Location = New System.Drawing.Point(280, 152)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl9.Size = New System.Drawing.Size(70, 13)
        Me.LabelControl9.TabIndex = 10
        Me.LabelControl9.Text = "ملاحظات البداية:"
        '
        'start_amount
        '
        Me.start_amount.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.start_amount.Location = New System.Drawing.Point(15, 120)
        Me.start_amount.Name = "start_amount"
        Me.start_amount.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.start_amount.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.start_amount.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.start_amount.Properties.ReadOnly = True
        Me.start_amount.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.start_amount.Size = New System.Drawing.Size(250, 20)
        Me.start_amount.TabIndex = 9
        '
        'LabelControl5
        '
        Me.LabelControl5.Location = New System.Drawing.Point(280, 123)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl5.Size = New System.Drawing.Size(68, 13)
        Me.LabelControl5.TabIndex = 8
        Me.LabelControl5.Text = "مبلغ البداية:"
        '
        'start_date
        '
        Me.start_date.EditValue = Nothing
        Me.start_date.Location = New System.Drawing.Point(15, 90)
        Me.start_date.Name = "start_date"
        Me.start_date.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.start_date.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.start_date.Properties.ReadOnly = True
        Me.start_date.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.start_date.Size = New System.Drawing.Size(250, 20)
        Me.start_date.TabIndex = 7
        '
        'LabelControl4
        '
        Me.LabelControl4.Location = New System.Drawing.Point(280, 93)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl4.Size = New System.Drawing.Size(68, 13)
        Me.LabelControl4.TabIndex = 6
        Me.LabelControl4.Text = "تاريخ البداية:"
        '
        'store_name
        '
        Me.store_name.Location = New System.Drawing.Point(15, 60)
        Me.store_name.Name = "store_name"
        Me.store_name.Properties.ReadOnly = True
        Me.store_name.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.store_name.Size = New System.Drawing.Size(250, 20)
        Me.store_name.TabIndex = 5
        '
        'LabelControl3
        '
        Me.LabelControl3.Location = New System.Drawing.Point(280, 63)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl3.Size = New System.Drawing.Size(58, 13)
        Me.LabelControl3.TabIndex = 4
        Me.LabelControl3.Text = "اسم المخزن:"
        '
        'cashier_name
        '
        Me.cashier_name.Location = New System.Drawing.Point(15, 30)
        Me.cashier_name.Name = "cashier_name"
        Me.cashier_name.Properties.ReadOnly = True
        Me.cashier_name.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.cashier_name.Size = New System.Drawing.Size(250, 20)
        Me.cashier_name.TabIndex = 3
        '
        'LabelControl2
        '
        Me.LabelControl2.Location = New System.Drawing.Point(280, 33)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl2.Size = New System.Drawing.Size(58, 13)
        Me.LabelControl2.TabIndex = 2
        Me.LabelControl2.Text = "اسم الكاشير:"
        '
        'shift_code
        '
        Me.shift_code.Location = New System.Drawing.Point(15, 0)
        Me.shift_code.Name = "shift_code"
        Me.shift_code.Properties.ReadOnly = True
        Me.shift_code.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.shift_code.Size = New System.Drawing.Size(250, 20)
        Me.shift_code.TabIndex = 1
        '
        'LabelControl1
        '
        Me.LabelControl1.Location = New System.Drawing.Point(280, 3)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl1.Size = New System.Drawing.Size(54, 13)
        Me.LabelControl1.TabIndex = 0
        Me.LabelControl1.Text = "كود الشيفت:"
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.total_items)
        Me.GroupControl2.Controls.Add(Me.LabelControl14)
        Me.GroupControl2.Controls.Add(Me.total_profit)
        Me.GroupControl2.Controls.Add(Me.LabelControl13)
        Me.GroupControl2.Controls.Add(Me.total_credit)
        Me.GroupControl2.Controls.Add(Me.LabelControl12)
        Me.GroupControl2.Controls.Add(Me.total_cash)
        Me.GroupControl2.Controls.Add(Me.LabelControl11)
        Me.GroupControl2.Controls.Add(Me.total_sales)
        Me.GroupControl2.Controls.Add(Me.LabelControl10)
        Me.GroupControl2.Controls.Add(Me.total_invoices)
        Me.GroupControl2.Controls.Add(Me.LabelControl8)
        Me.GroupControl2.Location = New System.Drawing.Point(390, 12)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupControl2.Size = New System.Drawing.Size(300, 220)
        Me.GroupControl2.TabIndex = 1
        Me.GroupControl2.Text = "إجماليات الشيفت"
        '
        'total_items
        '
        Me.total_items.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.total_items.Location = New System.Drawing.Point(15, 180)
        Me.total_items.Name = "total_items"
        Me.total_items.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.total_items.Properties.ReadOnly = True
        Me.total_items.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.total_items.Size = New System.Drawing.Size(200, 20)
        Me.total_items.TabIndex = 11
        '
        'LabelControl14
        '
        Me.LabelControl14.Location = New System.Drawing.Point(230, 183)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl14.Size = New System.Drawing.Size(60, 13)
        Me.LabelControl14.TabIndex = 10
        Me.LabelControl14.Text = "عدد الأصناف:"
        '
        'total_profit
        '
        Me.total_profit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.total_profit.Location = New System.Drawing.Point(15, 150)
        Me.total_profit.Name = "total_profit"
        Me.total_profit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.total_profit.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.total_profit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.total_profit.Properties.ReadOnly = True
        Me.total_profit.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.total_profit.Size = New System.Drawing.Size(200, 20)
        Me.total_profit.TabIndex = 9
        '
        'LabelControl13
        '
        Me.LabelControl13.Location = New System.Drawing.Point(230, 153)
        Me.LabelControl13.Name = "LabelControl13"
        Me.LabelControl13.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl13.Size = New System.Drawing.Size(65, 13)
        Me.LabelControl13.TabIndex = 8
        Me.LabelControl13.Text = "إجمالي الأرباح:"
        '
        'total_credit
        '
        Me.total_credit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.total_credit.Location = New System.Drawing.Point(15, 120)
        Me.total_credit.Name = "total_credit"
        Me.total_credit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.total_credit.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.total_credit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.total_credit.Properties.ReadOnly = True
        Me.total_credit.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.total_credit.Size = New System.Drawing.Size(200, 20)
        Me.total_credit.TabIndex = 7
        '
        'LabelControl12
        '
        Me.LabelControl12.Location = New System.Drawing.Point(230, 123)
        Me.LabelControl12.Name = "LabelControl12"
        Me.LabelControl12.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl12.Size = New System.Drawing.Size(70, 13)
        Me.LabelControl12.TabIndex = 6
        Me.LabelControl12.Text = "المبيعات الآجلة:"
        '
        'total_cash
        '
        Me.total_cash.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.total_cash.Location = New System.Drawing.Point(15, 90)
        Me.total_cash.Name = "total_cash"
        Me.total_cash.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.total_cash.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.total_cash.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.total_cash.Properties.ReadOnly = True
        Me.total_cash.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.total_cash.Size = New System.Drawing.Size(200, 20)
        Me.total_cash.TabIndex = 5
        '
        'LabelControl11
        '
        Me.LabelControl11.Location = New System.Drawing.Point(230, 93)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl11.Size = New System.Drawing.Size(75, 13)
        Me.LabelControl11.TabIndex = 4
        Me.LabelControl11.Text = "المبيعات النقدية:"
        '
        'total_sales
        '
        Me.total_sales.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.total_sales.Location = New System.Drawing.Point(15, 60)
        Me.total_sales.Name = "total_sales"
        Me.total_sales.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.total_sales.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.total_sales.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.total_sales.Properties.ReadOnly = True
        Me.total_sales.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.total_sales.Size = New System.Drawing.Size(200, 20)
        Me.total_sales.TabIndex = 3
        '
        'LabelControl10
        '
        Me.LabelControl10.Location = New System.Drawing.Point(230, 63)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl10.Size = New System.Drawing.Size(70, 13)
        Me.LabelControl10.TabIndex = 2
        Me.LabelControl10.Text = "إجمالي المبيعات:"
        '
        'total_invoices
        '
        Me.total_invoices.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.total_invoices.Location = New System.Drawing.Point(15, 30)
        Me.total_invoices.Name = "total_invoices"
        Me.total_invoices.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.total_invoices.Properties.ReadOnly = True
        Me.total_invoices.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.total_invoices.Size = New System.Drawing.Size(200, 20)
        Me.total_invoices.TabIndex = 1
        '
        'LabelControl8
        '
        Me.LabelControl8.Location = New System.Drawing.Point(230, 33)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl8.Size = New System.Drawing.Size(60, 13)
        Me.LabelControl8.TabIndex = 0
        Me.LabelControl8.Text = "عدد الفواتير:"
        '
        'GroupControl3
        '
        Me.GroupControl3.Controls.Add(Me.cash_difference)
        Me.GroupControl3.Controls.Add(Me.difference_label)
        Me.GroupControl3.Controls.Add(Me.expected_cash)
        Me.GroupControl3.Controls.Add(Me.LabelControl17)
        Me.GroupControl3.Controls.Add(Me.end_notes)
        Me.GroupControl3.Controls.Add(Me.LabelControl16)
        Me.GroupControl3.Controls.Add(Me.end_amount)
        Me.GroupControl3.Controls.Add(Me.LabelControl15)
        Me.GroupControl3.Controls.Add(Me.end_time)
        Me.GroupControl3.Controls.Add(Me.LabelControl7)
        Me.GroupControl3.Controls.Add(Me.end_date)
        Me.GroupControl3.Controls.Add(Me.LabelControl6)
        Me.GroupControl3.Location = New System.Drawing.Point(12, 250)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupControl3.Size = New System.Drawing.Size(678, 200)
        Me.GroupControl3.TabIndex = 2
        Me.GroupControl3.Text = "بيانات إقفال الشيفت"
        '
        'cash_difference
        '
        Me.cash_difference.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.cash_difference.Location = New System.Drawing.Point(350, 150)
        Me.cash_difference.Name = "cash_difference"
        Me.cash_difference.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cash_difference.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.cash_difference.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.cash_difference.Properties.ReadOnly = True
        Me.cash_difference.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.cash_difference.Size = New System.Drawing.Size(200, 20)
        Me.cash_difference.TabIndex = 11
        '
        'difference_label
        '
        Me.difference_label.Location = New System.Drawing.Point(570, 153)
        Me.difference_label.Name = "difference_label"
        Me.difference_label.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.difference_label.Size = New System.Drawing.Size(80, 13)
        Me.difference_label.TabIndex = 10
        Me.difference_label.Text = "الفرق في الخزينة:"
        '
        'expected_cash
        '
        Me.expected_cash.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.expected_cash.Location = New System.Drawing.Point(350, 120)
        Me.expected_cash.Name = "expected_cash"
        Me.expected_cash.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.expected_cash.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.expected_cash.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.expected_cash.Properties.ReadOnly = True
        Me.expected_cash.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.expected_cash.Size = New System.Drawing.Size(200, 20)
        Me.expected_cash.TabIndex = 9
        '
        'LabelControl17
        '
        Me.LabelControl17.Location = New System.Drawing.Point(570, 123)
        Me.LabelControl17.Name = "LabelControl17"
        Me.LabelControl17.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl17.Size = New System.Drawing.Size(80, 13)
        Me.LabelControl17.TabIndex = 8
        Me.LabelControl17.Text = "المبلغ المتوقع:"
        '
        'end_notes
        '
        Me.end_notes.Location = New System.Drawing.Point(15, 90)
        Me.end_notes.Name = "end_notes"
        Me.end_notes.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.end_notes.Size = New System.Drawing.Size(300, 80)
        Me.end_notes.TabIndex = 7
        '
        'LabelControl16
        '
        Me.LabelControl16.Location = New System.Drawing.Point(230, 92)
        Me.LabelControl16.Name = "LabelControl16"
        Me.LabelControl16.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl16.Size = New System.Drawing.Size(70, 13)
        Me.LabelControl16.TabIndex = 6
        Me.LabelControl16.Text = "ملاحظات النهاية:"
        '
        'end_amount
        '
        Me.end_amount.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.end_amount.Location = New System.Drawing.Point(350, 90)
        Me.end_amount.Name = "end_amount"
        Me.end_amount.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.end_amount.Properties.DisplayFormat.FormatString = "#,##0.00"
        Me.end_amount.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.end_amount.Properties.Mask.EditMask = "n2"
        Me.end_amount.Properties.MaxValue = New Decimal(New Integer() {999999, 0, 0, 0})
        Me.end_amount.Properties.MinValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.end_amount.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.end_amount.Size = New System.Drawing.Size(200, 20)
        Me.end_amount.TabIndex = 5
        '
        'LabelControl15
        '
        Me.LabelControl15.Location = New System.Drawing.Point(570, 93)
        Me.LabelControl15.Name = "LabelControl15"
        Me.LabelControl15.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl15.Size = New System.Drawing.Size(68, 13)
        Me.LabelControl15.TabIndex = 4
        Me.LabelControl15.Text = "مبلغ النهاية:"
        '
        'end_time
        '
        Me.end_time.EditValue = New System.DateTime(2025, 6, 25, 0, 0, 0, 0)
        Me.end_time.Location = New System.Drawing.Point(350, 60)
        Me.end_time.Name = "end_time"
        Me.end_time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.end_time.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.end_time.Size = New System.Drawing.Size(200, 20)
        Me.end_time.TabIndex = 3
        '
        'LabelControl7
        '
        Me.LabelControl7.Location = New System.Drawing.Point(570, 63)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl7.Size = New System.Drawing.Size(64, 13)
        Me.LabelControl7.TabIndex = 2
        Me.LabelControl7.Text = "وقت النهاية:"
        '
        'end_date
        '
        Me.end_date.EditValue = Nothing
        Me.end_date.Location = New System.Drawing.Point(350, 30)
        Me.end_date.Name = "end_date"
        Me.end_date.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.end_date.Properties.CalendarTimeProperties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.end_date.Properties.RightToLeft = DevExpress.Utils.DefaultBoolean.True
        Me.end_date.Size = New System.Drawing.Size(200, 20)
        Me.end_date.TabIndex = 1
        '
        'LabelControl6
        '
        Me.LabelControl6.Location = New System.Drawing.Point(570, 33)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.LabelControl6.Size = New System.Drawing.Size(68, 13)
        Me.LabelControl6.TabIndex = 0
        Me.LabelControl6.Text = "تاريخ النهاية:"
        '
        'end_shift_btn
        '
        Me.end_shift_btn.Appearance.Font = New System.Drawing.Font("Tahoma", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.end_shift_btn.Appearance.Options.UseFont = True
        Me.end_shift_btn.ImageOptions.Image = CType(resources.GetObject("end_shift_btn.ImageOptions.Image"), System.Drawing.Image)
        Me.end_shift_btn.Location = New System.Drawing.Point(470, 470)
        Me.end_shift_btn.Name = "end_shift_btn"
        Me.end_shift_btn.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.end_shift_btn.Size = New System.Drawing.Size(100, 35)
        Me.end_shift_btn.TabIndex = 3
        Me.end_shift_btn.Text = "إقفال الشيفت"
        '
        'cancel_btn
        '
        Me.cancel_btn.Appearance.Font = New System.Drawing.Font("Tahoma", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cancel_btn.Appearance.Options.UseFont = True
        Me.cancel_btn.ImageOptions.Image = CType(resources.GetObject("cancel_btn.ImageOptions.Image"), System.Drawing.Image)
        Me.cancel_btn.Location = New System.Drawing.Point(590, 470)
        Me.cancel_btn.Name = "cancel_btn"
        Me.cancel_btn.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cancel_btn.Size = New System.Drawing.Size(90, 35)
        Me.cancel_btn.TabIndex = 4
        Me.cancel_btn.Text = "إلغاء"
        '
        'refresh_btn
        '
        Me.refresh_btn.Appearance.Font = New System.Drawing.Font("Tahoma", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.refresh_btn.Appearance.Options.UseFont = True
        Me.refresh_btn.ImageOptions.Image = CType(resources.GetObject("refresh_btn.ImageOptions.Image"), System.Drawing.Image)
        Me.refresh_btn.Location = New System.Drawing.Point(350, 470)
        Me.refresh_btn.Name = "refresh_btn"
        Me.refresh_btn.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.refresh_btn.Size = New System.Drawing.Size(100, 35)
        Me.refresh_btn.TabIndex = 5
        Me.refresh_btn.Text = "تحديث البيانات"
        '
        'shift_end
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(702, 521)
        Me.Controls.Add(Me.refresh_btn)
        Me.Controls.Add(Me.cancel_btn)
        Me.Controls.Add(Me.end_shift_btn)
        Me.Controls.Add(Me.GroupControl3)
        Me.Controls.Add(Me.GroupControl2)
        Me.Controls.Add(Me.GroupControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.IconOptions.Image = CType(resources.GetObject("shift_end.IconOptions.Image"), System.Drawing.Image)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "shift_end"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "إقفال الشيفت"
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.start_notes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.start_amount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.start_date.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.store_name.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cashier_name.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.shift_code.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        CType(Me.total_items.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.total_profit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.total_credit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.total_cash.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.total_sales.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.total_invoices.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        Me.GroupControl3.PerformLayout()
        CType(Me.cash_difference.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.expected_cash.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.end_notes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.end_amount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.end_time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.end_date.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents start_notes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents start_amount As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents start_date As DevExpress.XtraEditors.DateEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents store_name As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents cashier_name As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents shift_code As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents total_items As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents total_profit As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl13 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents total_credit As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl12 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents total_cash As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents total_sales As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents total_invoices As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents cash_difference As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents difference_label As DevExpress.XtraEditors.LabelControl
    Friend WithEvents expected_cash As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl17 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents end_notes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LabelControl16 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents end_amount As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl15 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents end_time As DevExpress.XtraEditors.TimeEdit
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents end_date As DevExpress.XtraEditors.DateEdit
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents end_shift_btn As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents cancel_btn As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents refresh_btn As DevExpress.XtraEditors.SimpleButton
End Class
