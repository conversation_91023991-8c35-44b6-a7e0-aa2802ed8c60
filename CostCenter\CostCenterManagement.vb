Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid

Public Class CostCenterManagement
    Inherits XtraForm

    Private GridControl1 As DevExpress.XtraGrid.GridControl
    Private GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Private CostCenterNameEdit As DevExpress.XtraEditors.TextEdit
    Private CostCenterCodeEdit As DevExpress.XtraEditors.TextEdit
    Private DescriptionEdit As DevExpress.XtraEditors.MemoEdit
    Private IsActiveCheck As DevExpress.XtraEditors.CheckEdit
    Private AddBtn As DevExpress.XtraEditors.SimpleButton
    Private EditBtn As DevExpress.XtraEditors.SimpleButton
    Private DeleteBtn As DevExpress.XtraEditors.SimpleButton
    Private SaveBtn As DevExpress.XtraEditors.SimpleButton
    Private CancelBtn As DevExpress.XtraEditors.SimpleButton
    Private CloseBtn As DevExpress.XtraEditors.SimpleButton

    Private currentCostCenterId As Integer = 0
    Private isEditMode As Boolean = False

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.CostCenterNameEdit = New DevExpress.XtraEditors.TextEdit()
        Me.CostCenterCodeEdit = New DevExpress.XtraEditors.TextEdit()
        Me.DescriptionEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.IsActiveCheck = New DevExpress.XtraEditors.CheckEdit()
        Me.AddBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.EditBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.DeleteBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.SaveBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.CancelBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.CloseBtn = New DevExpress.XtraEditors.SimpleButton()

        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterNameEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterCodeEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DescriptionEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.IsActiveCheck.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()

        ' Form
        Me.Text = "إدارة مراكز التكلفة"
        Me.Size = New System.Drawing.Size(1000, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes

        ' GridControl1
        Me.GridControl1.Location = New System.Drawing.Point(12, 12)
        Me.GridControl1.Size = New System.Drawing.Size(600, 500)
        Me.GridControl1.MainView = Me.GridView1

        ' GridView1
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.OptionsView.ShowGroupPanel = False
        AddHandler Me.GridView1.FocusedRowChanged, AddressOf GridView1_FocusedRowChanged

        ' Input controls
        Me.CostCenterCodeEdit.Location = New System.Drawing.Point(750, 50)
        Me.CostCenterCodeEdit.Size = New System.Drawing.Size(200, 20)

        Me.CostCenterNameEdit.Location = New System.Drawing.Point(750, 90)
        Me.CostCenterNameEdit.Size = New System.Drawing.Size(200, 20)

        Me.DescriptionEdit.Location = New System.Drawing.Point(750, 130)
        Me.DescriptionEdit.Size = New System.Drawing.Size(200, 80)

        Me.IsActiveCheck.Location = New System.Drawing.Point(750, 230)
        Me.IsActiveCheck.Size = New System.Drawing.Size(100, 20)
        Me.IsActiveCheck.Text = "نشط"
        Me.IsActiveCheck.Checked = True

        ' Buttons
        Me.AddBtn.Location = New System.Drawing.Point(650, 280)
        Me.AddBtn.Size = New System.Drawing.Size(80, 30)
        Me.AddBtn.Text = "إضافة"
        AddHandler Me.AddBtn.Click, AddressOf AddBtn_Click

        Me.EditBtn.Location = New System.Drawing.Point(740, 280)
        Me.EditBtn.Size = New System.Drawing.Size(80, 30)
        Me.EditBtn.Text = "تعديل"
        AddHandler Me.EditBtn.Click, AddressOf EditBtn_Click

        Me.DeleteBtn.Location = New System.Drawing.Point(830, 280)
        Me.DeleteBtn.Size = New System.Drawing.Size(80, 30)
        Me.DeleteBtn.Text = "حذف"
        AddHandler Me.DeleteBtn.Click, AddressOf DeleteBtn_Click

        Me.SaveBtn.Location = New System.Drawing.Point(650, 320)
        Me.SaveBtn.Size = New System.Drawing.Size(80, 30)
        Me.SaveBtn.Text = "حفظ"
        Me.SaveBtn.Enabled = False
        AddHandler Me.SaveBtn.Click, AddressOf SaveBtn_Click

        Me.CancelBtn.Location = New System.Drawing.Point(740, 320)
        Me.CancelBtn.Size = New System.Drawing.Size(80, 30)
        Me.CancelBtn.Text = "إلغاء"
        Me.CancelBtn.Enabled = False
        AddHandler Me.CancelBtn.Click, AddressOf CancelBtn_Click

        Me.CloseBtn.Location = New System.Drawing.Point(830, 320)
        Me.CloseBtn.Size = New System.Drawing.Size(80, 30)
        Me.CloseBtn.Text = "إغلاق"
        AddHandler Me.CloseBtn.Click, AddressOf CloseBtn_Click

        ' Add controls to form
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.CostCenterCodeEdit)
        Me.Controls.Add(Me.CostCenterNameEdit)
        Me.Controls.Add(Me.DescriptionEdit)
        Me.Controls.Add(Me.IsActiveCheck)
        Me.Controls.Add(Me.AddBtn)
        Me.Controls.Add(Me.EditBtn)
        Me.Controls.Add(Me.DeleteBtn)
        Me.Controls.Add(Me.SaveBtn)
        Me.Controls.Add(Me.CancelBtn)
        Me.Controls.Add(Me.CloseBtn)

        ' Add labels
        Dim lblCode As New Label()
        lblCode.Text = "كود مركز التكلفة:"
        lblCode.Location = New System.Drawing.Point(650, 53)
        lblCode.Size = New System.Drawing.Size(90, 20)
        Me.Controls.Add(lblCode)

        Dim lblName As New Label()
        lblName.Text = "اسم مركز التكلفة:"
        lblName.Location = New System.Drawing.Point(650, 93)
        lblName.Size = New System.Drawing.Size(90, 20)
        Me.Controls.Add(lblName)

        Dim lblDescription As New Label()
        lblDescription.Text = "الوصف:"
        lblDescription.Location = New System.Drawing.Point(650, 133)
        lblDescription.Size = New System.Drawing.Size(50, 20)
        Me.Controls.Add(lblDescription)

        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterNameEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterCodeEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DescriptionEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.IsActiveCheck.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
    End Sub

    Private Sub CostCenterManagement_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            CostCenterHelper.CreateCostCenterTable()
            
            ' تحميل البيانات
            LoadCostCenters()
            
            ' تعطيل حقول الإدخال
            SetInputControlsEnabled(False)
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCostCenters()
        Try
            Dim sql As String = "SELECT cost_center_id, cost_center_code, cost_center_name, description, is_active, created_date FROM cost_centers ORDER BY cost_center_name"
            
            Using adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet()
                adp.Fill(ds, "CostCenters")
                
                GridControl1.DataSource = ds.Tables("CostCenters")
                
                ' تكوين الأعمدة
                With GridView1
                    .Columns("cost_center_id").Visible = False
                    .Columns("cost_center_code").Caption = "الكود"
                    .Columns("cost_center_code").Width = 80
                    .Columns("cost_center_name").Caption = "اسم مركز التكلفة"
                    .Columns("cost_center_name").Width = 200
                    .Columns("description").Caption = "الوصف"
                    .Columns("description").Width = 250
                    .Columns("is_active").Caption = "نشط"
                    .Columns("is_active").Width = 60
                    .Columns("created_date").Caption = "تاريخ الإنشاء"
                    .Columns("created_date").Width = 100
                    .Columns("created_date").DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
                    .Columns("created_date").DisplayFormat.FormatString = "dd/MM/yyyy"
                End With
            End Using
        Catch ex As Exception
            Throw New Exception("خطأ في تحميل مراكز التكلفة: " & ex.Message)
        End Try
    End Sub

    Private Sub GridView1_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs)
        If Not isEditMode Then
            LoadSelectedCostCenter()
        End If
    End Sub

    Private Sub LoadSelectedCostCenter()
        Try
            If GridView1.FocusedRowHandle >= 0 Then
                currentCostCenterId = Convert.ToInt32(GridView1.GetFocusedRowCellValue("cost_center_id"))
                CostCenterCodeEdit.Text = GridView1.GetFocusedRowCellValue("cost_center_code").ToString()
                CostCenterNameEdit.Text = GridView1.GetFocusedRowCellValue("cost_center_name").ToString()
                DescriptionEdit.Text = GridView1.GetFocusedRowCellValue("description").ToString()
                IsActiveCheck.Checked = Convert.ToBoolean(GridView1.GetFocusedRowCellValue("is_active"))
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحميل
        End Try
    End Sub

    Private Sub AddBtn_Click(sender As Object, e As EventArgs)
        StartEditMode(True)
        ClearInputs()
        currentCostCenterId = 0
    End Sub

    Private Sub EditBtn_Click(sender As Object, e As EventArgs)
        If currentCostCenterId > 0 Then
            StartEditMode(False)
        Else
            XtraMessageBox.Show("يرجى اختيار مركز تكلفة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub DeleteBtn_Click(sender As Object, e As EventArgs)
        If currentCostCenterId > 0 Then
            If XtraMessageBox.Show("هل أنت متأكد من حذف مركز التكلفة المحدد؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    CostCenterHelper.DeleteCostCenter(currentCostCenterId)
                    LoadCostCenters()
                    ClearInputs()
                    XtraMessageBox.Show("تم حذف مركز التكلفة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Catch ex As Exception
                    XtraMessageBox.Show("خطأ في حذف مركز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        Else
            XtraMessageBox.Show("يرجى اختيار مركز تكلفة للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub SaveBtn_Click(sender As Object, e As EventArgs)
        Try
            If ValidateInputs() Then
                If currentCostCenterId = 0 Then
                    ' إضافة جديد
                    CostCenterHelper.AddCostCenter(CostCenterCodeEdit.Text.Trim(), CostCenterNameEdit.Text.Trim(), DescriptionEdit.Text.Trim(), IsActiveCheck.Checked)
                    XtraMessageBox.Show("تم إضافة مركز التكلفة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    ' تعديل موجود
                    CostCenterHelper.UpdateCostCenter(currentCostCenterId, CostCenterCodeEdit.Text.Trim(), CostCenterNameEdit.Text.Trim(), DescriptionEdit.Text.Trim(), IsActiveCheck.Checked)
                    XtraMessageBox.Show("تم تعديل مركز التكلفة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
                
                LoadCostCenters()
                EndEditMode()
            End If
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في حفظ البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CancelBtn_Click(sender As Object, e As EventArgs)
        EndEditMode()
        LoadSelectedCostCenter()
    End Sub

    Private Sub CloseBtn_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub

    Private Sub StartEditMode(isNew As Boolean)
        isEditMode = True
        SetInputControlsEnabled(True)
        SetButtonsEnabled(False)
        SaveBtn.Enabled = True
        CancelBtn.Enabled = True
        
        If isNew Then
            CostCenterCodeEdit.Focus()
        Else
            CostCenterNameEdit.Focus()
        End If
    End Sub

    Private Sub EndEditMode()
        isEditMode = False
        SetInputControlsEnabled(False)
        SetButtonsEnabled(True)
        SaveBtn.Enabled = False
        CancelBtn.Enabled = False
    End Sub

    Private Sub SetInputControlsEnabled(enabled As Boolean)
        CostCenterCodeEdit.Enabled = enabled
        CostCenterNameEdit.Enabled = enabled
        DescriptionEdit.Enabled = enabled
        IsActiveCheck.Enabled = enabled
    End Sub

    Private Sub SetButtonsEnabled(enabled As Boolean)
        AddBtn.Enabled = enabled
        EditBtn.Enabled = enabled
        DeleteBtn.Enabled = enabled
    End Sub

    Private Sub ClearInputs()
        CostCenterCodeEdit.Text = ""
        CostCenterNameEdit.Text = ""
        DescriptionEdit.Text = ""
        IsActiveCheck.Checked = True
    End Sub

    Private Function ValidateInputs() As Boolean
        If String.IsNullOrWhiteSpace(CostCenterCodeEdit.Text) Then
            XtraMessageBox.Show("يرجى إدخال كود مركز التكلفة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CostCenterCodeEdit.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(CostCenterNameEdit.Text) Then
            XtraMessageBox.Show("يرجى إدخال اسم مركز التكلفة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            CostCenterNameEdit.Focus()
            Return False
        End If
        
        Return True
    End Function
End Class
