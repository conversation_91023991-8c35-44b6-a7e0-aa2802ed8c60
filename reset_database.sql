-- سكريبت تصفير قاعدة البيانات كاملة وإعادة تعيين الـ Identity
-- تحذير: هذا السكريبت سيحذف جميع البيانات في قاعدة البيانات

USE data_a
GO

PRINT '============================================'
PRINT 'بدء عملية تصفير قاعدة البيانات'
PRINT 'تحذير: سيتم حذف جميع البيانات!'
PRINT '============================================'

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
EXEC sp_MSforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all"
PRINT 'تم تعطيل فحص المفاتيح الخارجية'

-- حذف البيانات من الجداول الرئيسية
-- جداول الفواتير والمبيعات
IF EXISTS (SELECT * FROM sysobjects WHERE name='invoice_add' AND xtype='U')
BEGIN
    DELETE FROM invoice_add
    DBCC CHECKIDENT ('invoice_add', RESEED, 0)
    PRINT 'تم تصفير جدول invoice_add'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='invoice_details' AND xtype='U')
BEGIN
    DELETE FROM invoice_details
    DBCC CHECKIDENT ('invoice_details', RESEED, 0)
    PRINT 'تم تصفير جدول invoice_details'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add' AND xtype='U')
BEGIN
    DELETE FROM pos_add
    DBCC CHECKIDENT ('pos_add', RESEED, 0)
    PRINT 'تم تصفير جدول pos_add'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add2' AND xtype='U')
BEGIN
    DELETE FROM pos_add2
    DBCC CHECKIDENT ('pos_add2', RESEED, 0)
    PRINT 'تم تصفير جدول pos_add2'
END

-- جداول العملاء والموردين
IF EXISTS (SELECT * FROM sysobjects WHERE name='customer' AND xtype='U')
BEGIN
    DELETE FROM customer
    DBCC CHECKIDENT ('customer', RESEED, 0)
    PRINT 'تم تصفير جدول customer'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='supplier' AND xtype='U')
BEGIN
    DELETE FROM supplier
    DBCC CHECKIDENT ('supplier', RESEED, 0)
    PRINT 'تم تصفير جدول supplier'
END

-- جداول الأصناف والمخزون
IF EXISTS (SELECT * FROM sysobjects WHERE name='items' AND xtype='U')
BEGIN
    DELETE FROM items
    DBCC CHECKIDENT ('items', RESEED, 0)
    PRINT 'تم تصفير جدول items'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='stock' AND xtype='U')
BEGIN
    DELETE FROM stock
    DBCC CHECKIDENT ('stock', RESEED, 0)
    PRINT 'تم تصفير جدول stock'
END

-- جداول المصروفات والإيرادات
IF EXISTS (SELECT * FROM sysobjects WHERE name='expenses' AND xtype='U')
BEGIN
    DELETE FROM expenses
    DBCC CHECKIDENT ('expenses', RESEED, 0)
    PRINT 'تم تصفير جدول expenses'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='income' AND xtype='U')
BEGIN
    DELETE FROM income
    DBCC CHECKIDENT ('income', RESEED, 0)
    PRINT 'تم تصفير جدول income'
END

-- جداول السندات
IF EXISTS (SELECT * FROM sysobjects WHERE name='vouchers' AND xtype='U')
BEGIN
    DELETE FROM vouchers
    DBCC CHECKIDENT ('vouchers', RESEED, 0)
    PRINT 'تم تصفير جدول vouchers'
END

-- جداول الحسابات
IF EXISTS (SELECT * FROM sysobjects WHERE name='accounts' AND xtype='U')
BEGIN
    DELETE FROM accounts
    DBCC CHECKIDENT ('accounts', RESEED, 0)
    PRINT 'تم تصفير جدول accounts'
END

-- جداول مراكز التكلفة
IF EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
BEGIN
    DELETE FROM cost_centers
    DBCC CHECKIDENT ('cost_centers', RESEED, 0)
    PRINT 'تم تصفير جدول cost_centers'
END

-- جدول الشيفتات
IF EXISTS (SELECT * FROM sysobjects WHERE name='shifts' AND xtype='U')
BEGIN
    DELETE FROM shifts
    DBCC CHECKIDENT ('shifts', RESEED, 0)
    PRINT 'تم تصفير جدول shifts'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='shift_details' AND xtype='U')
BEGIN
    DELETE FROM shift_details
    DBCC CHECKIDENT ('shift_details', RESEED, 0)
    PRINT 'تم تصفير جدول shift_details'
END

-- جداول إضافية محتملة
IF EXISTS (SELECT * FROM sysobjects WHERE name='payments' AND xtype='U')
BEGIN
    DELETE FROM payments
    DBCC CHECKIDENT ('payments', RESEED, 0)
    PRINT 'تم تصفير جدول payments'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='receipts' AND xtype='U')
BEGIN
    DELETE FROM receipts
    DBCC CHECKIDENT ('receipts', RESEED, 0)
    PRINT 'تم تصفير جدول receipts'
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='returns' AND xtype='U')
BEGIN
    DELETE FROM returns
    DBCC CHECKIDENT ('returns', RESEED, 0)
    PRINT 'تم تصفير جدول returns'
END

-- إعادة تفعيل فحص المفاتيح الخارجية
EXEC sp_MSforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all"
PRINT 'تم إعادة تفعيل فحص المفاتيح الخارجية'

PRINT '============================================'
PRINT 'تم تصفير قاعدة البيانات بنجاح'
PRINT 'جميع الجداول فارغة والـ Identity يبدأ من 1'
PRINT '============================================'
