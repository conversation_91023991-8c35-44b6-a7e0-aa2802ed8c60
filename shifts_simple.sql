-- سكريبت إنشاء جدول الشيفتات
-- يمكن تشغيله مباشرة في SQL Server Management Studio

USE [مبيعات 2022]
GO

-- إنشاء جدول الشيفتات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='shifts' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[shifts](
        [shift_id] [int] IDENTITY(1,1) NOT NULL,
        [shift_code] [nvarchar](50) NOT NULL,
        [cashier_name] [nvarchar](100) NOT NULL,
        [cashier_id] [int] NULL DEFAULT (0),
        [store_name] [nvarchar](100) NULL,
        [start_date] [datetime] NOT NULL,
        [end_date] [datetime] NULL,
        [start_amount] [decimal](18, 2) NULL DEFAULT (0),
        [end_amount] [decimal](18, 2) NULL DEFAULT (0),
        [total_sales] [decimal](18, 2) NULL DEFAULT (0),
        [total_cash] [decimal](18, 2) NULL DEFAULT (0),
        [total_credit] [decimal](18, 2) NULL DEFAULT (0),
        [total_profit] [decimal](18, 2) NULL DEFAULT (0),
        [total_invoices] [int] NULL DEFAULT (0),
        [cash_difference] [decimal](18, 2) NULL DEFAULT (0),
        [notes] [nvarchar](500) NULL,
        [shift_status] [nvarchar](20) NOT NULL DEFAULT (N'مفتوح'),
        [created_by] [nvarchar](100) NULL,
        [created_date] [datetime] NULL DEFAULT (GETDATE()),
        [updated_by] [nvarchar](100) NULL,
        [updated_date] [datetime] NULL,
        CONSTRAINT [PK_shifts] PRIMARY KEY CLUSTERED ([shift_id] ASC)
    )
    
    PRINT 'تم إنشاء جدول shifts بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول shifts موجود بالفعل'
END
GO

-- إضافة عمود shift_id إلى جدول invoice_add إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'shift_id')
BEGIN
    ALTER TABLE [dbo].[invoice_add] ADD [shift_id] [int] NULL
    PRINT 'تم إضافة عمود shift_id إلى جدول invoice_add'
END
ELSE
BEGIN
    PRINT 'عمود shift_id موجود بالفعل في جدول invoice_add'
END
GO

-- إضافة عمود shift_id إلى جدول pos_add إذا كان موجوداً
IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pos_add' AND COLUMN_NAME = 'shift_id')
    BEGIN
        ALTER TABLE [dbo].[pos_add] ADD [shift_id] [int] NULL
        PRINT 'تم إضافة عمود shift_id إلى جدول pos_add'
    END
    ELSE
    BEGIN
        PRINT 'عمود shift_id موجود بالفعل في جدول pos_add'
    END
END
GO

-- إضافة عمود shift_id إلى جدول pos_add2 إذا كان موجوداً
IF EXISTS (SELECT * FROM sysobjects WHERE name='pos_add2' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pos_add2' AND COLUMN_NAME = 'shift_id')
    BEGIN
        ALTER TABLE [dbo].[pos_add2] ADD [shift_id] [int] NULL
        PRINT 'تم إضافة عمود shift_id إلى جدول pos_add2'
    END
    ELSE
    BEGIN
        PRINT 'عمود shift_id موجود بالفعل في جدول pos_add2'
    END
END
GO

-- إنشاء فهرس على shift_code للبحث السريع
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_shifts_shift_code')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_shifts_shift_code] ON [dbo].[shifts] ([shift_code] ASC)
    PRINT 'تم إنشاء فهرس على shift_code'
END
GO

-- إنشاء فهرس على cashier_name والحالة للبحث السريع
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_shifts_cashier_status')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_shifts_cashier_status] ON [dbo].[shifts] ([cashier_name] ASC, [shift_status] ASC)
    PRINT 'تم إنشاء فهرس على cashier_name و shift_status'
END
GO

-- إنشاء فهرس على shift_id في جدول invoice_add
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'shift_id')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_invoice_add_shift_id')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_invoice_add_shift_id] ON [dbo].[invoice_add] ([shift_id] ASC)
        PRINT 'تم إنشاء فهرس على shift_id في جدول invoice_add'
    END
END
GO

PRINT '============================================'
PRINT 'تم تنفيذ جميع العمليات بنجاح'
PRINT 'نظام إدارة الشيفتات جاهز للاستخدام'
PRINT '============================================'
