﻿Imports System.Data.SqlClient

Public Class Treasury_add
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_user()
        new_btn_Click(Nothing, Nothing)
    End Sub


    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from treasury_name", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("treasury_name"))
        Next
    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from treasury_name where treasury_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("treasury_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        treasury_name.Text = ""
        Dim sql = "select * from treasury_name where treasury_id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            treasury_name.Text = dr!treasury_name
            Storekeeper.Text = dr!Storekeeper
            balace.Text = dr!balace
            cash.Checked = dr!cash
            trans.Checked = dr!trans
            invoice.Checked = dr!invoice
            voucher.Checked = dr!voucher

            Treasury_active.Checked = dr!Treasury_active
        End If
        save_btn.Enabled = False
        delet_btn.Enabled = True
        edit_btn.Enabled = True
    End Sub
    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from treasury_name where treasury_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الخزينة")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف الخزينة", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الخزينة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from treasury_name where treasury_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!treasury_name = treasury_name.Text
                dr!Storekeeper = Storekeeper.Text
                dr!balace = balace.Text
                dr!cash = cash.Checked
                dr!trans = trans.Checked
                dr!invoice = invoice.Checked
                dr!voucher = voucher.Checked

                dr!Treasury_active = Treasury_active.Checked
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل الخزينة", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل الخزينة اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If treasury_name.Text = "" Then
            MsgBox("أدخل اسم الخزينة")
            treasury_name.Focus()
            Exit Sub
        End If

        Dim sql = "select * from treasury_name where treasury_name=N'" & (treasury_name.Text) & "' "
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            MsgBox("اسم الخزينة موجود سابقا", MsgBoxStyle.Critical, "خطأ")
        Else
            Dim dr = dt.NewRow
            dr!treasury_name = treasury_name.Text
            dr!Storekeeper = Storekeeper.Text
            dr!balace = balace.Text

            dr!cash = cash.Checked
            dr!trans = trans.Checked
            dr!invoice = invoice.Checked
            dr!voucher = voucher.Checked
            dr!Treasury_active = Treasury_active.Checked
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            MsgBox("تم أضافة الخزينة")
            new_btn_Click(Nothing, Nothing)
            fill_user()
        End If
       
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        treasury_name.Text = ""
        Storekeeper.Text = ""
        balace.Text = 0

        Storekeeper.Text = ""
        cash.Checked = True
        trans.Checked = True
        invoice.Checked = True
        voucher.Checked = True
        save_btn.Enabled = True
        delet_btn.Enabled = False
        edit_btn.Enabled = False
    End Sub

  
End Class