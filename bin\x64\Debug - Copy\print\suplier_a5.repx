﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.invoice_a5, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="invoice_a5" Margins="0, 0, 0, 0" PaperKind="A5" PageWidth="583" PageHeight="827" Version="17.2" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="6" ControlType="DetailBand" Name="Detail1" HeightF="25">
          <Controls>
            <Item1 Ref="7" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="491.6107,25" LocationFloat="45.55867, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="8" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="9" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.1483655486893258" TextFormatString="{0:n2}" Text="XrTableCell30" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="10" Expression="[invoice_print].[cus_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="11" UseFont="false" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="12" ControlType="XRTableCell" Name="XrTableCell9" Weight="1.1483655486893258" TextFormatString="{0:n2}" Text="XrTableCell9" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="13" Expression="[invoice_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="14" UseFont="false" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="15" ControlType="XRTableCell" Name="XrTableCell16" Weight="0.8307796527335698" Text="XrTableCell16" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="16" Expression="[invoice_print].[item_descound]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="17" UseFont="false" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="18" ControlType="XRTableCell" Name="XrTableCell10" Weight="1.0110397088115195" TextFormatString="{0:n2}" Text="XrTableCell10" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="19" Expression="[invoice_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="20" UseFont="false" UsePadding="false" />
                    </Item4>
                    <Item5 Ref="21" ControlType="XRTableCell" Name="XrTableCell8" Weight="1.0303016515208385" TextFormatString="{0:#,#}" Text="XrTableCell6" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="22" Expression="[invoice_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="23" UseFont="false" />
                    </Item5>
                    <Item6 Ref="24" ControlType="XRTableCell" Name="XrTableCell31" Weight="1.2066867670530213" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="25" Expression="[invoice_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item6>
                    <Item7 Ref="26" ControlType="XRTableCell" Name="XrTableCell32" Weight="2.705433995816513" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="27" Expression="[invoice_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="28" UseFont="false" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="29" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item4>
    <Item5 Ref="30" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="97.04431">
      <Controls>
        <Item1 Ref="31" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="114.6173,83.99998" LocationFloat="49.70967, 3.044335">
          <ExpressionBindings>
            <Item1 Ref="32" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item1>
        <Item2 Ref="33" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="270.0302, 46.00262" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="34" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="35" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="36" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="270.0302, 14.66931" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="37" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="38" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item5>
    <Item6 Ref="39" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="364.5485">
      <Controls>
        <Item1 Ref="40" ControlType="XRLabel" Name="XrLabel27" Text="XrLabel27" TextAlignment="TopRight" SizeF="229.1117,23" LocationFloat="233.7882, 331.5486" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="41" Expression="[user_invoice]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="42" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="43" ControlType="XRLabel" Name="XrLabel18" RightToLeft="Yes" Text="المستخدم :" TextAlignment="MiddleRight" SizeF="77.48438,25.08331" LocationFloat="462.8999, 329.4652" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="44" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="45" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="45.55867, 339.4652" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="46" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="47" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="45.55867, 303.9654" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="48" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="49" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="306.5962,23" LocationFloat="230.5726, 296.0487" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="50" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="51" UseFont="false" />
        </Item5>
        <Item6 Ref="52" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="272.5635,23.00001" LocationFloat="85.85519, 245.5489" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="53" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="54" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="55" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="272.5635,23" LocationFloat="85.85519, 272.7155" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="56" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="58" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="358.4188, 245.5487" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="60" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="358.4188, 270.632" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="62" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="303.3806,25.00002" LocationFloat="233.7882, 201.8636" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="63" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="64" UseFont="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="65" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="303.3808,25" LocationFloat="233.7882, 176.8636" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="66" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="68" ControlType="XRTable" Name="XrTable6" TextAlignment="MiddleCenter" SizeF="173.7818,25.00002" LocationFloat="45.55867, 205.4052" Font="Droid Arabic Kufi, 8pt" ForeColor="White" BackColor="DarkGreen" Borders="Bottom">
          <Rows>
            <Item1 Ref="69" ControlType="XRTableRow" Name="XrTableRow9" Weight="1">
              <Cells>
                <Item1 Ref="70" ControlType="XRTableCell" Name="XrTableCell25" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" ForeColor="White" BackColor="Chocolate" Padding="2,2,0,0,100" BorderColor="Black">
                  <ExpressionBindings>
                    <Item1 Ref="71" Expression="[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="72" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="73" ControlType="XRTableCell" Name="XrTableCell26" Weight="1" Text="الباقي" TextAlignment="MiddleRight" BackColor="Chocolate" Padding="2,2,0,0,100">
                  <StylePriority Ref="74" UseBackColor="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="75" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="76" ControlType="XRTable" Name="XrTable5" TextAlignment="MiddleCenter" SizeF="173.7818,50" LocationFloat="45.55867, 151.8636" Font="Droid Arabic Kufi, 8pt" Borders="Bottom">
          <Rows>
            <Item1 Ref="77" ControlType="XRTableRow" Name="XrTableRow7" Weight="1">
              <Cells>
                <Item1 Ref="78" ControlType="XRTableCell" Name="XrTableCell21" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="79" Expression="[money_plus]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="80" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="81" ControlType="XRTableCell" Name="XrTableCell22" Weight="1" Text="المطلوب" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="82" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="83" ControlType="XRTableRow" Name="XrTableRow8" Weight="1">
              <Cells>
                <Item1 Ref="84" ControlType="XRTableCell" Name="XrTableCell23" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" BorderColor="Black" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="85" Expression="[pay]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="86" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="87" ControlType="XRTableCell" Name="XrTableCell24" Weight="1" Text="المدفوع" TextAlignment="MiddleRight" ForeColor="White" BackColor="DarkGreen" Padding="2,2,0,0,100" Borders="None">
                  <StylePriority Ref="88" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="89" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="90" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="173.7818,100" LocationFloat="45.55871, 50" Font="Droid Arabic Kufi, 8pt" Borders="Bottom">
          <Rows>
            <Item1 Ref="91" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
              <Cells>
                <Item1 Ref="92" ControlType="XRTableCell" Name="XrTableCell17" Weight="1" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="93" Expression="[invoice_descound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="94" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="95" ControlType="XRTableCell" Name="XrTableCell18" Weight="1" Text="الخصم" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="96" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="97" ControlType="XRTableRow" Name="XrTableRow6" Weight="1">
              <Cells>
                <Item1 Ref="98" ControlType="XRTableCell" Name="XrTableCell19" Weight="1" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="99" Expression="[invoice_tax]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="100" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="101" ControlType="XRTableCell" Name="XrTableCell20" Weight="1" Text="الضريبة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="102" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
            <Item3 Ref="103" ControlType="XRTableRow" Name="XrTableRow10" Weight="1">
              <Cells>
                <Item1 Ref="104" ControlType="XRTableCell" Name="XrTableCell27" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell27" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="105" Expression="[total_invoice]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="106" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="107" ControlType="XRTableCell" Name="XrTableCell28" Weight="1" Text="صافي الفاتورة" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="108" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item3>
            <Item4 Ref="109" ControlType="XRTableRow" Name="XrTableRow11" Weight="1">
              <Cells>
                <Item1 Ref="110" ControlType="XRTableCell" Name="XrTableCell29" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell29" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="111" Expression="[past_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="112" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="113" ControlType="XRTableCell" Name="XrTableCell30" Weight="1" Text="السابق" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="114" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item4>
          </Rows>
          <StylePriority Ref="115" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="116" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="173.7818,50" LocationFloat="45.55871, 0" Font="Droid Arabic Kufi, 8pt" Borders="Bottom">
          <Rows>
            <Item1 Ref="117" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="118" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="119" Expression="[invoice_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="120" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="121" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="122" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="123" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="124" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="125" Expression="[invoice_pound]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="126" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="127" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="128" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="129" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item15>
      </Controls>
    </Item6>
    <Item7 Ref="130" ControlType="PageFooterBand" Name="PageFooter" HeightF="29.5871">
      <Controls>
        <Item1 Ref="131" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,23" LocationFloat="420.5023, 2.083333" Font="Times New Roman, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="132" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="133" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="469.4607, 0" Font="Times New Roman, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="134" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="135" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="45.55867, 0" Font="Times New Roman, 8pt" Padding="2,2,0,0,100">
          <StylePriority Ref="136" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item7>
    <Item8 Ref="137" ControlType="PageHeaderBand" Name="PageHeader" HeightF="286.5147">
      <Controls>
        <Item1 Ref="138" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="489.4228,33.33334" LocationFloat="47.74607, 253.1814" Font="Droid Arabic Kufi, 8pt" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="139" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="140" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.64785404692130832" Text="مستهلك" Padding="2,2,0,0,100">
                  <StylePriority Ref="141" UsePadding="false" />
                </Item1>
                <Item2 Ref="142" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.64785404692130832" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="143" UsePadding="false" />
                </Item2>
                <Item3 Ref="144" ControlType="XRTableCell" Name="XrTableCell15" Weight="0.46868698935474512" Text="الخصم" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100">
                  <StylePriority Ref="145" UseFont="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="146" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.57038110529438424" Text="السعر" Padding="2,2,0,0,100" />
                <Item5 Ref="147" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.58124792843161965" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="148" UsePadding="false" />
                </Item5>
                <Item6 Ref="149" ControlType="XRTableCell" Name="XrTableCell3" Weight="0.68075613084416631" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item7 Ref="150" ControlType="XRTableCell" Name="XrTableCell6" Weight="1.7166200372424629" Text="اسم الصنف" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="White">
                  <StylePriority Ref="151" UseFont="false" />
                </Item7>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="152" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="153" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="121.0718,31.25" LocationFloat="299.4305, 76.86749" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="154" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="155" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="156" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="المخزن :" TextAlignment="TopCenter" SizeF="116.6667,31.25" LocationFloat="420.5023, 76.86745" Font="Droid Arabic Kufi, 9pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="157" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="158" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="121.0718,31.24998" LocationFloat="299.4305, 108.1175" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="159" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="160" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="161" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="116.6667,31.25" LocationFloat="420.5023, 108.1175" Font="Droid Arabic Kufi, 9pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="162" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="163" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,73.54167" LocationFloat="437.1689, 3.32578">
          <ExpressionBindings>
            <Item1 Ref="164" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item6>
        <Item7 Ref="165" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="116.6671,31.25002" LocationFloat="420.5023, 151.8675" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="166" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="167" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="116.6671,31.24998" LocationFloat="420.5023, 183.1175" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="168" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="169" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="رقم الهاتف :" TextAlignment="TopCenter" SizeF="116.6667,31.25" LocationFloat="420.5023, 214.3674" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="170" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="171" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="140.8969,31.25004" LocationFloat="45.55871, 108.1175" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="172" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="173" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="174" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="112.9749,31.24999" LocationFloat="186.4555, 108.1175" Font="Droid Arabic Kufi, 9pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="175" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="176" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="39.57356, 64.08332" Padding="2,2,0,0,100">
          <StylePriority Ref="177" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="178" ControlType="XRLabel" Name="XrLabel19" Text="فاتورة مبيعات" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="39.57356, 0" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="179" UseFont="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="180" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="85.85518, 31.33332">
          <ExpressionBindings>
            <Item1 Ref="181" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item14>
        <Item15 Ref="182" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="374.9438,31.25002" LocationFloat="45.55871, 151.8675" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="183" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="184" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="185" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="374.9438,31.25" LocationFloat="45.55871, 183.1175" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="186" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="187" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="188" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="374.9435,31.25002" LocationFloat="45.55871, 214.3674" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="189" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="190" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
      </Controls>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="191" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="192" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="193" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="194" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="195" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="196" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>