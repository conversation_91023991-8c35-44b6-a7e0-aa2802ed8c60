﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.inventory_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="inventory_a4" Margins="0, 1, 0, 6" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="inventory_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="5.541738" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="122.9167">
      <Controls>
        <Item1 Ref="6" ControlType="XRLine" Name="XrLine2" SizeF="813.0001,4.791673" LocationFloat="12.63542, 0" />
        <Item2 Ref="7" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="545.6459, 23.29164" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="8" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="10" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="545.6459, 54.62497" Font="Droid Arabic Kufi, 12pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="11" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="12" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="13" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="128.1004,105.8334" LocationFloat="12.63542, 7.083305">
          <ExpressionBindings>
            <Item1 Ref="14" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="15" ControlType="PageHeaderBand" Name="PageHeader" HeightF="223.1875">
      <Controls>
        <Item1 Ref="16" ControlType="XRLine" Name="XrLine1" SizeF="813.0001,4.791673" LocationFloat="0, 0" />
        <Item2 Ref="17" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="825.9999,33.33334" LocationFloat="0, 189.8542" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="18" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="19" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.68744796491676619" Text="فرق السعر" Padding="2,2,0,0,100">
                  <StylePriority Ref="20" UsePadding="false" />
                </Item1>
                <Item2 Ref="21" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.53332130837482561" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="22" ControlType="XRTableCell" Name="XrTableCell12" Weight="0.68809498013216042" Text="فرق الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="23" UsePadding="false" />
                </Item3>
                <Item4 Ref="24" ControlType="XRTableCell" Name="XrTableCell11" Weight="0.86515959644525642" Text="الكمية الجديدة" Padding="2,2,0,0,100">
                  <StylePriority Ref="25" UsePadding="false" />
                </Item4>
                <Item5 Ref="26" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.89202402913429413" Text="الكمية الحالية" Padding="2,2,0,0,100">
                  <StylePriority Ref="27" UsePadding="false" />
                </Item5>
                <Item6 Ref="28" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.92133255573437278" Text="الوحدة" Padding="2,2,0,0,100" />
                <Item7 Ref="29" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.5774999660633404" Text="اسم الصنف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="30" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="31" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="101.1146, 49.02084">
          <ExpressionBindings>
            <Item1 Ref="32" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
        <Item4 Ref="33" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="691.2187, 4.791673">
          <ExpressionBindings>
            <Item1 Ref="34" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item4>
        <Item5 Ref="35" ControlType="XRLabel" Name="XrLabel19" Text="تسوية جردية" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="41.11459, 17.68753" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="36" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="37" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="41.11459, 94.27084" Padding="2,2,0,0,100">
          <StylePriority Ref="38" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="39" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="266.2188, 148.4584" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="40" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="41" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="675.5939, 148.4584" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="42" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="43" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="225.1043,31.25004" LocationFloat="41.11459, 148.4584" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="44" Expression="[inventory_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="45" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="46" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="277.0833,31.24999" LocationFloat="398.5104, 148.4584" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="47" Expression="[inventory_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="49" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="المخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="675.5939, 94.27084" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="50" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="51" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="277.0832,31.25" LocationFloat="398.5106, 94.27084" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="52" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="53" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item12>
      </Controls>
    </Item5>
    <Item6 Ref="54" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="232.875">
      <Controls>
        <Item1 Ref="55" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="487.8751, 199.875" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="56" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="57" UseFont="false" />
        </Item1>
        <Item2 Ref="58" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="86.75009, 97.70832" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="59" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="60" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="61" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="418.75,23" LocationFloat="86.75009, 124.875" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="62" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="63" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="64" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="62.0835,25.08331" LocationFloat="505.5001, 97.70832" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="66" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="107.9167,25.08332" LocationFloat="505.5001, 122.7916" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="67" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="68" ControlType="XRLabel" Name="XrLabel29" Text="............................." TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="3.000005, 199.875" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="69" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="70" ControlType="XRLabel" Name="XrLabel7" Text="توقيع وختم الشركة" TextAlignment="MiddleRight" SizeF="128.7499,25.08331" LocationFloat="3.000005, 164.375" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="71" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="72" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="200,50" LocationFloat="12.63552, 30.62496" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="73" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="74" ControlType="XRTableCell" Name="XrTableCell15" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="75" Expression="[damage_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="76" ControlType="XRTableCell" Name="XrTableCell16" Weight="1" Text="عدد الاصناف" Padding="2,2,0,0,100" />
              </Cells>
            </Item1>
            <Item2 Ref="77" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="78" ControlType="XRTableCell" Name="XrTableCell17" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell11" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="79" Expression="[total_inventory]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="80" UsePadding="false" />
                </Item1>
                <Item2 Ref="81" ControlType="XRTableCell" Name="XrTableCell18" Weight="1" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="82" UsePadding="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="83" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="84" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="526.5314,25" LocationFloat="287.1041, 55.62496" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="85" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="86" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="87" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="526.5315,25" LocationFloat="287.1041, 30.62496" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="88" Expression="[inventory_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="89" UseFont="false" UseTextAlignment="false" />
        </Item10>
      </Controls>
    </Item6>
    <Item7 Ref="90" ControlType="PageFooterBand" Name="PageFooter" HeightF="35.08329">
      <Controls>
        <Item1 Ref="91" ControlType="XRLine" Name="XrLine3" SizeF="813.0001,4.791673" LocationFloat="3.000005, 5.20827" />
        <Item2 Ref="92" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="700.5521, 9.999974" Padding="2,2,0,0,100">
          <StylePriority Ref="93" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="94" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52084,25.08331" LocationFloat="749.5104, 9.999974" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="95" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="96" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="20.03132, 9.999974" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="97" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item7>
    <Item8 Ref="98" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="99" ControlType="DetailBand" Name="Detail1" HeightF="28.125">
          <Controls>
            <Item1 Ref="100" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="826,25" LocationFloat="0, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="101" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="102" ControlType="XRTableCell" Name="XrTableCell14" Weight="0.79252109622627365" TextFormatString="{0:#,#}" Text="XrTableCell14" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="103" Expression="[inventory_print].[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="104" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="105" ControlType="XRTableCell" Name="XrTableCell13" Weight="0.61483675328568288" TextFormatString="{0:#,#}" Text="XrTableCell13" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="106" Expression="[inventory_print].[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="107" UsePadding="false" />
                    </Item2>
                    <Item3 Ref="108" ControlType="XRTableCell" Name="XrTableCell9" Weight="0.79326707554334475" TextFormatString="{0:#,#}" Text="XrTableCell9" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="109" Expression="[inventory_print].[item_betwen_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="110" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="111" ControlType="XRTableCell" Name="XrTableCell10" Weight="0.99739660680870013" TextFormatString="{0:#,#}" Text="XrTableCell10" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="112" Expression="[inventory_print].[item_count_new]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="113" UsePadding="false" />
                    </Item4>
                    <Item5 Ref="114" ControlType="XRTableCell" Name="XrTableCell6" Weight="1.0283641917043207" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="115" Expression="[inventory_print].[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item5>
                    <Item6 Ref="116" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.062153868176561" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="117" Expression="[inventory_print].[item_unit]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item6>
                    <Item7 Ref="118" ControlType="XRTableCell" Name="XrTableCell8" Weight="2.9714597145751771" Text="XrTableCell8" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="119" Expression="[inventory_print].[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="120" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="121" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="122" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="123" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="124" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="125" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="126" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="127" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>