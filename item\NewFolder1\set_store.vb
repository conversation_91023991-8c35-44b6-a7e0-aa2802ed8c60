﻿Imports System.Data.SqlClient

Public Class set_store
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_user()
    End Sub

    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from store", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from store where store_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("store_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        store_name.Text = ""
        Dim sql = "select * from store where store_id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            store_name.Text = dr!store_name
            Storekeeper.Text = dr!Storekeeper
            phone.Text = dr!phone
            adress.Text = dr!adress
            inventory.Checked = dr!inventory
            damage.Checked = dr!damage
            trans.Checked = dr!trans
            Drawings.Checked = dr!Drawings
            invoice.Checked = dr!invoice
            voucher.Checked = dr!voucher
            store_active.Checked = dr!store_active
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub
    Private Sub exit_button_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        store_name.Text = ""
        Storekeeper.Text = ""
        phone.Text = ""
        adress.Text = ""
        inventory.Checked = True
        damage.Checked = True
        trans.Checked = True
        Drawings.Checked = True
        invoice.Checked = True
        voucher.Checked = True
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
        store_active.Checked = True
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If store_name.Text = "" Then
            MsgBox("أدخل اسم المخزن")
            store_name.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from store where store_name=N'" & (store_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم المخزن موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!store_name = store_name.Text
                dr!Storekeeper = Storekeeper.Text
                dr!phone = phone.Text
                dr!adress = adress.Text
                dr!inventory = inventory.Checked
                dr!damage = damage.Checked
                dr!trans = trans.Checked
                dr!Drawings = Drawings.Checked
                dr!invoice = invoice.Checked
                dr!voucher = voucher.Checked
                dr!store_active = store_active.Checked
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة المخزن")
                new_btn_Click(Nothing, Nothing)
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ المخزن اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from store where store_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!store_name = store_name.Text
                dr!Storekeeper = Storekeeper.Text
                dr!phone = phone.Text
                dr!adress = adress.Text
                dr!inventory = inventory.Checked
                dr!damage = damage.Checked
                dr!trans = trans.Checked
                dr!Drawings = Drawings.Checked
                dr!invoice = invoice.Checked
                dr!voucher = voucher.Checked
                dr!store_active = store_active.Checked
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                new_btn_Click(Nothing, Nothing)
                MsgBox("تم تعديل المخزن", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل المخزن اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from store where store_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب المخزن")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                new_btn_Click(Nothing, Nothing)
                MsgBox("تم حذف المخزن", MsgBoxStyle.Information, "تنبيه")
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف المخزن اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub
End Class