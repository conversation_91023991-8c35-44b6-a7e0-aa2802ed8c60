<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CostCentersManagement
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(CostCentersManagement))
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.PictureEdit1 = New DevExpress.XtraEditors.PictureEdit()
        Me.SplitContainerControl1 = New DevExpress.XtraEditors.SplitContainerControl()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.CostCentersGrid = New DevExpress.XtraGrid.GridControl()
        Me.CostCentersView = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ColCostCenterCode = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColCostCenterName = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColCostCenterType = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColBudgetAmount = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.ColIsActive = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.CostCenterCodeEdit = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.CostCenterNameEdit = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.CostCenterDescriptionEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.CostCenterTypeCombo = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.ParentCostCenterCombo = New DevExpress.XtraEditors.LookUpEdit()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.BudgetAmountEdit = New DevExpress.XtraEditors.SpinEdit()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.NotesEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.IsActiveCheck = New DevExpress.XtraEditors.CheckEdit()
        Me.ButtonsPanel = New DevExpress.XtraEditors.PanelControl()
        Me.NewButton = New DevExpress.XtraEditors.SimpleButton()
        Me.SaveButton = New DevExpress.XtraEditors.SimpleButton()
        Me.EditButton = New DevExpress.XtraEditors.SimpleButton()
        Me.DeleteButton = New DevExpress.XtraEditors.SimpleButton()
        Me.RefreshButton = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SplitContainerControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SplitContainerControl1.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.CostCentersGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCentersView, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.CostCenterCodeEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterNameEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterDescriptionEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CostCenterTypeCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ParentCostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BudgetAmountEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NotesEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.IsActiveCheck.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ButtonsPanel, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ButtonsPanel.SuspendLayout()
        Me.SuspendLayout()
        '
        'PanelControl1
        '
        Me.PanelControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.PanelControl1.Appearance.Options.UseBackColor = True
        Me.PanelControl1.Controls.Add(Me.LabelControl1)
        Me.PanelControl1.Controls.Add(Me.PictureEdit1)
        Me.PanelControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelControl1.Location = New System.Drawing.Point(0, 0)
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(1200, 70)
        Me.PanelControl1.TabIndex = 0
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Segoe UI", 16.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseForeColor = True
        Me.LabelControl1.Location = New System.Drawing.Point(80, 20)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(150, 30)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "إدارة مراكز التكلفة"
        '
        'PictureEdit1
        '
        Me.PictureEdit1.Location = New System.Drawing.Point(20, 15)
        Me.PictureEdit1.Name = "PictureEdit1"
        Me.PictureEdit1.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit1.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        Me.PictureEdit1.Size = New System.Drawing.Size(40, 40)
        Me.PictureEdit1.TabIndex = 0
        '
        'SplitContainerControl1
        '
        Me.SplitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainerControl1.Location = New System.Drawing.Point(0, 70)
        Me.SplitContainerControl1.Name = "SplitContainerControl1"
        Me.SplitContainerControl1.Panel1.Controls.Add(Me.GroupControl1)
        Me.SplitContainerControl1.Panel1.Text = "Panel1"
        Me.SplitContainerControl1.Panel2.Controls.Add(Me.GroupControl2)
        Me.SplitContainerControl1.Panel2.Text = "Panel2"
        Me.SplitContainerControl1.Size = New System.Drawing.Size(1200, 530)
        Me.SplitContainerControl1.SplitterPosition = 600
        Me.SplitContainerControl1.TabIndex = 1
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.CostCentersGrid)
        Me.GroupControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl1.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(600, 530)
        Me.GroupControl1.TabIndex = 0
        Me.GroupControl1.Text = "قائمة مراكز التكلفة"
        '
        'CostCentersGrid
        '
        Me.CostCentersGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.CostCentersGrid.Location = New System.Drawing.Point(2, 23)
        Me.CostCentersGrid.MainView = Me.CostCentersView
        Me.CostCentersGrid.Name = "CostCentersGrid"
        Me.CostCentersGrid.Size = New System.Drawing.Size(596, 505)
        Me.CostCentersGrid.TabIndex = 0
        Me.CostCentersGrid.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.CostCentersView})
        '
        'CostCentersView
        '
        Me.CostCentersView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.ColCostCenterCode, Me.ColCostCenterName, Me.ColCostCenterType, Me.ColBudgetAmount, Me.ColIsActive})
        Me.CostCentersView.GridControl = Me.CostCentersGrid
        Me.CostCentersView.Name = "CostCentersView"
        Me.CostCentersView.OptionsView.ShowGroupPanel = False
        '
        'ColCostCenterCode
        '
        Me.ColCostCenterCode.Caption = "الكود"
        Me.ColCostCenterCode.FieldName = "cost_center_code"
        Me.ColCostCenterCode.Name = "ColCostCenterCode"
        Me.ColCostCenterCode.Visible = True
        Me.ColCostCenterCode.VisibleIndex = 0
        Me.ColCostCenterCode.Width = 80
        '
        'ColCostCenterName
        '
        Me.ColCostCenterName.Caption = "اسم مركز التكلفة"
        Me.ColCostCenterName.FieldName = "cost_center_name"
        Me.ColCostCenterName.Name = "ColCostCenterName"
        Me.ColCostCenterName.Visible = True
        Me.ColCostCenterName.VisibleIndex = 1
        Me.ColCostCenterName.Width = 200
        '
        'ColCostCenterType
        '
        Me.ColCostCenterType.Caption = "النوع"
        Me.ColCostCenterType.FieldName = "cost_center_type"
        Me.ColCostCenterType.Name = "ColCostCenterType"
        Me.ColCostCenterType.Visible = True
        Me.ColCostCenterType.VisibleIndex = 2
        Me.ColCostCenterType.Width = 100
        '
        'ColBudgetAmount
        '
        Me.ColBudgetAmount.Caption = "الميزانية"
        Me.ColBudgetAmount.FieldName = "budget_amount"
        Me.ColBudgetAmount.Name = "ColBudgetAmount"
        Me.ColBudgetAmount.Visible = True
        Me.ColBudgetAmount.VisibleIndex = 3
        Me.ColBudgetAmount.Width = 120
        '
        'ColIsActive
        '
        Me.ColIsActive.Caption = "نشط"
        Me.ColIsActive.FieldName = "is_active"
        Me.ColIsActive.Name = "ColIsActive"
        Me.ColIsActive.Visible = True
        Me.ColIsActive.VisibleIndex = 4
        Me.ColIsActive.Width = 60
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.ButtonsPanel)
        Me.GroupControl2.Controls.Add(Me.TableLayoutPanel1)
        Me.GroupControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl2.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(595, 530)
        Me.GroupControl2.TabIndex = 0
        Me.GroupControl2.Text = "بيانات مركز التكلفة"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl2, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.CostCenterCodeEdit, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl3, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.CostCenterNameEdit, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl4, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.CostCenterDescriptionEdit, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl5, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.CostCenterTypeCombo, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl6, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.ParentCostCenterCombo, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl7, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.BudgetAmountEdit, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl8, 0, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.NotesEdit, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.IsActiveCheck, 1, 7)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(2, 23)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.Padding = New System.Windows.Forms.Padding(10)
        Me.TableLayoutPanel1.RowCount = 8
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(591, 445)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl2.Appearance.Options.UseFont = True
        Me.LabelControl2.Location = New System.Drawing.Point(520, 20)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(58, 19)
        Me.LabelControl2.TabIndex = 0
        Me.LabelControl2.Text = "كود المركز:"
        '
        'CostCenterCodeEdit
        '
        Me.CostCenterCodeEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterCodeEdit.Location = New System.Drawing.Point(183, 18)
        Me.CostCenterCodeEdit.Name = "CostCenterCodeEdit"
        Me.CostCenterCodeEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.CostCenterCodeEdit.Properties.Appearance.Options.UseFont = True
        Me.CostCenterCodeEdit.Size = New System.Drawing.Size(395, 24)
        Me.CostCenterCodeEdit.TabIndex = 1
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl3.Appearance.Options.UseFont = True
        Me.LabelControl3.Location = New System.Drawing.Point(520, 60)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(58, 19)
        Me.LabelControl3.TabIndex = 2
        Me.LabelControl3.Text = "اسم المركز:"
        '
        'CostCenterNameEdit
        '
        Me.CostCenterNameEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterNameEdit.Location = New System.Drawing.Point(183, 58)
        Me.CostCenterNameEdit.Name = "CostCenterNameEdit"
        Me.CostCenterNameEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.CostCenterNameEdit.Properties.Appearance.Options.UseFont = True
        Me.CostCenterNameEdit.Size = New System.Drawing.Size(395, 24)
        Me.CostCenterNameEdit.TabIndex = 3
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl4.Appearance.Options.UseFont = True
        Me.LabelControl4.Location = New System.Drawing.Point(540, 130)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(38, 19)
        Me.LabelControl4.TabIndex = 4
        Me.LabelControl4.Text = "الوصف:"
        '
        'CostCenterDescriptionEdit
        '
        Me.CostCenterDescriptionEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterDescriptionEdit.Location = New System.Drawing.Point(183, 108)
        Me.CostCenterDescriptionEdit.Name = "CostCenterDescriptionEdit"
        Me.CostCenterDescriptionEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.CostCenterDescriptionEdit.Properties.Appearance.Options.UseFont = True
        Me.CostCenterDescriptionEdit.Size = New System.Drawing.Size(395, 64)
        Me.CostCenterDescriptionEdit.TabIndex = 5
        '
        'LabelControl5
        '
        Me.LabelControl5.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl5.Appearance.Options.UseFont = True
        Me.LabelControl5.Location = New System.Drawing.Point(530, 190)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(48, 19)
        Me.LabelControl5.TabIndex = 6
        Me.LabelControl5.Text = "نوع المركز:"
        '
        'CostCenterTypeCombo
        '
        Me.CostCenterTypeCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterTypeCombo.Location = New System.Drawing.Point(183, 188)
        Me.CostCenterTypeCombo.Name = "CostCenterTypeCombo"
        Me.CostCenterTypeCombo.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.CostCenterTypeCombo.Properties.Appearance.Options.UseFont = True
        Me.CostCenterTypeCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.CostCenterTypeCombo.Properties.Items.AddRange(New Object() {"عام", "إداري", "تشغيلي", "إنتاجي", "خدمي", "تسويقي", "تقني"})
        Me.CostCenterTypeCombo.Size = New System.Drawing.Size(395, 24)
        Me.CostCenterTypeCombo.TabIndex = 7
        '
        'LabelControl6
        '
        Me.LabelControl6.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl6.Appearance.Options.UseFont = True
        Me.LabelControl6.Location = New System.Drawing.Point(515, 230)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(63, 19)
        Me.LabelControl6.TabIndex = 8
        Me.LabelControl6.Text = "المركز الأب:"
        '
        'ParentCostCenterCombo
        '
        Me.ParentCostCenterCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ParentCostCenterCombo.Location = New System.Drawing.Point(183, 228)
        Me.ParentCostCenterCombo.Name = "ParentCostCenterCombo"
        Me.ParentCostCenterCombo.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.ParentCostCenterCombo.Properties.Appearance.Options.UseFont = True
        Me.ParentCostCenterCombo.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ParentCostCenterCombo.Properties.DisplayMember = "cost_center_name"
        Me.ParentCostCenterCombo.Properties.ValueMember = "cost_center_id"
        Me.ParentCostCenterCombo.Size = New System.Drawing.Size(395, 24)
        Me.ParentCostCenterCombo.TabIndex = 9
        '
        'LabelControl7
        '
        Me.LabelControl7.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl7.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl7.Appearance.Options.UseFont = True
        Me.LabelControl7.Location = New System.Drawing.Point(535, 270)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(43, 19)
        Me.LabelControl7.TabIndex = 10
        Me.LabelControl7.Text = "الميزانية:"
        '
        'BudgetAmountEdit
        '
        Me.BudgetAmountEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BudgetAmountEdit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.BudgetAmountEdit.Location = New System.Drawing.Point(183, 268)
        Me.BudgetAmountEdit.Name = "BudgetAmountEdit"
        Me.BudgetAmountEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.BudgetAmountEdit.Properties.Appearance.Options.UseFont = True
        Me.BudgetAmountEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.BudgetAmountEdit.Properties.DisplayFormat.FormatString = "N2"
        Me.BudgetAmountEdit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.BudgetAmountEdit.Properties.EditFormat.FormatString = "N2"
        Me.BudgetAmountEdit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.BudgetAmountEdit.Properties.MaxValue = New Decimal(New Integer() {-1, -1, -1, 0})
        Me.BudgetAmountEdit.Size = New System.Drawing.Size(395, 24)
        Me.BudgetAmountEdit.TabIndex = 11
        '
        'LabelControl8
        '
        Me.LabelControl8.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.LabelControl8.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelControl8.Appearance.Options.UseFont = True
        Me.LabelControl8.Location = New System.Drawing.Point(535, 330)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(43, 19)
        Me.LabelControl8.TabIndex = 12
        Me.LabelControl8.Text = "ملاحظات:"
        '
        'NotesEdit
        '
        Me.NotesEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.NotesEdit.Location = New System.Drawing.Point(183, 308)
        Me.NotesEdit.Name = "NotesEdit"
        Me.NotesEdit.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!)
        Me.NotesEdit.Properties.Appearance.Options.UseFont = True
        Me.NotesEdit.Size = New System.Drawing.Size(395, 64)
        Me.NotesEdit.TabIndex = 13
        '
        'IsActiveCheck
        '
        Me.IsActiveCheck.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.IsActiveCheck.Location = New System.Drawing.Point(520, 388)
        Me.IsActiveCheck.Name = "IsActiveCheck"
        Me.IsActiveCheck.Properties.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.IsActiveCheck.Properties.Appearance.Options.UseFont = True
        Me.IsActiveCheck.Properties.Caption = "مركز نشط"
        Me.IsActiveCheck.Size = New System.Drawing.Size(58, 20)
        Me.IsActiveCheck.TabIndex = 14
        '
        'ButtonsPanel
        '
        Me.ButtonsPanel.Controls.Add(Me.RefreshButton)
        Me.ButtonsPanel.Controls.Add(Me.DeleteButton)
        Me.ButtonsPanel.Controls.Add(Me.EditButton)
        Me.ButtonsPanel.Controls.Add(Me.SaveButton)
        Me.ButtonsPanel.Controls.Add(Me.NewButton)
        Me.ButtonsPanel.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ButtonsPanel.Location = New System.Drawing.Point(2, 468)
        Me.ButtonsPanel.Name = "ButtonsPanel"
        Me.ButtonsPanel.Size = New System.Drawing.Size(591, 60)
        Me.ButtonsPanel.TabIndex = 1
        '
        'NewButton
        '
        Me.NewButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.NewButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.NewButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.NewButton.Appearance.Options.UseBackColor = True
        Me.NewButton.Appearance.Options.UseFont = True
        Me.NewButton.Appearance.Options.UseForeColor = True
        Me.NewButton.Location = New System.Drawing.Point(480, 15)
        Me.NewButton.Name = "NewButton"
        Me.NewButton.Size = New System.Drawing.Size(90, 30)
        Me.NewButton.TabIndex = 0
        Me.NewButton.Text = "جديد"
        '
        'SaveButton
        '
        Me.SaveButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.SaveButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.SaveButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.SaveButton.Appearance.Options.UseBackColor = True
        Me.SaveButton.Appearance.Options.UseFont = True
        Me.SaveButton.Appearance.Options.UseForeColor = True
        Me.SaveButton.Location = New System.Drawing.Point(380, 15)
        Me.SaveButton.Name = "SaveButton"
        Me.SaveButton.Size = New System.Drawing.Size(90, 30)
        Me.SaveButton.TabIndex = 1
        Me.SaveButton.Text = "حفظ"
        '
        'EditButton
        '
        Me.EditButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.EditButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.EditButton.Appearance.ForeColor = System.Drawing.Color.Black
        Me.EditButton.Appearance.Options.UseBackColor = True
        Me.EditButton.Appearance.Options.UseFont = True
        Me.EditButton.Appearance.Options.UseForeColor = True
        Me.EditButton.Location = New System.Drawing.Point(280, 15)
        Me.EditButton.Name = "EditButton"
        Me.EditButton.Size = New System.Drawing.Size(90, 30)
        Me.EditButton.TabIndex = 2
        Me.EditButton.Text = "تعديل"
        '
        'DeleteButton
        '
        Me.DeleteButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.DeleteButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.DeleteButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.DeleteButton.Appearance.Options.UseBackColor = True
        Me.DeleteButton.Appearance.Options.UseFont = True
        Me.DeleteButton.Appearance.Options.UseForeColor = True
        Me.DeleteButton.Location = New System.Drawing.Point(180, 15)
        Me.DeleteButton.Name = "DeleteButton"
        Me.DeleteButton.Size = New System.Drawing.Size(90, 30)
        Me.DeleteButton.TabIndex = 3
        Me.DeleteButton.Text = "حذف"
        '
        'RefreshButton
        '
        Me.RefreshButton.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.RefreshButton.Appearance.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.RefreshButton.Appearance.ForeColor = System.Drawing.Color.White
        Me.RefreshButton.Appearance.Options.UseBackColor = True
        Me.RefreshButton.Appearance.Options.UseFont = True
        Me.RefreshButton.Appearance.Options.UseForeColor = True
        Me.RefreshButton.Location = New System.Drawing.Point(80, 15)
        Me.RefreshButton.Name = "RefreshButton"
        Me.RefreshButton.Size = New System.Drawing.Size(90, 30)
        Me.RefreshButton.TabIndex = 4
        Me.RefreshButton.Text = "تحديث"
        '
        'CostCentersManagement
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1200, 600)
        Me.Controls.Add(Me.SplitContainerControl1)
        Me.Controls.Add(Me.PanelControl1)
        Me.LookAndFeel.SkinName = "Office 2016 Colorful"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "CostCentersManagement"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "إدارة مراكز التكلفة"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        Me.PanelControl1.PerformLayout()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SplitContainerControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SplitContainerControl1.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.CostCentersGrid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCentersView, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        CType(Me.CostCenterCodeEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterNameEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterDescriptionEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CostCenterTypeCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ParentCostCenterCombo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BudgetAmountEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NotesEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.IsActiveCheck.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ButtonsPanel, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ButtonsPanel.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureEdit1 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents SplitContainerControl1 As DevExpress.XtraEditors.SplitContainerControl
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents CostCentersGrid As DevExpress.XtraGrid.GridControl
    Friend WithEvents CostCentersView As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents ColCostCenterCode As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColCostCenterName As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColCostCenterType As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColBudgetAmount As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ColIsActive As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents TableLayoutPanel1 As TableLayoutPanel
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CostCenterCodeEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CostCenterNameEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CostCenterDescriptionEdit As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CostCenterTypeCombo As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ParentCostCenterCombo As DevExpress.XtraEditors.LookUpEdit
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents BudgetAmountEdit As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents LabelControl8 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents NotesEdit As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents IsActiveCheck As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ButtonsPanel As DevExpress.XtraEditors.PanelControl
    Friend WithEvents RefreshButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents DeleteButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents EditButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SaveButton As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents NewButton As DevExpress.XtraEditors.SimpleButton

End Class
