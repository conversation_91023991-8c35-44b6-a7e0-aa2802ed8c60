Imports DevExpress.XtraEditors
Imports DevExpress.XtraCharts
Imports System.Data.SqlClient
Imports System.Drawing

Public Class CustomerStatisticsDashboard
    
    Private Sub CustomerStatisticsDashboard_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تطبيق نظام الألوان
            ApplyColorScheme()
            
            ' تحميل البيانات
            LoadStatistics()
            
            ' إعداد الرسم البياني
            SetupChart()
            
            ' تحميل أفضل العملاء
            LoadTopCustomers()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub ApplyColorScheme()
        ' تطبيق الألوان من نظام الألوان الموحد
        Me.BackColor = ColorScheme.BackgroundLight
        
        ' تطبيق الألوان على البانلات
        ColorScheme.ApplyButtonColors(RefreshButton, ColorScheme.PrimaryBlueHover, ColorScheme.PrimaryBlue)
    End Sub
    
    Private Sub LoadStatistics()
        Try
            ' إجمالي عدد العملاء
            LoadTotalCustomers()
            
            ' إجمالي المبيعات
            LoadTotalSales()
            
            ' إجمالي الأرصدة
            LoadTotalBalance()
            
            ' العملاء النشطين
            LoadActiveCustomers()
            
        Catch ex As Exception
            Throw New Exception("خطأ في تحميل الإحصائيات: " & ex.Message)
        End Try
    End Sub
    
    Private Sub LoadTotalCustomers()
        Try
            Dim sql = "SELECT COUNT(*) FROM customer"
            Dim cmd As New SqlCommand(sql, sqlconn)
            Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
            
            TotalCustomersValue.Text = count.ToString("N0")
            
        Catch ex As Exception
            TotalCustomersValue.Text = "خطأ"
        End Try
    End Sub
    
    Private Sub LoadTotalSales()
        Try
            Dim sql = "SELECT ISNULL(SUM(total_invoice), 0) FROM invoice_add"
            Dim cmd As New SqlCommand(sql, sqlconn)
            Dim total As Decimal = Convert.ToDecimal(cmd.ExecuteScalar())
            
            TotalSalesValue.Text = total.ToString("N0")
            
        Catch ex As Exception
            TotalSalesValue.Text = "خطأ"
        End Try
    End Sub
    
    Private Sub LoadTotalBalance()
        Try
            Dim sql = "SELECT ISNULL(SUM(Accounts_balace), 0) FROM customer"
            Dim cmd As New SqlCommand(sql, sqlconn)
            Dim total As Decimal = Convert.ToDecimal(cmd.ExecuteScalar())
            
            TotalBalanceValue.Text = total.ToString("N0")
            
            ' تغيير اللون حسب الرصيد
            If total < 0 Then
                TotalBalanceValue.Appearance.ForeColor = Color.Red
            ElseIf total > 0 Then
                TotalBalanceValue.Appearance.ForeColor = Color.Green
            Else
                TotalBalanceValue.Appearance.ForeColor = Color.Black
            End If
            
        Catch ex As Exception
            TotalBalanceValue.Text = "خطأ"
        End Try
    End Sub
    
    Private Sub LoadActiveCustomers()
        Try
            Dim sql = "SELECT COUNT(*) FROM customer WHERE cus_active = 1"
            Dim cmd As New SqlCommand(sql, sqlconn)
            Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
            
            ActiveCustomersValue.Text = count.ToString("N0")
            
        Catch ex As Exception
            ActiveCustomersValue.Text = "خطأ"
        End Try
    End Sub
    
    Private Sub SetupChart()
        Try
            ' إعداد الرسم البياني لعرض المبيعات الشهرية
            SalesChart.Series.Clear()

            ' إنشاء سلسلة بيانات للمبيعات
            Dim salesSeries As New Series("المبيعات", ViewType.Column)
            salesSeries.View.Color = ColorScheme.PrimaryBlue

            ' إنشاء سلسلة بيانات للأرباح
            Dim profitSeries As New Series("الأرباح", ViewType.Line)
            profitSeries.View.Color = ColorScheme.SuccessGreen

            ' تحميل بيانات المبيعات الشهرية
            LoadMonthlySalesData(salesSeries, profitSeries)

            ' إضافة السلاسل إلى الرسم البياني
            SalesChart.Series.Add(salesSeries)
            SalesChart.Series.Add(profitSeries)

            ' تخصيص مظهر الرسم البياني
            SalesChart.Titles.Clear()
            Dim title As New ChartTitle() With {
                .Text = "المبيعات والأرباح الشهرية",
                .Font = ColorScheme.GetStandardFont(14, True)
            }
            SalesChart.Titles.Add(title)

            ' إعداد المحاور
            SetupChartAxes()

            ' إعداد الألوان والتنسيق
            SetupChartAppearance()

        Catch ex As Exception
            ' في حالة الخطأ، إخفاء الرسم البياني
            ChartPanel.Visible = False
        End Try
    End Sub

    Private Sub SetupChartAxes()
        ' إعداد محور X (الأشهر)
        If TypeOf SalesChart.Diagram Is XYDiagram Then
            Dim diagram As XYDiagram = CType(SalesChart.Diagram, XYDiagram)
            diagram.AxisX.Title.Text = "الأشهر"
            diagram.AxisX.Title.Visibility = DevExpress.Utils.DefaultBoolean.True
            diagram.AxisY.Title.Text = "المبلغ"
            diagram.AxisY.Title.Visibility = DevExpress.Utils.DefaultBoolean.True

            ' تنسيق الأرقام
            diagram.AxisY.NumericScaleOptions.ScaleMode = ScaleMode.Automatic
            diagram.AxisY.Label.TextPattern = "{V:N0}"
        End If
    End Sub

    Private Sub SetupChartAppearance()
        ' تخصيص مظهر الرسم البياني
        SalesChart.BackColor = Color.White
        SalesChart.BorderOptions.Visibility = DevExpress.Utils.DefaultBoolean.False

        ' إعداد وسيلة الإيضاح
        SalesChart.Legend.Visibility = DevExpress.Utils.DefaultBoolean.True
        SalesChart.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.Center
        SalesChart.Legend.AlignmentVertical = LegendAlignmentVertical.Bottom
    End Sub
    
    Private Sub LoadMonthlySalesData(salesSeries As Series, profitSeries As Series)
        Try
            Dim sql = "SELECT MONTH(invoice_date) as Month, " &
                     "SUM(total_invoice) as TotalSales, " &
                     "SUM(earn_invoice) as TotalProfit " &
                     "FROM invoice_add " &
                     "WHERE YEAR(invoice_date) = YEAR(GETDATE()) " &
                     "GROUP BY MONTH(invoice_date) " &
                     "ORDER BY MONTH(invoice_date)"

            Dim cmd As New SqlCommand(sql, sqlconn)
            Dim reader As SqlDataReader = cmd.ExecuteReader()

            While reader.Read()
                Dim monthName = GetMonthName(reader.GetInt32("Month"))
                Dim totalSales = If(IsDBNull(reader("TotalSales")), 0, reader.GetDecimal("TotalSales"))
                Dim totalProfit = If(IsDBNull(reader("TotalProfit")), 0, reader.GetDecimal("TotalProfit"))

                salesSeries.Points.Add(New SeriesPoint(monthName, totalSales))
                profitSeries.Points.Add(New SeriesPoint(monthName, totalProfit))
            End While

            reader.Close()

            ' إذا لم توجد بيانات، إضافة بيانات فارغة للأشهر الحالية
            If salesSeries.Points.Count = 0 Then
                AddEmptyMonthsData(salesSeries, profitSeries)
            End If

        Catch ex As Exception
            ' في حالة الخطأ، إضافة بيانات وهمية
            AddEmptyMonthsData(salesSeries, profitSeries)
        End Try
    End Sub

    Private Sub AddEmptyMonthsData(salesSeries As Series, profitSeries As Series)
        ' إضافة بيانات فارغة للأشهر الحالية
        Dim currentMonth = DateTime.Now.Month
        For i = 1 To currentMonth
            Dim monthName = GetMonthName(i)
            salesSeries.Points.Add(New SeriesPoint(monthName, 0))
            profitSeries.Points.Add(New SeriesPoint(monthName, 0))
        Next
    End Sub
    
    Private Function GetMonthName(monthNumber As Integer) As String
        Dim months() As String = {"", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"}
        
        If monthNumber >= 1 And monthNumber <= 12 Then
            Return months(monthNumber)
        Else
            Return "غير محدد"
        End If
    End Function
    
    Private Sub LoadTopCustomers()
        Try
            Dim sql = "SELECT TOP 10 c.cusname as CustomerName, " &
                     "ISNULL(SUM(i.total_invoice), 0) as TotalSales, " &
                     "c.Accounts_balace as Balance " &
                     "FROM customer c " &
                     "LEFT JOIN invoice_add i ON c.cusname = i.Accounts_name " &
                     "GROUP BY c.cusname, c.Accounts_balace " &
                     "ORDER BY TotalSales DESC"
            
            Dim adapter As New SqlDataAdapter(sql, sqlconn)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            
            TopCustomersGrid.DataSource = dataTable
            
            ' تنسيق الأعمدة
            TopCustomersView.Columns("TotalSales").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            TopCustomersView.Columns("TotalSales").DisplayFormat.FormatString = "N0"
            
            TopCustomersView.Columns("Balance").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            TopCustomersView.Columns("Balance").DisplayFormat.FormatString = "N0"
            
        Catch ex As Exception
            ' في حالة الخطأ، إخفاء الجدول
            TopCustomersPanel.Visible = False
        End Try
    End Sub
    
    Private Sub RefreshButton_Click(sender As Object, e As EventArgs) Handles RefreshButton.Click
        Try
            ' تغيير نص الزر أثناء التحديث
            RefreshButton.Text = "جاري التحديث..."
            RefreshButton.Enabled = False

            ' إعادة تحميل جميع البيانات
            LoadStatistics()
            SetupChart()
            LoadTopCustomers()

            ' إعادة تعيين نص الزر
            RefreshButton.Text = "تحديث"
            RefreshButton.Enabled = True

            XtraMessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            ' إعادة تعيين نص الزر في حالة الخطأ
            RefreshButton.Text = "تحديث"
            RefreshButton.Enabled = True

            XtraMessageBox.Show("خطأ في تحديث البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' إضافة وظائف إضافية للإحصائيات المتقدمة
    ''' </summary>
    Public Sub ShowCustomerDetails(customerName As String)
        Try
            ' فتح شاشة تفاصيل العميل
            Dim detailForm As New customer_Detailed()
            detailForm.Text = "تفاصيل العميل: " & customerName
            detailForm.MdiParent = Me.MdiParent
            detailForm.Show()

            ' تعيين اسم العميل والبحث
            detailForm.customername.Text = customerName
            detailForm.search()

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في عرض تفاصيل العميل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub TopCustomersView_DoubleClick(sender As Object, e As EventArgs) Handles TopCustomersView.DoubleClick
        Try
            ' عند النقر المزدوج على عميل، عرض تفاصيله
            If TopCustomersView.FocusedRowHandle >= 0 Then
                Dim customerName = TopCustomersView.GetFocusedRowCellValue("CustomerName").ToString()
                ShowCustomerDetails(customerName)
            End If

        Catch ex As Exception
            ' تجاهل الأخطاء في هذه الحالة
        End Try
    End Sub

    ''' <summary>
    ''' تصدير البيانات إلى Excel
    ''' </summary>
    Public Sub ExportToExcel()
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.Title = "حفظ إحصائيات العملاء"
            saveDialog.FileName = "إحصائيات_العملاء_" & DateTime.Now.ToString("yyyy-MM-dd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                TopCustomersGrid.ExportToXlsx(saveDialog.FileName)
                XtraMessageBox.Show("تم تصدير البيانات بنجاح", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تصدير البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

End Class
    
End Class
