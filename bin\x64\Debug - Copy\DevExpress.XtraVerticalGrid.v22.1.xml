<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <assembly>
    <name>DevExpress.XtraVerticalGrid.v22.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraVerticalGrid">
      <summary>
        <para>Contains classes implementing the general functionality of the vertical grid controls that come with the XtraVerticalGrid Suite.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.BaseOptionsView">
      <summary>
        <para>Provides view options for vertical grid controls.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.BaseOptionsView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.BaseOptionsView"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.AllowGlyphSkinning">
      <summary>
        <para>Gets or sets whether row and band icons are painted using the foreground color of these rows and bands.</para>
      </summary>
      <value>true if row and band icons are painted using the foreground color of these rows and bands; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.AllowHtmlText">
      <summary>
        <para>Gets or sets whether row captions (and record headers in the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>) can be formatted using HTML tags.</para>
      </summary>
      <value>true if row captions and record headers can be formatted using HTML tags; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.AllowReadOnlyRowAppearance">
      <summary>
        <para>Gets or sets whether to paint read-only rows according to the <see cref="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRow"/> and <see cref="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRecordValue"/> appearance settings.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/> if read-only rows are painted according to the <see cref="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRow"/> and <see cref="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRecordValue"/> appearance settings; otherwise, <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> or <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.BaseOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.AutoScaleBands">
      <summary>
        <para>Gets or sets whether bands are stretched to occupy the entire control.</para>
      </summary>
      <value>true if bands are stretched to occupy the entire control; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.CategoryLevelIndentStyle">
      <summary>
        <para>Gets or sets how to draw indents for categories and row headers.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.CategoryLevelIndentStyle enumeration value that specifies the style used to indent row headers. The default is Vertical.</value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.BaseOptionsView.DefaultLevelIndent">
      <summary>
        <para>The default value for the <see cref="P:DevExpress.XtraVerticalGrid.BaseOptionsView.LevelIndent"/> property.</para>
        <para>The DefaultLevelIndent field is set to -1.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.DisableSkinBorder">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:System.Boolean"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.FixedLineWidth">
      <summary>
        <para>Gets or sets the width of fixed row dividers.</para>
      </summary>
      <value>An integer value that specifies the width of horizontal lines, in pixels, that separate anchored rows from other rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.FixRowHeaderPanelWidth">
      <summary>
        <para>Gets or sets whether the width of row headers remain constant when the control’s size or layout style is changed.</para>
      </summary>
      <value>true if the width of row headers remain constant  when the control’s size or layout style is changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.LevelIndent">
      <summary>
        <para>Gets or sets the horizontal indent for child rows.</para>
      </summary>
      <value>An integer value that specifies the horizontal indent for child rows. -1 if the indent is automatically calculated.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.MaxRowAutoHeight">
      <summary>
        <para>Gets or sets the maximum height for rows, when the row auto-height feature is enabled.</para>
      </summary>
      <value>The maximum row height, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.MinRowAutoHeight">
      <summary>
        <para>Gets or sets the minimum height for rows, when the row auto-height feature is enabled.</para>
      </summary>
      <value>The minimum row height, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowButtons">
      <summary>
        <para>Gets or sets whether collapse/expand buttons are displayed in parent rows. This property is obsolete. Use <see cref="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowCollapseButtons"/> instead.</para>
      </summary>
      <value>true if collapse/expand buttons are displayed in parent rows; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowCaption">
      <summary>
        <para>Gets or sets whether the control displays a caption (Caption) at the top.</para>
      </summary>
      <value>true if the control displays a caption; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowCollapseButtons">
      <summary>
        <para>Gets or sets whether collapse/expand buttons are displayed in parent rows.</para>
      </summary>
      <value>true if collapse/expand buttons are displayed in parent rows; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowEmptyRowImage">
      <summary>
        <para>Gets or sets whether the captions of row headers are shifted right regardless of whether an image is shown within the row header.</para>
      </summary>
      <value>true if the captions of row headers are shifted right regardless of whether an image is shown within the row header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowFocusedFrame">
      <summary>
        <para>Gets or sets whether a focus frame is displayed around the focused cell.</para>
      </summary>
      <value>true, to display a focus frame around the focused cell; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowHorzLines">
      <summary>
        <para>Gets or sets whether horizontal lines are displayed.</para>
      </summary>
      <value>true, to display horizontal lines; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowRootCategories">
      <summary>
        <para>Gets or sets whether top-level category rows are visible.</para>
      </summary>
      <value>true if root category rows are visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowRootLevelIndent">
      <summary>
        <para>Gets or sets whether root-level rows are indented.</para>
      </summary>
      <value>true if rows at the root level are indented; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowRows">
      <summary>
        <para>Gets or sets whether row headers are displayed.</para>
      </summary>
      <value>true, to display row headers; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowVertLines">
      <summary>
        <para>Gets or sets whether vertical lines are visible.</para>
      </summary>
      <value>true, to display vertical lines; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.EditorAlignment">
      <summary>
        <para>Enumerates values that specify how an editor is aligned in a grid row.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorAlignment.Center">
      <summary>
        <para>Aligns the editor at the row’s center.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorAlignment.Default">
      <summary>
        <para>The alignment is not specified explicitly. Uses the <see cref="F:DevExpress.XtraVerticalGrid.EditorAlignment.Center"/> value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorAlignment.Far">
      <summary>
        <para>Aligns the editor at the row’s far edge.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorAlignment.Near">
      <summary>
        <para>Aligns the editor at the row’s near edge.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorAlignment.Stretch">
      <summary>
        <para>The editor is stretched up to the caption.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.EditorPosition">
      <summary>
        <para>Enumerates values that specify the editor position relative to the property caption.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorPosition.AfterCaption">
      <summary>
        <para>Displays the editor after the property caption.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorPosition.BeforeCaption">
      <summary>
        <para>Displays the editor before the property caption.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.EditorPosition.Default">
      <summary>
        <para>The position is not specified explicitly. Uses the <see cref="F:DevExpress.XtraVerticalGrid.EditorPosition.AfterCaption"/> value for all editors except check editors.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraVerticalGrid.Events">
      <summary>
        <para>Contains classes providing data for events raised by the vertical grid controls included in the XtraVerticalGrid Suite.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs">
      <summary>
        <para>Serves as the base for classes providing data for all category row handling events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.CategoryRow)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs"/> object.</para>
      </summary>
      <param name="category">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object representing the processed category row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs.Category"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs.Category">
      <summary>
        <para>Gets the processed category row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object representing the processed category row.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CellValueChanging"/> and <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CellValueChanged"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32,System.Int32,System.Object)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the row where a cell resides. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="recordIndex">An integer value representing the index for the record where the cell resides. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.RecordIndex"/> property.</param>
      <param name="cellIndex">An integer value representing the cell’s index. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.CellIndex"/> property.</param>
      <param name="val">An object representing the new cell value. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventArgs.Value"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventArgs.Value">
      <summary>
        <para>Gets the current cell’s value.</para>
      </summary>
      <value>An object representing the current cell’s value.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CellValueChanged"/> and <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CellValueChanging"/> events.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CellValueChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs">
      <summary>
        <para>Serves as a base for classes providing data for all events used to custom paint the elements of the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> descendants.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs"/> class.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.Utils.Drawing.ObjectPainter)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element’s appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="painter">An DevExpress.Utils.Drawing.ObjectPainter object that provides facilities for painting an element using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Painter"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.Utils.Drawing.ObjectPainter,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element’s appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="painter">A DevExpress.Utils.Drawing.ObjectPainter object that provides facilities for painting an element using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Painter"/> property.</param>
      <param name="isRightToLeft">A Boolean value that specifies whether the control’s elements are aligned to support locales using right-to-left fonts. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.IsRightToLeft"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance">
      <summary>
        <para>Gets the painted element’s appearance settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds">
      <summary>
        <para>Gets a value specifying limits for the drawing area.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache">
      <summary>
        <para>Gets an object which specifies the storage for the most  used pens, fonts and brushes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.DefaultDraw">
      <summary>
        <para>Performs default painting of an element.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Graphics">
      <summary>
        <para>Gets an object used to paint.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Graphics"/> object providing painting facilities.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Handled">
      <summary>
        <para>Gets or sets a value specifying whether an event was handled, if handled, default actions are not required.</para>
      </summary>
      <value>true if default actions are not required; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.IsRightToLeft">
      <summary>
        <para>Gets a value indicating whether the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>‘s elements are aligned to support locales using right-to-left fonts.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>‘s elements are aligned to support locales using right-to-left fonts; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.ObjectArgs">
      <summary>
        <para>Gets an object containing information about the painted element.</para>
      </summary>
      <value>An ObjectInfoArgs object providing information about the painted element.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Painter">
      <summary>
        <para>Gets the painter object that provides the default element’s painting mechanism.</para>
      </summary>
      <value>An ObjectPainter descendant providing the default painting mechanism for the painted element.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs">
      <summary>
        <para>Serves as a base for classes providing data for all events used to custom paint row elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs"/> class.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row whose element is to be painted. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.Row"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most frequently used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="properties">Properties specific to the row being custom painted. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.Properties"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.Properties">
      <summary>
        <para>Provides properties specific to the row being custom painted.</para>
      </summary>
      <value>Properties specific to the row being custom painted.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.Row">
      <summary>
        <para>Gets the row whose element is to be drawn.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the row whose element is to be drawn.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowHeaderCell"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs"/> class.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.AllowGlyphSkinning">
      <summary>
        <para>Gets or sets whether this row header cell’s icon can be painted with a foreground color.</para>
      </summary>
      <value>true if this row header cell’s icon can be painted with a foreground color; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.AllowHtmlText">
      <summary>
        <para>Gets or sets whether the row caption can be formatted using HTML tags.</para>
      </summary>
      <value>true if the row caption is formatted using HTML tags; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.Caption">
      <summary>
        <para>Gets or sets the text displayed within the painted row header cell.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value representing the painted row header cell’s text.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.CaptionRect">
      <summary>
        <para>Gets the bounding rectangle of the row header cell’s caption.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure representing the caption’s bounding rectangle.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.CellIndex">
      <summary>
        <para>Gets the index of the painted row header cell.</para>
      </summary>
      <value>An integer value representing the painted row header cell’s index.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.Focused">
      <summary>
        <para>Gets a value indicating whether the painted row header cell has focus.</para>
      </summary>
      <value>true if the painted row header cell has focus; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.FocusRect">
      <summary>
        <para>Gets the bounds of the focus rectangle around the row header cell.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the boundaries of the focus rectangle around the row header cell.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.HotTrack">
      <summary>
        <para>Gets a value indicating whether the mouse pointer passes over the painted row header cell.</para>
      </summary>
      <value>true if the mouse pointer passes over the painted row header cell; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.ImageIndex">
      <summary>
        <para>Gets or sets the image displayed within the painted row header cell.</para>
      </summary>
      <value>A zero-based integer specifying the image displayed within the painted row header cell, -1 if no image is displayed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.ImageRect">
      <summary>
        <para>Gets the bounding rectangle within the painted row header cell where the image is to be drawn.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure specifying the image’s bounding rectangle.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.Pressed">
      <summary>
        <para>Gets a value indicating whether the painted row header cell is pressed.</para>
      </summary>
      <value>true if the painted row header cell is pressed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs.Selected">
      <summary>
        <para>Gets whether or not the current row is selected.</para>
      </summary>
      <value>true if the current row is selected; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowHeaderCell"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderCellEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowHeaderIndent"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventArgs"/> class.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventArgs.CategoryIndents">
      <summary>
        <para>Gets a collection of category indents to be painted.</para>
      </summary>
      <value>An Indents object representing the collection of painted category indents.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventArgs.RowIndents">
      <summary>
        <para>Gets a collection of row indents that are going to be painted.</para>
      </summary>
      <value>An Indents object representing the collection of painted row indents.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowHeaderIndent"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowHeaderIndentEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowValueCell"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,DevExpress.XtraVerticalGrid.ViewInfo.RowValueInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which stores pens, fonts and brushes. This object is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="rowValueInfo"></param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Int32,System.Int32,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most frequently used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="properties">Properties specific to the row being custom painted. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.Properties"/> property.</param>
      <param name="recordIndex">An integer value identifying the record that corresponds to the painted cell. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.RecordIndex"/> property.</param>
      <param name="cellIndex">A zero-based integer representing the painted cell’s index. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.CellIndex"/> property.</param>
      <param name="cellValue">An object representing the painted cell’s value. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.CellValue"/> property.</param>
      <param name="cellText">A System.String value representing the painted cell’s displayed text. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.CellText"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.CellIndex">
      <summary>
        <para>Gets the index of the painted row data cell.</para>
      </summary>
      <value>A zero-based integer representing the painted cell’s index.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.CellText">
      <summary>
        <para>Gets or sets the text displayed in the painted cell.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value representing the painted cell’s displayed text.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.CellValue">
      <summary>
        <para>Gets the painted cell’s value.</para>
      </summary>
      <value>An object representing the painted cell’s value.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.Enabled">
      <summary>
        <para>Gets a value indicating whether end-users can modify cell values within the painted row.</para>
      </summary>
      <value>true if the processed row is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.Item">
      <summary>
        <para>Returns the <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> for which the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowValueCell"/> event was raised.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> for which the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowValueCell"/> event was raised.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.RecordIndex">
      <summary>
        <para>Gets the index of the painted cell’s record.</para>
      </summary>
      <value>An integer value identifying the record that corresponds to the painted cell.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs.RowValueInfo">
      <summary>
        <para>Gets information on the row value view.</para>
      </summary>
      <value>A RowValueInfo object that specifies information on the row value view.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowValueCell"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawRowValueCellEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawSeparator"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraVerticalGrid.Rows.MultiEditorRow)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs"/> class.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> object which represents the multi-editor row whose separator is to be painted. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.Row"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.IsHeaderSeparator">
      <summary>
        <para>Gets a value indicating whether the painted cell separator is contained within the row header.</para>
      </summary>
      <value>true if the painted cell separator is contained within the row header; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.Row">
      <summary>
        <para>Gets the painted cell separator’s owning row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> object representing the row containing the painted cell separator.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.SeparatorIndex">
      <summary>
        <para>Gets the painted cell separator’s index.</para>
      </summary>
      <value>A zero-based integer representing the painted cell separator’s index.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.SeparatorKind">
      <summary>
        <para>Gets the painted cell separator’s type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.SeparatorKind"/> enumerator value specifying the painted cell separator’s type.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs.SeparatorString">
      <summary>
        <para>Gets or sets the painted cell separator’s text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value representing the painted cell separator’s text.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawSeparator"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawSeparatorEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawTreeButton"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.TreeButtonType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs"/> class.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Cache"/> property.</param>
      <param name="r">A <see cref="T:System.Drawing.Rectangle"/> structure representing the painted element’s bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Bounds"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawEventArgs.Appearance"/> property.</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row whose element is to be painted. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawRowEventArgs.Row"/> property.</param>
      <param name="treeButtonType">A <see cref="T:DevExpress.XtraVerticalGrid.TreeButtonType"/> enumeration member which specifies the painted button’s type. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs.TreeButtonType"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs.Expanded">
      <summary>
        <para>Gets a value indicating whether the painted button’s corresponding row is expanded.</para>
      </summary>
      <value>true if the painted button’s corresponding row is expanded; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs.TreeButtonType">
      <summary>
        <para>Gets the painted button’s type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.TreeButtonType"/> member specifying the painted button’s type.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawTreeButton"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomDrawTreeButtonEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormCreatingCategoryEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormCreatingCategory"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomizationFormCreatingCategoryEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.CategoryRow)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormCreatingCategoryEventArgs"/> object.</para>
      </summary>
      <param name="category">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object that represents the created category row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs.Category"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomizationFormCreatingCategoryEventArgs.CanCreate">
      <summary>
        <para>Gets or sets a value specifying whether the creation of a new category row with the specified caption is allowed within the Customization Form.</para>
      </summary>
      <value>true if the category row being created can be added to the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows"/> collection; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormCreatingCategoryEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormCreatingCategory"/> event.</para>
      </summary>
      <param name="sender">The event source (typically the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> control).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormCreatingCategoryEventArgs"/> class instance containing event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormDeletingCategoryEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormDeletingCategory"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomizationFormDeletingCategoryEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.CategoryRow)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormDeletingCategoryEventArgs"/> object.</para>
      </summary>
      <param name="category">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object representing the deleted category row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.CategoryEventArgs.Category"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomizationFormDeletingCategoryEventArgs.CanDelete">
      <summary>
        <para>Gets or sets a value specifying whether a row is allowed to be deleted.</para>
      </summary>
      <value>true if the specified category row can be deleted from the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRows"/> collection; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormDeletingCategoryEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormDeletingCategory"/> event.</para>
      </summary>
      <param name="sender">The event source (typically the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> control).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomizationFormDeletingCategoryEventArgs"/> class instance containing event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.PropertyGridControl.CustomPropertyDescriptors"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs.#ctor(System.Object,System.ComponentModel.ITypeDescriptorContext,System.Attribute[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="source">A source object.</param>
      <param name="context">An ITypeDescriptorContext object.</param>
      <param name="attributes">An array of Attribute objects.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs.Attributes">
      <summary>
        <para>Gets an array of the default browsable attributes used by the Property Grid Control to retrieve properties from the selected object(s).</para>
      </summary>
      <value>An array of Attribute objects.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs.Context">
      <summary>
        <para>Gets contextual information on a property.</para>
      </summary>
      <value>An ITypeDescriptorContext object that provides contextual information on a property.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs.Properties">
      <summary>
        <para>Gets or sets the collection of PropertyDescriptor objects that identify properties to be displayed in the control.</para>
      </summary>
      <value>A PropertyDescriptorCollection object that specifies the collection of PropertyDescriptor objects.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs.Source">
      <summary>
        <para>Gets the object whose properties must be retrieved via the event.</para>
      </summary>
      <value>An object whose properties must be retrieved via the event.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.PropertyGridControl.CustomPropertyDescriptors"/> event.</para>
      </summary>
      <param name="sender">The <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> control that fires the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomPropertyDescriptorsEventArgs"/> object that provides data for the event.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.CustomRecordDisplayTextEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControl.CustomRecordDisplayText"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.CustomRecordDisplayTextEventArgs.#ctor(System.Int32,DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.CustomRecordDisplayTextEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="record">The record index.</param>
      <param name="properties">The row’s properties.</param>
      <param name="value">The cell value.</param>
      <param name="displayText">Text displayed in the cell.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomRecordDisplayTextEventArgs.DisplayText">
      <summary>
        <para>Gets or sets the text displayed in the processed cell.</para>
      </summary>
      <value>A string value that specifies the text displayed in the precessed cell.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomRecordDisplayTextEventArgs.Properties">
      <summary>
        <para>Gets the processed row’s properties.</para>
      </summary>
      <value>An object that contains the row’s properties.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.CustomRecordDisplayTextEventArgs.Value">
      <summary>
        <para>Gets the processed cell’s value.</para>
      </summary>
      <value>An object that specifies the processed cell’s value.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs">
      <summary>
        <para>Provides common data for native drag-specific events in the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> descendants.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Drawing.Point,DevExpress.XtraVerticalGrid.RowDragEffect)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the row being dragged. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="pt">A <see cref="T:System.Drawing.Point"/> structure defining the screen coordinates of a point under the mouse cursor. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.ScreenLocation"/> property.</param>
      <param name="effect">A <see cref="T:DevExpress.XtraVerticalGrid.RowDragEffect"/> enumeration value specifying available dragging effects. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.Effect"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.Effect">
      <summary>
        <para>Gets or sets an effect available for the processed drag and drop operation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.RowDragEffect"/> enumeration value representing the pointer feedback indicating what happens if the mouse is released at any given moment.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.ScreenLocation">
      <summary>
        <para>Gets a point representing the current mouse cursor position.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure representing the screen coordinates of the current mouse cursor position.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.EndDragRowEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.EndDragRow"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.EndDragRowEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Drawing.Point,DevExpress.XtraVerticalGrid.RowDragEffect,System.Boolean)">
      <summary>
        <para>Creates an new <see cref="T:DevExpress.XtraVerticalGrid.Events.EndDragRowEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the row being dropped. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="pt">A <see cref="T:System.Drawing.Point"/> structure defining the screen coordinates for a point under the mouse cursor. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.ScreenLocation"/> property.</param>
      <param name="effect">A <see cref="T:DevExpress.XtraVerticalGrid.RowDragEffect"/> enumeration value specifying the effects available for the processed drag and drop operation. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.Effect"/> property.</param>
      <param name="canceled">true if the processed drag and drop operation was canceled by the end-user; otherwise false. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.EndDragRowEventArgs.Canceled"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.EndDragRowEventArgs.Canceled">
      <summary>
        <para>Gets a value indicating whether the processed drag operation is canceled.</para>
      </summary>
      <value>true if the drag operation was canceled by the end-user; otherwise false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.EndDragRowEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.EndDragRow"/> event.</para>
      </summary>
      <param name="sender">The event source (typically the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> control).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.EndDragRowEventArgs"/> class instance containing event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRowChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the currently focused row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="oldRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the previously focused row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventArgs.OldRow"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventArgs.OldRow">
      <summary>
        <para>Gets the previously focused row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the previously focused row. null (Nothing in Visual Basic) if no row previously had focus.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRowChanged"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.FocusedRowChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RecordCellStyle"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32,System.Int32,DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventArgs"/> class.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the row that contains the cell. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> value.</param>
      <param name="recordIndex">A zero-based integer specifying the index of the record containing the processed cell. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.RecordIndex"/> property.</param>
      <param name="cellIndex">A zero-based integer specifying the processed cell’s index. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.CellIndex"/> property.</param>
      <param name="repositoryItem">A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant representing the currently assigned editor. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventArgs.RepositoryItem"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventArgs.RepositoryItem">
      <summary>
        <para>Gets or sets the editor assigned to the processed cell.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant representing the currently assigned editor.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RecordCellStyle"/> event.</para>
      </summary>
      <param name="sender">The event’s source. Identifies the vertical grid that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellEditEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RecordCellStyle"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32,System.Int32,DevExpress.Utils.AppearanceObject)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventArgs"/> class.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the processed row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="recordIndex">An integer value specifying the index of the record which contains the processed cell. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.RecordIndex"/> property.</param>
      <param name="cellIndex">An integer value specifying the cell’s index. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.CellIndex"/> property.</param>
      <param name="appearance">A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the cell. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventArgs.Appearance"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventArgs.Appearance">
      <summary>
        <para>Gets the appearance settings used to paint the cell currently being processed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the cell.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RecordCellStyle"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.GetCustomRowCellStyleEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordChanged"/> and <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordCellChanged"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs.#ctor(System.Int32,System.Int32)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs"/> object.</para>
      </summary>
      <param name="newIndex">A zero-based integer representing the index of the currently focused element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs.NewIndex"/> property.</param>
      <param name="oldIndex">A zero-based integer representing the index of the previously focused element. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs.OldIndex"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs.NewIndex">
      <summary>
        <para>Gets the index of the currently focused element.</para>
      </summary>
      <value>A zero-based integer representing the currently focused element’s index.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs.OldIndex">
      <summary>
        <para>Gets the index of the previously focused element.</para>
      </summary>
      <value>A zero-based integer representing the previously focused element’s index.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.IndexChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordChanged"/> and <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordCellChanged"/> events.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.IndexChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraVerticalGrid.VGridPopupMenu,DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs"/> class.</para>
      </summary>
      <param name="menu">A VGridPopupMenu object that represents the menu to be displayed. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.Menu"/> property.</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object that represents the row where the menu should to be displayed. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.Row"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraVerticalGrid.VGridPopupMenu,DevExpress.XtraVerticalGrid.ViewInfo.RecordHeaderInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="menu"></param>
      <param name="headerInfo"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.Menu">
      <summary>
        <para>Gets or sets the control’s popup menu that will be shown.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.VGridPopupMenu object that represents the menu to be displayed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.RecordIndex">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.RecordObject">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs.Row">
      <summary>
        <para>Gets the row where the popup menu will be displayed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object that specifies the row where the popup menu will be displayed.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.PopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.PopupMenuShowingEventArgs"/> object which contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.ProcessDragRowEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.ProcessDragRow"/> event.</para>
      </summary>
      <param name="sender">The event source (typically the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> control).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.ProcessDragRowEventHandler"/> class instance containing event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs">
      <summary>
        <para>Serves as the base for classes providing data for cell handling events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32,System.Int32)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the processed cell’s owning row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="recordIndex">A zero-based integer representing the record containing the processed cell. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.RecordIndex"/> property.</param>
      <param name="cellIndex">A zero-based integer representing the processed cell’s index. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.CellIndex"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.CellIndex">
      <summary>
        <para>Gets the processed cell’s index.</para>
      </summary>
      <value>An integer value representing the processed cell’s index.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowCellEventArgs.RecordIndex">
      <summary>
        <para>Gets the index of the record containing the processed cell.</para>
      </summary>
      <value>An integer value representing the index of the record containing the processed cell.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.RowProperties,DevExpress.XtraVerticalGrid.RowChangeTypeEnum)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the processed row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="prop">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object containing the processed row’s properties settings. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.Properties"/> property.</param>
      <param name="changeType">A <see cref="T:DevExpress.XtraVerticalGrid.RowChangeTypeEnum"/> enumeration member specifying the way the row has been changed. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.ChangeType"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.ChangeType">
      <summary>
        <para>Gets the way in which the row has changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.RowChangeTypeEnum"/> enumeration member specifying the way the row has changed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.Properties">
      <summary>
        <para>Gets the processed row’s properties.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object containing property settings for the processed row.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.RowChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowChanged"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.RowProperties,DevExpress.XtraVerticalGrid.RowChangeTypeEnum,System.Object)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the processed row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="prop">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object containing the processed row’s properties settings. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.Properties"/> property.</param>
      <param name="changeType">A <see cref="T:DevExpress.XtraVerticalGrid.RowChangeTypeEnum"/> enumeration member specifying the way the row is being changed. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowChangedEventArgs.ChangeType"/> property.</param>
      <param name="propertyValue">An object representing the value of the modified row property. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs.PropertyValue"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs.CanChange">
      <summary>
        <para>Gets or sets a value specifying whether the row can be changed.</para>
      </summary>
      <value>true if the row can be changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs.PropertyValue">
      <summary>
        <para>Gets or sets the current value of the changed row property.</para>
      </summary>
      <value>An object representing the current value of the changed row property.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.RowChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowChanging"/> event.</para>
      </summary>
      <param name="sender">The event sender (the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.RowChangingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.RowEventArgs">
      <summary>
        <para>Serves as the base for classes providing data for all row handling events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.RowEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.RowEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the processed row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row">
      <summary>
        <para>Gets the processed row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the processed row.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.StartDragRowEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.StartDragRow"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.StartDragRowEventArgs.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Drawing.Point,DevExpress.XtraVerticalGrid.RowDragEffect,DevExpress.XtraVerticalGrid.RowDragSource)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Events.StartDragRowEventArgs"/> object.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the row being dragged. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.RowEventArgs.Row"/> property.</param>
      <param name="pt">A <see cref="T:System.Drawing.Point"/> structure defining the screen coordinates of the point where dragging was initiated. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.ScreenLocation"/> property.</param>
      <param name="effect">A <see cref="T:DevExpress.XtraVerticalGrid.RowDragEffect"/> enumeration value specifying available effects for the processed drag and drop operation. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.DragRowEventArgs.Effect"/> property.</param>
      <param name="source">A <see cref="T:DevExpress.XtraVerticalGrid.RowDragSource"/> enumeration value representing the source of the started dragging operation. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Events.StartDragRowEventArgs.Source"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.StartDragRowEventArgs.Source">
      <summary>
        <para>Gets a value indicating where the processed drag operation starts.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.RowDragSource"/> enumeration value specifying the source of the started drag operation.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.StartDragRowEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.XtraVerticalGrid.VGridControlBase.StartDragRow"/> event.</para>
      </summary>
      <param name="sender">The event source (typically the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> control).</param>
      <param name="e">A <see cref="T:DevExpress.XtraVerticalGrid.Events.StartDragRowEventArgs"/> class instance containing event data.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.PropertyGridControl.TabPanelCustomize"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs.#ctor(DevExpress.XtraVerticalGrid.TabPanel,DevExpress.XtraVerticalGrid.Rows.VGridRows)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="panel">An object that specifies the panel that contains tabs.</param>
      <param name="rows">An object that specifies a collection of rows in a vertical grid.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs.Buttons">
      <summary>
        <para>Gets the collection of tabs (buttons) displayed in the panel. This property is obsolete. Use the <see cref="P:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs.Tabs"/> property instead.</para>
      </summary>
      <value>An object that specifies a collection of tabs.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs.Panel">
      <summary>
        <para>Gets the panel that contains tabs.</para>
      </summary>
      <value>An object that specifies the panel that contains tabs.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs.Rows">
      <summary>
        <para>Gets the collection of rows in the property grid.</para>
      </summary>
      <value>An object that specifies a collection of rows in a vertical grid.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Events.TabPanelCustomizeEventArgs.Tabs">
      <summary>
        <para>Gets the collection of tabs (buttons) displayed in the panel.</para>
      </summary>
      <value>An object that specifies a collection of tabs.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.FilterPopupExcelParseFilterCriteriaEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraVerticalGrid.VGridControl.FilterPopupExcelParseFilterCriteria"/> event.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.FilterPopupExcelParseFilterCriteriaEventArgs.Grid">
      <summary>
        <para>Gets the vertical grid that raised the event.</para>
      </summary>
      <value>An object that specifies the vertical grid that raised the event.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.FilterPopupExcelParseFilterCriteriaEventArgs.Properties">
      <summary>
        <para>Gets the processed row’s properties.</para>
      </summary>
      <value>An object that specifies the processed row’s properties.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.FindMode">
      <summary>
        <para>Contains values that specify how data searching is initiated within <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> descendants.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.FindMode.Always">
      <summary>
        <para>A control starts searching data automatically, after a small delay.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.FindMode.Default">
      <summary>
        <para>The same as the Always option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.FindMode.FindClick">
      <summary>
        <para>A control starts searching data on clicking the Find button or pressing ENTER.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.FindPanelLocation">
      <summary>
        <para>Enumerates values that specify the location of the Find Panel in the Property Grid.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.FindPanelLocation.Default">
      <summary>
        <para>The Find Panel is located at the control’s top edge.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.FindPanelLocation.TabPanel">
      <summary>
        <para>The Find Panel is integrated into the tab panel.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.HitInfoTypeEnum">
      <summary>
        <para>Lists the values used to identify the grid element located under a specific point or this point’s position relative to the control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.BandEdge">
      <summary>
        <para>A point belongs to the band edge, which is the right edge of the leftmost visible record.  end-users can drag this edge to resize records.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.Border">
      <summary>
        <para>A point belongs to the border of the vertical grid control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.Caption">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.CustomizationForm">
      <summary>
        <para>A point belongs to the client region of the Customization Form.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.Empty">
      <summary>
        <para>A point belongs to an empty client area of the vertical grid control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.EmptyAreaLink">
      <summary>
        <para>A point belongs to a link displayed in an empty grid at design time.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.ExpandButton">
      <summary>
        <para>A point belongs to the expand button residing within a parent row. Note that category rows can display two types of expand buttons, the button type depends on the control’s look and feel settings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.FilterPanel">
      <summary>
        <para>A point belongs to the filter panel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.FilterPanelActiveButton">
      <summary>
        <para>A point belongs to the button in the filter panel used to activate filtering.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.FilterPanelCloseButton">
      <summary>
        <para>A point belongs to the filter panel‘s Close button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.FilterPanelCustomizeButton">
      <summary>
        <para>A point belongs to the filter panel‘s Edit Filter button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.FilterPanelMRUButton">
      <summary>
        <para>A point belongs to the button in the filter panel used to invoke a dropdown that contains a list of the most recently used filters.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.FilterPanelText">
      <summary>
        <para>A point belongs to a string in the filter panel which represents the filter applied to the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.GripPlace">
      <summary>
        <para>A point belongs to the grip of the vertical grid control. The grip place is visible if both the vertical and horizontal scroll bars are visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.HeaderCell">
      <summary>
        <para>A point belongs to a row header panel cell.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.HeaderCellFilterButton">
      <summary>
        <para>Reserved for future use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.HeaderCellImage">
      <summary>
        <para>A point belongs to the row header image displayed within a row header cell.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.HeaderCellSortShape">
      <summary>
        <para>Reserved for future use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.HeaderSeparator">
      <summary>
        <para>A point belongs to the header separator, which is a vertical line between row headers and value cells. It can be dragged to resize row header width.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.HorzScrollBar">
      <summary>
        <para>A point belongs to the grid’s horizontal scroll bar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.MultiEditorCellSeparator">
      <summary>
        <para>A point belongs to a separator that divides cells in multi-editors rows. The separator is either a vertical line or a specific string between cells located within a combined row header (or value) cell. The separator can be dragged to resize the widths of inner cells for the multi-editor row.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.None">
      <summary>
        <para>A point does not belong to any defined part of the vertical grid control. More precisely, it resides outside the grid control within the region which cannot be specified by any member of the <see cref="T:DevExpress.XtraVerticalGrid.HitInfoTypeEnum"/> enumerator.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.OutBottomSide">
      <summary>
        <para>A point resides outside the grid control. It is located below the grid but is still within its width. This can be used for handling a dragging operation initiated within the vertical grid control when it is necessary to determine the direction of dragging.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.OutLeftSide">
      <summary>
        <para>A point resides outside the grid control. It is located to the left of the grid’s left edge but is still within its height boundaries. This can be used for handling a dragging operation initiated within the vertical grid control when it is necessary to determine the direction of dragging.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.OutRightSide">
      <summary>
        <para>A point resides outside the grid control. It is located to the right of the grid’s right edge but is still within its height boundaries. This can be used for handling a dragging operation initiated within the vertical grid control when it is necessary to determine the direction of dragging.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.OutTopSide">
      <summary>
        <para>A point resides outside the grid control. It is located above the grid but is still within its width. This can be used for handling a dragging operation initiated within the vertical grid control when it is necessary to determine the direction of dragging.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.RecordHeader">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.RecordHeaderComparisonButton">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.RecordHeaderEdge">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.RecordValueEdge">
      <summary>
        <para>A point belongs to the right edge of the leftmost visible column representing a record. It can be dragged to resize the width of all records.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.Row">
      <summary>
        <para>A point belongs to a row. Such an area corresponds to the following elements: empty spaces followed by captions within header cells, horizontal and vertical lines between cells in adjacent rows, horizontal and vertical bars representing categories.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.RowBrick">
      <summary>
        <para>A point belongs to a brick button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.RowEdge">
      <summary>
        <para>A point belongs to a row edge. A row edge is the bottom edge of the row’s header cell. It can be dragged to resize row height.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.Tab">
      <summary>
        <para>A point belongs to a tab in a property grid.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.TabPanel">
      <summary>
        <para>A point belongs to the tab panel in a property grid.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.ValueCell">
      <summary>
        <para>A point belongs to a value cell.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.ValueCellEdge">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.HitInfoTypeEnum.VertScrollBar">
      <summary>
        <para>A point belongs to the grid’s vertical scroll bar.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.LayoutViewStyle">
      <summary>
        <para>Contains values that specify the layout style applied to a vertical grid control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.LayoutViewStyle.BandsView">
      <summary>
        <para>Applies the bands view layout to the control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.LayoutViewStyle.MultiRecordView">
      <summary>
        <para>Applies the multiple records view layout to the control. This layout is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.LayoutViewStyle.SingleRecordView">
      <summary>
        <para>Applies the single record view layout to the control.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraVerticalGrid.Localization">
      <summary>
        <para>Contains classes and enumerations that are intended to localize the User Interface of the DevExpress WinForms Vertical Grid.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer">
      <summary>
        <para>A base class that provides necessary functionality for custom localizers of the Vertical Grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer.Active">
      <summary>
        <para>Gets or sets a localizer object providing localization of the user interface at runtime.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> descendant, used to localize the user interface at runtime.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer.CreateDefaultLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread’s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread’s culture.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer.CreateResXLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread’s language and regional settings (culture).</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, which provides resources based on the thread’s culture.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Localization.VGridLocalizer.GetString(DevExpress.XtraVerticalGrid.Localization.VGridStringId)">
      <summary />
      <param name="id"></param>
      <returns></returns>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Localization.VGridResLocalizer">
      <summary>
        <para>A default localizer to translate resources for WinForms Vertical Grid.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Localization.VGridResLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Localization.VGridResLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.MultiSelectMode">
      <summary>
        <para>Contains values that specify multiple selection mode in the Vertical Grid control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect">
      <summary>
        <para>Enables selection of multiple blocks of cells.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RecordSelect">
      <summary>
        <para>Enables selection of multiple records.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect">
      <summary>
        <para>Enables selection of multiple rows.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.PGridOptionsBehavior">
      <summary>
        <para>Provides behavior options for a <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.PGridOptionsBehavior"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.AllowDynamicRowLoading">
      <summary>
        <para>Gets or sets whether a selected object’s properties are loaded dynamically.</para>
      </summary>
      <value>true if a selected object’s properties are loaded dynamically; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.AutoPostEditorDelay">
      <summary>
        <para>Gets or sets the time delay of actually changing the value after it was changed in the editor.</para>
      </summary>
      <value>An integer value which specifies the time delay in milliseconds.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.PropertySort">
      <summary>
        <para>Gets or sets the way properties are sorted in the Property Grid control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.PropertySort"/> value that specifies the way properties are sorted in the Property Grid control.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.RefreshOnSelectedObjectChanges">
      <summary>
        <para>Gets or sets whether the control automatically updates the content when the selected object’s property changes.</para>
      </summary>
      <value>true if the control updates the content when the selected object’s property changes; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.ShowOnlyBrowsableProperties">
      <summary>
        <para>Gets or sets whether to show only browsable properties, as specified in the System.ComponentModel.BrowsableAttribute.</para>
      </summary>
      <value>true, to show browsable properties only; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsBehavior.ValueDisplayMode">
      <summary />
      <value></value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.PGridOptionsView">
      <summary>
        <para>Provides view options for a property grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PGridOptionsView.#ctor(DevExpress.XtraVerticalGrid.PropertyGridControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.PGridOptionsView"/> class with specified settings.</para>
      </summary>
      <param name="grid"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.AllowPaintValue">
      <summary>
        <para>Gets or sets whether to display value representations provided by custom editors (Classic view only).</para>
      </summary>
      <value>true, to display a property value representation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.AllowRowHeaderReadOnlyAppearance">
      <summary>
        <para>Gets or sets whether to paint read-only row headers according to the <see cref="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRow"/> appearance settings. Office view only.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/> to paint read-only rows according to the <see cref="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRow"/> appearance settings; otherwise, <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> or <see cref="F:DevExpress.Utils.DefaultBoolean.False"/>.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PGridOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.EditorAlignment">
      <summary>
        <para>Gets or sets whether editors are aligned at the left, right, center, or stretched.</para>
      </summary>
      <value>A value that specifies whether editors are aligned at the left, right, center, or stretched.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.EditorPosition">
      <summary>
        <para>Gets or sets whether editors are located before or after captions.</para>
      </summary>
      <value>A value that specifies whether editors are located before or after captions.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.GradientHeight">
      <summary>
        <para>Gets or sets the height of the gradual fade out effect of property rows at the top and bottom when scrolling. The Office view only.</para>
      </summary>
      <value>An integer value that specifies the height, in pixels, of the gradual fade out effect.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.PasswordChar">
      <summary>
        <para>Gets or sets the password character displayed instead of actual characters. Password mode is enabled for fields marked with the <see cref="T:System.ComponentModel.PasswordPropertyTextAttribute"/> attribute.</para>
      </summary>
      <value>A character displayed instead of actual characters.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.ShowFocusedFrame">
      <summary>
        <para>Gets or sets whether a frame is displayed around the focused cell and the focused category row caption in the Classic view, and around the focused row caption in the Office view.</para>
      </summary>
      <value>true, to display a focus frame; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PGridOptionsView.ShowRowBrick">
      <summary>
        <para>Gets or sets whether to display brick buttons against properties that invoke a context menu (the Office view only).</para>
      </summary>
      <value>True to display brick buttons; otherwise, Default or False.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl">
      <summary>
        <para>The control that displays descriptions for properties being browsed in a <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.AllowDrop">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/> class.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.Appearance">
      <summary>
        <para>Provides access to the properties that control the appearance of the control’s elements.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.PDescControlAppearanceCollection object which provides the appearance settings for the vertical grid’s elements.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.AutoHeight">
      <summary>
        <para>Gets or sets whether the control automatically adjusts its height to display the text in its entirety.</para>
      </summary>
      <value>A value that specifies whether the control automatically adjusts its height to display the text in its entirety.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.BackColor">
      <summary>
        <para>Gets or sets the control’s background color. This property is not supported, use the Appearance.Panel.BackColor property instead.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.BackgroundImage">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/> class.</para>
      </summary>
      <value>An Image object.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.BackgroundImageLayout">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/> class.</para>
      </summary>
      <value>An ImageLayout object.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.CalcHeight(DevExpress.Utils.Drawing.GraphicsCache)">
      <summary>
        <para>Returns the control’s height.</para>
      </summary>
      <param name="cache">A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object used to calculate the height.</param>
      <returns>An integer value that specifies the control’s height.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.DefaultCaption">
      <summary>
        <para>Gets or sets the caption displayed when no row is focused.</para>
      </summary>
      <value>A string value specifying the caption displayed when no row is focused.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.DefaultDescription">
      <summary>
        <para>Gets or sets the description displayed when no row is focused.</para>
      </summary>
      <value>A string value specifying the description displayed when no row is focused.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.Font">
      <summary>
        <para>Gets or sets the control’s font settings. This property is not supported, use the Appearance.Description.Font property instead.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Font"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.ForeColor">
      <summary>
        <para>Gets or sets the control’s foreground color. This property is not supported, use the Appearance.Description.ForeColor property instead.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.HeightChanged">
      <summary>
        <para>Fires when the control’s height changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.PropertyGrid">
      <summary>
        <para>Gets or sets the target <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> for which property descriptions are displayed in the current control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.ResetBackColor">
      <summary>
        <para>Resets the BackColor property to its default value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.ResetForeColor">
      <summary>
        <para>Resets the ForeColor property to its default value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.RightToLeft">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/> class.</para>
      </summary>
      <value>A RightToLeft object.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.ShowHint">
      <summary>
        <para>Gets or sets whether to show a hint displaying the current property description when the property description is trimmed in the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/>.</para>
      </summary>
      <value>true, to show a hint when the property description is trimmed in the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/>; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyDescriptionControl.Text">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyDescriptionControl"/> class.</para>
      </summary>
      <value>A string value.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.PropertyGridControl">
      <summary>
        <para>The control that allows you to display and edit properties of any object or set of objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.#ctor">
      <summary>
        <para>Initiliazes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.ActiveViewType">
      <summary>
        <para>Gets or sets whether the control is displayed in the Classic (a single view) or Office (a tabbed view) style.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.PropertyGridView enumeration value that specifies whether the control is displayed in the Classic or Office style.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.ActiveViewTypeChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.XtraVerticalGrid.PropertyGridControl.ActiveViewType"/> property value changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.AppearanceTab">
      <summary>
        <para>Provides access to appearance settings of tabs available in the Office View.</para>
      </summary>
      <value>A DevExpress.XtraEditors.ButtonsPanelControl.ButtonsPanelControlAppearance object that comprises tab appearance settings in the normal, pressed and hovered states.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.AutoGenerateRows">
      <summary>
        <para>Gets or sets whether rows are automatically created in the control for all fields in the bound object.</para>
      </summary>
      <value>true, if the control automatically re-creates rows when you re-bind an object; false, if the control automatically creates rows only when you bind an object for the first time.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.BrowsableAttributes">
      <summary>
        <para>Gets or sets the collection of attributes that display the selected object’s properties.</para>
      </summary>
      <value>An <see cref="T:System.ComponentModel.AttributeCollection"/> object that specifies a collection of attributes.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.CanDrawTabPanelArrow">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.CanExpand(DevExpress.XtraVerticalGrid.ViewInfo.VGridHitTest)">
      <summary>
        <para>Returns whether the specified view element is a row header panel in a category row.</para>
      </summary>
      <param name="hitTest">A VGridHitTest object that contains information about view elements.</param>
      <returns>true if the specified view element is a row header panel in a category row; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.CreateEditorRow">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.CreateRow(System.Int32)">
      <summary>
        <para>Returns a new row of the specified type.</para>
      </summary>
      <param name="rowTypeId">An integer value that specifies the row type. 0 - to create a category row. 1 - to create an editor row.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that specifies the created row.</returns>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.CustomDrawRowBrick">
      <summary>
        <para>Allows you to customize brick buttons that invoke a context menu (the Office view only).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.CustomPropertyDescriptors">
      <summary>
        <para>Allows you to specify properties that should be displayed in the control. You can use this event to filter or sort properties, display rows that are not bound to the selected object’s properties, or display multiple objects’ properties.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.CustomRowCreated">
      <summary>
        <para>Allows you to customize <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> rows when this <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> has the Office View applied.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.DefaultCollectionEditors">
      <summary>
        <para>Provides access to the default editors for collections in the current <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.PGridDefaultCollectionEditors object that specifies the collection of the default editors for collections in the current <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.DefaultEditors">
      <summary>
        <para>Specifies the collection of in-place editors used by default to represent row values of specific data types</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Rows.PGridDefaultEditors collection which associates editors with specific data types.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.DesignTimeManager">
      <summary />
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.PropertyGridControl.FieldNameDelimiter">
      <summary>
        <para>A delimiter between property names.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.FilterCriteria">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.FilterString">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.GetPropertyDescriptor(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns the property descriptor for the property associated with the specified row.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object which is bound to the required property.</param>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptor"/> value.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.GetPropertyDescriptor(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Object@)">
      <summary>
        <para>Returns the property descriptor which is associated with the specified row. The object which owns the property is returned via the targetObject parameter.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object which is bound to the required property.</param>
      <param name="targetObject">Returns the object which contains the required property, or an array of objects which own the property (when the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> is bound to multiple objects via the <see cref="P:DevExpress.XtraVerticalGrid.PropertyGridControl.SelectedObjects"/> member).</param>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptor"/> value.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.GetRecordObject(System.Int32)">
      <summary>
        <para>Returns an <see cref="T:System.Object"/> at the specified index in the collection of selected objects.</para>
      </summary>
      <param name="recordIndex">An integer value that specifies the zero-based index of an object in the collection of selected objects.</param>
      <returns>An object.</returns>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.InitCustomRowBrick">
      <summary />
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.IsClassic">
      <summary>
        <para>Gets whether the control is displayed in the classic style.</para>
      </summary>
      <value>true if the control is displayed in the classic style; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.IsDataRecord(System.Int32)">
      <summary>
        <para>Returns whether or not the specified row handle corresponds to a data row.</para>
      </summary>
      <param name="record">An integer value specifying a row handle.</param>
      <returns>true if the specified row handle corresponds to a data row; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.LayoutStyle">
      <summary>
        <para>Gets or sets the control’s layout style.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.LayoutViewStyle"/> enumeration value that specifies the control’s layout style.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.LockSelectedTab">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OfficeRowStore">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OptionsBehavior">
      <summary>
        <para>Provides access to the grid’s behavior options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.PGridOptionsBehavior"/> object which contains the grid’s behavior options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OptionsCollectionEditor">
      <summary>
        <para>Provides access to options that specify whether to use the standard WinForms or DevExpress editor for collections, and appearance settings of the latter.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.PGridOptionsCollectionEditor object specifying collection editor’s options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OptionsFind">
      <summary>
        <para>Provides access to find panel options.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind object that comprises options specific to the find panel.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OptionsMenu">
      <summary>
        <para>Contains options that affect the display of context menus in a <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridOptionsMenu"/> object that contains corresponding options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OptionsPrint">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.OptionsView">
      <summary>
        <para>Provides access to the vertical grid’s display options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.PGridOptionsView"/> object which contains the grid’s display options.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.RefreshAllProperties">
      <summary>
        <para>Refreshes data in property rows, invalidates them, and causes the control to redraw the invalidated rows.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.Refreshed">
      <summary />
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.RefreshEditor(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Object)">
      <summary />
      <param name="properties"></param>
      <param name="value"></param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.RepaintAllProperties">
      <summary>
        <para>Invalidates property rows, and causes the control to redraw the invalidated rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.RetrieveFields">
      <summary>
        <para>Creates rows for the bound object’s properties.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.RetrieveFields(System.Boolean)">
      <summary>
        <para>Creates rows for the bound object’s properties, allowing rows for nested properties to be created.</para>
      </summary>
      <param name="forceChildRowsCreating">true to create rows for nested properties; false to only create root rows.</param>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.RowBrickMenuShowing">
      <summary>
        <para>Allows you to populate a context menu invoked with a click on a brick button (the Office view only).</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.ScrollVisibility">
      <summary>
        <para>Gets or sets a value that specifies the availability of the vertical scroll bar.</para>
      </summary>
      <value>A PGridScrollVisibility enumeration value that specifies when the vertical scroll bar should be displayed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.SelectedObject">
      <summary>
        <para>Gets or sets the object whose settings are edited by the PropertyGridControl.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> whose settings are edited by the PropertyGridControl.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.SelectedObjects">
      <summary>
        <para>Gets or sets the currently selected objects.</para>
      </summary>
      <value>An array of objects that are currently browsed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.SelectedTab">
      <summary>
        <para>Gets or sets the currently selected tab in the Office View.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Tab object representing the currently selected tab.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.SelectedTabChanged">
      <summary>
        <para>Occurs when the <see cref="P:DevExpress.XtraVerticalGrid.PropertyGridControl.SelectedTab"/> property value changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.ServiceProvider">
      <summary>
        <para>Gets or sets an object implementing the <see cref="T:System.IServiceProvider"/> interface.</para>
      </summary>
      <value>An object which implements the <see cref="T:System.IServiceProvider"/> interface.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.ShowMenu">
      <summary>
        <para>Allows context menus for rows to be customized.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.SupportDefaultPropertyAttribute">
      <summary>
        <para>Gets or sets whether to automatically focus the default property on loading the control and selecting a new tab.</para>
      </summary>
      <value>true, to automatically focus the default property on selecting a new tab; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.TabPanel">
      <summary>
        <para>Provides access to an object representing the tab panel available in the Office View.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.TabPanel object representing the tab panel.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.PropertyGridControl.TabPanelCustomize">
      <summary>
        <para>Fires before the control is loaded. Allows you to create tabs with properties and categories in the Office View.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.PropertyGridControl.Tabs">
      <summary>
        <para>Provides access to the collection of tabs available in the Office View.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.TabCollection object specifying the collection of tabs available in the Office style.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.UnlockSelectedTab">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.PropertyGridControl.UpdateRows">
      <summary>
        <para>Updates grid rows for all fields in the bound data source and forces the control to immediately redraw itself.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.PropertyGridValueDisplayMode">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.PropertyGridValueDisplayMode.Default">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.PropertyGridValueDisplayMode.DisplayText">
      <summary />
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.PropertyGridValueDisplayMode.TypeConverter">
      <summary />
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.PropertySort">
      <summary>
        <para>Enumerates modes that specify how property rows are sorted in the Property Grid control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.PropertySort.Alphabetical">
      <summary>
        <para>Properties are sorted in alphabetical order.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.PropertySort.NoSort">
      <summary>
        <para>Properties are displayed in the order specified by the underlying object.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.RowChangeTypeEnum">
      <summary>
        <para>Contains values indicating how the row has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Add">
      <summary>
        <para>A new row has been added to the vertical grid’s <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows"/> or a specific row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.ChildRows"/> collection. This can be performed by calling the collection’s Add method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.AllowCollapse">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.AllowCollapse"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.AllowEdit">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AllowEdit"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.AllowHtmlText">
      <summary>
        <para>The row’s caption HTML formatting setting has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.AllowSortOnClick">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AllowSortOnClick"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Caption">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Caption"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.CellWidth">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.CellWidth"/> property has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.CustomizationCaption">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.CustomizationCaption"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Delete">
      <summary>
        <para>A row has been removed from the vertical grid’s <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows"/> or a specific row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.ChildRows"/> collection.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.DrawEditorBorder">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.DrawEditorBorder"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Enabled">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.EditorRow.Enabled"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Expanded">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Expanded"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.FieldName">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Fixed">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Fixed"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Format">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Format"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.FormatString">
      <summary>
        <para>The <see cref="P:DevExpress.Utils.FormatInfo.FormatString"/> attribute of the row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Format"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.FormatType">
      <summary>
        <para>The <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> attribute of the row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Format"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Height">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Height"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.ImageIndex">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ImageIndex"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Index">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Index"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.MaxCaptionLineCount">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.MaxCaptionLineCount"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Move">
      <summary>
        <para>A row has been moved within the control. This can be performed using drag and drop or via the VGridControlBase.MoveRow method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Name">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Name"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Options">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.OptionsRow"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Padding">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Padding"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.PropertiesAdded">
      <summary>
        <para>A new <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object or its descendant has been added to the multi-editor row’s properties collection. This can be performed by calling the MultiEditorRowPropertiesCollection.Add method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.PropertiesAssigned">
      <summary>
        <para>The row item’s settings have been replaced with the settings from another row item using the <see cref="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.AssignTo(DevExpress.XtraVerticalGrid.Rows.RowProperties)"/> method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.PropertiesCleared">
      <summary>
        <para>The multi-editor row’s properties collection has been cleared. This can be performed by calling the <see cref="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Clear"/> method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.PropertiesDeleted">
      <summary>
        <para>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object or its descendant has been removed from the properties collection. This can be performed by calling the <see cref="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.RemoveAt(System.Int32)"/> method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.PropertiesReplaced">
      <summary>
        <para>This property is used internally only. It is inaccessible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.ReadOnly">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ReadOnly"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.RowAssigned">
      <summary>
        <para>Row settings have been replaced by another row’s settings using the <see cref="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.AssignTo(DevExpress.XtraVerticalGrid.Rows.BaseRow)"/> method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.RowEdit">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.RowEdit"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.SearchTags">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.SearchTags"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.SeparatorKind">
      <summary>
        <para>The multi-editor row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.SeparatorKind"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.SeparatorString">
      <summary>
        <para>The multi-editor row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.SeparatorString"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.ShowCaption">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ShowCaption"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.ShowCollapseButton">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.ShowCollapseButton"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.ShowUnboundExpressionMenu">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ShowUnboundExpressionMenu"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.SortOrder">
      <summary>
        <para>Reserved for future use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.StyleName">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.StyleName"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.SummaryFooter">
      <summary>
        <para>Reserved for future use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.SummaryFooterStrFormat">
      <summary>
        <para>Reserved for future use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.TabStop">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.TabStop"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Tag">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Tag"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.ToolTip">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ToolTip"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UnboundExpression">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.UnboundExpression"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UnboundType">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.UnboundType"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty1">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty10">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty2">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty3">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty4">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty5">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty6">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty7">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty8">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.UserProperty9">
      <summary>
        <para>The corresponding user’s property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Value">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Value"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Visible">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowChangeTypeEnum.Width">
      <summary>
        <para>The row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.Width"/> property has changed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.RowDragEffect">
      <summary>
        <para>Contains values specifying the available effect for the processed drag operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragEffect.InsertAfter">
      <summary>
        <para>A row is inserted after another row.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragEffect.InsertBefore">
      <summary>
        <para></para>
        <para>Specifies the pointer feedback indicating that the dragged row is allowed to be inserted before a target row if the mouse is released at a given moment during a drag operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragEffect.MoveChild">
      <summary>
        <para></para>
        <para>Specifies the pointer feedback indicating that the dragged row is allowed to be inserted as a child of a target row if the mouse is released at a given moment during a drag operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragEffect.MoveToEnd">
      <summary>
        <para></para>
        <para>Specifies the pointer feedback indicating that the dragged row is allowed to be inserted after the last row in the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows"/> collection if the mouse is released at a given moment during a drag operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragEffect.None">
      <summary>
        <para></para>
        <para>Specifies the pointer feedback indicating that none of the predefined effects is allowed for the dragged row if the mouse is released at a given moment during a drag operation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.RowDragSource">
      <summary>
        <para>Contains values that specify the source of a row drag operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragSource.Control">
      <summary>
        <para>Indicates that a drag operation starts within the vertical grid control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.RowDragSource.CustomizationForm">
      <summary>
        <para>Indicates that a drag operation starts within the Customization Form.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraVerticalGrid.Rows">
      <summary>
        <para>Contains classes representing rows within the vertical grid controls that are included in the XtraVerticalGrid Suite and classes providing particular aspects of row functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.BaseRow">
      <summary>
        <para>Serves as the base class for rows of different types.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.AllowCollapse">
      <summary>
        <para>Gets or sets whether users can click the header or collapse button to collapse the row.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/> if users can collapse the row; <see cref="F:DevExpress.Utils.DefaultBoolean.False"/> if not; <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> if this functionality depends on the control’s <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowCollapseRows"/> option.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Appearance">
      <summary>
        <para>Provides access to appearance settings used to paint row cells. This property is obsolete, use <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.AppearanceCell"/> instead.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> object that provides appearance settings used to paint row cells.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.AppearanceCell">
      <summary>
        <para>Contains appearance settings used to paint row cells.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> object that contains settings used to paint row cells.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.AppearanceHeader">
      <summary>
        <para>Provides access to appearance settings used to paint row headers.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> object that provides appearance settings used to paint row headers.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.AssignTo(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Copies the current row’s settings to the row specified.</para>
      </summary>
      <param name="destinationRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object or descendant that represents the row object into which settings are copied.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.ChildRows">
      <summary>
        <para>Gets a collection of child rows for a row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRows"/> object representing a collection of child rows (if any) for a row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Expanded">
      <summary>
        <para>Gets or sets a value specifying whether the current row is expanded.</para>
      </summary>
      <value>true if the current row is expanded; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Fixed">
      <summary>
        <para>Gets or sets whether the row is fixed (anchored to a control’s top or bottom edge).</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Rows.FixedStyle value that specifies whether the row is fixed.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.GetRowProperties">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.GetRowProperties(System.Int32)">
      <summary>
        <para>Returns an object containing type specific row settings.</para>
      </summary>
      <param name="index">A zero-based integer value representing the index of the object that contains the row settings.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object or its descendant that represents type specific row properties. null (Nothing in Visual Basic) if the index parameter value is negative or exceeds the maximum index available.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.GetRowProperties(System.String)">
      <summary>
        <para>Returns row properties by the specified field name.</para>
      </summary>
      <param name="fieldName">A string value specifying the required field name in the underlying data source.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object corresponding to the row displaying values from the specified fieldName in the data source.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Grid">
      <summary>
        <para>Gets or sets the vertical grid control that owns the current row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> object which represents the vertical grid control that owns the current row.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.HasAsChild(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Indicates whether the row has the child row specified.</para>
      </summary>
      <param name="gridRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the row to test.</param>
      <returns>true if the row is the parent for the row specified; otherwise false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.HasAsParent(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Determines whether the current row has a specific row as its parent.</para>
      </summary>
      <param name="gridRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row to check.</param>
      <returns>true if the current row has the specified row as its parent; otherwise false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.HasChildren">
      <summary>
        <para>Gets a value indicating whether a specific row contains child rows.</para>
      </summary>
      <value>true if a specific row contains one or more child rows; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.HeaderInfo">
      <summary>
        <para>Provides view information on the row’s header.</para>
      </summary>
      <value>The DevExpress.XtraVerticalGrid.ViewInfo.BaseRowHeaderInfo class descendant that provides view information on the row’s header.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Height">
      <summary>
        <para>Gets or sets the row’s height.</para>
      </summary>
      <value>An integer value specifying the row’s height in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Index">
      <summary>
        <para>Gets or sets the row’s index within a collection of rows located at the same level (sibling rows).</para>
      </summary>
      <value>A zero-based index value representing the position of a specific row within a <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRows"/> collection.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.InternalFixed">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.IsFocused">
      <summary>
        <para>Gets whether or not the current row is focused.</para>
      </summary>
      <value>true if the current row is focused; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.IsSelected">
      <summary>
        <para>Gets whether or not the current row is selected.</para>
      </summary>
      <value>true if the current row is selected; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Level">
      <summary>
        <para>Gets the row’s nesting level.</para>
      </summary>
      <value>An integer value representing the row’s zero-based nesting level.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.MaxCaptionLineCount">
      <summary>
        <para>Gets or sets the maximum number of text lines used to display the row header’s caption.</para>
      </summary>
      <value>An integer value specifying the maximum number of text lines within the row header.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.MinHeight">
      <summary>
        <para>Gets the row’s minimum height.</para>
      </summary>
      <value>An integer value specifying the grid row’s minimum height.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Name">
      <summary>
        <para>Gets or sets the row name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the row name.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.OptionsRow">
      <summary>
        <para>Provides access to the current row’s options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow"/> object which contains the row’s options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.ParentRow">
      <summary>
        <para>Gets the parent row of the current grid row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the row’s parent.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Properties">
      <summary>
        <para>Gets an object containing type specific row settings.</para>
      </summary>
      <value>An instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> class or its descendant representing type specific row properties.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.RowPropertiesCount">
      <summary>
        <para>Returns the number of objects owned by a row that contains row settings.</para>
      </summary>
      <value>An integer value representing the total number of objects representing specific row settings.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.BaseRow.SetOrder(System.Int32)">
      <summary>
        <para>Sets the row’s order index.</para>
      </summary>
      <param name="order">An integer value that specifies the row’s order index. 0 corresponds to the last row.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.ShowCollapseButton">
      <summary>
        <para>Gets or sets whether the row displays the collapse/expand button.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.True"/> if the row displays the collapse/expand button; <see cref="F:DevExpress.Utils.DefaultBoolean.False"/> if not; <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> if the collapse/expand button visibility depends on the control’s <see cref="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowCollapseButtons"/> option.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Site">
      <summary>
        <para>Gets or sets a System.ComponentModel.ISite for a row.</para>
      </summary>
      <value>A System.ComponentModel.ISite for a row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.StyleName">
      <summary>
        <para>Gets or sets the name of the custom style to be applied to the current row. This property is obsolete, use <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.AppearanceCell"/> instead.</para>
      </summary>
      <value>A string representing the name of the custom style set for the current row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.TabStop">
      <summary>
        <para>Gets or sets whether the user can move focus to the cell using the TAB/SHIFT+TAB or ENTER key.</para>
      </summary>
      <value>true if the user can focus the cell using the TAB/SHIFT+TAB or ENTER key; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Tag">
      <summary>
        <para>Gets or sets a value that identifies the current row object.</para>
      </summary>
      <value>An object  that identifies the current row object.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.TreeButtonType">
      <summary>
        <para>Gets the row’s tree button type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.TreeButtonType"/> enumeration member that specifies the row’s tree button type.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible">
      <summary>
        <para>Gets or sets a value indicating whether a row can be displayed within a vertical grid control.</para>
      </summary>
      <value>true if a row can be displayed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.VisibleIndex">
      <summary>
        <para>Gets the row’s visible position within a vertical grid control.</para>
      </summary>
      <value>A zero based integer value specifying the row’s visible position among the other visible rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.XtraRowTypeID">
      <summary>
        <para>Gets a value indicating the row‘s type.</para>
      </summary>
      <value>An integer value representing the row’s type identifier.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow">
      <summary>
        <para>Represents a category row within your grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.CategoryRow.#ctor">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.CategoryRow.#ctor(System.String)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object with the caption specified.</para>
      </summary>
      <param name="fCaption">A string value specifying the category row’s caption. Sets the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Caption"/> property value.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.CategoryRow.GetRowProperties">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRow.MaxCaptionLineCount">
      <summary>
        <para>Gets or sets the maximum number of text lines used to display the category’s caption.</para>
      </summary>
      <value>An integer value specifying the maximum number of text lines within the category.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRow.Properties">
      <summary>
        <para>Gets the category row’s settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object containing properties specific to category rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRow.TabStop">
      <summary>
        <para>Gets or sets whether the user can move focus to the category row using the TAB/SHIFT+TAB key.</para>
      </summary>
      <value>true if the user can focus the category row using the TAB/SHIFT+TAB key; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRow.TreeButtonType">
      <summary>
        <para>Gets the category row’s tree button type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.TreeButtonType"/> enumeration member specifying the button’s type.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRow.XtraRowTypeID">
      <summary>
        <para>Gets a value indicating the category row‘s type.</para>
      </summary>
      <value>An integer value indicating a category row’s type.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties">
      <summary>
        <para>Contains row item settings for a category row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.#ctor">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.#ctor(System.String)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties"/> object with the specified caption.</para>
      </summary>
      <param name="fCaption">A string value specifying the category’s caption. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Caption"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.Bindable">
      <summary>
        <para>Gets a value indicating whether the corresponding row can display data from a data source field.</para>
      </summary>
      <value>true if the row can be bound to a data source field; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.FieldName">
      <summary>
        <para>Overrides the inherited <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName"/> property to return an empty string and prohibit property value modifications.</para>
      </summary>
      <value>The String.Empty value.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.OptionsFilter">
      <summary>
        <para>This property is not relevant for this class.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.ReadOnly">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>Always returns true.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.RowEdit">
      <summary>
        <para>Gets or sets the repository item specifying the editor used to edit a row’s cell values.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.RowEditName">
      <summary>
        <para>Gets or sets the name of the row’s editor.</para>
      </summary>
      <value>A string value specifying the editor’s name.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.CategoryRowProperties.RowHandle">
      <summary>
        <para>Gets the position of the associated field within a data source.</para>
      </summary>
      <value>The -1 value.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.EditorRow">
      <summary>
        <para>Specifies a single-edtior data row within the vertical grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.EditorRow.#ctor">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/> object with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.EditorRow.#ctor(System.String)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/> object and binds it to the specified datasource field.</para>
      </summary>
      <param name="fFieldName">A string value specifying the name of the data field to which the created row should be bound. Sets the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName"/> property value.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.EditorRow.Enabled">
      <summary>
        <para>Gets or sets a value indicating whether the row allows cell editors to be invoked.</para>
      </summary>
      <value>true if the row allows you to invoke cell editors; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.EditorRow.GetRowProperties">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.EditorRow.Properties">
      <summary>
        <para>Gets an object containing settings specific to an editor row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object containing the properties of an editor row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.EditorRow.XtraRowTypeID">
      <summary>
        <para>Gets a value indicating the editor row‘s type.</para>
      </summary>
      <value>An integer value indicating the type of an editor row.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow">
      <summary>
        <para>Represents a multi-editor row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.#ctor">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> object with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.GetRowProperties">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.Properties">
      <summary>
        <para>Gets an object containing settings specific to the first item in a multi-editor row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> descendant representing the type specific properties of the first item within a multi-editor row. null (Nothing in Visual Basic) if the multi-editor row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/> collection is empty.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection">
      <summary>
        <para>Gets a collection of items within a multi-editor row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection"/> object representing a collection of multi-editor row items.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.SeparatorKind">
      <summary>
        <para>Gets or sets a value specifying the type of the item cell separator displayed within a multi-editor row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.SeparatorKind"/> enumerator value specifying the type of item cell separators.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.SeparatorString">
      <summary>
        <para>Gets or sets the string used as an item cell separator within a multi-editor row.</para>
      </summary>
      <value>A string representing an item separator in a multi-editor row.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.ToString">
      <summary>
        <para>Returns the text representation of the current object.</para>
      </summary>
      <returns>A string that is the text representation of the current object.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.XtraRowTypeID">
      <summary>
        <para>Gets a value indicating the multi-editor row‘s type.</para>
      </summary>
      <value>An integer value indicating the type of a multi-editor row.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties">
      <summary>
        <para>Contains row item settings for a multi-editor row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.#ctor">
      <summary>
        <para>Cteates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.#ctor(System.String)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object and binds it to the specified data field.</para>
      </summary>
      <param name="fFieldName">A string value specifying the name of the data field to which the created row item should be bound. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.CellWidth">
      <summary>
        <para>Gets or sets the width of the current data cell relative to other data cells in the multi-editor row.</para>
      </summary>
      <value>The relative width of the data cell in the multi-editor row.</value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.MinWidth">
      <summary>
        <para>Specifies the minimum row item width. By default, it is set to 5 pixels.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.Name">
      <summary>
        <para>Gets or sets the row name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the row name.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties.Width">
      <summary>
        <para>Gets or sets the width of a row item.</para>
      </summary>
      <value>An integer representing a row item’s width.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection">
      <summary>
        <para>Represents a collection of items within a multi-editor row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Add">
      <summary>
        <para>Creates and adds a new row item to the collection.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object representing the newly created row item.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Add(DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties)">
      <summary>
        <para>Adds an existing row item to the end of the collection.</para>
      </summary>
      <param name="rowProperties">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object representing the row item to add.</param>
      <returns>An integer value representing the collection index of the row item added.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.AddProperties(System.String)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object which is bound to a specific data field and appends it to the collection.</para>
      </summary>
      <param name="fieldName">A <see cref="T:System.String"/> value which specifies the name of the data field to which the newly created row item should be bound.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object which represents the new row item.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.AddRange(DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties[])">
      <summary>
        <para>Adds an array of <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> objects to the collection of row items.</para>
      </summary>
      <param name="props">An array of <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> objects to be added to the row item collection of a multi-editor row.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Clear">
      <summary>
        <para>Removes all row items from the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Contains(System.Object)">
      <summary>
        <para>Determines whether a specific row item is a member of the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/>.</para>
      </summary>
      <param name="rowProperties">The <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object, to locate in the row item collection.</param>
      <returns>true if the specified row item is contained within the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/>; otherwise false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Count">
      <summary>
        <para>Gets the actual number of row items  contained within the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/>.</para>
      </summary>
      <value>An integer value representing the number of row items in the row item collection.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.IndexOf(System.Object)">
      <summary>
        <para>Searches for a specific row item object and returns the zero-based index of the first occurrence within the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/>.</para>
      </summary>
      <param name="rowProperties">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object representing the row item to locate within the collection.</param>
      <returns>The zero-based index of the first occurrence of a specific row item within the row item collection, if found; otherwise -1.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Insert(System.Int32,DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties)">
      <summary>
        <para>Adds the specified row item to the specified position within the collection.</para>
      </summary>
      <param name="index">An integer value representing the zero-based index at which the specified row item should be inserted. If negative or it exceeds the number of elements, an exception is raised.</param>
      <param name="rowProperties">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object representing the row item to be inserted.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Item(System.Int32)">
      <summary>
        <para>Gets a row item object from the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/> at the specified position.</para>
      </summary>
      <param name="index">An integer value specifying the zero-based index of the desired row item.</param>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object or descendant representing the row item located at the specified position within the row item collection.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Item(System.String)">
      <summary>
        <para>Gets a row item from the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/> by the bound field name.</para>
      </summary>
      <param name="fFieldName">A string value specifying the name of the data field to which a row item is bound.</param>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object or descendant representing a row item bound to a field with the required name. null (Nothing in Visual Basic) if there are no row items bound to the specified field.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.Remove(System.Object)">
      <summary>
        <para>Removes a specific row item object from the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/>.</para>
      </summary>
      <param name="rowProperties">An object representing the row item to remove from the row item collection.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowPropertiesCollection.RemoveAt(System.Int32)">
      <summary>
        <para>Removes a row item object from the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.PropertiesCollection"/> at the specified position.</para>
      </summary>
      <param name="index">An integer value representing the index of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRowProperties"/> object in the row item collection to remove.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow">
      <summary>
        <para>Specifies a property grid row in the Office view.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.#ctor(DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow"/> class with specified settings.</para>
      </summary>
      <param name="baseRepository">An object that specifies a repository item of the editor used to edit values in this row. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.Repository"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.EditorAlignment">
      <summary>
        <para>Gets or sets whether the editor is aligned at the left, right, center, or stretched.</para>
      </summary>
      <value>A value that specifies how the editor is aligned in the row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.EditorPosition">
      <summary>
        <para>Gets or sets whether the editor is located before or after the caption.</para>
      </summary>
      <value>A value that specifies whether the editor is located before or after the caption.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.EditorSize">
      <summary>
        <para>Gets the editor size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that specifies a size.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.GetLineCount">
      <summary>
        <para>Gets the number of text lines in the row.</para>
      </summary>
      <returns>An integer value that specifies the number of text lines in the row.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.Grid">
      <summary>
        <para>Gets the property grid that contains this row.</para>
      </summary>
      <value>An object that specifies a property grid.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.Indent">
      <summary>
        <para>Gets the value that specifies padding, line spacing, etc.</para>
      </summary>
      <value>An integer value that specifies padding, line spacing, etc.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.OptionsBrick">
      <summary>
        <para>Contains options that specify property markers (small rectangles displayed next to property editors).</para>
      </summary>
      <value>An object that contains property marker options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.Repository">
      <summary>
        <para>Gets or sets the repository item of the editor used to edit values in this row.</para>
      </summary>
      <value>An object that specifies a repository item of the editor used to edit values in this row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.RowBricks">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridCustomEditorRow.TryGetEditingValue(System.Object@)">
      <summary>
        <para>Gets the row editor’s value.</para>
      </summary>
      <param name="editingValue">An object that specifies the edit value.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.PGridEditorRow">
      <summary>
        <para>Specifies a single-editor data row within the property grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridEditorRow.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridEditorRow"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridEditorRow.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridEditorRow"/> class with specified settings.</para>
      </summary>
      <param name="fieldName">A string value that specifies the name of the data field bound to the row being created. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName"/> property.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind">
      <summary>
        <para>Contains options that specify the Find Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Assigns the specified object’s property values to the current object’s corresponding properties.</para>
      </summary>
      <param name="options">The object whose property values to assign to the current object’s corresponding properties.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind.Location">
      <summary>
        <para>Gets or sets whether the Find Panel is located at the control’s top edge or integrated into the tab panel (in the Office View only).</para>
      </summary>
      <value>A value that specifies the Find Panel location.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.PGridOptionsFind.MinWidth">
      <summary>
        <para>Gets or sets the Find Panel‘s minimum width (in the Office View only).</para>
      </summary>
      <value>An integer value that specifies the Find Panel’s minimum width.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.PGridOptionsMenu">
      <summary>
        <para>Contains options that affect the display of context menus in a <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.PGridOptionsMenu.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.PGridOptionsMenu"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions">
      <summary>
        <para>Contains options that specify property markers (small rectangles displayed next to property editors).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.#ctor(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions"/> class with specified settings.</para>
      </summary>
      <param name="row">The row that uses these options.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.AllowBrick">
      <summary>
        <para>Gets or sets whether the row displays a property marker (a small rectangle next to the property editor).</para>
      </summary>
      <value>true if the row displays a property marker; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.BackColor">
      <summary>
        <para>Gets the property marker background color.</para>
      </summary>
      <value>A structure that specifies a color.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.BorderColor">
      <summary>
        <para>Gets or sets the property marker border color.</para>
      </summary>
      <value>A structure that specifies a color.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.DefaultValueColor">
      <summary>
        <para>Gets or sets the property marker color when the property value is modified.</para>
      </summary>
      <value>A structure that specifies a color.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.ModifiedValueColor">
      <summary>
        <para>Gets or sets the property marker color when the property is set to its default value.</para>
      </summary>
      <value>A structure that specifies a color.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowBrickOptions.ReadOnlyValueColor">
      <summary>
        <para>Gets or sets the property marker color when the property cannot be modified.</para>
      </summary>
      <value>A structure that specifies a color.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.RowOperation">
      <summary>
        <para>Serves as the base for classes, performing operations on rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowOperation.CanContinueIteration(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns a value indicating whether the iteration must be stopped.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the processed row.</param>
      <returns>true to continue iteration; false to stop iteration.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowOperation.Execute(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Performs the operation on the visited row.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the processed row against which the operation is performed.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowOperation.Init">
      <summary>
        <para>Performs specific actions before recursion starts.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowOperation.NeedsFullIteration">
      <summary>
        <para>Gets a value indicating whether all rows or only parent rows are processed.</para>
      </summary>
      <value>true if all rows must be processed by the operation; false if only rows that have children are to be processed.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowOperation.NeedsVisitChildren(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns a value specifying whether the operation is performed on the specified row’s children.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing the processed row whose child rows are to be visited.</param>
      <returns>true if the operation must be performed on the specified row’s children; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowOperation.NeedsVisitUnloadedRows">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowOperation.Release">
      <summary>
        <para>Performs actions after a recursion has finished.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.RowProperties">
      <summary>
        <para>Represents settings common to all row items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.#ctor(System.String)">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object with the specified data field name.</para>
      </summary>
      <param name="fFieldName">A <see cref="T:System.String"/> value specifying the name of a data field. This value is assigned to the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AccessibleDescription">
      <summary>
        <para>Gets or sets the object’s description used by accessibility client applications.</para>
      </summary>
      <value>The object’s description used by accessibility client applications.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AccessibleName">
      <summary>
        <para>Gets or sets the object’s name used by accessibility client applications.</para>
      </summary>
      <value>The object’s name used by accessibility client applications.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AccessibleRole">
      <summary>
        <para>Gets or sets the object’s accessible role.</para>
      </summary>
      <value>The object’s accessible role.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AllowEdit">
      <summary>
        <para>Gets or sets whether end-users are allowed to invoke cell editors for the current row.</para>
      </summary>
      <value>true if end-users are allowed to invoke cell editors for the current row; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AllowSortOnClick">
      <summary>
        <para>Gets or sets whether users can click the row header to sort data.</para>
      </summary>
      <value><see cref="F:DevExpress.Utils.DefaultBoolean.False"/> if users cannot click on the row header to sort data in the control; <see cref="F:DevExpress.Utils.DefaultBoolean.Default"/> if this behavior depends on the control’s <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowSortOnClick"/> option.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.AnnotationAttributes">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.AssignTo(DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Copies the current row‘s settings to the specified row item.</para>
      </summary>
      <param name="destinationRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object or a descendant, representing the row item into which the settings are copied.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.BaseName">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Bindable">
      <summary>
        <para>Gets a value indicating whether the corresponding row can display data from a data source field.</para>
      </summary>
      <value>true if the row can be bound to a data source field; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.CanDrawEditorBorder">
      <summary>
        <para>Returns whether to draw borders around row items.</para>
      </summary>
      <returns>A value that specifies whether to draw borders around row items.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Caption">
      <summary>
        <para>Gets or sets the row caption.</para>
      </summary>
      <value>A string value that specifies the row caption.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.CustomizationCaption">
      <summary>
        <para>Gets or sets the row’s caption when its header is displayed within the Customization Form.</para>
      </summary>
      <value>A string that specifies the row’s caption when its header is displayed within the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.DisplayFormat">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.DrawEditorBorder">
      <summary>
        <para>Gets or sets whether to draw borders around row items.</para>
      </summary>
      <value>A value that specifies whether to draw borders around row items. The null value is equivalent to false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FieldName">
      <summary>
        <para>Gets or sets the name of the data source field to bind to a row item.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying the name of the data field bound to a row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FilterInfo">
      <summary>
        <para>Gets or sets an object specifying the filter applied to the current row.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Rows.VGridRowFilterInfo object specifying the filter applied to the row owning the current properties.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.FilterMode">
      <summary>
        <para>Gets or sets whether display or edit values of this row used to filter the control’s data.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraGrid.ColumnFilterMode"/> enumeration value that specifies whether display or edit values of this row are used to filter the control’s data.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Format">
      <summary>
        <para>Gets the object that specifies the formatting applied to row values.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.FormatInfo"/> object that specifies formatting settings for row values.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.GetActualImage(System.Func{DevExpress.Utils.Design.ISvgPaletteProvider})">
      <summary>
        <para>Returns the actual image displayed in the row item’s header.</para>
      </summary>
      <param name="paletteGetter">A function that returns a provider of palettes used to colorize vector images.</param>
      <returns>An Image object specifying the image displayed in the row item’s header.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.GetActualImageWidth">
      <summary>
        <para>Returns the actual width of the image displayed in the row item’s header.</para>
      </summary>
      <returns>An integer value that specifies the image width.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ImageIndex">
      <summary>
        <para>Gets or sets the index of the image displayed within a row item header.</para>
      </summary>
      <value>An integer value representing the zero-based index of the image displayed within a row item header.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ImageOptions">
      <summary>
        <para>Provides access to settings that allow you to set up raster and vector icons for this row item’s header.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Rows.BaseRowImageOptions object that stores image-related options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Images">
      <summary>
        <para>Gets the source of the images displayed within row headers.</para>
      </summary>
      <value>An object which represents the source of the images displayed within row headers.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.IsLoading">
      <summary>
        <para>Gets whether the object is being initialized.</para>
      </summary>
      <value>true if the object is being initiailized; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.IsSourceReadOnly">
      <summary>
        <para>Gets whether the data field corresponding to the current editor row is read only in the data source.</para>
      </summary>
      <value>true if the data field is read only in the data source.; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.OptionsFilter">
      <summary>
        <para>Provides access to filter options specific to the current row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter"/> object representing a set of filter options specific to the current row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Padding">
      <summary>
        <para>Gets or sets padding for row items.</para>
      </summary>
      <value>Padding for row items.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ReadOnly">
      <summary>
        <para>Gets or sets whether the current editor row is read only.</para>
      </summary>
      <value>true if the current editor row is read only ; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.ResetPadding">
      <summary>
        <para>Sets the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Padding"/> property to Padding.Empty.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Row">
      <summary>
        <para>Gets an object representing the row to which a row belongs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant object containing a row item.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.RowEdit">
      <summary>
        <para>Gets or sets the repository item that specifies the editor used to edit a row item cell values.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.RowEditName">
      <summary>
        <para>Gets or sets the name of the row item’s editor.</para>
      </summary>
      <value>A string value specifying the editor’s name.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.RowHandle">
      <summary>
        <para>Gets the position of the associated field within a data source.</para>
      </summary>
      <value>An integer value representing the zero-based index of the corresponding field within a data source.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.RowType">
      <summary>
        <para>Gets the type of data represented by a row item.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object indicating the row item’s data type.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.SearchTags">
      <summary>
        <para>Gets or sets a comma-separated list of tags by which the find panel can find this row.</para>
      </summary>
      <value>A string value that specifies a comma-separated list of tags by which the find panel can find this row.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.ShouldSerializePadding">
      <summary>
        <para>Returns whether the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Padding"/> property should be serialized.</para>
      </summary>
      <returns>true if the <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Padding"/> property should be serialized; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ShowCaption">
      <summary>
        <para>Gets or sets whether to show the row caption.</para>
      </summary>
      <value>true to show the row caption; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ShowUnboundExpressionMenu">
      <summary>
        <para>Gets or sets whether an end-user can open an Expression Editor for the current unbound row, using a context menu.</para>
      </summary>
      <value>true if an end-user can open an Expression Editor for the current unbound row, using a context menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.SortIndex">
      <summary>
        <para>Gets or sets the row’s index in the collection of rows by which data in the control is sorted.</para>
      </summary>
      <value>The row’s index in the collection of rows by which data in the control is sorted; -1 if data is not sorted by this row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.SortOrder">
      <summary>
        <para>Gets or sets whether the row data is sorted in ascending or descending order.</para>
      </summary>
      <value>A row sort order.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.ToolTip">
      <summary>
        <para>Gets or sets a custom tooltip for the current row header.</para>
      </summary>
      <value>A string, which is a custom tooltip for the row header.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.UnboundDataType">
      <summary>
        <para>Allows you to make the row unbound, and specify the type of data it stores.</para>
      </summary>
      <value>The type of data to store in the unbound row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.UnboundDataTypeName">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.UnboundExpression">
      <summary>
        <para>Gets or sets an expression used to evaluate values for the current unbound row.</para>
      </summary>
      <value>A string that specifies an expression used to evaluate values for the current row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.UnboundType">
      <summary>
        <para>Specifies whether this row is unbound, and if so, the type of data it stores.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.UnboundColumnType"/> enumeration value that specifies the row’s data type and binding mode.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Value">
      <summary>
        <para>Gets or sets the value of a row item’s data cell when the grid is in Unbound Mode.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> representing the value associated with a row item.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.RowProperties.XtraShouldSerializeValue">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.RowSortOrder">
      <summary>
        <para>Lists values specifying the sort orders.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.Rows.RowSortOrder.Ascending">
      <summary>
        <para>Reserved for future use. Sorts row cells in ascending order.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.Rows.RowSortOrder.Descending">
      <summary>
        <para>Reserved for future use. Sorts row cells in descending order.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.Rows.RowSortOrder.None">
      <summary>
        <para>Reserved for future use. Cancels the sorting applied.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.SeparatorKind">
      <summary>
        <para>Contains values specifying the type of item cell separators.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.Rows.SeparatorKind.String">
      <summary>
        <para>Specifies that cell separators have a custom string representation defined by the <see cref="P:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow.SeparatorString"/> property. In this case the Separator style is used to control the look and feel of separator elements.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.Rows.SeparatorKind.VertLine">
      <summary>
        <para>Specifies that a cell separator is displayed as a vertical line. The separator style is specified by the VertLine style, which affects the look and feel of all vertical lines within your grid control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm">
      <summary>
        <para>Represents the grid’s Customization Form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm.CategoryItemWidth">
      <summary>
        <para>Gets the width of category row headers displayed on the Categories page of the Customization Form.</para>
      </summary>
      <value>An integer value representing the width of category row headers displayed on the Categories page of the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm.PressedRow">
      <summary>
        <para>Gets or sets a row whose header is dragged from the Customization Form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant representing a row whose header is pressed in the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm.RightToLeft">
      <summary>
        <para>Gets or sets whether the form’s visual elements are aligned to support locales using right-to-left fonts.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.RightToLeft"/> enumeration value that specifies whether the form’s visual elements are aligned to support locales using right-to-left fonts.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm.RowItemWidth">
      <summary>
        <para>Represents the width of row headers displayed on the Rows page of the Customization Form.</para>
      </summary>
      <value>An integer value representing the width of row headers displayed on the Rows page of the Customization Form.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm.UpdateSize">
      <summary>
        <para>Updates the size of the Customization Form.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind">
      <summary>
        <para>Contains options controlling the view and behavior settings of the Find Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings of the object passed as this method’s parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.ClearFindOnClose">
      <summary>
        <para>Gets or sets whether the Find Panel‘s search (filter) string is cleared when the Find Panel is hidden.</para>
      </summary>
      <value>true if the Find Panel’s search string is cleared on hiding the panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.Condition">
      <summary>
        <para>Gets or sets the operator (Starts With, Contains, etc.) used to evaluate words in cells against keywords in the Find Panel.</para>
      </summary>
      <value>Specifies the filter condition.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.FindDelay">
      <summary>
        <para>Gets or sets the delay, in milliseconds, after which a data search is initiated (if automatic search mode is active).</para>
      </summary>
      <value>An integer that specifies the delay, in milliseconds, after which a data search is initiated.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.FindFilterColumns">
      <summary>
        <para>Gets or sets the field names against which searches are performed by the Find Panel.</para>
      </summary>
      <value>A string specifying the field names against which searches are performed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.FindMode">
      <summary>
        <para>Gets or sets whether a data search starts automatically, or must be started manually.</para>
      </summary>
      <value>A value that specifies whether a data search starts automatically, or must be started manually.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.FindNullPrompt">
      <summary>
        <para>Gets or sets the text that is displayed grayed out when the search box is empty.</para>
      </summary>
      <value>The text that is displayed grayed out when the search box is empty.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.HighlightFindResults">
      <summary>
        <para>Gets or sets whether the search string is highlighted within located records.</para>
      </summary>
      <value>true if the search string is highlighted within located records; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.ParserKind">
      <summary>
        <para>Gets or sets whether a search query in the Find Panel is treated as a whole, or as separate keywords for a logical conjunction or disjunction.</para>
      </summary>
      <value>A FindPanelParserKind enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.ShowClearButton">
      <summary>
        <para>Gets or sets whether the Clear button is displayed within the Find Panel.</para>
      </summary>
      <value>true if the Clear button is displayed within the Find Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.ShowCloseButton">
      <summary>
        <para>Gets or sets whether the close (‘x’) button is displayed within the Find Panel.</para>
      </summary>
      <value>true if the close (‘x’) button is displayed within the Find Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.ShowFindButton">
      <summary>
        <para>Gets or sets whether the Find button is displayed within the Find Panel.</para>
      </summary>
      <value>true if the Find button is displayed within the Find Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsFind.Visibility">
      <summary>
        <para>Gets or sets whether the Find Panel can be accessed by an end-user.</para>
      </summary>
      <value>A value that specifies whether the Find Panel can be accessed by an end-user.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenu">
      <summary>
        <para>Contains a <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>‘s context menu options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenu.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenu"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenu.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenu.EnableContextMenu">
      <summary>
        <para>Gets or sets whether users can invoke a context menu with a right-click on a property.</para>
      </summary>
      <value>true if users can invoke a context menu with a right-click on a property; otherwise, false. Since version 17.2, the default is true.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenu.ShowConditionalFormattingItem">
      <summary>
        <para>Gets or sets whether the control displays the Conditional Formatting sub-menu in a row’s context menu. Conditional formatting allows users to apply appearance settings to rows based on cell values.</para>
      </summary>
      <value>true if the control displays the Conditional Formatting sub-menu in a row’s context menu; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenuEx">
      <summary />
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenuEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenuEx"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenuEx.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary />
      <param name="options"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsMenuEx.EnableRecordHeaderMenu">
      <summary>
        <para>Gets or sets whether a right-click on a record header invokes a context menu.</para>
      </summary>
      <value>true, to allow a context menu for a record header; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow">
      <summary>
        <para>Provides row options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowFocus">
      <summary>
        <para>Gets or sets whether end-users can move focus to the row using either the mouse or keyboard.</para>
      </summary>
      <value>true if end-users can move focus to the row; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowHtmlText">
      <summary>
        <para>Gets or sets whether a row caption can be formatted using HTML tags.</para>
      </summary>
      <value>True if a row caption is formatted using HTML tags; False if not; Default uses the VGridControl.OptionsView.AllowHtmlText setting.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowMove">
      <summary>
        <para>Gets or sets whether end-users can drag the row’s header.</para>
      </summary>
      <value>true if end-users can drag the row’s header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowMoveToCustomizationForm">
      <summary>
        <para>Gets or sets whether end-users can drag the row’s header to the Customization Form.</para>
      </summary>
      <value>true to allow end-users to drag the row’s header to the Customization Form; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowSize">
      <summary>
        <para>Gets or sets whether end-users can drag the bottom edge of a row’s header to change the row’s height.</para>
      </summary>
      <value>true if end-users can change the height of rows; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.DblClickExpanding">
      <summary>
        <para>Gets or sets whether a parent row is expanded after its header has been double clicked.</para>
      </summary>
      <value>true to allow a parent row to be expanded by a double click on its header; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.ShowInCustomizationForm">
      <summary>
        <para>Gets or sets whether the row’s header is displayed within the Customization Form when the row is hidden.</para>
      </summary>
      <value>true if the row’s header is displayed within the Customization Form when the row is hidden; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter">
      <summary>
        <para>Contains filter options for individual rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.AllowFilter">
      <summary>
        <para>Gets or sets whether the filter button is displayed in the row header.</para>
      </summary>
      <value>true if the row’s filter button is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Assigns property values of the specified <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter"/> object to the corresponding properties of this object.</para>
      </summary>
      <param name="options">An object whose property values are copied to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.ImmediateUpdatePopupExcelFilter">
      <summary>
        <para>Gets or sets whether filter conditions immediately apply to data, or only when the Excel-style Filter Dropdown closes.</para>
      </summary>
      <value>True or Default, if filter conditions immediately apply to data; otherwise, False.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.PopupExcelFilterDateTimeValuesTabFilterType">
      <summary>
        <para>Gets or sets whether date-time values are arranged in an outline view or a list in the Excel style pop-up filter menus.</para>
      </summary>
      <value>An DevExpress.XtraVerticalGrid.ExcelFilterDateTimeValuesTabFilterType enumeration value that specifies whether date-time values are arranged in an outline view or a list in the Excel style pop-up filter menus.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.PopupExcelFilterDefaultTab">
      <summary>
        <para>Gets or sets whether the tab with a list of available data values or a list of predefined filter conditions is active by default in the Excel-style Filter Dropdown.</para>
      </summary>
      <value>An DevExpress.XtraVerticalGrid.ExcelFilterDefaultTab enumeration value that specifies whether the ‘Values’ or ‘Filters’ tab opens by default in the Excel-style Filter Dropdown. By Default, the opened tab depends on the column data type.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.PopupExcelFilterEnumFilters">
      <summary>
        <para>If the current row contains enumeration data, gets or sets whether to show relational (equality and inequalities) filter conditions in the Excel-style Filter Dropdown.</para>
      </summary>
      <value>An DevExpress.XtraVerticalGrid.ExcelFilterEnumFilters enumeration value that specifies whether to show relational (equality and inequalities) filter conditions in the Excel-style Filter Dropdown. Default, if the ‘Filters’ tab is hidden.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.PopupExcelFilterGrouping">
      <summary>
        <para>Gets or sets data field (row) names separated by a comma, semicolon, space or tab character by which filter values are grouped in the column’s filter menu.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value specifying data field names separated by a comma, semicolon, space or tab character by which filter values are grouped in the column’s filter menu.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.PopupExcelFilterNumericValuesTabFilterType">
      <summary>
        <para>If the current row contains numeric data, gets or sets whether to display a list of available data values or a range track bar in the ‘Values’ tab of the Excel-style Filter Dropdown.</para>
      </summary>
      <value>The value that specifies the type of filtering UI in the ‘Values’ tab in the filter dropdown.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.PopupExcelFilterTextFilters">
      <summary>
        <para>Gets or sets whether to show pattern-matching (e.g., Is Like) and relational (e.g., Greater Than) operators for string values in the Excel style pop-up filter menus.</para>
      </summary>
      <value>AllFilters, to show relational and pattern-matching operators in the Excel style pop-up filter menus; otherwise, TextFilters or Default.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.Reset">
      <summary>
        <para>Resets all options to their default values.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.ShowBlanksFilterItems">
      <summary>
        <para>Gets or sets whether the filter dropdown displays filter conditions that select blank and not blank data values.</para>
      </summary>
      <value>Default or True if  the filter dropdown displays filter conditions that select blank and not blank data values; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRowFilter.ShowEmptyDateFilter">
      <summary>
        <para>If the current row contains dates, gets or sets whether the filter dropdown displays a filter condition that selects the empty (null) and not empty (not null) data values.</para>
      </summary>
      <value>true, to show the filter for empty values; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus">
      <summary>
        <para>Represents a set of selection and focus options for the vertical grid.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings of the object passed as this method’s parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.EnableAppearanceFocusedRow">
      <summary>
        <para>Gets or sets whether the appearance settings used to paint the header of the focused row are enabled.</para>
      </summary>
      <value>true if the appearance settings are enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.EnableAppearanceSelectedRowHeader">
      <summary>
        <para>Gets or sets whether or not the appearance settings used to paint the header of the selected row are enabled.</para>
      </summary>
      <value>true if the appearance settings are enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelect">
      <summary>
        <para>Gets or sets whether selection of multiple records, rows and cells is enabled.</para>
      </summary>
      <value>true if selection of multiple records, rows and cells is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelectMode">
      <summary>
        <para>Gets or sets whether multiple records, rows or cells can be selected.</para>
      </summary>
      <value>A value that specifies whether multiple records, rows or cells can be selected.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocusEx">
      <summary>
        <para>Contains selection and focus options for the vertical grid.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocusEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocusEx"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocusEx.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary />
      <param name="options"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocusEx.EnableAppearanceFocusedAndSelectedRecordHeader">
      <summary>
        <para>Gets or sets whether to enable the appearance settings used to paint headers of the focused and selected records.</para>
      </summary>
      <value>true if the appearance settings are enabled; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridRows">
      <summary>
        <para>Represents a collection of rows within the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.Add(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Adds a specific row to the end of the rows collection and returns its index.</para>
      </summary>
      <param name="row">An instance of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> class descendant representing the row to add.</param>
      <returns>The zero-based index at which a specific row object has been added to the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.AddCategoryRow(System.String)">
      <summary />
      <param name="caption"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.AddEditorRow(System.String)">
      <summary />
      <param name="fieldName"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.AddMultiEditorRow(System.String[])">
      <summary />
      <param name="fieldNames"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.AddRange(DevExpress.XtraVerticalGrid.Rows.BaseRow[])">
      <summary>
        <para>Adds an array of row objects to the rows collection.</para>
      </summary>
      <param name="rows">An array of <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> derived objects to be added to the collection of rows.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.ColumnByFieldName(System.String)">
      <summary>
        <para>Returns a row specified by its bound field name in the data source.</para>
      </summary>
      <param name="fieldName">A string value representing the row’s bound field name.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object specifying the row bound to the specified field. null (Nothing in Visual Basic) if the collection has no row bound to the field.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.Count">
      <summary>
        <para>Gets the number of elements in the collection.</para>
      </summary>
      <value>An integer value that specifies the number of elements contained in the collection.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.DestroyRows">
      <summary>
        <para>Releases all resources used by the rows in the current collection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.FirstVisible">
      <summary>
        <para>Gets the first row from the collection whose <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property is true.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the first row in the collection whose <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property is true.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.GetEnumerator">
      <summary>
        <para>Returns an enumerator that iterates through the collection.</para>
      </summary>
      <returns>An IEnumerator&lt;BaseRow&gt; object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.GetRowByFieldName(System.String)">
      <summary>
        <para>Gets a row by the name of the field to which it’s bound.</para>
      </summary>
      <param name="fieldName">A <see cref="T:System.String"/> value which specifies the name of the field in the data source.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row bound to the data source field with the specified name. null (Nothing in Visual Basic) if no row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.GetRowByFieldName(System.String,System.Boolean)">
      <summary>
        <para>Gets a row by the name of the field to which it’s bound.</para>
      </summary>
      <param name="fieldName">A <see cref="T:System.String"/> value which specifies the name of the field in the data source.</param>
      <param name="recursive">true to process the child rows that reside at greater nesting levels; false to process only the rows within the current rows collection.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row bound to the data source field with the specified name. null (Nothing in Visual Basic) if no row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.GetRowPropertiesByFieldName(System.String)">
      <summary>
        <para>Returns row properties based on a data source field name of the required row.</para>
      </summary>
      <param name="fieldName">A string value specifying the filed name of the required row in the data source.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object of the required row.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.Grid">
      <summary>
        <para>Gets or sets the vertical grid control that owns the rows collection.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> object which represents the vertical grid control which the rows collection belongs to.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.HasVisibleItems">
      <summary>
        <para>Returns true if visible rows exist.</para>
      </summary>
      <value>true if visible rows exist; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.IndexOf(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Searches for a specific row object and returns the zero-based index of the first occurrence within the rows collection.</para>
      </summary>
      <param name="Row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> derived object representing the row to locate within the collection.</param>
      <returns>The zero-based index of the first occurrence of a specific row within the rows collection, if found; otherwise -1.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.Insert(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Inserts a specific row object into the collection at the specified position.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> class descendant representing the row object to insert into the rows collection.</param>
      <param name="index">The zero-based location index where a specific row object should be inserted.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.IsEmpty">
      <summary>
        <para>Gets whether the collection is empty.</para>
      </summary>
      <value>true if the collection is empty; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.IsSelected">
      <summary>
        <para>Returns whether the current collection contains a selected row.</para>
      </summary>
      <returns>true if the current collection contains a selected row; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.Item(System.Int32)">
      <summary>
        <para>Gets a row object from the rows collection at the specified position.</para>
      </summary>
      <param name="index">An integer value specifying the desired row’s zero-based index.</param>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing a row located at a specific position within the rows collection.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.Item(System.String)">
      <summary>
        <para>Gets a row from the rows collection by its name.</para>
      </summary>
      <param name="nameOrFieldName">A string value specifying the name of the desired row.</param>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing a row with the required name. null (Nothing in Visual Basic) if there are no rows with the specified name in the collection.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.Iterator">
      <summary>
        <para>Provides access to the Rows Iterator.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRowsIterator"/> object that represents the Rows Iterator.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.LastVisible">
      <summary>
        <para>Gets the last row from the collection whose <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property is true.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the last row whose <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property is true.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.Remove(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Removes the specified row from the collection.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing the row to remove. If the row is not found within the collection, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRows.RowByName(System.String)">
      <summary>
        <para>Returns a row specified by its name.</para>
      </summary>
      <param name="columnName">A string value specifying the row’s name.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object representing a row with the specified name. null (Nothing in Visual Basic) if the collection doesn’t contain a column with the specified name.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.Rows.VGridRows.VisibleRowsCount">
      <summary>
        <para>Gets the number of rows whose <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property is true.</para>
      </summary>
      <value>An integer value representing the number of collection members whose <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Visible"/> property is true.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.Rows.VGridRowsIterator">
      <summary>
        <para>Enables you to perform operations over the predefined rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRowsIterator.DoLocalOperation(DevExpress.XtraVerticalGrid.Rows.RowOperation,DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Performs the specified operation on the specified row and its child rows.</para>
      </summary>
      <param name="operation">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowOperation"/> object representing the operation to perform.</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object to be processed.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRowsIterator.DoLocalOperation(DevExpress.XtraVerticalGrid.Rows.RowOperation,DevExpress.XtraVerticalGrid.Rows.VGridRows)">
      <summary>
        <para>Performs the specified operation on the collection of rows (and their child rows), specified by the parameter.</para>
      </summary>
      <param name="operation">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowOperation"/> object representing the operation to perform.</param>
      <param name="rows">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRows"/> object representing the collection of rows across which the operation is performed.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.Rows.VGridRowsIterator.DoOperation(DevExpress.XtraVerticalGrid.Rows.RowOperation)">
      <summary>
        <para>Performs the specified operation across all rows within a vertical grid control.</para>
      </summary>
      <param name="operation">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowOperation"/> descendant representing the operation to perform.</param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.ScrollVisibility">
      <summary>
        <para>Lists values specifying the availability of control’s elements that enable data scrolling.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ScrollVisibility.Auto">
      <summary>
        <para>The scrolling element is visible only when data scrolling can be performed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ScrollVisibility.Both">
      <summary>
        <para>Horizontal and vertical scrolling elements are always visible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ScrollVisibility.Horizontal">
      <summary>
        <para>The scrolling element is only visible when horizontal scrolling can be performed. The vertical scroll bar is invisible in this case.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ScrollVisibility.Never">
      <summary>
        <para>The scrolling element is invisible regardless of whether data scrolling can be performed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ScrollVisibility.Vertical">
      <summary>
        <para>The scrolling element is only visible when vertical scrolling can be performed. The horizontal scroll bar is invisible in this case.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.ShowButtonModeEnum">
      <summary>
        <para>Lists values that specify the cells that display editor buttons.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ShowButtonModeEnum.ShowAlways">
      <summary>
        <para>Editor buttons are displayed for all cells within the control</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ShowButtonModeEnum.ShowForFocusedCell">
      <summary>
        <para>Editor buttons are displayed for the focused cell only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ShowButtonModeEnum.ShowForFocusedRecord">
      <summary>
        <para>Editor buttons are displayed for cells that reside within the focused record.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ShowButtonModeEnum.ShowForFocusedRow">
      <summary>
        <para>Editor buttons are displayed for cells that reside within the focused row.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.ShowButtonModeEnum.ShowOnlyInEditor">
      <summary>
        <para>Editor buttons are displayed only when a cell editor is active.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraVerticalGrid.StyleFormatConditions">
      <summary>
        <para>Contains classes that specify rules based on which appearance settings are applied to grid rows.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule">
      <summary>
        <para>Specifies a rule that contains a condition and appearance settings applied to a row if the condition is met.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.ApplyToRecord">
      <summary>
        <para>Gets or sets whether to apply appearance settings to all rows in the record.</para>
      </summary>
      <value>true to apply appearance settings to all rows in the record; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.Assign(DevExpress.XtraEditors.FormatRuleBase)">
      <summary>
        <para>Assigns the specified object’s property values to the current object’s corresponding properties.</para>
      </summary>
      <param name="source">The object whose property values to assign to the current object’s corresponding properties.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.IsFit(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Returns whether the current format rule evaluates to “true” for the specified cell.</para>
      </summary>
      <param name="row">The row to which the cell belongs.</param>
      <param name="recordIndex">The index of the record to which the cell belongs.</param>
      <returns>true if the current rule is true for the specified cell; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.IsValid">
      <summary>
        <para>Gets whether the rule can be applied.</para>
      </summary>
      <value>true if the rule can be applied; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.RowProperties">
      <summary>
        <para>Gets or sets a row that contains values against which the rule’s condition is evaluated and to which the format is applied.</para>
      </summary>
      <value>An object that is a set of properties that describe a grid row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.RowPropertiesApplyTo">
      <summary>
        <para>Gets or sets the row to which the format is applied if the rule’s condition is met. Allows you to evaluate the condition against one row but apply the format to another row.</para>
      </summary>
      <value>An object that is a set of properties that describe a grid row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule.VGrid">
      <summary>
        <para>Gets the Vertical Grid control that owns this rule.</para>
      </summary>
      <value>The Vertical Grid control that owns this rule.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRuleCollection">
      <summary>
        <para>A collection of <see cref="T:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRuleCollection.#ctor(DevExpress.XtraVerticalGrid.VGridControlBase)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRuleCollection"/> class with specified settings.</para>
      </summary>
      <param name="vGridControl"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRuleCollection.VGrid">
      <summary>
        <para>Gets the Vertical Grid control that owns this collection.</para>
      </summary>
      <value>An object that specifies a Vertical Grid control.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.TreeButtonStyle">
      <summary>
        <para>Lists values specifying the appearance of the category row’s tree button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.TreeButtonStyle.Default">
      <summary>
        <para>Either the Explorer Button, displayed on the right, or the Tree Button, displayed on the left, depending on the current paint scheme.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.TreeButtonStyle.ExplorerBar">
      <summary>
        <para>The Explorer Button, displayed on the right (the appearance is dependent on the current paint scheme):</para>
        <para>“DevExpress Style” skin:</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.TreeButtonStyle.TreeView">
      <summary>
        <para>The Tree Button, displayed on the left (the appearance is dependent on the current paint scheme):</para>
        <para>“DevExpress Style” skin:</para>
        <para>Office2003 style:</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.TreeButtonType">
      <summary>
        <para>Lists values that specify a tree button’s type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.TreeButtonType.ExplorerBarButton">
      <summary>
        <para>A tree button is displayed as an explorer button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.TreeButtonType.TreeViewButton">
      <summary>
        <para>A tree button is displayed as a standard tree view button.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollection">
      <summary>
        <para>Provides the appearance settings used to paint elements in a vertical grid.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.#ctor(DevExpress.XtraVerticalGrid.VGridControlBase)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollection"/> class with the default settings.</para>
      </summary>
      <param name="grid">A <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> control which will become the owner of the created collection.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.BandBorder">
      <summary>
        <para>Provides appearance settings used to paint the borders of a Band.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.Caption">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.Category">
      <summary>
        <para>Provides appearance settings used to paint Category Rows.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.CategoryExpandButton">
      <summary>
        <para>Provides appearance settings used to paint Category Expand Buttons.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.DisabledRecordValue">
      <summary>
        <para>Provides appearance settings used to paint values of disabled rows.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.DisabledRow">
      <summary>
        <para>Provides appearance settings used to paint the headers of disabled rows.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.Empty">
      <summary>
        <para>Provides appearance settings used to paint the empty area of the control.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ExpandButton">
      <summary>
        <para>Provides appearance settings used to paint Expand Buttons.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.FilterPanel">
      <summary>
        <para>Provides access to the appearance settings used to paint the filter panel (shown when a filter is applied to the data, allowing end-users to customize the filter).</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that specifies the appearance settings used to paint the filter panel.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.FixedLine">
      <summary>
        <para>Provides access to the appearance settings used to paint fixed row dividers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> that provides corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.FocusedCell">
      <summary>
        <para>Provides appearance settings used to paint the currently focused cell.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.FocusedRecord">
      <summary>
        <para>Provides appearance settings used to paint cells that belong to the focused record.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.FocusedRow">
      <summary>
        <para>Provides appearance settings used to paint the header of the focused row.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.HideSelectionRow">
      <summary>
        <para>Provides appearance settings used to paint the header of the focused row when the control is not focused.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.HorzLine">
      <summary>
        <para>Provides appearance settings used to paint horizontal lines that separate the control’s rows.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.IsLoading">
      <summary>
        <para>Indicates whether the grid control that owns the current collection is being initialized.</para>
      </summary>
      <value>true if the grid control that owns the current collection is being initialized; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ModifiedRecordValue">
      <summary>
        <para>Provides appearance settings used to paint modified values in a PropertyGridControl.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ModifiedRow">
      <summary>
        <para>Provides appearance settings used to paint the headers of rows that contain modified values.</para>
        <para>This property is only in effect for the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> control.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.PressedRow">
      <summary>
        <para>Provides appearance settings used to paint the headers of pressed rows.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRecordValue">
      <summary>
        <para>Provides access to appearance settings used to paint values of read-only rows.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ReadOnlyRow">
      <summary>
        <para>Provides access to appearance settings used to paint headers of read-only rows.</para>
      </summary>
      <value>An object that contains appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.RecordValue">
      <summary>
        <para>Provides the default appearance settings used to paint values of regular rows.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.RowHeaderPanel">
      <summary>
        <para>Provides appearance settings used to paint the Row Header Panel.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.SelectedCategory">
      <summary>
        <para>Contains appearance settings used to paint selected category rows in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <value>An object that provides appearance settings used to paint selected category rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.SelectedCell">
      <summary>
        <para>Contains appearance settings used to paint selected cells in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>).</para>
      </summary>
      <value>An object that provides appearance settings used to paint selected cells.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.SelectedRecord">
      <summary>
        <para>Contains appearance settings used to paint selected records in multiple record selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RecordSelect"/>).</para>
      </summary>
      <value>An object that provides appearance settings used to paint selected cells.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.SelectedRow">
      <summary>
        <para>Contains appearance settings used to paint selected rows in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <value>An object that provides appearance settings used to paint selected cells.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.ToString">
      <summary>
        <para>Returns the textual representation of <see cref="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollection"/>.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value that is the textual representation of <see cref="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollection"/>.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollection.VertLine">
      <summary>
        <para>Provides appearance settings used to paint vertical lines that separate the control’s columns.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx">
      <summary>
        <para>Contains appearance settings for the control’s elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx.#ctor(DevExpress.XtraVerticalGrid.VGridControlBase)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx"/> class with specified settings.</para>
      </summary>
      <param name="grid"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx.FocusedRecordHeader">
      <summary>
        <para>Contains appearance settings used to paint the focused record’s header.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx.HideSelectionRecordHeader">
      <summary>
        <para>Contains appearance settings used to paint the focused record’s header when the control is not focused.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx.RecordHeader">
      <summary>
        <para>Contains appearance settings used to paint headers of regular records.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridAppearanceCollectionEx.SelectedRecordHeader">
      <summary>
        <para>Contains appearance settings used to paint the headers of selected records (see <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelect"/>).</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> that contains the corresponding appearance settings.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridControl">
      <summary>
        <para>The grid control that displays data in a tabular form with data fields rendered as rows, and records rendered as columns. See Vertical Grid.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.#ctor">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> object with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.ActiveFilter">
      <summary>
        <para>Provides access to the collection of filters applied to particular rows, and the overall filter.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.VGridFilter object representing the collection of filters applied to the rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.ActiveFilterCriteria">
      <summary>
        <para>Gets or sets the current filter criteria.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the overall filter criteria.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.ActiveFilterEnabled">
      <summary>
        <para>Gets or sets whether the filtering functionality is enabled.</para>
      </summary>
      <value>true if the filtering functionality is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.ActiveFilterInfo">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.ActiveFilterString">
      <summary>
        <para>Gets or sets the overall filter expression.</para>
      </summary>
      <value>A string that specifies the overall filter expression applied to the control.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.AddSelectionToComparison">
      <summary>
        <para>Adds the selected records to the comparison collection.</para>
      </summary>
      <returns>true if the selected records were successfully added to the comparison; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.AddToComparison(System.Int32)">
      <summary>
        <para>Adds a specific record to the comparison collection.</para>
      </summary>
      <param name="recordIndex">The index of the record to be added to the comparison.</param>
      <returns>true if the record was added successfully to the comparison; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.AddToComparison(System.Int32[])">
      <summary>
        <para>Adds records to the comparison collection.</para>
      </summary>
      <param name="recordIndices">The indices of the records to be added to the comparison.</param>
      <returns>true indicates that the records were added successfully to the comparison; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.Appearance">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.BeginSort">
      <summary>
        <para>Prevents visual and data updates until an <see cref="M:DevExpress.XtraVerticalGrid.VGridControl.EndSort"/> method call.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.CanUseSelectionForComparison">
      <summary>
        <para>Indicates whether you can add or remove the selected records from the comparison.</para>
      </summary>
      <returns>true if there is more than one selected record; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.ClearComparison">
      <summary>
        <para>Clears the comparison collection.</para>
      </summary>
      <returns>true indicates that the comparison was successfully cleared; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.ClearRowsFilter">
      <summary>
        <para>Clears data filters applied to the control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.ClearSorting">
      <summary>
        <para>Removes the sort order for all rows.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomDrawFilterPanel">
      <summary>
        <para>Enables you to paint the Filter Panel manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomDrawRecordHeader">
      <summary>
        <para>Allows you to paint record headers.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomFilterDisplayText">
      <summary>
        <para>Allows you to customize the display text representing the current filter within the filter panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomRecordDisplayText">
      <summary>
        <para>Allows you to customize the text displayed in a cell and the corresponding filter.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomRecordFilter">
      <summary>
        <para>Allows you to hide a record regardless of the applied filter.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomRecordHeaderDisplayText">
      <summary>
        <para>Allows you to assign text to individual record headers.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.CustomUnboundData">
      <summary>
        <para>Enables data to be provided to and modified data to be saved from unbound rows.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.DataMember">
      <summary>
        <para>Gets or sets a data source member whose data is supplied to the vertical grid control.</para>
      </summary>
      <value>A string value representing the data source member.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.DataSource">
      <summary>
        <para>Gets or sets the vertical grid’s data source.</para>
      </summary>
      <value>An object representing the grid’s data source.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.EndSort">
      <summary>
        <para>Enables visual and data updates after a <see cref="M:DevExpress.XtraVerticalGrid.VGridControl.BeginSort"/> method call, and forces an immediate update.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.EndSorting">
      <summary>
        <para>Fires when the control completes sorting data.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterEditorCreated">
      <summary>
        <para>Allows you to customize the filter editor before it is displayed on screen.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterExpressionEditorCreated">
      <summary>
        <para>Allows you to customize the Expression Editor used to edit custom expressions that are displayed in the control’s Filter Editor.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterPopupExcelCustomizeTemplate">
      <summary>
        <para>Allows you to customize templates used by Excel-style Filter Dropdowns and external editors generated using Filtering UI Context.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterPopupExcelData">
      <summary>
        <para>Allows you to add, remove, and modify data values and customize predefined filters in the Excel style pop-up filter menus. Filter items added manually on this event must be unique and sorted.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterPopupExcelParseFilterCriteria">
      <summary>
        <para>Allows you to parse the filter criteria applied to data and select the corresponding values in the filter menu.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterPopupExcelPrepareTemplate">
      <summary>
        <para>Allows you to replace templates used by Excel-style Filter Dropdowns and external editors generated using Filtering UI Context.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FilterPopupExcelQueryFilterCriteria">
      <summary>
        <para>Fires when a filter criteria is about to be applied to data and allows you to customize the filter criteria.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.FilterRecords">
      <summary>
        <para>Forces the control to re-filter its records according to the active data filter.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.FormatRuleExpressionEditorCreated">
      <summary />
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.FormatRules">
      <summary>
        <para>Provides access to the collection of rules that specify conditions and appearance settings applied to rows when these conditions are met.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraVerticalGrid.StyleFormatConditions.VGridFormatRule"/> objects.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.GetAllRowProperties">
      <summary />
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.GetComparisonRecords">
      <summary>
        <para>Gets the indices of records in the comparison collection.</para>
      </summary>
      <returns>An array of integers that specify the zero-based indices of the records in the comparison collection.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.GetRecordHeaderText(System.Int32)">
      <summary>
        <para>Returns the text displayed in the specified record header.</para>
      </summary>
      <param name="record">The index (visual position) of the target record.</param>
      <returns>The text displayed in the specified record header.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.HideComparison">
      <summary>
        <para>Hides the comparison.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.InvalidateFilterPanel">
      <summary>
        <para>Invalidates the filter panel, and forces it to be redrawn.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.InvalidateRecordHeader(System.Int32)">
      <summary>
        <para>Invalidates the header of the specified record, and forces it to be redrawn.</para>
      </summary>
      <param name="record">The index (visual position) of the target record.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.InvalidateRecordHeaders">
      <summary>
        <para>Invalidates headers of visible records, and forces them to be redrawn.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.IsInComparison(System.Int32)">
      <summary>
        <para>Indicates whether a specific record is in the comparison collection.</para>
      </summary>
      <param name="recordIndex">The index of the record to be tested.</param>
      <returns>true if the record is in the comparison collection; otherwise, false</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.LayoutChangedCore">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.LayoutStyle">
      <summary>
        <para>Gets or sets the Vertical Grid Control’s layout style.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.LayoutViewStyle"/> enumeration member specifying the control’s layout style.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.LocateByDisplayText(System.Int32,DevExpress.XtraVerticalGrid.Rows.EditorRow,System.String)">
      <summary>
        <para>Returns the handle of the record that displays the specified text in the specified row.</para>
      </summary>
      <param name="startRecord">An integer value that specifies the row handle from which to start searching.</param>
      <param name="row">An <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/> object that specifies the row in which to search for the specified value.</param>
      <param name="text">A string value that specifies the text to find.</param>
      <returns>An integer value that specifies the handle of the record that contains the specified text in the specified row. The DevExpress.Data.DataController.InvalidRow field value is returned, if no record is found that matches the specified conditions.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.LocateByValue(System.Int32,DevExpress.XtraVerticalGrid.Rows.EditorRow,System.Object)">
      <summary>
        <para>Returns the handle of the record that contains the specified value in the specified row.</para>
      </summary>
      <param name="startRecord">An integer value that specifies the row handle from which to start searching.</param>
      <param name="row">An <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/> object that specifies the row in which to search for the specified value.</param>
      <param name="val">An object that specifies the value to find.</param>
      <returns>An integer value that specifies the handle of the record that contains the specified value in the specified row. The DevExpress.Data.DataController.InvalidRow field value is returned, if no record is found that matches the specified conditions.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.LocateByValue(System.String,System.Object)">
      <summary>
        <para>Returns the handle of the record that contains the specified value in the specified field.</para>
      </summary>
      <param name="fieldName">A string that specifies the name of the field in which to search for the specified value.</param>
      <param name="val">An object that specifies the value to find.</param>
      <returns>An integer value that specifies the handle of the record that contains the specified value in the specified field. The DevExpress.Data.DataController.InvalidRow field value is returned, if no record is found that matches the specified conditions.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.MRUFilters">
      <summary>
        <para>Provides access to the list of most recently used filters.</para>
      </summary>
      <value>An object that stores the filters recently applied to the control’s data.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.OptionsFilter">
      <summary>
        <para>Provides access to data filter options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsFilter"/> object comprising data filter options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.OptionsHint">
      <summary>
        <para>Contains the control’s tooltip options.</para>
      </summary>
      <value>An object that contains the control’s tooltip options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.OptionsMenu">
      <summary>
        <para>Contains options that specify availability of context menus for the control’s elements.</para>
      </summary>
      <value>An object that contains options used to enable context menus for the control’s elements.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.OptionsPrint">
      <summary>
        <para>Contains options that specify how the control is printed/exported.</para>
      </summary>
      <value>An object that contains the control’s print and export options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.OptionsSelectionAndFocus">
      <summary>
        <para>Contains the control’s selection and focus options.</para>
      </summary>
      <value>An object that contains the control’s selection and focus options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.OptionsView">
      <summary>
        <para>Provides access to the vertical grid’s display options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsView"/> object that specifies the grid’s display options.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.QueryCustomFunctions">
      <summary>
        <para>Allows you to add custom-function-based filters (for example, ‘discount is more than 15%’) to Excel-style pop-up filter menus and/or the filter editor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.RecordCount">
      <summary>
        <para>Gets the number of records in the control, fitting the currently applied data filter.</para>
      </summary>
      <value>An integer value that specifies the number of records in the control that fit the currently applied data filter.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.RecordCountChanged">
      <summary>
        <para>Occurs when the number of visible records is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.RecordHeaderFormat">
      <summary>
        <para>Gets or sets the format string used to generate header text for the control’s records.</para>
      </summary>
      <value>The record header format string.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControl.RecordHeaderHeight">
      <summary>
        <para>Gets or sets the height of record headers.</para>
      </summary>
      <value>The height of record headers.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.RecordUpdated">
      <summary>
        <para>Occurs after the modified focused record has been saved to the underlying data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.RefreshDataSource">
      <summary>
        <para>Updates the grid to reflect changes made to a data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.RemoveFromComparison(System.Int32)">
      <summary>
        <para>Removes a specific record from the comparison collection.</para>
      </summary>
      <param name="recordIndex">The index of the record to be removed from the comparison.</param>
      <returns>true if the record was successfully removed from the comparison; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.RemoveFromComparison(System.Int32[])">
      <summary>
        <para>Removes records from the comparison collection.</para>
      </summary>
      <param name="recordIndices">An array of integer values that specify the indices of the records to be removed from the comparison.</param>
      <returns>true if the records were successfully removed from the comparison; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.RemoveSelectionFromComparison">
      <summary>
        <para>Removes the selected records from the comparison collection.</para>
      </summary>
      <returns>true if the selected records were removed from the comparison; otherwise, false</returns>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.RowFilterChanged">
      <summary>
        <para>Occurs when a row’s filter condition is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.ShowComparison">
      <summary>
        <para>Displays the selected records in a side-by-side comparison</para>
      </summary>
      <returns>true if the comparison is displayed; otherwise, false</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.ShowFilterEditor(DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Invokes the filter editor, that allows an end-user to change the filter condition currently applied to the control’s data.</para>
      </summary>
      <param name="properties">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object identifying the row for which the opened filter editor pre-creates a filter condition. null/Nothing to invoke the filter editor without a pre-created filter.</param>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.ShowFilterPopupExcel">
      <summary>
        <para>Allows you to hide specific filter conditions from the ‘Filters’ tab of the Excel-style Filter Dropdown.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.ShowUnboundExpressionEditor(DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Invokes an Expression Editor that enables editing an expression for the specified unbound column.</para>
      </summary>
      <param name="properties">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object that identifies the row for which the Expression Editor should be created.</param>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.StartSorting">
      <summary>
        <para>Fires when the control starts to sort data.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControl.UnboundExpressionEditorCreated">
      <summary>
        <para>Fires after an Expression Editor has been created for an unbound column.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridControl.VGridIDataColumnInfoWrapper">
      <summary />
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControl.VGridIDataColumnInfoWrapper.#ctor(DevExpress.XtraVerticalGrid.VGridControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl.VGridIDataColumnInfoWrapper"/> class with specified settings.</para>
      </summary>
      <param name="vGrid"></param>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridControlBase">
      <summary>
        <para>Serves as a base for the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> and <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/> classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.About">
      <summary>
        <para>Activates the vertical grid’s About dialog box.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.AccessibleNotifyClients(System.Windows.Forms.AccessibleEvents,DevExpress.Accessibility.BaseAccessible)">
      <summary>
        <para>Notifies accessibility client applications of the specified System.Windows.Forms.AccessibleEvents event for the specified child control.</para>
      </summary>
      <param name="accEvents">An System.Windows.Forms.AccessibleEvents event being fired.</param>
      <param name="acc">An object that identifies a control to notify of the accessible event.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.AccessibleNotifyClients(System.Windows.Forms.AccessibleEvents,System.Int32)">
      <summary>
        <para>Notifies accessibility client applications of the specified System.Windows.Forms.AccessibleEvents event for the specified child control.</para>
      </summary>
      <param name="accEvents">An System.Windows.Forms.AccessibleEvents event being fired.</param>
      <param name="childID">A child System.Windows.Forms.Control which fires the event.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ActiveEditor">
      <summary>
        <para>Gets a control’s active editor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> object that represents the currently active editor. null (Nothing in Visual Basic) if no cell is being edited at the moment.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ActiveEditorViewInfo">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.AddNewRecord">
      <summary>
        <para>Adds a new record.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.Appearance">
      <summary>
        <para>Provides access to the properties that control the appearance of the vertical grid’s elements.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridAppearanceCollection"/> object which provides the appearance settings for the vertical grid’s elements.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ApplyFindFilter(System.String)">
      <summary>
        <para>Fills in the Find Panel with the specified query and applies it.</para>
      </summary>
      <param name="filter">A string value that specifies the query.</param>
      <returns>true if the query is successfully applied; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.BandCount">
      <summary>
        <para>Gets the number of bands currently displayed.</para>
      </summary>
      <value>An integer value that indicates the number of currently displayed bands.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.BandMinWidth">
      <summary>
        <para>Gets the minimum allowed width of bands.</para>
      </summary>
      <value>An integer value which specifies the minimum allowed width of bands, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.BandsInterval">
      <summary>
        <para>Gets or sets the distance between bands.</para>
      </summary>
      <value>An integer value that specifies the distance between bands, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.BandWidth">
      <summary>
        <para>Gets the band width.</para>
      </summary>
      <value>An integer value which specifies the band width, in pixels.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.BeforeLoadLayout">
      <summary>
        <para>Occurs before a layout is restored from storage (a stream, xml file or the system registry).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.BeginDataUpdate">
      <summary>
        <para>Prevents visual updates and data from being reloaded until the <see cref="M:DevExpress.XtraVerticalGrid.VGridControlBase.EndDataUpdate"/> method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"></see>, preventing visual updates of the object and its elements until the EndUpdate or CancelUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.BestFit">
      <summary>
        <para>Resizes row headers to the minimum width required to completely display their contents.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.BorderStyle">
      <summary>
        <para>Gets or sets the border style for the vertical grid.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value that specifies the border style of a grid.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CalcHitInfo(System.Drawing.Point)">
      <summary>
        <para>Returns information on the vertical grid elements located at the specified point.</para>
      </summary>
      <param name="ptGridClient">A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the vertical grid’s top-left corner.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.VGridHitInfo"/> object which contains information about the vertical grid elements located at the test point.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CancelDataUpdate">
      <summary>
        <para>Enables visual updates and data reloading after a call to the <see cref="M:DevExpress.XtraVerticalGrid.VGridControlBase.BeginDataUpdate"/> method without forcing an immediate update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CancelUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"></see> object after it has been locked by the BeginUpdate method, without causing an immediate visual update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CancelUpdateFocusedRecord">
      <summary>
        <para>Discards any changes made to focused record cells.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.CanShowEditor">
      <summary>
        <para>Gets a value which indicates whether the vertical grid can activate cell editors.</para>
      </summary>
      <value>true if the vertical grid can activate cell editors; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.Caption">
      <summary>
        <para>Gets or sets the control’s caption. Enable the <see cref="P:DevExpress.XtraVerticalGrid.BaseOptionsView.ShowCaption"/> property to display the caption.</para>
      </summary>
      <value>The control’s caption.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.CaptionHeight">
      <summary />
      <value></value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CellValueChanged">
      <summary>
        <para>Fires immediately after a cell’s value has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CellValueChanging">
      <summary>
        <para>Fires in response to changing the edit value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ClearFindFilter">
      <summary>
        <para>Discards the query in the Find Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ClearRowErrors">
      <summary>
        <para>Removes the error descriptions for the focused record.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ClearSelection">
      <summary>
        <para>Clears the current selection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CloseEditor">
      <summary>
        <para>Hides the active editor saving any changes made. The active editor is destroyed after this method call.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CollapseAllRows">
      <summary>
        <para>Collapses all rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CopyToClipboard">
      <summary>
        <para>Copies the focused cell or selection to the Clipboard as text.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CreateEditorRow">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.CreateRow(System.Int32)">
      <summary>
        <para>Returns a newly created <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant (the <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/>, <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> or <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> object depending on the passed parameter).</para>
      </summary>
      <param name="rowTypeId">An integer value that specifies which <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant should be created.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.Cursor">
      <summary>
        <para>Gets or sets the cursor that is displayed when the mouse pointer is over the control.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Cursor"/> that represents the cursor to display when the mouse pointer is over the control.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawCaption">
      <summary />
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowHeaderCell">
      <summary>
        <para>Enables row header cells to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowHeaderIndent">
      <summary>
        <para>Enables row header indents to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawRowValueCell">
      <summary>
        <para>Enables data cells to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawSeparator">
      <summary>
        <para>Enables cell separators to be painted manually.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomDrawTreeButton">
      <summary>
        <para>Enables expand buttons to be painted manually.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationForm">
      <summary>
        <para>Provides access to the Customization Form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridCustomizationForm"/> object that represents the Customization Form. null (Nothing in Visual Basic) if the Customization Form is closed.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormBounds">
      <summary>
        <para>Gets or sets the boundaries of the Customization Form.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the Customization Form’s boundaries.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormCreatingCategory">
      <summary>
        <para>Fires when a new category is about to be created in Customization Form.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomizationFormDeletingCategory">
      <summary>
        <para>Fires when a category is about to be deleted in Customization Form.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomRecordCellEdit">
      <summary>
        <para>Allows you to associate an editor with an individual cell. To avoid performance issues and increased memory consumption, assign repository items that already exist in the VGridControl.RepositoryItems collection. Do not create new repository items in this handler.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.CustomRecordCellEditForEditing">
      <summary>
        <para>Allows you to assign a custom editor to a cell for in-place editing, and so override the default row editor, which is by default, used both in display and edit modes. To avoid performance issues and increased memory consumption, assign repository items that already exist in the VGridControl.RepositoryItems collection. Do not create new repository items in this handler.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.DataSourceChanged">
      <summary>
        <para>Fires after the grid’s data source has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.DeleteRecord(System.Int32)">
      <summary>
        <para>Deletes a record from the vertical grid.</para>
      </summary>
      <param name="recordIndex">An integer value that specifies the index of the record to be deleted.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.DropRow(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Boolean)">
      <summary>
        <para>For internal use.</para>
      </summary>
      <param name="source">For internal use.</param>
      <param name="destinationRow">For internal use.</param>
      <param name="insertBefore">For internal use.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.EditingValue">
      <summary>
        <para>Gets or sets the cell value currently being edited.</para>
      </summary>
      <value>A <see cref="T:System.Object"/> object that represents the cell value currently being edited. null (Nothing in Visual Basic) if no cell is currently being edited.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.EmptyAreaHelper">
      <summary>
        <para>Gets an object that paints an empty area. For internal use.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Utils.EmptyAreaHelper object that specifies an empty area painter.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.EndDataUpdate">
      <summary>
        <para>Enables visual updates and data reloading after a call to the <see cref="M:DevExpress.XtraVerticalGrid.VGridControlBase.BeginDataUpdate"/> method and forces an immediate update.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.EndDragRow">
      <summary>
        <para>Fires after a row drag and drop operation has been completed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"></see> object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExpandAllRows">
      <summary>
        <para>Expands all rows.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.Export(DevExpress.XtraPrinting.ExportTarget,System.IO.Stream)">
      <summary>
        <para>Exports the control’s data in the specified format to a stream.</para>
      </summary>
      <param name="target">An <see cref="T:DevExpress.XtraPrinting.ExportTarget"/> value that specifies the format in which the control’s data is exported.</param>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the data is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.Export(DevExpress.XtraPrinting.ExportTarget,System.IO.Stream,DevExpress.XtraPrinting.ExportOptionsBase)">
      <summary>
        <para>Exports the control’s data in the specified format to a stream, using the specified options.</para>
      </summary>
      <param name="target">An <see cref="T:DevExpress.XtraPrinting.ExportTarget"/> value that specifies the format in which the control’s data is exported.</param>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the data is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.ExportOptionsBase"/> descendant that provides export options.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.Export(DevExpress.XtraPrinting.ExportTarget,System.String)">
      <summary>
        <para>Exports the control’s data in the specified format to a file.</para>
      </summary>
      <param name="target">An <see cref="T:DevExpress.XtraPrinting.ExportTarget"/> value that specifies the format in which the control’s data is exported.</param>
      <param name="filePath">A string that specifies the full path to the file to which the View’s data will be exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.Export(DevExpress.XtraPrinting.ExportTarget,System.String,DevExpress.XtraPrinting.ExportOptionsBase)">
      <summary>
        <para>Exports the control’s data in the specified format to a file, using the specified options.</para>
      </summary>
      <param name="target">An <see cref="T:DevExpress.XtraPrinting.ExportTarget"/> value that specifies the format in which the control’s data is exported.</param>
      <param name="filePath">A string that specifies the full path to the file to which the View’s data will be exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.ExportOptionsBase"/> descendant that provides export options.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToDocx(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data in the Office Open XML file format (DOCX file) and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream object to which the created document should be exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToDocx(System.IO.Stream,DevExpress.XtraPrinting.DocxExportOptions)">
      <summary>
        <para>Exports the control’s data in the Office Open XML file format (DOCX file) and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream object to which the created document should be exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.DocxExportOptions"/> object specifying how the data should be exported to the Office Open XML file format.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToDocx(System.String)">
      <summary>
        <para>Exports the control’s data in the Office Open XML file format and saves it to the specified DOCX file.</para>
      </summary>
      <param name="filePath">A System.String value which specifies the full path (including the file name and extension) where the DOCX file should be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToDocx(System.String,DevExpress.XtraPrinting.DocxExportOptions)">
      <summary>
        <para>Exports the control’s data in the Office Open XML file format and saves it to the specified DOCX file.</para>
      </summary>
      <param name="filePath">A System.String value which specifies the full path (including the file name and extension) where the DOCX file should be created.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.DocxExportOptions"/> object specifying how the data should be exported to the Office Open XML format.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToHtml(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data as HTML and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToHtml(System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in HTML format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToHtml(System.String)">
      <summary>
        <para>Exports the control’s data to the specified file as HTML.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> value which contains the full path (including the file name and extension) specifying where the HTML file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToHtml(System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in HTML format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToMht(System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in MHT format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToMht(System.String)">
      <summary>
        <para>Exports the control’s data to an MHT file (Web archive, single file) at the specified path.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) to where the MHT file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToMht(System.String,DevExpress.XtraPrinting.MhtExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in MHT format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToPdf(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data to a PDF document and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToPdf(System.String)">
      <summary>
        <para>Exports the control’s data to the specified PDF file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) where the PDF file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToRtf(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data as Rich Text and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported .</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToRtf(System.IO.Stream,DevExpress.XtraPrinting.RtfExportOptions)">
      <summary>
        <para>Exports the control’s data in the Rich Text Format (RTF file) and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document should be exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object specifying how the data should be exported to the Rich Text Format.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToRtf(System.String)">
      <summary>
        <para>Exports the control’s data to the specified RTF file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) where the RTF file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToRtf(System.String,DevExpress.XtraPrinting.RtfExportOptions)">
      <summary>
        <para>Exports the control’s data in the Rich Text Format and saves it to the specified RTF file.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) where the RTF file should be created.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object specifying how the data should be exported to the Rich Text Format.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToText(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data to a text document and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToText(System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in Text format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToText(System.String)">
      <summary>
        <para>Exports the control’s data to a text file at the specified path.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) where the text file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToText(System.String,DevExpress.XtraPrinting.TextExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in Text format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXls(System.IO.Stream)">
      <summary>
        <para>Exports the control’s data as XLS and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified stream in XLS format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXls(System.String)">
      <summary>
        <para>Exports the control’s data to the specified file as XLS.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) where the XLS file will be created.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXls(System.String,DevExpress.XtraPrinting.XlsExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in XLS format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXlsx(System.IO.Stream)">
      <summary>
        <para>Exports data to the specified stream in XLSX (MS Excel 2007) format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
      <summary>
        <para>Exports data to the specified stream in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object that contains export options.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXlsx(System.String)">
      <summary>
        <para>Exports data to the specified file in XLSX (MS Excel 2007) format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the data is exported.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ExportToXlsx(System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
      <summary>
        <para>Exports the control’s data to the specified file in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the data is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object that contains export options.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FilterCriteria">
      <summary>
        <para>Gets or sets the filter criteria applied to the control.</para>
      </summary>
      <value>An object that specifies the filter criteria applied to the control.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FilterString">
      <summary>
        <para>Gets or sets the filter criteria applied to the control in text format.</para>
      </summary>
      <value>A string that specifies the filter criteria applied to the control.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FindFilterText">
      <summary>
        <para>Gets or sets the query in the Find Panel.</para>
      </summary>
      <value>A String value that specifies the query in the Find Panel.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FindPanelVisible">
      <summary>
        <para>Gets or sets whether the Find Panel is visible.</para>
      </summary>
      <value>true if the Find Panel is visible; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.FireChanged">
      <summary>
        <para>Forces an update to the control’s properties listed within the property grid at design time.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FixedBottomRows">
      <summary>
        <para>Provides access to the rows that are fixed to the control’s bottom edge.</para>
      </summary>
      <value>A collection of rows fixed to the bottom edge.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FixedTopRows">
      <summary>
        <para>Provides access to the rows that are fixed to the control’s top edge.</para>
      </summary>
      <value>A collection of rows fixed to the top edge.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecord">
      <summary>
        <para>Gets or sets the focused record by its index.</para>
      </summary>
      <value>An integer value that specifies the index of the focused record.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordCellChanged">
      <summary>
        <para>Fires in response to cell focus changing from one cell to another in a record if the previously and currently focused cells have different indexes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordCellIndex">
      <summary>
        <para>Gets or sets the index of the focused cell in the focused record.</para>
      </summary>
      <value>An integer value that specifies the index of the focused cell.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordChanged">
      <summary>
        <para>Fires in response to record focus changing.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRecordModified">
      <summary>
        <para>Gets whether the focused record’s cell values have been modified since the record last received focus.</para>
      </summary>
      <value>true if the focused record’s data has been changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRow">
      <summary>
        <para>Gets or sets the focused row.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the focused row. null (Nothing in Visual Basic) if no row is focused.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.FocusedRowChanged">
      <summary>
        <para>Fires in response to row focus moving.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.FocusFirst">
      <summary>
        <para>Moves focus to the first visible row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.FocusLast">
      <summary>
        <para>Moves row focus to the last visible row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.FocusNext">
      <summary>
        <para>Moves row focus to the next visible row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.FocusPrev">
      <summary>
        <para>Moves row focus to the previous visible row.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ForceInitialize">
      <summary>
        <para>Forces the control to finish its initialization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.FullExpandRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Expands the specified row and all its children.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row to expand.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellDisplayText(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Returns the display text of the specified cell.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row where the desired cell resides.</param>
      <param name="recordIndex">A zero-based integer that specifies the index of the record which contains the cell.</param>
      <returns>A <see cref="T:System.String"/> value that represents the cell’s display text.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellDisplayText(DevExpress.XtraVerticalGrid.Rows.MultiEditorRow,System.Int32,System.Int32)">
      <summary>
        <para>Returns the display text of the specified cell within the multi-editor row.</para>
      </summary>
      <param name="meRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> object that represents the row where the cell resides.</param>
      <param name="recordIndex">A zero-based integer that specifies the index of the record which contains the cell.</param>
      <param name="cellIndex">A zero-based integer that specifies the cell’s index.</param>
      <returns>A <see cref="T:System.String"/> value that represents the cell’s display text.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellDisplayText(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Int32)">
      <summary>
        <para>Returns the display text of the specified cell.</para>
      </summary>
      <param name="rowProperties">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object that identifies a row.</param>
      <param name="recordIndex">A zero-based integer that specifies the index of the record which contains the cell.</param>
      <returns>A System.String value that represents the cell’s display text.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellDisplayText(System.String,System.Int32)">
      <summary />
      <param name="fieldName"></param>
      <param name="recordIndex"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellValue(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Returns the value of a cell.</para>
      </summary>
      <param name="row">The row that contains the cell.</param>
      <param name="recordIndex">The index of the record that contains the cell.</param>
      <returns>An object that specifies the cell’s value. null (Nothing in Visual Basic) if no cell is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellValue(DevExpress.XtraVerticalGrid.Rows.MultiEditorRow,System.Int32,System.Int32)">
      <summary>
        <para>Returns the value of a cell in a multi-editor row.</para>
      </summary>
      <param name="meRow">The row that contains the cell.</param>
      <param name="recordIndex">The index of the record that contains the cell.</param>
      <param name="cellIndex">The index of the cell in a multi-editor row.</param>
      <returns>An object that specifies the cell’s value. null (Nothing in Visual Basic) if no cell is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellValue(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Int32)">
      <summary>
        <para>Returns the value of a cell.</para>
      </summary>
      <param name="props">The row that contains the cell.</param>
      <param name="recordIndex">The index of the record that contains the cell.</param>
      <returns>An object that specifies the cell’s value. null (Nothing in Visual Basic) if no cell is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetCellValue(System.String,System.Int32)">
      <summary>
        <para>Returns the value of a cell.</para>
      </summary>
      <param name="fieldName">The name of the field that contains the cell.</param>
      <param name="recordIndex">The index of the record that contains the cell.</param>
      <returns>An object that specifies the cell’s value. null (Nothing in VB.NET) if no cell is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetDataSourceRecordIndex(System.Int32)">
      <summary>
        <para>Returns the index of the data source record that corresponds to the vertical grid record with the specified index (visual position).</para>
      </summary>
      <param name="recordIndex">The vertical grid record index (visual position) for which to return the corresponding data source record index.</param>
      <returns>The index of the data source record that corresponds to the vertical grid record with the specified index (visual position)</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetFindColumnNames">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetFirst">
      <summary>
        <para>Returns the first row within the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows"/> collection.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the first element in the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows"/> collection. null (Nothing in Visual Basic) if the vertical grid has no rows.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetFirstVisible">
      <summary>
        <para>Returns the first visible row.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the first visible row. null (Nothing in Visual Basic) if no visible row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetLast">
      <summary>
        <para>Returns the last row.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the last row. null (Nothing in Visual Basic) if the vertical grid has no rows.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetLastVisible">
      <summary>
        <para>Returns the last visible row.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the last visible row. null (Nothing in Visual Basic) if no visible row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetNext(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns the row next to the specified one.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row whose next row is returned.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row next to the specified one.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetNextVisible(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns the visible row next to the specified one.</para>
      </summary>
      <param name="visibleRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the visible row whose next visible row is returned.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the visible row next to the specified one.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetPrev(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns the row previous to the specified one.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row whose previous row is returned.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row previous to the specified one.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetPrevVisible(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Returns the visible row previous to the specified one.</para>
      </summary>
      <param name="visibleRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the visible row whose previous visible row is returned.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the visible row previous to the specified one.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRecordIndex(System.Int32)">
      <summary>
        <para>Returns the index (visual position) of the vertical grid record that corresponds to the data source record with the specified index.</para>
      </summary>
      <param name="dataSourceRecordIndex">The data source record index for which to return the corresponding vertical grid record index (visual position).</param>
      <returns>The index (visual position) of the vertical grid record that corresponds to the data source record with the specified index.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRecordObject(System.Int32)">
      <summary>
        <para>Returns an <see cref="T:System.Object"/> in the bound data source that contains data for the specified vertical grid record.</para>
      </summary>
      <param name="recordIndex">An integer value that specifies the vertical grid record’s index (visual position).</param>
      <returns>An object that specifies a row of data in a data source. null (Nothing in Visual Basic) if the record at the specified index is not found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowByCaption(System.String)">
      <summary>
        <para>Returns the <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> with the specified <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Caption"/> property value.</para>
      </summary>
      <param name="caption">The string value specifying the caption of the row to return.</param>
      <returns>The <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant with the specified <see cref="P:DevExpress.XtraVerticalGrid.Rows.RowProperties.Caption"/> property value. null (Nothing in Visual Basic) if no row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowByFieldName(System.String)">
      <summary>
        <para>Returns a row by the name of the field which it’s bound to.</para>
      </summary>
      <param name="fieldName">A <see cref="T:System.String"/> value which specifies the name of the field in the data source.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row bound to the data source field with the specified name. null (Nothing in Visual Basic) if no row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowByHandle(System.Int32)">
      <summary>
        <para>Returns a row by its handle.</para>
      </summary>
      <param name="rowHandle">An integer value which represents the handle of the row to return.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row with the specified handle. null (Nothing in Visual Basic) is no row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowByName(System.String)">
      <summary>
        <para>Returns the <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> with the specified <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Name"/> property value.</para>
      </summary>
      <param name="name">The string value specifying the name of the row to return.</param>
      <returns>The <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant with the specified <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Name"/> property value. null (Nothing in Visual Basic) if no row is found.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowError(DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Gets an error description for the focused cell or the entire focused record.</para>
      </summary>
      <param name="props">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object which identifies the cell within the focused record.</param>
      <returns>A <see cref="T:System.String"/> value that represents an error description.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowErrorType(DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Gets an error icon type for the focused cell or for the entire record.</para>
      </summary>
      <param name="props">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object that represents the row item that contains an error cell.</param>
      <returns>A <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorType"/> value that represents the error icon type.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetRowPropertiesByFieldName(System.String)">
      <summary>
        <para>Returns row properties based on a data source field name of the required row.</para>
      </summary>
      <param name="fieldName">A string value specifying the filed name of the required row in the data source.</param>
      <returns>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object of the required row.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetSelectedCells">
      <summary>
        <para>Returns selected cells in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>).</para>
      </summary>
      <returns>An array of objects that refer to the selected cells.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetSelectedRecords">
      <summary>
        <para>Returns selected records in multiple record selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RecordSelect"/>).</para>
      </summary>
      <returns>An array of record indexes (record visual positions) that identify the selected records.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetSelectedRows">
      <summary>
        <para>Returns selected cells in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <returns>An array of row objects.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.GetShowEditorMode">
      <summary>
        <para>Returns whether a cell editor is activated when the left mouse button is pressed or released, and whether the cell should be focused to activate the editor.</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.EditorShowMode"/> enumeration value that specifies how a cell editor is activated.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.GridDisposing">
      <summary>
        <para>Gets a value which indicates whether the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> is being unloaded from memory.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/> is being disposed of; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.GridLayout">
      <summary>
        <para>Fires after the grid’s layout has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.HasRowErrors">
      <summary>
        <para>Indicates whether the focused record or at least a single cell within the focused record has an error assigned.</para>
      </summary>
      <value>true if the focused record contains errors set via the VGridControlBase.SetRowError method; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.HiddenEditor">
      <summary>
        <para>Fires after an inplace editor has been closed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.HideCustomization">
      <summary>
        <para>Closes the Customization Form.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.HideCustomizationForm">
      <summary>
        <para>Fires before the Customization Form is closed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.HideEditor">
      <summary>
        <para>Hides and destroys the active editor, and discards any changes made.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.HideFindPanel">
      <summary>
        <para>Hides the Find Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.HorzScroll(System.Int32)">
      <summary>
        <para>Scrolls the vertical grid by the specified number of records.</para>
      </summary>
      <param name="recordsCount">An integer value that specifies the number of records to scroll. If positive, forward scrolling is performed; otherwise the vertical grid is scrolled backwards.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.HorzScrollPixel(System.Int32)">
      <summary>
        <para>Scrolls the vertical grid horizontally by the specified number of pixels.</para>
      </summary>
      <param name="pixelCount">An integer value that specifies the number of pixels to scroll. If positive, forward scrolling is performed; otherwise the vertical grid is scrolled backwards.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.HtmlImages">
      <summary>
        <para>Gets or sets a collection of images that can be embedded in row headers and cells using the image tag.</para>
      </summary>
      <value>An image collection.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.HyperlinkClick">
      <summary>
        <para>Fires when a hyperlink in a row header or record header is activated.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ImageList">
      <summary>
        <para>Gets or sets the source of row header images.</para>
      </summary>
      <value>An object that is the source of images displayed within row headers.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.InitNewRecord">
      <summary>
        <para>Enables added records to be initialized.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.InternalGetService(System.Type)">
      <summary>
        <para>This member supports the infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="service"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.InvalidateData">
      <summary>
        <para>Marks the control as not valid (needs repainting) and causes the control to reload data from the data source with the next repaint operation.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.InvalidateRecord(System.Int32)">
      <summary>
        <para>Invalidates the specified record.</para>
      </summary>
      <param name="recordIndex">An integer value that identifies the record to be invalidated.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.InvalidateRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Invalidates the specified row.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row to be invalidated.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.InvalidateRowCells(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Invalidates the specified row cells.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant which represents the row to which the cells belong.</param>
      <param name="recordIndex">A zero-based integer value that specifies the index of the record that contains the cells.</param>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.InvalidRecordException">
      <summary>
        <para>Fires when a record fails validation or when it cannot be saved to the data source.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.InvalidValueException">
      <summary>
        <para>Enables a proper response to entering an invalid cell value to be provided.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsCategoryRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Gets whether the specified row is of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> type.</para>
      </summary>
      <param name="row">A row to be tested.</param>
      <returns>true if the specified row is of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.CategoryRow"/> type; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsCellDefaultValue(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Returns whether the specified cell’s value is the default value.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> object which identifies the row.</param>
      <param name="recordIndex">An integer which identifies the record which contains the required cell.</param>
      <returns>true if the specified cell’s value is the default value; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsCellDefaultValue(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Int32)">
      <summary>
        <para>Returns whether the specified cell’s value is defaut.</para>
      </summary>
      <param name="props">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object representing the required row item.</param>
      <param name="recordIndex">An integer which identifies the record which contains the required cell.</param>
      <returns>true if the specified cell’s value is default; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsDataRecord(System.Int32)">
      <summary>
        <para>Returns whether or not the specified row handle corresponds to a data row.</para>
      </summary>
      <param name="recordIndex"></param>
      <returns>true if the specified row handle corresponds to a data row; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsEditorRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Gets whether the specified row is of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/> type.</para>
      </summary>
      <param name="row">A row to be tested.</param>
      <returns>true if the specified row is of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.EditorRow"/> type; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsMultiEditorRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Gets whether the specified row is of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> type.</para>
      </summary>
      <param name="row">A row to be tested.</param>
      <returns>true if the specified row is of the <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> type; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.IsPrintingAvailable">
      <summary>
        <para>Indicates whether the vertical grid control can be printed.</para>
      </summary>
      <value>true if the Pivot Grid Control can be printed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.IsUnboundMode">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.IsValidRecordHandle(System.Int32)">
      <summary>
        <para>Returns whether or not the specified record handle is valid.</para>
      </summary>
      <param name="recordIndex"></param>
      <returns>true if the specified record handle is valid; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.LayoutChanged">
      <summary>
        <para>Updates the vertical grid.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.LayoutChangedCore">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.LayoutUpgrade">
      <summary>
        <para>Occurs after a layout whose version doesn’t match the current layout version has been loaded from storage (a stream, xml file or system registry).</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.LayoutVersion">
      <summary>
        <para>Gets or sets the version of the control’s layout.</para>
      </summary>
      <value>A string that specifies the version of the layout in the control.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.LeftVisibleBand">
      <summary>
        <para>Gets or sets the index of the leftmost visible band.</para>
      </summary>
      <value>An integer value that specifies the zero-based index of the leftmost visible band.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.LeftVisibleRecord">
      <summary>
        <para>Gets or sets the leftmost visible record.</para>
      </summary>
      <value>A zero-based integer value that specifies the index of the leftmost visible record.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.LeftVisibleRecordChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.LeftVisibleRecord"/> property’s value is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.LeftVisibleRecordPixel">
      <summary>
        <para>Gets or sets the horizontal scroll position of the leftmost record (column).</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value that specifies the horizontal scroll position of the leftmost record (column).</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.LockDataUpdate">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.LockUpdate">
      <summary />
      <value></value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.MakeRecordVisible(System.Int32)">
      <summary>
        <para>Makes the specified record visible on screen.</para>
      </summary>
      <param name="recordIndex">An integer value which identifies the record that should be made visible.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.MakeRowVisible(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Makes the specified row visible on screen.</para>
      </summary>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row to be made visible.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.MaxRowAutoHeight">
      <summary>
        <para>Gets or sets the maximum height that rows can be stretched, to accommodate their values completely.</para>
      </summary>
      <value>The maximum row height, in pixels.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.MoveRow(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.RowDragEffect)">
      <summary>
        <para>Moves a row next to another row, as specified by the <see cref="T:DevExpress.XtraVerticalGrid.RowDragEffect"/> parameter.</para>
      </summary>
      <param name="source">A row to be moved.</param>
      <param name="destinationRow">A row next to which the first row is inserted.</param>
      <param name="effect">A <see cref="T:DevExpress.XtraVerticalGrid.RowDragEffect"/> value that specifies the insertion mode for a row.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.MoveRow(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Boolean)">
      <summary>
        <para>Moves the specified row to the position specified.</para>
      </summary>
      <param name="source">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row moved.</param>
      <param name="destinationRow">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.BaseRow"/> descendant that represents the row before which the source row should be moved or the parent row whose child row collection it should be appended to.</param>
      <param name="insertBefore">true if the source row should be moved before the destination row; false if the source row should be appended to the child rows collection of the destination row.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.NotFixedRows">
      <summary>
        <para>Provides access to the collection of non-fixed rows.</para>
      </summary>
      <value>A collection of non-fixed rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsBehavior">
      <summary>
        <para>Provides access to the vertical grid’s behavior options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsBehavior"/> object which contains the grid’s behavior options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsFind">
      <summary>
        <para>Provides access to settings controlling the behavior and visibility of the Find Panel and its elements.</para>
      </summary>
      <value>The settings controlling the behavior and visibility of the Find Panel and its elements.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsHint">
      <summary>
        <para>Provides access to the control’s tooltip options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsHint"/> object containing the control’s tooltip options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsLayout">
      <summary>
        <para>Provides access to options that specify how a control’s layout is stored to and restored from a data store (a stream, xml file or the system registry).</para>
      </summary>
      <value>An object that contains options for controlling how the layout is stored and restored.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsMenu">
      <summary>
        <para>Provides access to the control’s menu options.</para>
      </summary>
      <value>A VGridOptionsMenu object containing the control’s menu options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsPrint">
      <summary />
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsSelectionAndFocus">
      <summary>
        <para>Provides access to the vertical grid’s selection and focus options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus"/> object which contains the vertical grid’s selection and focus options.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.OptionsView">
      <summary>
        <para>Provides access to the vertical grid’s display options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsView"/> object which contains the grid’s display options.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.PaintEx">
      <summary>
        <para>Occurs when the control is redrawn.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ParseFindPanelText">
      <summary>
        <para>Fires after the query in the find panel changes. Allows you to create a filter condition based on the query and specify how to highlight results in the control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.PopupMenuShowing">
      <summary>
        <para>Allows you to populate a context menu invoked with a right-click on a property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.PostEditor">
      <summary>
        <para>Posts the value being edited to the associated data source without closing the active in-place editor.</para>
      </summary>
      <returns>true if the value being edited has been successfully saved to the associated data source; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.Print">
      <summary>
        <para>Prints the vertical grid control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ProcessDragRow">
      <summary>
        <para>Fires when a user drags a row.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RecordCellStyle">
      <summary>
        <para>Enables the appearance settings of individual cells to be changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RecordCount">
      <summary>
        <para>Gets the number of records within the vertical grid.</para>
      </summary>
      <value>An integer value that specifies the number of records within the vertical grid.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RecordMinWidth">
      <summary>
        <para>Gets the record’s minimum width.</para>
      </summary>
      <value>Returns 15.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RecordsInterval">
      <summary>
        <para>Gets or sets the distance between records.</para>
      </summary>
      <value>An integer value that specifies the distance between records, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RecordsIterator">
      <summary>
        <para>Allows you to iterate all available records, or only records that are currently visible (fit the currently applied filter), and perform a custom operation on the iterated records.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Rows.VGridRecordsIterator object that iterates records.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RecordWidth">
      <summary>
        <para>Gets or sets a record’s width (as an absolute or relative value) based on the current layout. This setting affects all Vertical Grid or Property Grid records. Note that you cannot change the width of an individual record.</para>
      </summary>
      <value>An integer value that specifies the record’s width.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RecordWidthChanged">
      <summary>
        <para>Fires after a record’s width has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.Refresh">
      <summary>
        <para>Forces the vertical grid to invalidate its client area and immediately redraw itself.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RefreshEditor">
      <summary />
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RefreshEditor(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Object)">
      <summary />
      <param name="properties"></param>
      <param name="value"></param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ResetCursor">
      <summary>
        <para>Resets the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.Cursor"/> property to its default value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreDefaultStyles">
      <summary>
        <para>Resets the appearance settings to their default values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreLayoutFromRegistry(System.String)">
      <summary>
        <para>Restores the layout stored at the specified system registry path.</para>
      </summary>
      <param name="path">A string value which specifies the system registry path. If the specified path doesn’t exist, this method does nothing.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreLayoutFromRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Restores the control’s layout stored at the specified system registry path.</para>
      </summary>
      <param name="path">A string value specifying the system registry path.</param>
      <param name="options">An object that specifies which options must be restored.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreLayoutFromStream(System.IO.Stream)">
      <summary>
        <para>Restores a vertical grid’s layout from the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant from which the vertical grid’s settings are read. If null (Nothing in Visual Basic), an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreLayoutFromStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Loads the control’s layout from a stream, using the specified options.</para>
      </summary>
      <param name="stream">A System.IO.Stream object from which the control’s settings are read.</param>
      <param name="options">An object that specifies which options must be restored.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreLayoutFromXml(System.String)">
      <summary>
        <para>Restores a vertical grid’s layout from the specified XML file.</para>
      </summary>
      <param name="xmlFile">A string value which specifies the path to the XML file from which the vertical grid’s settings are read. If the specified file doesn’t exist, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RestoreLayoutFromXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Loads the control’s layout from the specified XML file, using the specified options.</para>
      </summary>
      <param name="xmlFile">A string value specifying the XML file from which control settings are read.</param>
      <param name="options">An object that specifies which options must be restored.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.RetrieveFields">
      <summary>
        <para>Creates rows for all the fields in the bound data source.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowChanged">
      <summary>
        <para>Fires after a row’s property has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowChanging">
      <summary>
        <para>Fires when changing a row’s property.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowCollapsed">
      <summary>
        <para>Fires after a row is collapsed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowCollapsing">
      <summary>
        <para>Fires before a row is collapsed and allows you to cancel the action.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowExpanded">
      <summary>
        <para>Fires after a row is expanded.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowExpanding">
      <summary>
        <para>Fires before a row is expanded and allows you to cancel the action.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RowHeaderMinWidth">
      <summary>
        <para>Gets the row header’s minimum width.</para>
      </summary>
      <value>Returns 15.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RowHeaderWidth">
      <summary>
        <para>Gets or sets the width of row headers.</para>
      </summary>
      <value>An integer value which specifies the width of row headers.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.RowHeaderWidthChanged">
      <summary>
        <para>Fires after the width of row headers has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RowHeaderWidthChangeStep">
      <summary>
        <para>Gets or sets a value by which the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.RowHeaderWidth"/> property changes when using CTRL + LEFT or CTRL + RIGHT key combinations.</para>
      </summary>
      <value>An integer value that specifies the number of pixels by which the width of row headers changes.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.Rows">
      <summary>
        <para>Gets a collection of root rows.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRows"/> object that represents the collection of root rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.RowsIterator">
      <summary>
        <para>Provides access to the Rows Iterator.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.Rows.VGridRowsIterator"/> object that represents Rows Iterator.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SaveLayoutToRegistry(System.String)">
      <summary>
        <para>Saves a vertical grid’s layout to a system registry path.</para>
      </summary>
      <param name="path">A string value which specifies the system registry path to which the layout is saved.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SaveLayoutToRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Saves the control’s layout to the specified system registry path, using the specified options.</para>
      </summary>
      <param name="path">A string value specifying the system registry path.</param>
      <param name="options">An object that specifies which options must be saved.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SaveLayoutToStream(System.IO.Stream)">
      <summary>
        <para>Saves a vertical grid’s layout to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which a vertical grid’s layout is written.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SaveLayoutToStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Saves the control’s layout to a stream, using the specified options.</para>
      </summary>
      <param name="stream">A System.IO.Stream object to which the control’s layout is written.</param>
      <param name="options">An object that specifies which options must be saved.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SaveLayoutToXml(System.String)">
      <summary>
        <para>Saves a vertical grid’s layout to an XML file.</para>
      </summary>
      <param name="xmlFile">A string value which specifies the path to the file where the vertical grid’s layout should be stored. If an empty string is specified, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SaveLayoutToXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
      <summary>
        <para>Saves a control’s layout to a specified XML file, using the specified options.</para>
      </summary>
      <param name="xmlFile">A string value specifying the XML file name.</param>
      <param name="options">An object that specifies which options must be saved.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ScrollsStyle">
      <summary>
        <para>Gets the style which is applied to the scroll bars.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.VGridScrollStylesController object that contains the style settings for scroll bars.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ScrollVisibility">
      <summary>
        <para>Gets or sets a value that specifies the availability of scroll elements.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.ScrollVisibility"/> enumeration value that specifies when horizontal and vertical scroll bars should be displayed.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectAll">
      <summary>
        <para>Selects all records, rows, or cells (depending on the selection mode).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectCell(System.Int32,DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Adds a cell to the current selection in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>). This overload allows you to address a certain cell within a <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/>.</para>
      </summary>
      <param name="recordIndex">A record’s visual position..</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/> object.</param>
      <param name="cellIndex">The index of a cell within the specified <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/>.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectCell(System.Int32,DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Adds a cell to the current selection in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>).</para>
      </summary>
      <param name="recordIndex">A record’s visual position.</param>
      <param name="properties">A row identifier, which matches the <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Properties"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectCell(System.Int32,System.String)">
      <summary>
        <para>Adds a cell to the current selection in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>).</para>
      </summary>
      <param name="recordIndex">A record’s visual position..</param>
      <param name="fieldName">A string that identifies a row’s field name.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectCells(System.Int32,DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32,System.Int32,DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Selects cells in the specified range. Has effect only if <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelectMode"/> is set to <see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>.</para>
      </summary>
      <param name="startRecord">The start record index (visual position).</param>
      <param name="startRow">The start row.</param>
      <param name="startCell">The start cell index in multi-editor rows.</param>
      <param name="endRecord">The end record index (visual position).</param>
      <param name="endRow">The end row.</param>
      <param name="endCell">The end cell index in multi-editor rows.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectCells(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
        <para>Selects cells in the specified range. Has effect only if <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelectMode"/> is set to <see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>.</para>
      </summary>
      <param name="startRecord">The start record index (visual position).</param>
      <param name="startRowIndex">The start row index.</param>
      <param name="startCell">The start cell index in multi-editor rows.</param>
      <param name="endRecord">The end record index (visual position).</param>
      <param name="endRowIndex">The end row index.</param>
      <param name="endCell">The end cell index in multi-editor rows.</param>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.SelectedChanged">
      <summary>
        <para>Allows you to respond to changes in the current selection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.SelectedManager">
      <summary>
        <para>Gets or sets the manager performing record/row/cell selection operations.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.Internal.SelectedManager object representing the manager that performs record/row/cell selection operations.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectRecord(System.Int32)">
      <summary>
        <para>Adds a record to the current selection in multiple record selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RecordSelect"/>).</para>
      </summary>
      <param name="recordIndex">A record’s visual position.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectRecords(System.Int32,System.Int32)">
      <summary>
        <para>Selects records in the specified range. Has effect only if <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelectMode"/> is set to <see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RecordSelect"/>.</para>
      </summary>
      <param name="startRecord">The start record index (visual position).</param>
      <param name="endRecord">The end record index (visual position).</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Adds a row to the current selection in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <param name="row">A row object.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectRow(System.String)">
      <summary>
        <para>Adds a row to the current selection in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <param name="fieldName">A string that identifies a row’s field name.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectRows(DevExpress.XtraVerticalGrid.Rows.BaseRow,DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Selects rows in the specified range. Has effect only if <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelectMode"/> is set to <see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>.</para>
      </summary>
      <param name="startRow">The start row.</param>
      <param name="endRow">The end row.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SelectRows(System.Int32,System.Int32)">
      <summary>
        <para>Selects rows in the specified range. Has effect only if <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsSelectionAndFocus.MultiSelectMode"/> is set to <see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>.</para>
      </summary>
      <param name="startRowIndex">The start row index.</param>
      <param name="endRowIndex">The end row index.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetCellValue(DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32,System.Object)">
      <summary>
        <para>Assigns a value to a cell.</para>
      </summary>
      <param name="gridRow">The row that contains the cell.</param>
      <param name="recordIndex">The index (visual position) of the record that contains the cell.</param>
      <param name="value">An object that specifies the cell’s new value.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetCellValue(DevExpress.XtraVerticalGrid.Rows.MultiEditorRow,System.Int32,System.Int32,System.Object)">
      <summary>
        <para>Assigns a value to a cell in a multi-editor row.</para>
      </summary>
      <param name="meRow">The row that contains the cell.</param>
      <param name="recordIndex">The index (visual position) of the record that contains the cell.</param>
      <param name="cellIndex">The index of the cell in a multi-editor row.</param>
      <param name="value">An object that specifies the cell’s new value.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetCellValue(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.Int32,System.Object)">
      <summary>
        <para>Assigns a value to a cell.</para>
      </summary>
      <param name="props">The properties of the row that contains the cell.</param>
      <param name="recordIndex">The index (visual position) of the record that contains the cell.</param>
      <param name="value">An object that represents the cell’s new value.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetCellValue(System.String,System.Int32,System.Object)">
      <summary>
        <para>Assigns a value to a cell.</para>
      </summary>
      <param name="fieldName">The name of the field that contains the cell.</param>
      <param name="recordIndex">The index (visual position) of the record that contains the cell.</param>
      <param name="value">An object that specifies the cell’s new value.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetDefaultBehaviorOptions">
      <summary>
        <para>Sets the vertical grid’s behavior options to their default values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetDefaultViewOptions">
      <summary>
        <para>Sets the vertical grid’s view options to their default states.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetRowError(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.String)">
      <summary>
        <para>For the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>, this method sets or clears an error for the specified cell within the focused record. For the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>, this method sets or clears an error for the specified row.</para>
      </summary>
      <param name="props">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object that identifies the target cell.</param>
      <param name="errorText">The error description. An empty string to clear the previously assigned error.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.SetRowError(DevExpress.XtraVerticalGrid.Rows.RowProperties,System.String,DevExpress.XtraEditors.DXErrorProvider.ErrorType)">
      <summary>
        <para>For the <see cref="T:DevExpress.XtraVerticalGrid.VGridControl"/>, this method sets or clears a cell error within the focused record. For the <see cref="T:DevExpress.XtraVerticalGrid.PropertyGridControl"/>, this method sets an error for the specified row. This overload allows you to assign an error icon along with the error description.</para>
      </summary>
      <param name="props">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.RowProperties"/> object that identifies the target cell.</param>
      <param name="errorText">The error description. An empty string to clear the previously assigned error.</param>
      <param name="errorType">The error icon type.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ShowButtonMode">
      <summary>
        <para>Gets or sets the manner in which editor buttons are displayed within a vertical grid.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.ShowButtonModeEnum"/> enumeration value that specifies the manner in which editor buttons are displayed within a vertical grid.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ShowCustomization">
      <summary>
        <para>Displays the Customization Form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ShowCustomization(System.Drawing.Point)">
      <summary>
        <para>Displays the Customization Form at the specified point.</para>
      </summary>
      <param name="showPoint">The position of the Customization Form‘s top-left corner, in screen coordinates.</param>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ShowCustomizationForm">
      <summary>
        <para>Fires after the Customization Form has been invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ShowEditor">
      <summary>
        <para>Invokes the focused cell’s editor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ShowFindPanel">
      <summary>
        <para>Displays the Find Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ShowingEditor">
      <summary>
        <para>Allows the editor’s activation to be canceled.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ShowMenu">
      <summary>
        <para>Allows context menus for rows to be customized.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ShownEditor">
      <summary>
        <para>Fires after a cell editor has been invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ShowPrintPreview">
      <summary>
        <para>Opens the Print Preview window with a Bars UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.ShowRibbonPrintPreview">
      <summary>
        <para>Displays the Print Preview window with a Ribbon UI.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.StartDragRow">
      <summary>
        <para>Fires before a row is dragged by an end-user.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.State">
      <summary>
        <para>Indicates the vertical grid’s current state.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.VGridState"/> enumeration value that indicates the vertical grid’s current state.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.StateChanged">
      <summary>
        <para>Fires after the vertical grid’s state has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.TopVisibleRowIndex">
      <summary>
        <para>Gets or sets the top visible row.</para>
      </summary>
      <value>An integer value that specifies the index of the top visible row.</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.TopVisibleRowIndexChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.XtraVerticalGrid.VGridControlBase.TopVisibleRowIndex"/> property’s value is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.TopVisibleRowIndexPixel">
      <summary>
        <para>Gets or sets the vertical scroll position of the top row.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value that specifies the vertical scroll position of the top row.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.TreeButtonStyle">
      <summary>
        <para>Gets or sets the style which is used to display category row tree buttons.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraVerticalGrid.TreeButtonStyle"/> enumeration value that specifies the style which is used to display category row tree buttons.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UnselectCell(System.Int32,DevExpress.XtraVerticalGrid.Rows.BaseRow,System.Int32)">
      <summary>
        <para>Removes the specified cell from the current selection in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>). This overload allows you to address a certain cell within a <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/>.</para>
      </summary>
      <param name="recordIndex">A record’s visual position.</param>
      <param name="row">A <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/>.</param>
      <param name="cellIndex">The index of a cell within the specified <see cref="T:DevExpress.XtraVerticalGrid.Rows.MultiEditorRow"/>.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UnselectCell(System.Int32,DevExpress.XtraVerticalGrid.Rows.RowProperties)">
      <summary>
        <para>Removes the specified cell from the current selection in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>).</para>
      </summary>
      <param name="recordIndex">A record’s visual position.</param>
      <param name="properties">A row identifier, which matches the <see cref="P:DevExpress.XtraVerticalGrid.Rows.BaseRow.Properties"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UnselectCell(System.Int32,System.String)">
      <summary>
        <para>Removes the specified cell from the current selection in multiple cell selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.CellSelect"/>).</para>
      </summary>
      <param name="recordIndex">A record’s visual position.</param>
      <param name="fieldName">A string that identifies a row’s field name.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UnselectRecord(System.Int32)">
      <summary>
        <para>Removes a record from the current selection in multiple record selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RecordSelect"/>).</para>
      </summary>
      <param name="recordIndex">A record’s visual position.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UnselectRow(DevExpress.XtraVerticalGrid.Rows.BaseRow)">
      <summary>
        <para>Removes a row from the current selection in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <param name="row">A row object.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UnselectRow(System.String)">
      <summary>
        <para>Removes a row from the current selection in multiple row selection mode (<see cref="F:DevExpress.XtraVerticalGrid.MultiSelectMode.RowSelect"/>).</para>
      </summary>
      <param name="fieldName">A string that identifies a row’s field name.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UpdateData">
      <summary>
        <para>Reloads data from the data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.UpdateFocusedRecord">
      <summary>
        <para>Validates the focused record and saves its values to the data source.</para>
      </summary>
      <returns>true if the record has been updated; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.UseDirectXPaint">
      <summary>
        <para>Gets or sets whether the DirectX Hardware Acceleration is enabled.</para>
      </summary>
      <value>The DefaultBoolean.Default value is equivalent to DefaultBoolean.True if the static <see cref="M:DevExpress.XtraEditors.WindowsFormsSettings.ForceDirectXPaint"/> method was called, or to DefaultBoolean.False otherwise.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.UseDisabledStatePainter">
      <summary>
        <para>Gets or sets whether the control is painted grayed out, when it’s in the disabled state.</para>
      </summary>
      <value>true if the control is painted grayed out, when it’s in the disabled state; otherwise, false</value>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ValidateRecord">
      <summary>
        <para>Enables you to specify whether record data is valid and whether the record can lose focus.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraVerticalGrid.VGridControlBase.ValidatingEditor">
      <summary>
        <para>Enables you to perform manual validation of cell values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.VertScroll(System.Int32)">
      <summary>
        <para>Scrolls the vertical grid’s view vertically by the specified number of rows.</para>
      </summary>
      <param name="rowsCount">An integer value which specifies the number of rows that the grid’s view is scrolled by.</param>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridControlBase.VertScrollPixel(System.Int32)">
      <summary>
        <para>Scrolls the vertical grid vertically by the specified number of pixels.</para>
      </summary>
      <param name="pixelCount">An integer value that specifies the number of pixels to scroll. If positive, downward scrolling is performed; otherwise the vertical grid is scrolled upwards.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.ViewInfo">
      <summary>
        <para>Gets the vertical grid’s view information.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.ViewInfo.BaseViewInfo object that contains the information used to draw a control.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridControlBase.VisibleRows">
      <summary>
        <para>Gets a collection of visible rows.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.GridRowReadOnlyCollection object that represents the collection of visible rows.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridHitInfo">
      <summary>
        <para>Contains information about a point referenced by specified coordinates.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridHitInfo.#ctor">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraVerticalGrid.VGridHitInfo"/> object with the default settings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.BandIndex">
      <summary>
        <para>Represents the index of the band located at a specific point.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.CellIndex">
      <summary>
        <para>Represents the index of a cell located at a specific point.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.HitInfoType">
      <summary>
        <para>Contains information on which grid element is located under the test point.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.PtMouse">
      <summary>
        <para>Contains the coordinates of the point under the mouse cursor relative to the <see cref="T:DevExpress.XtraVerticalGrid.VGridControlBase"/> control’s upper left corner. Can be used for handling a dragging operation initiated within a vertical grid control when it is necessary to determine the direction and distance of the drag.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.RecordIndex">
      <summary>
        <para>Represents the index of the record to which a specific point corresponds.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.Row">
      <summary>
        <para>Specifies the row located under the hit (test) point.</para>
      </summary>
      <value></value>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridHitInfo.Tab">
      <summary>
        <para>Specifies the tab located under the hit (test) point.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsBehavior">
      <summary>
        <para>Provides behavior options for a vertical grid control..</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsBehavior"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowAnimatedScrolling">
      <summary>
        <para>Gets or sets whether an animation effect is used while scrolling vertically.</para>
      </summary>
      <value>true if an animation effect is used while scrolling vertically; false if vertical scrolling is discrete.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowCollapseRows">
      <summary>
        <para>Gets or sets whether users can click row headers or collapse buttons to collapse parent rows.</para>
      </summary>
      <value>true if users can collapse parent rows; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowGroupExpandAnimation">
      <summary>
        <para>Gets or sets whether grid rows are expanded and collapsed using an animation effect.</para>
      </summary>
      <value>True, if grid rows are expanded and collapsed using an animation effect; otherwise, Default or False.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowRecordComparison">
      <summary>
        <para>Gets or sets whether record comparison is enabled.</para>
      </summary>
      <value>true to allow users to compare records; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowSort">
      <summary>
        <para>Gets or sets whether users can sort data in the control.</para>
      </summary>
      <value>true if users can sort data in the control; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowSortOnClick">
      <summary>
        <para>Gets or sets whether users can click a row header to sort data.</para>
      </summary>
      <value>false if users cannot click on a row header to sort data in the control; true if this behavior depends on the <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AllowSort"/> option.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AutoFocusNewRecord">
      <summary>
        <para>Gets or sets whether adding a new record to an underlying data source automatically moves focus to the corresponding grid record.</para>
      </summary>
      <value>true if a newly added record is automatically focused; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.AutoSelectAllInEditor">
      <summary>
        <para>Gets or sets whether the entire contents of the editor is selected when a cell’s editor is activated using the ENTER or F2 keys.</para>
      </summary>
      <value>true to select the entire cell’s content when editing starts; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.CopyToClipboardWithRowHeaders">
      <summary>
        <para>Gets or sets whether row captions are copied to the Clipboard when CTRL+C is pressed or the <see cref="M:DevExpress.XtraVerticalGrid.VGridControlBase.CopyToClipboard"/> method is called.</para>
      </summary>
      <value>true if row captions are copied to the Clipboard when CTRL+C is pressed or the <see cref="M:DevExpress.XtraVerticalGrid.VGridControlBase.CopyToClipboard"/> method is called; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.DragRowHeaders">
      <summary>
        <para>Gets or sets whether end-users are allowed to move row headers using drag and drop.</para>
      </summary>
      <value>true to allow end-users to move row headers using drag-and-drop; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.Editable">
      <summary>
        <para>Gets or sets whether end-users are allowed to invoke cell editors.</para>
      </summary>
      <value>true if end-users are allowed to invoke cell editors; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.EditorShowMode">
      <summary>
        <para>Gets or sets  whether a cell editor is activated when the left mouse button is pressed or released, and whether the cell should be focused to activate the editor.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.EditorShowMode"/> enumeration value that specifies how a cell editor is activated.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.HyperlinkClickMode">
      <summary>
        <para>Gets or sets whether and how hyperlinks in row captions and record headers are activated.</para>
      </summary>
      <value>A value that specifies whether and how hyperlinks in row captions and record headers are activated.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.PreserveChildRows">
      <summary>
        <para>Gets or sets whether child rows remain hidden if their parent row is removed from the Customization Form using drag and drop.</para>
      </summary>
      <value>true if child rows remain hidden when their parent row is removed from the Customization Form using drag and drop; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.RecordsMouseWheel">
      <summary>
        <para>Gets or sets whether end-users scroll through records or rows when scrolling using the mouse wheel.</para>
      </summary>
      <value>true if end-users use the mouse wheel to scroll through the records; false if the mouse wheel is used to scroll through the rows.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ResizeHeaderPanel">
      <summary>
        <para>Gets or sets whether end-users can change the row header panel’s width.</para>
      </summary>
      <value>true if end-users can change the row header panel’s width; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ResizeRowHeaders">
      <summary>
        <para>Gets or sets whether end-users can resize row headers.</para>
      </summary>
      <value>true to allow end-users to resize row headers; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ResizeRowValues">
      <summary>
        <para>Gets or sets whether end-users can change the width of the records by dragging their edges.</para>
      </summary>
      <value>true to allow end-users to change the width of the records; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ShowEditorOnMouseUp">
      <summary>
        <para>Gets or sets whether a cell editor is activated when the left mouse button is released. This property is obsolete, use <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.EditorShowMode"/> instead.</para>
      </summary>
      <value>true if <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.EditorShowMode"/> is set to <see cref="F:DevExpress.Utils.EditorShowMode.MouseUp"/>; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.SmartExpand">
      <summary>
        <para>Gets or sets whether rows are automatically scrolled vertically when a parent row is expanded so that the maximum number of child rows possible is visible onscreen.</para>
      </summary>
      <value>true if rows are automatically scrolled vertically when a parent row is expanded so that the maximum number of child rows is visible onscreen; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.UnboundColumnExpressionEditorMode">
      <summary>
        <para>Gets or sets the type of unbound expression editor that end-users can utilize to modify expressions for unbound rows owned by this Vertical Grid.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraEditors.ExpressionEditorMode"/> object that specifies the type of unbound expression editor that end-users can utilize to modify expressions for unbound rows owned by this Vertical Grid.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.UseDefaultEditorsCollection">
      <summary>
        <para>Gets or sets whether editors from the <see cref="P:DevExpress.XtraVerticalGrid.PropertyGridControl.DefaultEditors"/> collection are used to represent and edit row values of specific types.</para>
      </summary>
      <value>true if editors from the <see cref="P:DevExpress.XtraVerticalGrid.PropertyGridControl.DefaultEditors"/> collection are used; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.UseEnterAsTab">
      <summary>
        <para>Gets or sets whether pressing the ENTER key hides the currently active cell editor saving the changes made to its content and moves focus to the next row’s cell activating its editor.</para>
      </summary>
      <value>true if pressing the ENTER key hides the currently active cell editor saving the changes made to its content and moves focus to the next row’s cell activating its editor; false if pressing the ENTER key hides the currently active cell editor saving the changes made to its content.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.UseTabKey">
      <summary>
        <para>Gets or sets whether the TAB/SHIFT + TAB key combinations move focus to the next/previous cell or to the next/previous control in the tab order.</para>
      </summary>
      <value>true if the TAB/SHIFT+TAB key combinations are processed by the vertical grid; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsFilter">
      <summary>
        <para>Provides filtering options for the control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsFilter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsFilter"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.AllowFilter">
      <summary>
        <para>Gets or sets whether end-users can filter data using the filter dropdowns.</para>
      </summary>
      <value>true if end-users can filter data using the filter dropdowns; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.AllowFilterEditor">
      <summary>
        <para>Gets or sets whether the filter editor (invoked form the filter panel) can be used to build complex filter criteria.</para>
      </summary>
      <value>true, to allow using the filter editor; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.AllowMRUFilterList">
      <summary>
        <para>Gets or sets whether the filter panel displays the list of most recently used filters.</para>
      </summary>
      <value>true if the filter panel displays the list of most recently used filters; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsFilter.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Assigns property values of the specified <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsFilter"/> object to the corresponding properties of this object.</para>
      </summary>
      <param name="options">An object whose property values are copied to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.DefaultFilterEditorView">
      <summary>
        <para>Gets or sets whether complex filter conditions in the filter editor (invoked from the filter panel) are built using a visual constructor, or written as plain text according to filter criteria syntax.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.FilterEditorViewMode"/> that specifies how complex filter conditions are built by end-users in the filter editor.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.FilterEditorAggregateEditing">
      <summary>
        <para>Gets or sets whether filters can be created against properties that are List objects.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.FilterControlAllowAggregateEditing"/> value that specifies if filters can be created against properties that are List objects.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.FilterEditorAllowCustomExpressions">
      <summary>
        <para>Gets or sets whether the Filter Editor allows you to display, create and edit custom expressions—expressions that cannot be converted to Filter Editor nodes.</para>
      </summary>
      <value>A value that specifies whether the Filter Editor allows you to display, create and edit custom expressions.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.FilterEditorUseMenuForOperandsAndOperators">
      <summary>
        <para>Gets or sets  whether to use a pop-up menu instead of a combo box that supports the incremental search to edit operators and operands in the Filter Editor.</para>
      </summary>
      <value>true, to use a pop-up menu to edit operators and operands in the Filter Editor; false, to use a combo box.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.MRUFilterListCount">
      <summary>
        <para>Gets or sets the MRU Filter List capacity.</para>
      </summary>
      <value>An integer value that specifies the MRU Filter List capacity.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.MRUFilterListPopupCount">
      <summary>
        <para>Gets or sets the number of filters displayed simultaneously in the filter panel‘s MRU filter list.</para>
      </summary>
      <value>An integer value that specifies the the number of filters displayed simultaneously in the filter panel’s MRU filter list.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.ShowAllValuesInFilterPopup">
      <summary>
        <para>Gets or sets whether the filter dropdown for a particular column shows all values available in the data source for this column, or only values that fit the filters already applied to data in other columns.</para>
      </summary>
      <value>true to display all values in the filter dropdown; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsFilter.ShowCustomFunctions">
      <summary>
        <para>Gets or sets whether to show custom function-based filters.</para>
      </summary>
      <value>True to show custom function-based filters; False to not show them; Default to enable/disable custom function-based filters depending on the global <see cref="F:DevExpress.XtraEditors.WindowsFormsSettings.DefaultSettingsCompatibilityMode"></see> setting.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsHint">
      <summary>
        <para>Provides options that control the tooltip feature for a vertical grid’s elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsHint.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsHint"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsHint.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsHint.ShowCellHints">
      <summary>
        <para>Gets or sets whether hints are displayed for cells with truncated content.</para>
      </summary>
      <value>true to display hints for cells with truncated content; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsHint.ShowRowHeaderHints">
      <summary>
        <para>Gets or sets whether tooltips are supported for row headers.</para>
      </summary>
      <value>true if  tooltips are supported for row headers.; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsHintEx">
      <summary />
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsHintEx.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsHintEx"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsHintEx.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary />
      <param name="options"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsHintEx.ShowRecordHeaderHints">
      <summary>
        <para>Gets or sets whether to display hints for record headers when a user hovers the mouse cursor over them.</para>
      </summary>
      <value>true, to enable record header hints; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsLayout">
      <summary>
        <para>Provides options that control how the current layout is stored to/restored from a data store (a stream, xml file or system registry).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsLayout.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsLayout"/> class</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsLayout.StoreAllOptions">
      <summary>
        <para>Not supported by the current class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsLayout.StoreDataSettings">
      <summary>
        <para>Not supported by the current class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsLayout.StoreVisualOptions">
      <summary>
        <para>Not supported by the current class.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsPrint">
      <summary />
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsPrint.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsPrint"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsPrint.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary />
      <param name="options"></param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsPrint.PrintRecordHeaders">
      <summary>
        <para>Gets or sets whether to print record headers.</para>
      </summary>
      <value>true, to print record headers; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridOptionsView">
      <summary>
        <para>Provides view options for a vertical grid control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraVerticalGrid.VGridOptionsView"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraVerticalGrid.VGridOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all the settings from the options object passed as the parameter to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsView.FilterCriteriaDisplayStyle">
      <summary>
        <para>Gets or sets the display style of filter conditions in the Filter Panel, MRU Filter List and built-in Filter Editor.</para>
      </summary>
      <value>The display style of filter conditions in the Filter Panel and built-in Filter Editor. When the FilterCriteriaDisplayStyle property is set to Default, the actual display style is specified by the static <see cref="P:DevExpress.XtraEditors.WindowsFormsSettings.FilterCriteriaDisplayStyle"/> property.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsView.HeaderFilterButtonShowMode">
      <summary>
        <para>Gets or sets whether to display smart tags or buttons in row headers to invoke the filter dropdown.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Controls.FilterButtonShowMode"/> enumeration value, such as SmartTag or Button, that specifies whether to display smart tags or buttons in row headers to invoke the filter dropdowns. Default equals SmartTag.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsView.ShowFilterPanelMode">
      <summary>
        <para>Gets or sets whether the filter panel is automatically shown when a filter is applied (the default), is always visible, or is always hidden.</para>
      </summary>
      <value>A DevExpress.XtraVerticalGrid.ShowFilterPanelMode enumeration value, such as Default, ShowAlways or Never, that specifies when the Filter Panel is shown.</value>
    </member>
    <member name="P:DevExpress.XtraVerticalGrid.VGridOptionsView.ShowRecordHeaders">
      <summary>
        <para>Gets or sets whether record headers are enabled. This property is in effect in Multi-Record view mode (see <see cref="P:DevExpress.XtraVerticalGrid.VGridControl.LayoutStyle"/>).</para>
      </summary>
      <value>A value that specifies whether record headers are enabled.</value>
    </member>
    <member name="T:DevExpress.XtraVerticalGrid.VGridState">
      <summary>
        <para>Contains values indicating all the available states for the vertical grid.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.Disposed">
      <summary>
        <para>A vertical grid is being disposed of.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.Editing">
      <summary>
        <para>A cell editor is currently active.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.FocusedRowChanging">
      <summary>
        <para>The focused row has changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.HeaderPanelSizing">
      <summary>
        <para>A row header panel’s right edge is being dragged to change the row header’s width. The width of the row header panel can also be changed using the CTRL-LEFT and CTRL-RIGHT key combinations. The control’s <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ResizeHeaderPanel"/> option must be enabled to allow such resizing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.MultiEditorRowCellSizing">
      <summary>
        <para>A multi-editor row cell’s separator is being dragged by the end-user. This action results in changing cell width.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.RecordSizing">
      <summary>
        <para>A record’s right edge is being dragged to change record width. The control’s <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ResizeRowValues"/> option must be enabled to allow such resizing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.Regular">
      <summary>
        <para>The control is in its normal state. No specific action is being performed by the end-user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.RowDragging">
      <summary>
        <para>A row’s header is being dragged. The desired row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowMove"/> and the control’s <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.DragRowHeaders"/> options must both be enabled to allow row header dragging. Note that the user can drag row headers regardless of these options’ states if the Customization Form is shown.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraVerticalGrid.VGridState.RowSizing">
      <summary>
        <para>A row header’s bottom edge is being dragged to change row height. The desired row’s <see cref="P:DevExpress.XtraVerticalGrid.Rows.VGridOptionsRow.AllowSize"/> and control’s <see cref="P:DevExpress.XtraVerticalGrid.VGridOptionsBehavior.ResizeRowHeaders"/> options must both be enabled to allow such resizing operations.</para>
      </summary>
    </member>
  </members>
</doc>