-- حل سريع لمشاكل NULL في جدول shifts
USE [مبيعات 2022]
GO

-- إصلا<PERSON> عمود cashier_id
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'cashier_id' AND IS_NULLABLE = 'NO')
BEGIN
    ALTER TABLE shifts ALTER COLUMN cashier_id int NULL
    PRINT 'تم تعديل عمود cashier_id ليسمح بـ NULL'
END

UPDATE shifts SET cashier_id = 0 WHERE cashier_id IS NULL
PRINT 'تم تحديث cashier_id NULL إلى 0'

-- إصلاح عمود store_name
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'shifts' AND COLUMN_NAME = 'store_name' AND IS_NULLABLE = 'NO')
BEGIN
    ALTER TABLE shifts ALTER COLUMN store_name nvarchar(100) NULL
    PRINT 'تم تعديل عمود store_name ليسمح بـ NULL'
END

UPDATE shifts SET store_name = N'المتجر الرئيسي' WHERE store_name IS NULL OR store_name = ''
PRINT 'تم تحديث store_name NULL إلى المتجر الرئيسي'

-- إصلاح باقي الأعمدة التي قد تسبب مشاكل
UPDATE shifts SET start_amount = 0 WHERE start_amount IS NULL
UPDATE shifts SET end_amount = 0 WHERE end_amount IS NULL
UPDATE shifts SET total_sales = 0 WHERE total_sales IS NULL
UPDATE shifts SET total_cash = 0 WHERE total_cash IS NULL
UPDATE shifts SET total_credit = 0 WHERE total_credit IS NULL
UPDATE shifts SET total_profit = 0 WHERE total_profit IS NULL
UPDATE shifts SET total_invoices = 0 WHERE total_invoices IS NULL
UPDATE shifts SET cash_difference = 0 WHERE cash_difference IS NULL

PRINT 'تم تحديث جميع القيم NULL في الأعمدة الرقمية إلى 0'

-- التأكد من وجود قيمة افتراضية لحالة الشيفت
UPDATE shifts SET shift_status = N'مفتوح' WHERE shift_status IS NULL OR shift_status = ''
PRINT 'تم تحديث shift_status NULL إلى مفتوح'

PRINT '============================================'
PRINT 'تم إصلاح جميع مشاكل NULL في جدول shifts'
PRINT 'يمكن الآن استخدام النظام بدون مشاكل'
PRINT '============================================'
