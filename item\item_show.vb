﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors.XtraMessageBox

Public Class item_show
    Private ctr As Integer = 0
    Sub fill_dgv()
        If store.SelectedIndex = 0 Then
            Dim adp As New SqlDataAdapter("select (itemcode),(itemnamearabic),(itemstore),(itemcount),(item_active),(Itemtacategory),(itemcompany),(item_group),(itembuyprice),(Profits),(itemprice1),(total_price),(item_type) from item", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            dgv.DataSource = dt
        Else
            Dim adp As New SqlDataAdapter("select (itemcode),(itemnamearabic),(itemstore),(itemcount),(item_active),(Itemtacategory),(itemcompany),(item_group),(itembuyprice),(Profits),(itemprice1),(total_price),(item_type) from item where itemstore=N'" & (store.Text) & "'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            dgv.DataSource = dt
        End If
        sun_cost()
    End Sub
    Private Sub customer_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        fillstore()
        store.Text = XtraForm1.store.Text
        BackgroundWorker1.RunWorkerAsync()




    End Sub
    Sub fill_barcode()
        For i = 0 To GridView2.RowCount - 1
            Dim sql = "select * from unit_item where code=N'" & GridView2.GetRowCellValue(i, "itemcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)

                Dim tp As Double = 0.0
                Dim dp As Double = 0.0
                Dim sp = (Val(dr!disc_buy) / 100)
                Dim sump = Format(Val(dr!price_buy) * Val(sp), "0.00")

                GridView2.SetRowCellValue(i, "item_make", dr!item_unit)
            End If

        Next
    End Sub
    Sub sun_cost()

        For i = 0 To GridView2.RowCount - 1
            GridView2.SetRowCellValue(i, "total_price", Val(GridView2.GetRowCellValue(i, "itemcount").ToString) * Val(GridView2.GetRowCellValue(i, "itembuyprice").ToString))
        Next
        GridView2.UpdateTotalSummary()
      
    End Sub


    Private Sub fillstore()

        Dim adp As New SqlDataAdapter("select * from store where store_active=N'true'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            store.Properties.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub
    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        Dim f As item_add = New item_add()
        f.Text = "تسجيل صنف"
        f.MdiParent = XtraForm1
        f.new_item()
        f.count_item.ReadOnly = False
        f.Show()
        f.new_unit()
        f.itemunit()  
        f.itemnamearabic.Focus()
    End Sub

    Private Sub delete_btn_Click(sender As Object, e As EventArgs) Handles delete_btn.Click
        If XtraForm1.m55.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد صنف للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If XtraMessageBox.Show("يوجد تعاملات سابقة مع صنف هل انت متاكد من حذفة ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then

            Dim sql = "select * from item where itemcode=N'" & GridView2.GetFocusedRowCellValue("itemcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                delete()
                delete2()
                delete_collection()

                XtraMessageBox.Show("تم حذف صنف بنجاح")
                fill_dgv()
            End If
        End If
    End Sub
    Sub delete()
        For i2 = 0 To 10
            Dim sql = "select * from unit_item where code=N'" & GridView2.GetFocusedRowCellValue("itemcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub
    Sub delete_collection()
        For i2 = 0 To 10
            Dim sql = "select * from item_collection where item_first=N'" & GridView2.GetFocusedRowCellValue("itemcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub
    Sub delete2()
        For i2 = 0 To 10
            Dim sql = "select * from barcode where itemcode=N'" & GridView2.GetFocusedRowCellValue("itemcode") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub
    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If XtraForm1.m54.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد صنف للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تسجيل صنف"
        f.MdiParent = XtraForm1
        f.show_data(GridView2.GetFocusedRowCellValue("itemcode"))
        f.count_item.ReadOnly = True
        f.Show()
    End Sub
    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        GridColumn2.AppearanceCell.Font = New Font("Arial", 11.25, FontStyle.Bold)
        dgv.ShowPrintPreview()
    End Sub
  
    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub store_SelectedIndexChanged(sender As Object, e As EventArgs)
        fill_dgv()
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles w8.Click
        Me.Dispose()
    End Sub
 
    Private Sub report_btn_Click(sender As Object, e As EventArgs) Handles report_btn.Click
       item_trans_show.ShowDialog()

    End Sub
    Private Sub statistic_Click(sender As Object, e As EventArgs) Handles statistic.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Statistic_item = New Statistic_item()
        f.Text = "احصائية صنف"
        f.MdiParent = XtraForm1
        f.Show()
        f.item_name.Text = GridView2.GetFocusedRowCellValue("itemnamearabic")
        f.search()
    End Sub
    Private Sub w1_Click(sender As Object, e As EventArgs) Handles w1.Click
        new_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w2_Click(sender As Object, e As EventArgs) Handles w2.Click
        edit_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w3_Click(sender As Object, e As EventArgs) Handles w3.Click
        delete_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w4_Click(sender As Object, e As EventArgs) Handles w4.Click
        print_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w5_Click(sender As Object, e As EventArgs) Handles w5.Click
        report_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub w6_Click(sender As Object, e As EventArgs) Handles w6.Click
        statistic_Click(Nothing, Nothing)
    End Sub

    Private Sub w7_Click(sender As Object, e As EventArgs) Handles w7.Click
        fill_dgv()
    End Sub

    Private Sub item_show_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            w1_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            w2_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F3 Then
            w3_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F4 Then
            w4_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F5 Then
            w5_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F6 Then
            w6_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F7 Then
            w7_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub store_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles store.SelectedIndexChanged
        fill_dgv()

    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد صنف للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        price_item.code.Text = GridView2.GetFocusedRowCellValue("itemcode")
        price_item.item_name.Text = GridView2.GetFocusedRowCellValue("itemnamearabic")
        price_item.Show()
    End Sub

    Private Sub تكرارالصنفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تكرارالصنفToolStripMenuItem.Click
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد صنف للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تسجيل صنف"
        f.MdiParent = XtraForm1
        f.show_data(GridView2.GetFocusedRowCellValue("itemcode"))
        f.item_id()
        f.clearpic()
        f.Show()
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        Dim f As item_unit = New item_unit()
        f.Text = "عرض بالوحدات"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        Dim f As item_edit = New item_edit()
        f.Text = "التعديل السريع"
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub اعادةاحتسابالتكلفةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles اعادةاحتسابالتكلفةToolStripMenuItem.Click
        sun_cost()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        Dim f As frmbarcode = New frmbarcode()
        f.Text = "طباعة باركود"
        f.MdiParent = XtraForm1
        f.Show()
        f.txtmarket.Text = GridView2.GetFocusedRowCellValue("Itemtacategory")
        f.txtpname.Text = GridView2.GetFocusedRowCellValue("itemnamearabic")
        f.txtPrice.Text = GridView2.GetFocusedRowCellValue("itemprice1")
        Dim sql = "select * from unit_item where code=N'" & (GridView2.GetFocusedRowCellValue("itemcode")) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            f.txtbarcode.Text = dr!item_unit
        End If
    End Sub

    Private Sub Button11_Click(sender As Object, e As EventArgs) Handles Button11.Click
        GridView2.ShowCustomization()
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        Dim adp As New SqlDataAdapter("select * from item", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If

        Dim rept
        rept = New itemReport1


     
        rept.datasource = dt
        Dim frm As New preview
        preview.DocumentViewer1.DocumentSource = rept
        preview.Show()
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        Dim adp As New SqlDataAdapter("select * from item where itemcode=N'" & (TextBox2.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt
        barc_show.Text = ""
        barc_show.Focus()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        If barc_show.Visible = False Then
            barc_show.Visible = True
            barc_show.Text = ""
            barc_show.Focus()
            Exit Sub
        End If
        If barc_show.Visible = True Then
            barc_show.Visible = False

        End If
    End Sub


    Private Sub barc_show_KeyDown(sender As Object, e As KeyEventArgs) Handles barc_show.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from unit_item where item_unit=N'" & (barc_show.Text) & "'"
             Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)

                TextBox2.Text = dr!code
            End If
        End If
    End Sub

    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        Dim f As frmbarcode = New frmbarcode()
     
        f.txtmarket.Text = GridView2.GetFocusedRowCellValue("Itemtacategory")
        f.txtpname.Text = GridView2.GetFocusedRowCellValue("itemnamearabic")
        f.txtPrice.Text = GridView2.GetFocusedRowCellValue("itemprice1")
        Dim sql = "select * from unit_item where code=N'" & (GridView2.GetFocusedRowCellValue("itemcode")) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            f.txtbarcode.Text = dr!item_unit
        End If
        f.ComboBox1.Text = My.Settings.u1
        f.ComboBox2.Text = My.Settings.u2
        f.sa()
    End Sub

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork

    End Sub
End Class