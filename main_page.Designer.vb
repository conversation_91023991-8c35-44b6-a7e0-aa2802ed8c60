﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class main_page
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(main_page))
        Dim TileItemElement1 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement2 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement3 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement4 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement5 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement6 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement7 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemFrame1 As DevExpress.XtraEditors.TileItemFrame = New DevExpress.XtraEditors.TileItemFrame()
        Dim TileItemElement8 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemFrame2 As DevExpress.XtraEditors.TileItemFrame = New DevExpress.XtraEditors.TileItemFrame()
        Dim TileItemElement9 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement10 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement11 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement12 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement13 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement14 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement15 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement16 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement17 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label51 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Button5 = New System.Windows.Forms.Button()
        Me.TileControl1 = New DevExpress.XtraEditors.TileControl()
        Me.TileGroup2 = New DevExpress.XtraEditors.TileGroup()
        Me.TileItem1 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem2 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem3 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem4 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem5 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem6 = New DevExpress.XtraEditors.TileItem()
        Me.TileGroup3 = New DevExpress.XtraEditors.TileGroup()
        Me.TileItem7 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem8 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem9 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem10 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem11 = New DevExpress.XtraEditors.TileItem()
        Me.TileGroup4 = New DevExpress.XtraEditors.TileGroup()
        Me.TileItem12 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem13 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem15 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem16 = New DevExpress.XtraEditors.TileItem()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Label8
        '
        Me.Label8.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Label8.Location = New System.Drawing.Point(-1, -1)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(1165, 60)
        Me.Label8.TabIndex = 1165
        '
        'Label51
        '
        Me.Label51.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Label51.Font = New System.Drawing.Font("Segoe UI", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label51.ForeColor = System.Drawing.Color.White
        Me.Label51.Location = New System.Drawing.Point(120, 15)
        Me.Label51.Name = "Label51"
        Me.Label51.Size = New System.Drawing.Size(200, 30)
        Me.Label51.TabIndex = 1182
        Me.Label51.Text = "نظام إدارة المبيعات"
        Me.Label51.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Label1.Font = New System.Drawing.Font("Segoe UI", 14.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(330, 15)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(80, 30)
        Me.Label1.TabIndex = 1253
        Me.Label1.Text = "POS"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox1
        '
        Me.PictureBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.PictureBox1.Image = Global.spaxet.My.Resources.Resources.hhhjjjj
        Me.PictureBox1.Location = New System.Drawing.Point(40, 8)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(70, 44)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 1257
        Me.PictureBox1.TabStop = False
        '
        'Button5
        '
        Me.Button5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Button5.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Button5.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button5.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.Button5.FlatAppearance.BorderSize = 0
        Me.Button5.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.Button5.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button5.Font = New System.Drawing.Font("Segoe UI", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button5.Image = CType(resources.GetObject("Button5.Image"), System.Drawing.Image)
        Me.Button5.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button5.Location = New System.Drawing.Point(1110, 15)
        Me.Button5.Margin = New System.Windows.Forms.Padding(2, 3, 2, 3)
        Me.Button5.Name = "Button5"
        Me.Button5.Size = New System.Drawing.Size(35, 30)
        Me.Button5.TabIndex = 1254
        Me.Button5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button5.UseVisualStyleBackColor = False
        '
        'TileControl1
        '
        Me.TileControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TileControl1.AppearanceGroupText.Font = New System.Drawing.Font("Segoe UI", 14.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileControl1.AppearanceGroupText.ForeColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.TileControl1.AppearanceGroupText.Options.UseFont = True
        Me.TileControl1.AppearanceGroupText.Options.UseForeColor = True
        Me.TileControl1.AppearanceGroupText.Options.UseTextOptions = True
        Me.TileControl1.AppearanceGroupText.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.TileControl1.AppearanceItem.Hovered.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Hovered.Options.UseFont = True
        Me.TileControl1.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Normal.Options.UseFont = True
        Me.TileControl1.AppearanceItem.Pressed.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Pressed.Options.UseFont = True
        Me.TileControl1.AppearanceItem.Selected.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Selected.Options.UseFont = True
        Me.TileControl1.AppearanceText.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileControl1.AppearanceText.Options.UseFont = True
        Me.TileControl1.BackgroundImage = CType(resources.GetObject("TileControl1.BackgroundImage"), System.Drawing.Image)
        Me.TileControl1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.TileControl1.Groups.Add(Me.TileGroup2)
        Me.TileControl1.Groups.Add(Me.TileGroup3)
        Me.TileControl1.Groups.Add(Me.TileGroup4)
        Me.TileControl1.Location = New System.Drawing.Point(-1, 60)
        Me.TileControl1.MaxId = 37
        Me.TileControl1.Name = "TileControl1"
        Me.TileControl1.RowCount = 8
        Me.TileControl1.Size = New System.Drawing.Size(1162, 579)
        Me.TileControl1.ItemSize = 120
        Me.TileControl1.ItemPadding = New System.Windows.Forms.Padding(8)
        Me.TileControl1.TabIndex = 1100
        Me.TileControl1.Text = "TileControl1"
        '
        'TileGroup2
        '
        Me.TileGroup2.Items.Add(Me.TileItem1)
        Me.TileGroup2.Items.Add(Me.TileItem2)
        Me.TileGroup2.Items.Add(Me.TileItem3)
        Me.TileGroup2.Items.Add(Me.TileItem4)
        Me.TileGroup2.Items.Add(Me.TileItem5)
        Me.TileGroup2.Items.Add(Me.TileItem6)
        Me.TileGroup2.Name = "TileGroup2"
        Me.TileGroup2.Text = "المدخلات"
        '
        'TileItem1
        '
        Me.TileItem1.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.TileItem1.AppearanceItem.Hovered.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem1.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem1.AppearanceItem.Hovered.Options.UseFont = True
        Me.TileItem1.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.TileItem1.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem1.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem1.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem1.AppearanceItem.Pressed.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold)
        Me.TileItem1.AppearanceItem.Pressed.Options.UseFont = True
        Me.TileItem1.AppearanceItem.Selected.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold)
        Me.TileItem1.AppearanceItem.Selected.Options.UseFont = True
        TileItemElement1.AnchorIndent = 8
        TileItemElement1.Height = -2
        TileItemElement1.ImageOptions.Image = CType(resources.GetObject("resource.Image"), System.Drawing.Image)
        TileItemElement1.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement1.Text = "الاصناف"
        TileItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement1.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem1.Elements.Add(TileItemElement1)
        Me.TileItem1.Id = 0
        Me.TileItem1.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem1.Name = "TileItem1"
        '
        'TileItem2
        '
        Me.TileItem2.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.TileItem2.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem2.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem2.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem2.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(56, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(133, Byte), Integer))
        Me.TileItem2.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement2.ImageOptions.Image = CType(resources.GetObject("resource.Image1"), System.Drawing.Image)
        TileItemElement2.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement2.Text = "العملاء"
        TileItemElement2.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement2.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem2.Elements.Add(TileItemElement2)
        Me.TileItem2.Id = 1
        Me.TileItem2.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem2.Name = "TileItem2"
        '
        'TileItem3
        '
        Me.TileItem3.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.TileItem3.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem3.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem3.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem3.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(175, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(202, Byte), Integer))
        Me.TileItem3.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement3.ImageOptions.Image = CType(resources.GetObject("resource.Image2"), System.Drawing.Image)
        TileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement3.Text = "الموردين"
        TileItemElement3.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement3.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem3.Elements.Add(TileItemElement3)
        Me.TileItem3.Id = 2
        Me.TileItem3.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem3.Name = "TileItem3"
        '
        'TileItem4
        '
        Me.TileItem4.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.TileItem4.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem4.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem4.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem4.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(80, Byte), Integer))
        Me.TileItem4.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement4.ImageOptions.Image = CType(resources.GetObject("resource.Image3"), System.Drawing.Image)
        TileItemElement4.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement4.Text = "الموظفين"
        TileItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement4.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem4.Elements.Add(TileItemElement4)
        Me.TileItem4.Id = 3
        Me.TileItem4.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem4.Name = "TileItem4"
        '
        'TileItem5
        '
        Me.TileItem5.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.TileItem5.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem5.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem5.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem5.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(213, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.TileItem5.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement5.ImageOptions.Image = CType(resources.GetObject("resource.Image4"), System.Drawing.Image)
        TileItemElement5.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement5.Text = "الخزينة"
        TileItemElement5.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement5.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem5.Elements.Add(TileItemElement5)
        Me.TileItem5.Id = 4
        Me.TileItem5.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem5.Name = "TileItem5"
        '
        'TileItem6
        '
        Me.TileItem6.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.TileItem6.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem6.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem6.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem6.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(114, Byte), Integer))
        Me.TileItem6.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement6.ImageOptions.Image = CType(resources.GetObject("resource.Image5"), System.Drawing.Image)
        TileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement6.Text = "المصروفات"
        TileItemElement6.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement6.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem6.Elements.Add(TileItemElement6)
        Me.TileItem6.Id = 5
        Me.TileItem6.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem6.Name = "TileItem6"
        '
        'TileGroup3
        '
        Me.TileGroup3.Items.Add(Me.TileItem7)
        Me.TileGroup3.Items.Add(Me.TileItem8)
        Me.TileGroup3.Items.Add(Me.TileItem9)
        Me.TileGroup3.Items.Add(Me.TileItem10)
        Me.TileGroup3.Items.Add(Me.TileItem11)
        Me.TileGroup3.Name = "TileGroup3"
        Me.TileGroup3.Text = "التعاملات"
        '
        'TileItem7
        '
        Me.TileItem7.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(188, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.TileItem7.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem7.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem7.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem7.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(208, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.TileItem7.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem7.CurrentFrameIndex = 1
        TileItemElement7.ImageOptions.Image = CType(resources.GetObject("resource.Image6"), System.Drawing.Image)
        TileItemElement7.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement7.Text = "فاتورة بيع"
        TileItemElement7.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement7.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem7.Elements.Add(TileItemElement7)
        TileItemElement8.ImageOptions.Image = CType(resources.GetObject("resource.Image7"), System.Drawing.Image)
        TileItemElement8.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement8.Text = "فاتورة بيع"
        TileItemElement8.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement8.TextLocation = New System.Drawing.Point(0, -10)
        TileItemFrame1.Elements.Add(TileItemElement8)
        TileItemFrame1.Image = CType(resources.GetObject("TileItemFrame1.Image"), System.Drawing.Image)
        TileItemElement9.ImageOptions.Image = CType(resources.GetObject("resource.Image8"), System.Drawing.Image)
        TileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement9.Text = "فاتورة بيع"
        TileItemElement9.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement9.TextLocation = New System.Drawing.Point(0, -10)
        TileItemFrame2.Elements.Add(TileItemElement9)
        TileItemFrame2.Image = CType(resources.GetObject("TileItemFrame2.Image"), System.Drawing.Image)
        Me.TileItem7.Frames.Add(TileItemFrame1)
        Me.TileItem7.Frames.Add(TileItemFrame2)
        Me.TileItem7.Id = 9
        Me.TileItem7.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide
        Me.TileItem7.Name = "TileItem7"
        '
        'TileItem8
        '
        Me.TileItem8.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.TileItem8.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem8.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem8.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem8.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(146, Byte), Integer), CType(CType(54, Byte), Integer))
        Me.TileItem8.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement10.ImageOptions.Image = CType(resources.GetObject("resource.Image9"), System.Drawing.Image)
        TileItemElement10.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement10.Text = "فاتوة مشتريات"
        TileItemElement10.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement10.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem8.Elements.Add(TileItemElement10)
        Me.TileItem8.Id = 10
        Me.TileItem8.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem8.Name = "TileItem8"
        '
        'TileItem9
        '
        Me.TileItem9.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.TileItem9.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem9.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem9.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem9.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(172, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.TileItem9.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement11.ImageOptions.Image = CType(resources.GetObject("resource.Image10"), System.Drawing.Image)
        TileItemElement11.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement11.Text = "مرتجع مبيعات"
        TileItemElement11.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement11.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem9.Elements.Add(TileItemElement11)
        Me.TileItem9.Id = 11
        Me.TileItem9.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem9.Name = "TileItem9"
        '
        'TileItem10
        '
        Me.TileItem10.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.TileItem10.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem10.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem10.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem10.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(175, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(202, Byte), Integer))
        Me.TileItem10.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement12.ImageOptions.Image = CType(resources.GetObject("resource.Image11"), System.Drawing.Image)
        TileItemElement12.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement12.Text = "مرتجع مشتريات"
        TileItemElement12.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement12.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem10.Elements.Add(TileItemElement12)
        Me.TileItem10.Id = 12
        Me.TileItem10.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem10.Name = "TileItem10"
        '
        'TileItem11
        '
        Me.TileItem11.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.TileItem11.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem11.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem11.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem11.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(66, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(133, Byte), Integer))
        Me.TileItem11.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement13.ImageOptions.Image = CType(resources.GetObject("resource.Image12"), System.Drawing.Image)
        TileItemElement13.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement13.Text = "بيان أسعار"
        TileItemElement13.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement13.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem11.Elements.Add(TileItemElement13)
        Me.TileItem11.Id = 13
        Me.TileItem11.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem11.Name = "TileItem11"
        '
        'TileGroup4
        '
        Me.TileGroup4.Items.Add(Me.TileItem12)
        Me.TileGroup4.Items.Add(Me.TileItem13)
        Me.TileGroup4.Items.Add(Me.TileItem15)
        Me.TileGroup4.Items.Add(Me.TileItem16)
        Me.TileGroup4.Name = "TileGroup4"
        Me.TileGroup4.Text = "اخري"
        '
        'TileItem12
        '
        Me.TileItem12.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.TileItem12.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem12.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem12.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem12.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(80, Byte), Integer))
        Me.TileItem12.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement14.ImageOptions.Image = CType(resources.GetObject("resource.Image13"), System.Drawing.Image)
        TileItemElement14.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement14.Text = "تحصيل الفواتير"
        TileItemElement14.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement14.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem12.Elements.Add(TileItemElement14)
        Me.TileItem12.Id = 14
        Me.TileItem12.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem12.Name = "TileItem12"
        '
        'TileItem13
        '
        Me.TileItem13.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.TileItem13.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem13.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem13.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem13.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(114, Byte), Integer))
        Me.TileItem13.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement15.ImageOptions.Image = CType(resources.GetObject("resource.Image14"), System.Drawing.Image)
        TileItemElement15.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement15.Text = "سداد الفواتير"
        TileItemElement15.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement15.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem13.Elements.Add(TileItemElement15)
        Me.TileItem13.Id = 15
        Me.TileItem13.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem13.Name = "TileItem13"
        '
        'TileItem15
        '
        Me.TileItem15.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.TileItem15.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem15.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem15.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem15.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(175, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(202, Byte), Integer))
        Me.TileItem15.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement16.ImageOptions.Image = CType(resources.GetObject("resource.Image15"), System.Drawing.Image)
        TileItemElement16.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement16.Text = "تحويلات مخازن"
        TileItemElement16.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement16.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem15.Elements.Add(TileItemElement16)
        Me.TileItem15.Id = 17
        Me.TileItem15.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem15.Name = "TileItem15"
        '
        'TileItem16
        '
        Me.TileItem16.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.TileItem16.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem16.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem16.AppearanceItem.Normal.Options.UseFont = True
        Me.TileItem16.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(66, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(133, Byte), Integer))
        Me.TileItem16.AppearanceItem.Hovered.Options.UseBackColor = True
        TileItemElement17.ImageOptions.Image = CType(resources.GetObject("resource.Image16"), System.Drawing.Image)
        TileItemElement17.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement17.Text = "حضور وانصراف"
        TileItemElement17.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement17.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem16.Elements.Add(TileItemElement17)
        Me.TileItem16.Id = 18
        Me.TileItem16.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem16.Name = "TileItem16"
        '
        'main_page
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(1200, 650)
        Me.ControlBox = False
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.Label51)
        Me.Controls.Add(Me.Button5)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.TileControl1)
        Me.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ForeColor = System.Drawing.Color.Black
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "main_page"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "برنامج بيتا سوفت المحاسبي من شركة بيتا سوفت للبرمجيات (01028521771)"
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label51 As System.Windows.Forms.Label
    Friend WithEvents TileControl1 As DevExpress.XtraEditors.TileControl
    Friend WithEvents TileGroup2 As DevExpress.XtraEditors.TileGroup
    Friend WithEvents TileItem1 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem2 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem3 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem4 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem5 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem6 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileGroup3 As DevExpress.XtraEditors.TileGroup
    Friend WithEvents TileItem7 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem8 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem9 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem10 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem11 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileGroup4 As DevExpress.XtraEditors.TileGroup
    Friend WithEvents TileItem12 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem13 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem15 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem16 As DevExpress.XtraEditors.TileItem
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Button5 As System.Windows.Forms.Button
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
End Class
