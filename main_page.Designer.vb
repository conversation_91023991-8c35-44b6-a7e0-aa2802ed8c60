﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class main_page
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(main_page))
        Dim TileItemElement30 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement29 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement21 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemFrame5 As DevExpress.XtraEditors.TileItemFrame = New DevExpress.XtraEditors.TileItemFrame()
        Dim TileItemElement22 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemFrame6 As DevExpress.XtraEditors.TileItemFrame = New DevExpress.XtraEditors.TileItemFrame()
        Dim TileItemElement23 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement25 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement24 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement26 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement27 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Dim TileItemElement28 As DevExpress.XtraEditors.TileItemElement = New DevExpress.XtraEditors.TileItemElement()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label51 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Button5 = New System.Windows.Forms.Button()
        Me.TileControl1 = New DevExpress.XtraEditors.TileControl()
        Me.TileItem5 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem6 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem7 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem8 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem9 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem10 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem12 = New DevExpress.XtraEditors.TileItem()
        Me.TileItem13 = New DevExpress.XtraEditors.TileItem()
        Me.TileGroup2 = New DevExpress.XtraEditors.TileGroup()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Label8
        '
        Me.Label8.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Label8.Location = New System.Drawing.Point(-1, 0)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(1427, 60)
        Me.Label8.TabIndex = 1165
        '
        'Label51
        '
        Me.Label51.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Label51.Font = New System.Drawing.Font("Segoe UI", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label51.ForeColor = System.Drawing.Color.White
        Me.Label51.Location = New System.Drawing.Point(120, 15)
        Me.Label51.Name = "Label51"
        Me.Label51.Size = New System.Drawing.Size(200, 30)
        Me.Label51.TabIndex = 1182
        Me.Label51.Text = "نظام إدارة المبيعات"
        Me.Label51.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Label1.Font = New System.Drawing.Font("Segoe UI", 14.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(330, 15)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(80, 30)
        Me.Label1.TabIndex = 1253
        Me.Label1.Text = "POS"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox1
        '
        Me.PictureBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.PictureBox1.Image = Global.spaxet.My.Resources.Resources.hhhjjjj
        Me.PictureBox1.Location = New System.Drawing.Point(56, 12)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(70, 44)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 1257
        Me.PictureBox1.TabStop = False
        '
        'Button5
        '
        Me.Button5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Button5.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.Button5.Cursor = System.Windows.Forms.Cursors.Hand
        Me.Button5.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.Button5.FlatAppearance.BorderSize = 0
        Me.Button5.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.Button5.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Button5.Font = New System.Drawing.Font("Segoe UI", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button5.Image = CType(resources.GetObject("Button5.Image"), System.Drawing.Image)
        Me.Button5.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button5.Location = New System.Drawing.Point(1341, 15)
        Me.Button5.Margin = New System.Windows.Forms.Padding(2, 3, 2, 3)
        Me.Button5.Name = "Button5"
        Me.Button5.Size = New System.Drawing.Size(35, 30)
        Me.Button5.TabIndex = 1254
        Me.Button5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button5.UseVisualStyleBackColor = False
        '
        'TileControl1
        '
        Me.TileControl1.AppearanceGroupText.Font = New System.Drawing.Font("Segoe UI", 14.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileControl1.AppearanceGroupText.ForeColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(185, Byte), Integer))
        Me.TileControl1.AppearanceGroupText.Options.UseFont = True
        Me.TileControl1.AppearanceGroupText.Options.UseForeColor = True
        Me.TileControl1.AppearanceGroupText.Options.UseTextOptions = True
        Me.TileControl1.AppearanceGroupText.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.TileControl1.AppearanceItem.Hovered.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Hovered.Options.UseFont = True
        Me.TileControl1.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Normal.Options.UseFont = True
        Me.TileControl1.AppearanceItem.Pressed.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Pressed.Options.UseFont = True
        Me.TileControl1.AppearanceItem.Selected.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TileControl1.AppearanceItem.Selected.Options.UseFont = True
        Me.TileControl1.AppearanceText.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileControl1.AppearanceText.Options.UseFont = True
        Me.TileControl1.BackgroundImage = CType(resources.GetObject("TileControl1.BackgroundImage"), System.Drawing.Image)
        Me.TileControl1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.TileControl1.Dock = System.Windows.Forms.DockStyle.Left
        Me.TileControl1.Groups.Add(Me.TileGroup2)
        Me.TileControl1.ItemPadding = New System.Windows.Forms.Padding(8)
        Me.TileControl1.Location = New System.Drawing.Point(0, 0)
        Me.TileControl1.MaxId = 37
        Me.TileControl1.Name = "TileControl1"
        Me.TileControl1.Orientation = System.Windows.Forms.Orientation.Vertical
        Me.TileControl1.RowCount = 8
        Me.TileControl1.Size = New System.Drawing.Size(1423, 687)
        Me.TileControl1.TabIndex = 1100
        Me.TileControl1.Text = "TileControl1"
        Me.TileControl1.VerticalContentAlignment = DevExpress.Utils.VertAlignment.Bottom
        '
        'TileItem5
        '
        Me.TileItem5.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(213, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.TileItem5.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem5.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.TileItem5.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem5.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem5.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement30.ImageOptions.Image = CType(resources.GetObject("resource.Image9"), System.Drawing.Image)
        TileItemElement30.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement30.Text = "الخزينة"
        TileItemElement30.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement30.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem5.Elements.Add(TileItemElement30)
        Me.TileItem5.Id = 4
        Me.TileItem5.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem5.Name = "TileItem5"
        '
        'TileItem6
        '
        Me.TileItem6.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(114, Byte), Integer))
        Me.TileItem6.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem6.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.TileItem6.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem6.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem6.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement29.ImageOptions.Image = CType(resources.GetObject("resource.Image8"), System.Drawing.Image)
        TileItemElement29.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement29.Text = "المصروفات"
        TileItemElement29.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement29.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem6.Elements.Add(TileItemElement29)
        Me.TileItem6.Id = 5
        Me.TileItem6.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem6.Name = "TileItem6"
        '
        'TileItem7
        '
        Me.TileItem7.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(208, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.TileItem7.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem7.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(188, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.TileItem7.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem7.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem7.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement21.ImageOptions.Image = CType(resources.GetObject("resource.Image"), System.Drawing.Image)
        TileItemElement21.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement21.Text = "فاتورة بيع"
        TileItemElement21.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement21.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem7.Elements.Add(TileItemElement21)
        TileItemElement22.ImageOptions.Image = CType(resources.GetObject("resource.Image1"), System.Drawing.Image)
        TileItemElement22.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement22.Text = "فاتورة بيع"
        TileItemElement22.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement22.TextLocation = New System.Drawing.Point(0, -10)
        TileItemFrame5.Elements.Add(TileItemElement22)
        TileItemFrame5.Image = CType(resources.GetObject("TileItemFrame5.Image"), System.Drawing.Image)
        TileItemElement23.ImageOptions.Image = CType(resources.GetObject("resource.Image2"), System.Drawing.Image)
        TileItemElement23.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement23.Text = "فاتورة بيع"
        TileItemElement23.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement23.TextLocation = New System.Drawing.Point(0, -10)
        TileItemFrame6.Elements.Add(TileItemElement23)
        TileItemFrame6.Image = CType(resources.GetObject("TileItemFrame6.Image"), System.Drawing.Image)
        Me.TileItem7.Frames.Add(TileItemFrame5)
        Me.TileItem7.Frames.Add(TileItemFrame6)
        Me.TileItem7.Id = 9
        Me.TileItem7.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide
        Me.TileItem7.Name = "TileItem7"
        '
        'TileItem8
        '
        Me.TileItem8.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(146, Byte), Integer), CType(CType(54, Byte), Integer))
        Me.TileItem8.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem8.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.TileItem8.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem8.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem8.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement25.ImageOptions.Image = CType(resources.GetObject("resource.Image4"), System.Drawing.Image)
        TileItemElement25.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement25.Text = "فاتوة مشتريات"
        TileItemElement25.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement25.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem8.Elements.Add(TileItemElement25)
        Me.TileItem8.Id = 10
        Me.TileItem8.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem8.Name = "TileItem8"
        '
        'TileItem9
        '
        Me.TileItem9.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(172, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.TileItem9.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem9.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.TileItem9.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem9.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem9.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement24.ImageOptions.Image = CType(resources.GetObject("resource.Image3"), System.Drawing.Image)
        TileItemElement24.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement24.Text = "مرتجع مبيعات"
        TileItemElement24.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement24.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem9.Elements.Add(TileItemElement24)
        Me.TileItem9.Id = 11
        Me.TileItem9.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem9.Name = "TileItem9"
        '
        'TileItem10
        '
        Me.TileItem10.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(175, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(202, Byte), Integer))
        Me.TileItem10.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem10.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.TileItem10.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem10.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem10.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement26.ImageOptions.Image = CType(resources.GetObject("resource.Image5"), System.Drawing.Image)
        TileItemElement26.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement26.Text = "مرتجع مشتريات"
        TileItemElement26.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement26.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem10.Elements.Add(TileItemElement26)
        Me.TileItem10.Id = 12
        Me.TileItem10.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem10.Name = "TileItem10"
        '
        'TileItem12
        '
        Me.TileItem12.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(80, Byte), Integer))
        Me.TileItem12.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem12.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.TileItem12.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem12.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem12.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement27.ImageOptions.Image = CType(resources.GetObject("resource.Image6"), System.Drawing.Image)
        TileItemElement27.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement27.Text = "تحصيل الفواتير"
        TileItemElement27.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement27.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem12.Elements.Add(TileItemElement27)
        Me.TileItem12.Id = 14
        Me.TileItem12.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem12.Name = "TileItem12"
        '
        'TileItem13
        '
        Me.TileItem13.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(114, Byte), Integer))
        Me.TileItem13.AppearanceItem.Hovered.Options.UseBackColor = True
        Me.TileItem13.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.TileItem13.AppearanceItem.Normal.Font = New System.Drawing.Font("Segoe UI", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TileItem13.AppearanceItem.Normal.Options.UseBackColor = True
        Me.TileItem13.AppearanceItem.Normal.Options.UseFont = True
        TileItemElement28.ImageOptions.Image = CType(resources.GetObject("resource.Image7"), System.Drawing.Image)
        TileItemElement28.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter
        TileItemElement28.Text = "سداد الفواتير"
        TileItemElement28.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter
        TileItemElement28.TextLocation = New System.Drawing.Point(0, -10)
        Me.TileItem13.Elements.Add(TileItemElement28)
        Me.TileItem13.Id = 15
        Me.TileItem13.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium
        Me.TileItem13.Name = "TileItem13"
        '
        'TileGroup2
        '
        Me.TileGroup2.Items.Add(Me.TileItem7)
        Me.TileGroup2.Items.Add(Me.TileItem9)
        Me.TileGroup2.Items.Add(Me.TileItem8)
        Me.TileGroup2.Items.Add(Me.TileItem10)
        Me.TileGroup2.Items.Add(Me.TileItem12)
        Me.TileGroup2.Items.Add(Me.TileItem13)
        Me.TileGroup2.Items.Add(Me.TileItem6)
        Me.TileGroup2.Items.Add(Me.TileItem5)
        Me.TileGroup2.Name = "TileGroup2"
        '
        'main_page
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(1431, 687)
        Me.ControlBox = False
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.Label51)
        Me.Controls.Add(Me.Button5)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.TileControl1)
        Me.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ForeColor = System.Drawing.Color.Black
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "main_page"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "برنامج بيتا سوفت المحاسبي من شركة بيتا سوفت للبرمجيات (01028521771)"
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label51 As System.Windows.Forms.Label
    Friend WithEvents TileControl1 As DevExpress.XtraEditors.TileControl
    Friend WithEvents TileItem5 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem6 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem7 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem8 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem9 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem10 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem12 As DevExpress.XtraEditors.TileItem
    Friend WithEvents TileItem13 As DevExpress.XtraEditors.TileItem
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Button5 As System.Windows.Forms.Button
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents TileGroup2 As DevExpress.XtraEditors.TileGroup
End Class
