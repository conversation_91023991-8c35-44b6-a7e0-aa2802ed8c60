﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox


Public Class customer_add
    Private rng As New Random
    Private Sub customer_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        show_price1()
        show_price2()
        show_price3()
        show_price4()
        show_price5()


    End Sub
    Sub show_price1()
        Dim sql = "select * from name_price where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price1.Text = dr!name_price
        price1.Visible = dr!price_check

    End Sub
    Sub show_price2()
        Dim sql = "select * from name_price where id=N'" & (2) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price2.Text = dr!name_price
        price2.Visible = dr!price_check

    End Sub
    Sub show_price3()
        Dim sql = "select * from name_price where id=N'" & (3) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price3.Text = dr!name_price
        price3.Visible = dr!price_check

    End Sub
    Sub show_price4()
        Dim sql = "select * from name_price where id=N'" & (4) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price6.Text = dr!name_price
        price6.Visible = dr!price_check

    End Sub
    Sub show_price5()
        Dim sql = "select * from name_price where id=N'" & (5) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price5.Text = dr!name_price
        price5.Visible = dr!price_check
    End Sub

    Sub new_cus()
        GroupBox2.Enabled = True
        Accounts_balace.Enabled = True
        Cuscode.Text = getlastcode("customer", "cuscode") + 1
        PictureBox1.Image = Nothing
        Cusname.Text = ""
        Accounts_balace.Text = 0
        cus_pic.Image = My.Resources.talibov180200087

        cus_active.Checked = True
        CusDebtor.Checked = True
        cus_address.Text = ""
        cus_phone1.Text = ""
        cus_phone2.Text = ""
        Administrator.Text = ""
        cus_fax.Text = ""
        cus_post.Text = ""
        cus_email.Text = ""
        cus_card.Text = ""
        cus_job.Text = ""
        CusDiscountauto.Text = 0
        Custaxauto.Text = 0
        CusCreditlimit.Text = 1000000
        cash.Checked = True
        price1.Checked = True
        cus_price_private.Text = 0
        showyes.Checked = True
        user_name.Text = ""
        user_pass.Text = ""
        Cusname.Focus()
    End Sub
    Sub filldelegate()
        CusDelegate.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from delegate order by delegate_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            CusDelegate.Properties.Items.Add(dt.Rows(i).Item("delegate_name"))
        Next
    End Sub
    Sub fillgroup1()

        Cuscusgroup.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from group1 order by group_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            Cuscusgroup.Properties.Items.Add(dt.Rows(i).Item("group_name"))
        Next
    End Sub
    Sub fillcity()

        Cuscity.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from city order by city_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            Cuscity.Properties.Items.Add(dt.Rows(i).Item("city_name"))
        Next

    End Sub
    Sub fillGovernorate()

        CusGovernorate.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Governorate order by Governorate_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            CusGovernorate.Properties.Items.Add(dt.Rows(i).Item("Governorate_name"))
        Next
    End Sub
    Sub fill()
        fillgroup1()
        filldelegate()
        fillGovernorate()
        fillcity()
    End Sub
    '============================ show data
    Public Sub show_data(x)
        Dim sql = "select * from customer where Cuscode=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("لم يتم العثور علي بيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim dr = dt.Rows(0)
            Cuscode.Text = dr!Cuscode
            Cusname.Text = dr!cusname
            Accounts_balace.Text = dr!Accounts_balace
            cus_active.Checked = dr!cus_active

            cus_address.Text = dr!cus_address
            cus_phone1.Text = dr!cus_phone1
            cus_phone2.Text = dr!cus_phone2
            Administrator.Text = dr!Administrator
            cus_fax.Text = dr!cus_fax
            cus_post.Text = dr!cus_post
            cus_email.Text = dr!cus_email
            cus_card.Text = dr!cus_card
            cus_job.Text = dr!cus_job
            CusDiscountauto.Text = dr!CusDiscountauto
            Custaxauto.Text = dr!Custaxauto
            CusCreditlimit.Text = dr!CusCreditlimit
            Cuscusgroup.Text = dr!Cuscusgroup
            CusDelegate.Text = dr!CusDelegate
            CusGovernorate.Text = dr!cusGovernorate
            Cuscity.Text = dr!cuscity
            user_name.Text = dr!user_name
            user_pass.Text = dr!user_pass
            If dr!custypesale = 1 Then price1.Checked = True
            If dr!custypesale = 2 Then price2.Checked = True
            If dr!custypesale = 3 Then price3.Checked = True
            If dr!custypesale = 4 Then price4.Checked = True
            If dr!custypesale = 5 Then price5.Checked = True
            If dr!custypesale = 6 Then price6.Checked = True
            If dr!custypemoney = 1 Then cash.Checked = True
            If dr!custypemoney = 2 Then ajl.Checked = True
            If dr!cusshow = True Then showyes.Checked = True
            If dr!cusshow = False Then showno.Checked = True
            '========================================
            '======================== فك تشفير الصورة------
            If IsDBNull(dr!cus_pic) = False Then
                Dim imagbytearray() As Byte
                imagbytearray = CType(dr!cus_pic, Byte())
                Dim stream As New MemoryStream(imagbytearray)
                Dim bmp As New Bitmap(stream)
                cus_pic.Image = Image.FromStream(stream)
                stream.Close()
            End If
            '========================================
            code_pic.Text = 0
            Accounts_balace.Enabled = False
            GroupBox2.Enabled = False
            Cuscode.Enabled = False

            Cusname.Focus()
        End If
    End Sub
    Private Sub customer_add_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.Enter Then
            save_btn_Click()
        End If
        If e.KeyCode = Keys.F9 Then
            fill()
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub
    Private Sub exit_btn_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub
    Sub save_btn_Click()
        If Accounts_balace.Enabled = True Then
            Cuscode.Text = getlastcode("customer", "cuscode") + 1
        End If
        erorr_number()
        If Cusname.Text = "" Then
            XtraMessageBox.Show("اسم العميل فارغ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ErrorProvider1.SetError(Cusname, "اسم العميل فارغ")
            Cusname.Focus()
            Exit Sub
        End If
        If Accounts_balace.Enabled = True Then
            Dim sql1 = "select * from customer where cusname=N'" & (Cusname.Text) & "'"
            Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
            Dim ds1 As New DataSet
            adp1.Fill(ds1)
            Dim dt1 = ds1.Tables(0)
            If dt1.Rows.Count > 0 Then
                XtraMessageBox.Show("يوجد عميل بهذا الاسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                ErrorProvider1.SetError(Cusname, "يوجد عميل بهذا الاسم")
                Exit Sub
            End If
        End If

        Dim sql = "select * from customer where cuscode=N'" & (Cuscode.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            '========= بيانات اساسية============
            dr!cusname = Cusname.Text
            dr!cus_active = cus_active.Checked

            dr!cus_address = cus_address.Text
            dr!cus_phone1 = cus_phone1.Text
            dr!cus_phone2 = cus_phone2.Text
            dr!Administrator = Administrator.Text
            dr!cus_fax = cus_fax.Text
            dr!cus_post = cus_post.Text
            dr!cus_email = cus_email.Text
            dr!cus_card = cus_card.Text
            dr!cus_job = cus_job.Text
            dr!CusDiscountauto = CusDiscountauto.Text
            dr!Custaxauto = Custaxauto.Text
            dr!CusCreditlimit = CusCreditlimit.Text
            dr!Cuscusgroup = Cuscusgroup.Text
            dr!CusDelegate = CusDelegate.Text
            dr!cusGovernorate = CusGovernorate.Text
            dr!cuscity = Cuscity.Text
            dr!user_name = CusGovernorate.Text
            dr!user_pass = Cuscity.Text
            If price1.Checked = True Then dr!custypesale = 1
            If price2.Checked = True Then dr!custypesale = 2
            If price3.Checked = True Then dr!custypesale = 3
            If price4.Checked = True Then dr!custypesale = 4
            If price5.Checked = True Then dr!custypesale = 5
            If price6.Checked = True Then dr!custypesale = 6
            If price1.Checked = True Then dr!esale_name = price1.Text
            If price2.Checked = True Then dr!esale_name = price2.Text
            If price3.Checked = True Then dr!esale_name = price3.Text
            If price4.Checked = True Then dr!esale_name = price4.Text
            If price5.Checked = True Then dr!esale_name = price5.Text
            If price6.Checked = True Then dr!esale_name = price6.Text
            dr!cus_price_private = cus_price_private.Text
            If cash.Checked = True Then dr!custypemoney = 1
            If ajl.Checked = True Then dr!custypemoney = 2
            If showyes.Checked = True Then dr!cusshow = True
            If showno.Checked = True Then dr!cusshow = False
            If code_pic.Text = 1 Then
                ''============حفظ الصورة=============
                If OpenFileDialog1.FileName.Length > 0 Then
                    Dim imagbytearray() As Byte
                    Dim stream As New MemoryStream
                    cus_pic.Image.Save(stream, ImageFormat.Jpeg)
                    imagbytearray = stream.ToArray
                    dr!cus_pic = imagbytearray
                    stream.Close()
                End If
            End If
            '============================
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============
            dr!Cuscode = Cuscode.Text
            dr!cusname = Cusname.Text
            If CusDebtor.Checked = True Then
                dr!Accounts_balace = Accounts_balace.Text
            End If
            If CusCreditor.Checked = True Then
                dr!Accounts_balace = Val(Accounts_balace.Text) * -1
            End If
            dr!cus_active = cus_active.Checked

            dr!cus_address = cus_address.Text
            dr!cus_phone1 = cus_phone1.Text
            dr!cus_phone2 = cus_phone2.Text
            dr!Administrator = Administrator.Text
            dr!cus_fax = cus_fax.Text
            dr!cus_post = cus_post.Text
            dr!cus_email = cus_email.Text
            dr!cus_card = cus_card.Text
            dr!cus_job = cus_job.Text
            dr!CusDiscountauto = CusDiscountauto.Text
            dr!Custaxauto = Custaxauto.Text
            dr!CusCreditlimit = CusCreditlimit.Text
            dr!Cuscusgroup = Cuscusgroup.Text
            dr!CusDelegate = CusDelegate.Text
            dr!cusGovernorate = CusGovernorate.Text
            dr!cuscity = Cuscity.Text
            dr!Profits = 0
            dr!user_name = user_name.Text
            dr!user_pass = user_pass.Text
            If price1.Checked = True Then dr!custypesale = 1
            If price2.Checked = True Then dr!custypesale = 2
            If price3.Checked = True Then dr!custypesale = 3
            If price4.Checked = True Then dr!custypesale = 4
            If price5.Checked = True Then dr!custypesale = 5
            If price6.Checked = True Then dr!custypesale = 6
            If price1.Checked = True Then dr!esale_name = price1.Text
            If price2.Checked = True Then dr!esale_name = price2.Text
            If price3.Checked = True Then dr!esale_name = price3.Text
            If price4.Checked = True Then dr!esale_name = price4.Text
            If price5.Checked = True Then dr!esale_name = price5.Text
            If price6.Checked = True Then dr!esale_name = price6.Text
            dr!cus_price_private = cus_price_private.Text
            If cash.Checked = True Then dr!custypemoney = 1
            If ajl.Checked = True Then dr!custypemoney = 2
            If showyes.Checked = True Then dr!cusshow = True
            If showno.Checked = True Then dr!cusshow = False
            ''============حفظ الصورة=============
            If OpenFileDialog1.FileName.Length > 0 Then
                Dim imagbytearray() As Byte
                Dim stream As New MemoryStream
                cus_pic.Image.Save(stream, ImageFormat.Jpeg)
                imagbytearray = stream.ToArray
                dr!cus_pic = imagbytearray
                stream.Close()
            End If
            '============================
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
    
            If CusDebtor.Checked = True Then
                set_customertrans(Cusname.Text, "رصيد افتتاحي", "", Now, "", "رصيد افتتاحي", Val(Accounts_balace.Text), 0, Val(Accounts_balace.Text), Val(Accounts_balace.Text))
            End If
            If CusCreditor.Checked = True Then
                set_customertrans(Cusname.Text, "رصيد افتتاحي", "", Now, "", "رصيد افتتاحي", 0, Val(Accounts_balace.Text), Val(Accounts_balace.Text) * -1, Val(Accounts_balace.Text) * -1)
            End If
        End If



        My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
        new_cus()
    End Sub
    Private Sub جديدToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles جديدToolStripMenuItem.Click
        new_cus()
    End Sub
    Private Sub حفظToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles حفظToolStripMenuItem.Click
        save_btn_Click()
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles خروجToolStripMenuItem.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        cus_pic.Image = My.Resources.talibov180200087
    End Sub

    Private Sub Cusname_TextChanged(sender As Object, e As EventArgs) Handles Cusname.TextChanged
        If Cusname.Text <> Nothing Then
            Dim sql = "select * from customer where Cusname=N'" & (Cusname.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                PictureBox1.Image = My.Resources.wrong
            Else
                PictureBox1.Image = My.Resources.check_true
            End If
        Else
            PictureBox1.Image = Nothing
        End If
        ErrorProvider1.SetError(Cusname, "")
    End Sub
    Private Sub تحديثToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تحديثToolStripMenuItem.Click
        fill()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        delegte.Show()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        city.Show()
    End Sub

    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        group.Show()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        Governorate.Show()
    End Sub

    Private Sub Accounts_balace_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Double.TryParse((Accounts_balace.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub cus_phone1_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Double.TryParse((cus_phone1.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub cus_phone2_KeyPress(sender As Object, e As KeyPressEventArgs) Handles cus_phone2.KeyPress
        If Not Double.TryParse((cus_phone2.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub CusDiscountauto_KeyPress(sender As Object, e As KeyPressEventArgs) Handles CusDiscountauto.KeyPress
        If Not Double.TryParse((CusDiscountauto.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub Custaxauto_KeyPress(sender As Object, e As KeyPressEventArgs) Handles Custaxauto.KeyPress
        If Not Double.TryParse((Custaxauto.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub CusCreditlimit_KeyPress(sender As Object, e As KeyPressEventArgs) Handles CusCreditlimit.KeyPress
        If Not Double.TryParse((CusCreditlimit.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub cus_price_private_KeyPress(sender As Object, e As KeyPressEventArgs) Handles cus_price_private.KeyPress
        If Not Double.TryParse((cus_price_private.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub
    Sub erorr_number()
        If Accounts_balace.Text = "" Then
            Accounts_balace.Text = 0
        End If
        If CusDiscountauto.Text = "" Then
            CusDiscountauto.Text = 0
        End If
        If Custaxauto.Text = "" Then
            Accounts_balace.Text = 0
        End If
        If CusCreditlimit.Text = "" Then
            CusCreditlimit.Text = 1000000
        End If
        If cus_price_private.Text = "" Then
            cus_price_private.Text = 0
        End If
    End Sub
    Private Sub CusDelegate_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CusDelegate.SelectedIndexChanged
        CusDelegate.Properties.ImmediatePopup = True
    End Sub

    Private Sub Cuscity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cuscity.SelectedIndexChanged
        Cuscity.Properties.ImmediatePopup = True
    End Sub

    Private Sub Cuscusgroup_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cuscusgroup.SelectedIndexChanged
        Cuscusgroup.Properties.ImmediatePopup = True
    End Sub

    Private Sub CusGovernorate_SelectedIndexChanged(sender As Object, e As EventArgs) Handles CusGovernorate.SelectedIndexChanged
        CusGovernorate.Properties.ImmediatePopup = True
    End Sub

    Private Sub CusDelegate_MouseEnter(sender As Object, e As EventArgs) Handles CusGovernorate.MouseEnter, CusDelegate.MouseEnter, Cuscusgroup.MouseEnter, Cuscity.MouseEnter
        fill()
    End Sub

    Private Sub cus_pic_Click(sender As Object, e As EventArgs) Handles cus_pic.Click
        OpenFileDialog1.FileName = ""
        OpenFileDialog1.Filter = "jpeg|*.jpg|bitmap|*.bmp|gif|*.gif"
        OpenFileDialog1.ShowDialog()
        If OpenFileDialog1.FileName = "" Then Exit Sub
        cus_pic.Image = Image.FromFile(OpenFileDialog1.FileName)
        code_pic.Text = 1
    End Sub

    Private Sub Label16_Click(sender As Object, e As EventArgs) Handles Label16.Click
        save_btn_Click()
    End Sub
   
    Private Sub Label21_Click(sender As Object, e As EventArgs) Handles Label21.Click
        exit_btn_Click(Nothing, Nothing)
    End Sub
End Class