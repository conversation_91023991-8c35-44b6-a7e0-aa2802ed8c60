﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox

Public Class users

    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from users order by user_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("user_name"))
        Next

    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from users where user_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("user_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox1.Text = get_id(ListBox1.Text)

    End Sub
    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        OpenFileDialog1.FileName = ""
        OpenFileDialog1.Filter = "jpeg|*.jpg|bitmap|*.bmp|gif|*.gif|PNG|*.PNG"
        OpenFileDialog1.ShowDialog()
        If OpenFileDialog1.FileName = "" Then Exit Sub
        user_pic.Image = Image.FromFile(OpenFileDialog1.FileName)
        code_pic.Text = 1
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        user_name.Text = ""
        user_pass.Text = ""
        fill_power()
        user_pass2.Text = ""
        user_city.Text = ""
        user_phone.Text = ""
        user_email.Text = ""
        user_power.SelectedIndex = 0
        user_pic.Image = My.Resources.boy
        code_pic.Text = 1
        save_btn.Enabled = True
        delet_btn.Enabled = False
        edit_btn.Enabled = False
        fill_user()
        fill_treasury_name()
        fill_store()
        store.SelectedIndex = 0
        type_pay.SelectedIndex = 0
    End Sub

    Private Sub users_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        new_btn_Click(Nothing, Nothing)
    End Sub
    Sub fill_store()
        Try
            store.Properties.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from store", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                store.Properties.Items.Add(dt.Rows(i).Item("store_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub
    Sub fill_treasury_name()
        Try
            type_pay.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from treasury_name", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                type_pay.Items.Add(dt.Rows(i).Item("treasury_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطأ برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try

    End Sub
    Sub fill_power()
        user_power.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from power_user order by power_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            user_power.Items.Add(dt.Rows(i).Item("power_name"))
        Next
    End Sub

    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        user_pic.Image = My.Resources.boy
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If user_name.Text = "" Then
            MsgBox("أدخل اسم المستخدم")
            user_name.Focus()
            Exit Sub
        End If
        If user_pass.Text <> user_pass2.Text Then
            MsgBox("كلمة السر غير متطابقة")
            user_pass.Focus()
            Exit Sub
        End If

        Try
            Dim sql = "select * from users where user_name=N'" & (user_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم المستخدم موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!user_name = user_name.Text
                dr!user_pass = user_pass.Text
                dr!user_city = user_city.Text
                dr!user_phone = user_phone.Text
                dr!user_email = user_email.Text
                dr!user_power = user_power.Text
                dr!type_pay = type_pay.Text
                dr!store = store.Text
                If code_pic.Text = 1 Then
                    ''============حفظ الصورة=============
                    If OpenFileDialog1.FileName.Length > 0 Then
                        Dim imagbytearray() As Byte
                        Dim stream As New MemoryStream
                        user_pic.Image.Save(stream, ImageFormat.Jpeg)
                        imagbytearray = stream.ToArray
                        dr!user_pic = imagbytearray
                        stream.Close()
                    End If
                    '============================
                End If
                
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم أضافة المستخدم")
                new_btn_Click(Nothing, Nothing)
                fill_user()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ المستخدم اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        Try
            Dim sql = "select * from users where user_id  = N'" & TextBox1.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم حذف المستخدم", MsgBoxStyle.Information, "تنبيه")

                new_btn_Click(Nothing, Nothing)
            End If
        Catch ex As Exception
            MsgBox("فشل حذف المستخدم اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        If user_name.Text = "" Then
            MsgBox("أدخل اسم المستخدم")
            user_name.Focus()
            Exit Sub
        End If
        If user_pass.Text <> user_pass2.Text Then
            MsgBox("كلمة السر غير متطابقة")
            user_pass.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from users where user_name=N'" & (user_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0) 
            Dim dr = dt.Rows(0)
                dr!user_name = user_name.Text
                dr!user_pass = user_pass.Text
                dr!user_city = user_city.Text
                dr!user_phone = user_phone.Text
                dr!user_email = user_email.Text
            dr!user_power = user_power.Text
            dr!type_pay = type_pay.Text
            dr!store = store.Text
                If code_pic.Text = 1 Then
                    ''============حفظ الصورة=============
                    If OpenFileDialog1.FileName.Length > 0 Then
                        Dim imagbytearray() As Byte
                        Dim stream As New MemoryStream
                        user_pic.Image.Save(stream, ImageFormat.Jpeg)
                        imagbytearray = stream.ToArray
                        dr!user_pic = imagbytearray
                        stream.Close()
                    End If
                    '============================
                End If
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم تعديل المستخدم")
                new_btn_Click(Nothing, Nothing)
                fill_user()
        Catch ex As Exception
            MsgBox("فشل نعديل المستخدم اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles TextBox1.TextChanged
        Dim sql = "select * from users where user_id=N'" & (TextBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("لم يتم العثور علي بيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim dr = dt.Rows(0)
            user_name.Text = dr!user_name
            user_pass.Text = dr!user_pass
            user_city.Text = dr!user_city
            user_email.Text = dr!user_email
            user_power.Text = dr!user_power
            type_pay.Text = dr!type_pay
            store.Text = dr!store
            '======================== فك تشفير الصورة------
            If IsDBNull(dr!user_pic) = False Then
                Dim imagbytearray() As Byte
                imagbytearray = CType(dr!user_pic, Byte())
                Dim stream As New MemoryStream(imagbytearray)
                Dim bmp As New Bitmap(stream)
                user_pic.Image = Image.FromStream(stream)
                stream.Close()
            End If
            '========================================
            code_pic.Text = 0
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Power_user.ShowDialog()
    End Sub
End Class