﻿
Imports System.Data.SqlClient
Imports System.IO
Imports System.Drawing.Imaging
Imports DevExpress.XtraEditors.XtraMessageBox
Imports DevExpress.XtraEditors
Imports System.Data
Imports System.Data.OleDb

Public Class barcode_export

    Dim aaa As String = ""
    Sub new_item()
        new_unit()
        item_type.SelectedIndex = 0
        itemunit()
        fill()
        store.Text = XtraForm1.store.Text
        item_pic.Image = My.Resources.talibov180200087
        itemcode.Text = getlastcode("item", "itemcode") + 1
        itemnamearabic.Text = ""
        itemcount.Text = 0
        colour.Text = ""
        Demand_limit.Text = ""
        count_item.Text = 0
        Itemtacategory.Text = ""
        itemloucation.Text = ""

        item_group.Text = ""

        item_notes.Text = ""
        item_Recession.Text = 0
        item_barcode.Text = ""
        barcode_remove.Text = ""
        item_make.Text = ""
        colour.Text = ""
        item_size.Text = ""
        tax_add.Text = 0
        item_tax_add.Text = 0
        item_tax.Checked = True
        show_print.Checked = True
        block_buy.Checked = False
        block_sale.Checked = False
        show_minus.Checked = True
        dgv_item.DataSource = Nothing
        dgv_item.Rows.Clear()
        itemcompany.Text = ""
        Demand_limit.Text = 0
        itemcouta.Text = 10000
        itemnamearabic.Focus()
    End Sub
    Sub item_id()
        itemcode.Text = getlastcode("item", "itemcode") + 1
    End Sub
    Private Sub customer_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fillstore()
        fill_barcode()
        show_price()
    End Sub
    Sub fill_item()
        item_name.Properties.DataSource = Nothing
        item_name.EditValue = ""
        Dim adp As New SqlDataAdapter("select * from item where itemstore=N'" & (store.Text) & "' and item_active = 'true' order by itemnamearabic ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        item_name.Properties.DataSource = dt
        item_name.Properties.DisplayMember = "itemnamearabic"
        item_name.Properties.ValueMember = "itemcode"
    End Sub
    Sub show_price()
        show_price1()
        show_price2()
        show_price3()
        show_price4()
        show_price5()
        Column13.HeaderText = price1.Text
        Column2.HeaderText = price2.Text
        Column3.HeaderText = price3.Text
        Column4.HeaderText = price4.Text
        Column5.HeaderText = price5.Text

        Column13.Visible = pricecheck1.Checked
        Column2.Visible = pricecheck2.Checked
        Column3.Visible = pricecheck3.Checked
        Column4.Visible = pricecheck4.Checked
        Column5.Visible = pricecheck5.Checked
    End Sub
    Sub show_price1()
        Dim sql = "select * from name_price where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price1.Text = dr!name_price
        pricecheck1.Checked = dr!price_check
    End Sub
    Sub show_price2()
        Dim sql = "select * from name_price where id=N'" & (2) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price2.Text = dr!name_price
        pricecheck2.Checked = dr!price_check
    End Sub
    Sub show_price3()
        Dim sql = "select * from name_price where id=N'" & (3) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price3.Text = dr!name_price
        pricecheck3.Checked = dr!price_check
    End Sub
    Sub show_price4()
        Dim sql = "select * from name_price where id=N'" & (4) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price4.Text = dr!name_price
        pricecheck4.Checked = dr!price_check
    End Sub
    Sub show_price5()
        Dim sql = "select * from name_price where id=N'" & (5) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        price5.Text = dr!name_price
        pricecheck5.Checked = dr!price_check
    End Sub
    Sub new_unit()
        dgv_unit.DataSource = Nothing
        dgv_unit.Rows.Clear()
        dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(0).Value = "قطعة"
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(1).Value = 1
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(2).Value = 0
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(3).Value = 0
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(4).Value = 0
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(5).Value = 0
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(6).Value = 0
        dgv_unit.Rows(dgv_unit.Rows.Count - 1).Cells(7).Value = 0

    End Sub


    Sub fill_barcode()
        Dim adp As New SqlDataAdapter("select * from barcode where itemcode=N'" & (itemcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_item.DataSource = dt
    End Sub
    Private Sub fillstore()
        store.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from store", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            store.Items.Add(dt.Rows(i).Item("store_name"))
        Next
    End Sub

    Sub fillcatorgy()

        Itemtacategory.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from Category order by Category_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            Itemtacategory.Items.Add(dt.Rows(i).Item("Category_name"))
        Next
    End Sub

    Sub itemunit()
        Try
            unit_item.Items.Clear()
            For i = 0 To dgv_unit.Rows.Count - 1
                unit_item.Items.Add(dgv_unit.Rows(i).Cells(0).Value)
            Next
            unit_item.SelectedIndex = 0
        Catch ex As Exception

        End Try

    End Sub
    Sub fillcompany()

        itemcompany.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from company order by company_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            itemcompany.Items.Add(dt.Rows(i).Item("company_name"))
        Next
    End Sub
    Sub fillgroup()
        item_group.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from contact order by contact_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            item_group.Items.Add(dt.Rows(i).Item("contact_name"))
        Next
    End Sub
    Sub fill()
        fillcatorgy()
        fillcompany()
        fillgroup()

    End Sub
    ' ============================ show data
    Public Sub show_data(x)
        new_item()
        dgv_unit.DataSource = Nothing
        dgv_unit.Rows.Clear()
        Dim sql = "select * from item where itemcode=N'" & (x) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لم يتم العثور علي بيانات")
        Else
            Dim dr = dt.Rows(0)
            itemcode.Text = dr!itemcode
            store.Text = dr!itemstore
            itemnamearabic.Text = dr!itemnamearabic
            item_type.Text = dr!item_type
            count_item.Text = dr!itemcount
            itemcount.Text = dr!itemcount
            total_price.Text = dr!total_price
            colour.Text = dr!Colour
            item_group.Text = dr!item_group
            Demand_limit.Text = dr!Demand_limit
            item_notes.Text = dr!item_notes
            item_Recession.Text = dr!item_Recession
            item_make.Text = dr!item_make
            colour.Text = dr!colour
            item_size.Text = dr!item_size
            tax_add.Text = dr!tax_add
            item_tax_add.Text = dr!item_tax_add
            item_tax.Checked = dr!item_tax
            show_print.Checked = dr!show_print
            block_buy.Checked = dr!block_buy
            block_sale.Checked = dr!block_sale
            show_minus.Checked = dr!show_minus
            item_active.Checked = dr!item_active
            Itemtacategory.Text = dr!Itemtacategory
            itemloucation.Text = dr!itemloucation
            itemcompany.Text = dr!itemcompany
            itemcouta.Text = dr!itemcouta
            '========================================  

            show_item.Enabled = False
            '========================================
            '======================== فك تشفير الصورة------
            If IsDBNull(dr!item_pic) = False Then
                Dim imagbytearray() As Byte
                imagbytearray = CType(dr!item_pic, Byte())
                Dim stream As New MemoryStream(imagbytearray)
                Dim bmp As New Bitmap(stream)
                item_pic.Image = Image.FromStream(stream)
                stream.Close()
            End If
            code_pic.Text = 0
            fill_barcode()
            fill_unit_edit()
            itemunit()
            unit_item.SelectedIndex = 0
            If item_type.SelectedIndex = 1 Then
                fill_collection()
            End If
            itemnamearabic.Select()
        End If
    End Sub
    Sub fill_unit_edit()
        dgv_unit.DataSource = Nothing
        dgv_unit.Rows.Clear()
        Dim adp As New SqlDataAdapter("select * from unit_item  where code=N'" & (itemcode.Text) & "'", sqlconn)
        Dim ds = New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For s = 0 To dt.Rows.Count - 1
            dgv_unit.Rows.Add()
            Dim dgvcc As DataGridViewComboBoxCell
            dgvcc = dgv_unit.Rows(s).Cells(0)
            dgvcc.Items.Add(dt.Rows(s).Item("unit"))
            dgvcc.Value = dt.Rows(s).Item("unit")
            dgv_unit.Rows(s).Cells(1).Value = dt.Rows(s).Item("count")
            dgv_unit.Rows(s).Cells(2).Value = dt.Rows(s).Item("price_buy")
            dgv_unit.Rows(s).Cells(3).Value = dt.Rows(s).Item("price1")
            dgv_unit.Rows(s).Cells(4).Value = dt.Rows(s).Item("price2")
            dgv_unit.Rows(s).Cells(5).Value = dt.Rows(s).Item("price3")
            dgv_unit.Rows(s).Cells(6).Value = dt.Rows(s).Item("price4")
            dgv_unit.Rows(s).Cells(7).Value = dt.Rows(s).Item("price5")
        Next
    End Sub
    Sub fill_collection()
        dgv_total.DataSource = Nothing
        dgv_total.Rows.Clear()
        Dim adp As New SqlDataAdapter("select * from item_collection  where item_first=N'" & (itemcode.Text) & "'", sqlconn)
        Dim ds = New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For s = 0 To dt.Rows.Count - 1
            dgv_total.Rows.Add()
            dgv_total.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
            dgv_total.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_name")
            dgv_total.Rows(s).Cells(2).Value = dt.Rows(s).Item("item_count")
        Next
    End Sub
    Private Sub Button1_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub
    Sub item_unit()
        Dim sql = "select * from unit_item where code=N'" & (itemcode.Text) & "' "
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dgv_unit.Rows.Count - 1
            If dgv_unit.Rows(i).Cells(0).Value <> "" Then
                Dim dr = dt.NewRow
                dr!code = itemcode.Text
                dr!unit = dgv_unit.Rows(i).Cells(0).Value
                dr!count = dgv_unit.Rows(i).Cells(1).Value
                dr!price_buy = Convert.ToDecimal(dgv_unit.Rows(i).Cells(2).Value)
                dr!price1 = Convert.ToDecimal(dgv_unit.Rows(i).Cells(3).Value)
                dr!price2 = Convert.ToDecimal(dgv_unit.Rows(i).Cells(4).Value)
                dr!price3 = Convert.ToDecimal(dgv_unit.Rows(i).Cells(5).Value)
                dr!price4 = Convert.ToDecimal(dgv_unit.Rows(i).Cells(6).Value)
                dr!price5 = Convert.ToDecimal(dgv_unit.Rows(i).Cells(7).Value)
                dr!dicoundprice1 = 0
                dr!dicoundprice2 = 0
                dr!dicoundprice3 = 0
                dr!dicoundprice4 = 0
                dr!dicoundprice5 = 0
                dr!disc_buy = 0
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If

        Next

    End Sub
    Sub delete()
        For i2 = 0 To 10
            Dim sql = "select * from unit_item where code=N'" & itemcode.Text & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub
    Sub delete_collection()
        For i2 = 0 To 20
            Dim sql = "select * from item_collection where item_first=N'" & itemcode.Text & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                Exit Sub
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If show_item.Enabled = True Then
            itemcode.Text = getlastcode("item", "itemcode") + 1
        End If
        If itemnamearabic.Text = "" Then
            MsgBox("ادخل اسم الصنف", MsgBoxStyle.Critical, "خطأ")
            itemnamearabic.Focus()
            Exit Sub
        End If

        If show_item.Enabled = True Then
            Dim sql1 = "select * from item where itemstore=N'" & (store.Text) & "' and  itemnamearabic=N'" & (itemnamearabic.Text) & "'"
            Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
            Dim ds1 As New DataSet
            adp1.Fill(ds1)
            Dim dt1 = ds1.Tables(0)
            If dt1.Rows.Count > 0 Then
                MsgBox("يوجد صنف بهذا الاسم")
                Exit Sub
            End If
        End If

        Dim sql = "select * from item where itemcode=N'" & (itemcode.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            '========= بيانات اساسية============
            dr!itemnamearabic = itemnamearabic.Text
            dr!itemstore = store.Text
            dr!itemnamearabic = itemnamearabic.Text
            dr!item_type = item_type.Text
            dr!itemcount = itemcount.Text
            dr!total_price = total_price.Text
            dr!Colour = colour.Text
            dr!item_group = item_group.Text
            dr!Demand_limit = Demand_limit.Text
            dr!item_notes = item_notes.Text
            dr!item_Recession = item_Recession.Text
            dr!item_make = item_make.Text
            dr!colour = colour.Text
            dr!item_size = item_size.Text
            dr!tax_add = tax_add.Text
            dr!item_tax_add = item_tax_add.Text
            dr!itemcouta = itemcouta.Text
            dr!item_tax = item_tax.Checked
            dr!show_print = show_print.Checked
            dr!block_buy = block_buy.Checked
            dr!block_sale = block_sale.Checked
            dr!show_minus = show_minus.Checked
            dr!item_active = item_active.Checked
            dr!Itemtacategory = Itemtacategory.Text
            dr!itemloucation = itemloucation.Text
            dr!itemcompany = itemcompany.Text
            dr!itembuyprice = dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(2).Value
            dr!itemprice1 = dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(3).Value
            dr!itemcouta = itemcouta.Text
            If code_pic.Text = 1 Then
                ''============حفظ الصورة=============
                If OpenFileDialog1.FileName.Length > 0 Then
                    Dim imagbytearray() As Byte
                    Dim stream As New MemoryStream
                    item_pic.Image.Save(stream, ImageFormat.Jpeg)
                    imagbytearray = stream.ToArray
                    dr!item_pic = imagbytearray
                    stream.Close()
                End If
                '============================
            End If

            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
            delete()
            If item_type.SelectedIndex = 1 Then
                delete_collection()
            End If
        Else
            Dim dr = dt.NewRow
            '========= بيانات اساسية============
            dr!itemcode = itemcode.Text
            dr!itemstore = store.Text
            dr!itemnamearabic = itemnamearabic.Text
            dr!item_type = item_type.Text
            dr!itemcouta = itemcouta.Text
            dr!itemcount = itemcount.Text
            dr!total_price = total_price.Text
            dr!Colour = colour.Text
            dr!item_group = item_group.Text
            dr!Demand_limit = Demand_limit.Text
            dr!item_notes = item_notes.Text
            dr!item_Recession = item_Recession.Text
            dr!item_make = item_make.Text
            dr!colour = colour.Text
            dr!item_size = item_size.Text
            dr!tax_add = tax_add.Text
            dr!item_tax_add = item_tax_add.Text
            dr!item_tax = item_tax.Checked
            dr!show_print = show_print.Checked
            dr!block_buy = block_buy.Checked
            dr!block_sale = block_sale.Checked
            dr!show_minus = show_minus.Checked
            dr!item_active = item_active.Checked
            dr!Itemtacategory = Itemtacategory.Text
            dr!itemloucation = itemloucation.Text
            dr!itemcompany = itemcompany.Text
            dr!itembuyprice = dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(2).Value
            dr!itemprice1 = dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(3).Value

            dr!Profits = 0
            dr!date_sale = Now.Date
            ''============حفظ الصورة=============
            If OpenFileDialog1.FileName.Length > 0 Then
                Dim imagbytearray() As Byte
                Dim stream As New MemoryStream
                item_pic.Image.Save(stream, ImageFormat.Jpeg)
                imagbytearray = stream.ToArray
                dr!item_pic = imagbytearray
                stream.Close()
            End If

            '============================
            dt.Rows.Add(dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        End If
        If item_type.SelectedIndex = 1 Then
            item_collection()
        End If
        item_unit()
        set_itemtrans(itemnamearabic.Text, Now.Date, "", "كمية افتتاحية", itemcount.Text, 0, itemcount.Text)
        new_item()
    End Sub
    Sub item_collection()
        Dim sql = "select * from item_collection where item_first=N'" & (itemcode.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dgv_total.Rows.Count - 1
            Dim Dr = dt.NewRow

            '========= بيانات اساسية============
            Dr!item_first = itemcode.Text
            Dr!item_code = dgv_total.Rows(i).Cells(0).Value
            Dr!item_name = dgv_total.Rows(i).Cells(1).Value
            Dr!item_count = dgv_total.Rows(i).Cells(2).Value
            dt.Rows.Add(Dr)
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
        Next
    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Category.Show()
    End Sub
    Private Sub جديدToolStripMenuItem_Click(sender As Object, e As EventArgs)
        new_item()
    End Sub

    Private Sub حفظToolStripMenuItem_Click(sender As Object, e As EventArgs)
        save_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub تحديثالبياناتToolStripMenuItem_Click(sender As Object, e As EventArgs)
        fill()
    End Sub

    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub
    Private Sub insert_barcode_Click(sender As Object, e As EventArgs) Handles insert_barcode.Click
        If item_barcode.Text = "" Then
            MsgBox("أدخل الباركود")
            item_barcode.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from barcode where barcode=N'" & (item_barcode.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("الباركود موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!itemcode = itemcode.Text
                dr!barcode = item_barcode.Text
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                item_barcode.Text = ""
                barcode_remove.Text = ""
                fill_barcode()
                item_barcode.Focus()
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ الباركود اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub itemnamearabic_TextChanged(sender As Object, e As EventArgs) Handles itemnamearabic.TextChanged
        Try
            If itemnamearabic.Text <> Nothing Then
                Dim sql = "select * from item where itemnamearabic=N'" & (itemnamearabic.Text) & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count > 0 Then
                    PictureBox1.Image = My.Resources.wrong
                Else
                    PictureBox1.Image = My.Resources.check_true
                End If
            Else
                PictureBox1.Image = Nothing
            End If
        Catch ex As Exception

        End Try

    End Sub
    Private Sub remove_barcode_Click(sender As Object, e As EventArgs) Handles remove_barcode.Click
        Try
            Dim sql = "select * from barcode where barcode=N'" & barcode_remove.Text & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الباركود")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                barcode_remove.Text = ""
                fill_barcode()
                barcode_remove.Focus()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الباركود اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delete_barcode_Click(sender As Object, e As EventArgs) Handles delete_barcode.Click
        Try
            Dim sql = "select * from barcode where barcode=N'" & dgv_item.CurrentRow.Cells(2).Value & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب الباركود")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                barcode_remove.Text = ""
                fill_barcode()
            End If
        Catch ex As Exception
            MsgBox("فشل حذف الباركود اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub
    Sub clearpic()
        item_pic.Image = My.Resources.talibov180200087
        code_pic.Text = 1
    End Sub

    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        item_pic.Image = My.Resources.talibov180200087
        code_pic.Text = 1
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs)
        whada.Show()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        company.Show()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        contact.Show()
    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub itemcompany_MouseEnter(sender As Object, e As EventArgs) Handles Itemtacategory.MouseEnter, itemcompany.MouseEnter, item_group.MouseEnter
        fill()
    End Sub
    Private Sub dgv_unit_CellPainting_1(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv_unit.CellPainting
        If e.ColumnIndex = 8 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub

    Private Sub dgv_unit_CellEndEdit(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_unit.CellEndEdit
        If dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(1).Value <> 1 Then
            MsgBox("يجب ان تكون عدد القطع في الوحدة الاولي مساوي لواحد صحيح", MsgBoxStyle.Information, "خطأ")
            dgv_unit.Rows(dgv_unit.Rows.Count = 0).Cells(1).Value = 1
            Exit Sub
        End If
        itemunit()
        unit_item.SelectedIndex = 0

    End Sub


    Private Sub dgv_unit_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_unit.CellClick
        Try
            If e.ColumnIndex = 8 Then
                If XtraMessageBox.Show("هل تريد حذف هذا السطر", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
                    If dgv_unit.Rows.Count - 1 = 0 Then
                        MsgBox("لايمكن حذف هذا السطر يمكن التعديل عليه فقط")
                        Exit Sub
                    End If
                    dgv_unit.Rows.Remove(dgv_unit.CurrentRow)
                End If
            End If

        Catch ex As Exception

        End Try

    End Sub

    Private Sub unit_item_SelectedIndexChanged(sender As Object, e As EventArgs) Handles unit_item.SelectedIndexChanged

        For i = 0 To dgv_unit.Rows.Count - 1
            If unit_item.Text = dgv_unit.Rows(i).Cells(0).Value Then
                unit_number.Text = dgv_unit.Rows(i).Cells(1).Value
                price_unit.Text = dgv_unit.Rows(i).Cells(2).Value
            End If
        Next
        count_item_TextChanged(Nothing, Nothing)
    End Sub

    Private Sub count_item_TextChanged(sender As Object, e As EventArgs) Handles count_item.TextChanged
        Try
            itemcount.Text = Val(unit_number.Text) * Val(count_item.Text)
            total_price.Text = Val(price_unit.Text) * Val(count_item.Text)
        Catch ex As Exception

        End Try

    End Sub

    Private Sub item_add_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F2 Then
            save_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.Escape Then
            Me.Dispose()
        End If
    End Sub

    Private Sub item_barcode_KeyDown(sender As Object, e As KeyEventArgs) Handles item_barcode.KeyDown
        If e.KeyCode = Keys.Enter Then
            insert_barcode_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub item_pic_Click(sender As Object, e As EventArgs) Handles item_pic.Click
        OpenFileDialog1.FileName = ""
        OpenFileDialog1.Filter = "jpeg|*.jpg|PNG|*.PNG|bitmap|*.bmp|gif|*.gif"
        OpenFileDialog1.ShowDialog()
        If OpenFileDialog1.FileName = "" Then Exit Sub
        item_pic.Image = Image.FromFile(OpenFileDialog1.FileName)
        code_pic.Text = 1
    End Sub

    Private Sub item_type_SelectedIndexChanged(sender As Object, e As EventArgs) Handles item_type.SelectedIndexChanged
        If item_type.SelectedIndex = 1 Then
            fill_item()
            dgv_total.DataSource = Nothing
            dgv_total.Rows.Clear()
            XtraTabPage5.PageVisible = True
            XtraTabPage6.PageVisible = False
            item_name.Text = 0
            item_count.Text = 0
        Else
            XtraTabPage5.PageVisible = False
        End If
    End Sub

    Private Sub inserte_Click(sender As Object, e As EventArgs) Handles inserte.Click
        dgv_total.Rows.Add()
        dgv_total.Rows(dgv_total.Rows.Count - 1).Cells(0).Value = item_name.EditValue
        dgv_total.Rows(dgv_total.Rows.Count - 1).Cells(1).Value = item_name.Text
        dgv_total.Rows(dgv_total.Rows.Count - 1).Cells(2).Value = item_count.Text
    End Sub

    Private Sub dgv_total_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_total.CellClick
        Try
            If XtraMessageBox.Show("هل تريد حذف هذا السطر", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then
                If e.ColumnIndex = 3 Then
                    dgv_total.Rows.Remove(dgv_total.CurrentRow)
                End If
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub dgv_total_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv_total.CellPainting
        If e.ColumnIndex = 3 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub
    Sub sss()
        
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click

        For i = 0 To grd.Rows.Count - 2
            Dim sql = "select * from unit_item where code=N'" & (grd.Rows(i).Cells(0).Value.ToString) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                '========= بيانات اساسية============

                dr!item_unit = (grd.Rows(i).Cells(1).Value.ToString)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
        Next
        MsgBox("تم استيراد الاصناف بنجاح")
    End Sub

    Private Sub SimpleButton2_Click_1(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        ofd.Filter = "Excel 2007|*.xlsx|Excel 2003|*.xls"
        If ofd.ShowDialog = DialogResult.OK Then
            grd.DataSource = Nothing
            grd.Columns.Clear()
            Dim path As String = ofd.FileName
            Dim conn As New OleDbConnection("PROVIDER=MICROSOFT.ACE.OLEDB.12.0;DATA SOURCE=" & path & "; EXTENDED PROPERTIES=EXCEL 12.0;")
            Dim ds As New DataSet
            Dim da As New OleDbDataAdapter("SELECT * FROM [Sheet1$]", conn)
            da.Fill(ds, "[Sheet1$]")
            grd.DataSource = ds.Tables("[Sheet1$]")
        Else
            End
        End If
        Label40.Text = grd.RowCount - 2
    End Sub
End Class
