﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.7.0" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v17.2, Version=17.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="cus_trnas_print_a4" Margins="0, 0, 0, 0" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="cus_trans_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="106.6667">
      <Controls>
        <Item1 Ref="6" ControlType="XRLabel" Name="lblco" Text="ااا" TextAlignment="MiddleRight" SizeF="382.999969,36.1433029" LocationFloat="407.918274, 12.2617073" Font="Dexef, 20pt, style=Bold" ForeColor="255,192,64,0" BackColor="Transparent" Padding="2,2,0,0,100" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="7" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="8" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="lblfield" Text="ااا" TextAlignment="MiddleRight" SizeF="363.666626,23" LocationFloat="343.4096, 48.40501" Font="Dexef, 12pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="10" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="12" ControlType="XRPictureBox" Name="picture" Sizing="StretchImage" SizeF="140.000031,70" LocationFloat="36.0816956, 12.2617073">
          <ExpressionBindings>
            <Item1 Ref="13" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
        <Item4 Ref="14" ControlType="XRLabel" Name="label10" Text="ااا" TextAlignment="MiddleRight" SizeF="363.666626,23" LocationFloat="343.4096, 71.405" Font="Dexef, 12pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="15" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="16" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item4>
    <Item5 Ref="17" ControlType="PageHeaderBand" Name="PageHeader" HeightF="258.3333">
      <Controls>
        <Item1 Ref="18" ControlType="XRPageInfo" Name="XrPageInfo1" RightToLeft="Yes" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="253.483063,23" LocationFloat="413.885681, 153.1041" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom">
          <StylePriority Ref="19" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="20" ControlType="XRLabel" Name="LblOrangeDt1" Text="في الفترة من" TextAlignment="MiddleCenter" SizeF="92.708374,28.4112167" LocationFloat="489.6544, 49.0522156" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100">
          <StylePriority Ref="21" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="22" ControlType="XRLabel" Name="DT1" TextFormatString="{0:yyyy-MM-dd}" Text="Dateto" TextAlignment="MiddleCenter" SizeF="119.212463,28.4112167" LocationFloat="210.604263, 49.0523758" Font="Droid Arabic Kufi, 8.25pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="255,192,97,109" Borders="Left, Right, Bottom">
          <ExpressionBindings>
            <Item1 Ref="23" Expression="[Dateto]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="24" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="25" ControlType="XRLabel" Name="LblOrangeDt2" Text="الى" TextAlignment="MiddleCenter" SizeF="40.62506,28.4112167" LocationFloat="329.816864, 49.0523758" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100">
          <StylePriority Ref="26" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="27" ControlType="XRLabel" Name="Dt2" TextFormatString="{0:yyyy-MM-dd}" TextAlignment="MiddleCenter" SizeF="119.212524,28.4112167" LocationFloat="370.441864, 49.0523758" Font="Droid Arabic Kufi, 8.25pt" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="255,192,97,109" Borders="Left, Right, Bottom">
          <ExpressionBindings>
            <Item1 Ref="28" Expression="[Datefrom]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="30" ControlType="XRLabel" Name="lbldealing" Text="حركة صنف" TextAlignment="TopCenter" SizeF="727,48.5315475" LocationFloat="44.7084274, 0.520813" Font="Dexef, 17pt, style=Bold" ForeColor="Black" BackColor="Transparent" Padding="2,2,0,0,100" BorderColor="255,192,97,109" Borders="Bottom">
          <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="32" ControlType="XRLabel" Name="lblname" Text="lblname" TextAlignment="MiddleRight" SizeF="253.4831,25.0832977" LocationFloat="413.8858, 128.020813" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="33" Expression="[itemname]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="34" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="35" ControlType="XRLabel" Name="label2" TextTrimming="None" Text="الفرع" TextAlignment="MiddleCenter" SizeF="105.905334,25.0833435" LocationFloat="671.136169, 178.187454" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="36" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="37" ControlType="XRLabel" Name="label4" Text="label4" TextAlignment="MiddleRight" SizeF="253.4831,25.0832977" LocationFloat="413.885681, 178.187515" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="38" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="40" ControlType="XRLabel" Name="label8" Text="المستخدم" TextAlignment="MiddleCenter" SizeF="105.905365,25.0833435" LocationFloat="294.168518, 178.187454" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="41" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="42" ControlType="XRLabel" Name="label7" Text="الكمية الحالية" TextAlignment="MiddleCenter" SizeF="105.905365,25.083313" LocationFloat="294.168762, 128.020813" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="43" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="44" ControlType="XRLabel" Name="label12" Text="label12" TextAlignment="MiddleRight" SizeF="223.2748,25.0833282" LocationFloat="70.89397, 128.020813" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[itemcount]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="46" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="47" ControlType="XRLabel" Name="label1" TextTrimming="None" Text="الاسم" TextAlignment="MiddleCenter" SizeF="105.905334,25.0833282" LocationFloat="671.1363, 128.020813" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="49" ControlType="XRTable" Name="table2" RightToLeft="Yes" TextAlignment="MiddleCenter" SizeF="795.000061,35" LocationFloat="15.9999695, 222.812485" Font="Droid Arabic Kufi, 8.25pt, charSet=0" ForeColor="White" BackColor="Highlight" BorderColor="White" Borders="All">
          <Rows>
            <Item1 Ref="50" ControlType="XRTableRow" Name="tableRow2" Weight="11.5">
              <Cells>
                <Item1 Ref="51" ControlType="XRTableCell" Name="tableCell5" Weight="0.77471649499827033" Text="الرصيد" />
                <Item2 Ref="52" ControlType="XRTableCell" Name="tableCell6" Weight="0.76665665505677294" Text="دائن" />
                <Item3 Ref="53" ControlType="XRTableCell" Name="tableCell7" Weight="0.798447465420561" Text="مدين" />
                <Item4 Ref="54" ControlType="XRTableCell" Name="tableCell8" Weight="2.0120044958465813" Text="الرقم" />
                <Item5 Ref="55" ControlType="XRTableCell" Name="tableCell9" Weight="0.812596065511535" Text="التاريخ" />
                <Item6 Ref="56" ControlType="XRTableCell" Name="tableCell10" Weight="0.87596442863256829" Text="التعامل" />
                <Item7 Ref="57" ControlType="XRTableCell" Name="tableCell11" Weight="0.25645239505197526" Text="م" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="58" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="59" ControlType="XRLabel" Name="label9" TextTrimming="None" Text="التاريخ" TextAlignment="MiddleCenter" SizeF="105.905334,25.0833282" LocationFloat="671.136169, 153.1041" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="60" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="61" ControlType="XRLabel" Name="label6" Text="label12" TextAlignment="MiddleRight" SizeF="223.2748,25.0833282" LocationFloat="70.89397, 153.1041" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="62" Expression="[itemcount2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="63" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="64" ControlType="XRLabel" Name="label5" Text="الكمية السابقة" TextAlignment="MiddleCenter" SizeF="105.905365,25.083313" LocationFloat="294.168762, 153.1041" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight" Padding="2,2,0,0,100" BorderColor="White" Borders="Left, Bottom">
          <StylePriority Ref="65" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="66" ControlType="XRLabel" Name="label3" Text="lblname" TextAlignment="MiddleRight" SizeF="223.274551,25.0832977" LocationFloat="70.89397, 178.187515" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100" BorderColor="Gray" Borders="Bottom" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="67" Expression="[user_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="68" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item18>
      </Controls>
    </Item5>
    <Item6 Ref="69" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="25.08331">
      <Controls>
        <Item1 Ref="70" ControlType="XRPageInfo" Name="pageInfo1" TextAlignment="TopRight" SizeF="48.95844,23" LocationFloat="667.8232, 1.99998224" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100">
          <StylePriority Ref="71" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="72" ControlType="XRLabel" Name="label11" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="74.1366,25.08331" LocationFloat="716.7816, 0" Font="Droid Arabic Kufi, 8.25pt" Padding="2,2,0,0,100">
          <StylePriority Ref="73" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item6>
    <Item7 Ref="74" ControlType="PageFooterBand" Name="PageFooter" HeightF="0" />
    <Item8 Ref="75" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="76" ControlType="DetailBand" Name="Detail1" HeightF="34.99997">
          <Controls>
            <Item1 Ref="77" ControlType="XRTable" Name="table1" RightToLeft="Yes" SizeF="795.5,34.99997" LocationFloat="15.5000305, 0" Font="Droid Arabic Kufi, 8.25pt">
              <Rows>
                <Item1 Ref="78" ControlType="XRTableRow" Name="tableRow1" Weight="11.5">
                  <Cells>
                    <Item1 Ref="79" ControlType="XRTableCell" Name="tableCell1" Weight="0.61231204583009924" TextFormatString="{0:0.##;[0.##];0}" Text="1" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt" ForeColor="Black" BackColor="0,0,0,0">
                      <Summary Ref="80" Func="RecordNumber" />
                      <ExpressionBindings>
                        <Item1 Ref="81" Expression="[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="82" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="83" ControlType="XRTableCell" Name="tableCell2" Weight="0.59980872584337108" TextFormatString="{0:0.##;[0.##];0}" Text="XrTableCell1" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="84" Expression="[monsarf]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="85" UseFont="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="86" ControlType="XRTableCell" Name="tableCell3" Weight="0.62468246560757" Text="XrTableCell2" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="87" Expression="[ward]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="88" UseFont="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="89" ControlType="XRTableCell" Name="tableCell4" Weight="1.5741311523126949" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="90" Expression="Iif([Deleted] == True, '128, 0, 0', [Updated] == True, '0, 128, 0', ?)" PropertyName="ForeColor" EventName="BeforePrint" />
                        <Item2 Ref="91" Expression="Iif([Deleted] == True, '9', [Updated] == True, '9', ?)" PropertyName="Font.Size" EventName="BeforePrint" />
                        <Item3 Ref="92" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" PropertyName="Font.Italic" EventName="BeforePrint" />
                        <Item4 Ref="93" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" PropertyName="Font.Strikeout" EventName="BeforePrint" />
                        <Item5 Ref="94" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" PropertyName="Font.Underline" EventName="BeforePrint" />
                        <Item6 Ref="95" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="96" UseFont="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="97" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.63575180262322628" TextFormatString="{0:yyyy-MM-dd}" Text="XrTableCell4" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="98" Expression="[item_date]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="99" UseFont="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="100" ControlType="XRTableCell" Name="XrTableCell5" Weight="0.68223050612893421" Text="XrTableCell5" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt">
                      <ExpressionBindings>
                        <Item1 Ref="101" Expression="[item_tybe]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="102" UseFont="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="103" ControlType="XRTableCell" Name="LblTotal" Weight="0.20064071644355336" Text="0" TextAlignment="MiddleCenter" Font="Droid Arabic Kufi, 8.25pt" ForeColor="White" BackColor="Highlight">
                      <Summary Ref="104" Running="Report" />
                      <ExpressionBindings>
                        <Item1 Ref="105" Expression="sumRecordNumber([ward])" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="106" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <ExpressionBindings>
                <Item1 Ref="107" Expression="Iif([Deleted] == True, '128, 0, 0', [Updated] == True, '0, 128, 0', ?)" PropertyName="ForeColor" EventName="BeforePrint" />
                <Item2 Ref="108" Expression="Iif([Deleted] == True, 'Dexef', [Updated] == True, 'Dexef', ?)" PropertyName="Font.Name" EventName="BeforePrint" />
                <Item3 Ref="109" Expression="Iif([Deleted] == True, '9', [Updated] == True, '9', ?)" PropertyName="Font.Size" EventName="BeforePrint" />
                <Item4 Ref="110" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" PropertyName="Font.Italic" EventName="BeforePrint" />
                <Item5 Ref="111" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" PropertyName="Font.Strikeout" EventName="BeforePrint" />
                <Item6 Ref="112" Expression="Iif([Deleted] == True, 'True', [Updated] == True, 'True', ?)" PropertyName="Font.Underline" EventName="BeforePrint" />
              </ExpressionBindings>
              <StylePriority Ref="113" UseFont="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>