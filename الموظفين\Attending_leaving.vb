﻿Imports System.Data.SqlClient

Public Class Attending_leaving
    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fillemp()

        new_fill()
    End Sub
    Sub new_fill()
        emp_name.Text = ""
        date_ancestor.Value = Now.Date
        time_attendance.EditValue = Now.ToString("hh:mm")
        type_attance.Text = "حضور"
        save_btn.Visible = True
        type_attance.Visible = True
        roseca.Text = ""
        holiday.Text = ""
        Holiday1.Text = ""
        Holiday2.Text = ""
        date_ancestor1.Value = "2010-01-01"
        date_ancestor_2.Value = "2010-02-01"
        emp_code.Text = 0
    End Sub

    Sub fillemp()
        emp_name.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from employees order by emp_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            emp_name.Properties.Items.Add(dt.Rows(i).Item("emp_name"))
        Next
    End Sub
    Private Sub save_btn_Click_1(sender As Object, e As EventArgs) Handles save_btn.Click

        If emp_name.Text = "" Then
            MsgBox("أدخل اسم الموظف")
            emp_name.Focus()
            Exit Sub
        End If

        emp_name_SelectedIndexChanged(Nothing, Nothing)
        If holiday.Text = Holiday1.Text Then
            MsgBox("هذا اليوم اجازة لهذا الموظف لايمكن تحضيره")
            Exit Sub
        End If
        If holiday.Text = Holiday2.Text Then
            MsgBox("هذا اليوم اجازة لهذا الموظف لايمكن تحضيره")
            Exit Sub
        End If
        If date_ancestor.Value >= Format(date_ancestor1.Value, "yyyy/MM/dd") And date_ancestor.Value <= Format(date_ancestor_2.Value, "yyyy/MM/dd") Then
            MsgBox("هذا اليوم اجازة لهذا الموظف لايمكن تحضيره")
            Exit Sub
        End If

        Try
            Dim sql = "select * from Attending_leaving where emp_name=N'" & (emp_name.Text) & "' and date_ancestor='" & Format(date_ancestor.Value, "yyyy/MM/dd") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                '========= بيانات اساسية============
                dr!time_leave = time_attendance.Text
                time_attendance1.EditValue = dr!time_attendance

                Dim ts2 As TimeSpan = TimeSpan.Parse(time_attendance1.Text)
                Dim ts4 As TimeSpan = TimeSpan.Parse(time_attendance.Text)
                Dim x = ts4 - ts2
                dr!betwen_attendance = CType(x.TotalHours, String)

                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)

                MsgBox("تم تسجيل الانصراف")
            Else
                Dim dr = dt.NewRow
                dr!emp_code = emp_code.Text
                dr!emp_name = emp_name.Text
                dr!date_ancestor = date_ancestor.Text
                dr!time_attendance = time_attendance.Text
                dr!time_leave = "00:00:00"
                dr!betwen_attendance = 0
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                MsgBox("تم تسجيل الحضور")
            End If

            new_fill()
        Catch ex As Exception
            MsgBox("فشل حفظ التسجيل اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub

    Private Sub emp_name_SelectedIndexChanged(sender As Object, e As EventArgs) Handles emp_name.SelectedIndexChanged
        fill_name()
        fill_holiday()
        Dim sql = "select * from employees where emp_name=N'" & (emp_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            '========= بيانات اساسية============  
            emp_code.Text = dr!emp_code
            roseca.Text = dr!Rosacea
        End If

    End Sub
    Sub fill_holiday()
        Dim sql = "select * from holiday where emp_name=N'" & (emp_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            '========= بيانات اساسية============    
            date_ancestor1.Text = dr!date_ancestor
            date_ancestor_2.Text = dr!date_ancestor_2
        Else
            date_ancestor1.Text = "2010-01-01"
            date_ancestor_2.Text = "2010-02-01"
        End If
    End Sub
    Sub fill_name()

        Dim sql = "select * from Attending_leaving where emp_name=N'" & (emp_name.Text) & "' and date_ancestor='" & Format(date_ancestor.Value, "yyyy/MM/dd") & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            type_attance.Text = "انصراف"
            If dr!betwen_attendance > 0 Then
                save_btn.Visible = False
                type_attance.Visible = False
                Exit Sub
            Else
                save_btn.Visible = True
                type_attance.Visible = True
            End If
        End If

    End Sub


    Private Sub date_ancestor_ValueChanged(sender As Object, e As EventArgs) Handles date_ancestor.ValueChanged
        emp_name_SelectedIndexChanged(Nothing, Nothing)
        Dim DayName As String = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.GetDayName(DateTime.Parse(date_ancestor.Value).DayOfWeek)
        holiday.Text = DayName
    End Sub

    Private Sub roseca_TextChanged(sender As Object, e As EventArgs) Handles roseca.TextChanged

        Dim sql = "select * from Rosacea where Rosacea_name=N'" & (roseca.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)

            Holiday1.Text = dr!Holiday1
            Holiday2.Text = dr!Holiday2
        End If
    End Sub
End Class