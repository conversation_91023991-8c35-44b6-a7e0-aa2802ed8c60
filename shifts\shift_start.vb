﻿﻿Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.XtraMessageBox

Public Class shift_start
    
    Public current_shift_id As Integer = 0
    
    Private Sub shift_start_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تهيئة النموذج
            InitializeForm()
            
            ' تحميل بيانات الكاشير الحالي
            LoadCurrentCashierData()
            
            ' التحقق من وجود شيفت مفتوح
            CheckActiveShift()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل النموذج: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub InitializeForm()
        ' إعداد النموذج
        Me.Text = "تهيئة شيفت جديد"
        Me.RightToLeft = RightToLeft.Yes
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        
        ' تعيين التاريخ والوقت الحالي
        shift_date.EditValue = Now.Date
        shift_time.EditValue = Now
        
        ' تعيين مبلغ البداية الافتراضي
        start_amount.Value = 0
        
        ' تفعيل التركيز على مبلغ البداية
        start_amount.Focus()
    End Sub
    
    Private Sub LoadCurrentCashierData()
        Try
            ' تحميل بيانات الكاشير من النموذج الرئيسي
            If XtraForm1 IsNot Nothing Then
                cashier_name.Text = XtraForm1.user_name.Text
                store_name.Text = XtraForm1.store.Text
                
                ' الحصول على معرف المستخدم
                Dim sql As String = "SELECT user_id FROM users WHERE user_name = N'" & cashier_name.Text & "'"
                Using cmd As New SqlCommand(sql, sqlconn)
                    If sqlconn.State = ConnectionState.Closed Then
                        sqlconn.Open()
                    End If
                    
                    Dim result = cmd.ExecuteScalar()
                    If result IsNot Nothing Then
                        cashier_id.Text = result.ToString()
                    End If
                End Using
            End If
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل بيانات الكاشير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub CheckActiveShift()
        Try
            ' التحقق من وجود شيفت مفتوح للكاشير الحالي
            Dim sql As String = "SELECT shift_id, shift_code, start_date, start_amount FROM shifts " &
                               "WHERE cashier_name = N'" & cashier_name.Text & "' AND shift_status = N'مفتوح'"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then
                        ' يوجد شيفت مفتوح
                        current_shift_id = reader("shift_id")
                        
                        Dim message As String = "يوجد شيفت مفتوح بالفعل:" & vbCrLf &
                                              "كود الشيفت: " & reader("shift_code").ToString() & vbCrLf &
                                              "تاريخ البداية: " & reader("start_date").ToString() & vbCrLf &
                                              "مبلغ البداية: " & Format(reader("start_amount"), "#,##0.00") & vbCrLf & vbCrLf &
                                              "هل تريد إغلاق الشيفت الحالي وبدء شيفت جديد؟"
                        
                        Dim result As DialogResult = XtraMessageBox.Show(message, "شيفت مفتوح", 
                                                                        MessageBoxButtons.YesNoCancel, 
                                                                        MessageBoxIcon.Question)
                        
                        If result = DialogResult.Yes Then
                            ' فتح نموذج إغلاق الشيفت
                            reader.Close()
                            OpenShiftEndForm()
                            Me.Close()
                            Return
                        ElseIf result = DialogResult.No Then
                            ' الاستمرار مع الشيفت الحالي
                            reader.Close()
                            Me.Close()
                            Return
                        Else
                            ' إلغاء
                            reader.Close()
                            Me.Close()
                            Return
                        End If
                    End If
                End Using
            End Using
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في التحقق من الشيفت النشط: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub OpenShiftEndForm()
        Try
            ' فتح نموذج إغلاق الشيفت
            Dim shiftEndForm As New shift_end()
            shiftEndForm.current_shift_id = current_shift_id
            shiftEndForm.ShowDialog()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في فتح نموذج إغلاق الشيفت: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub start_shift_btn_Click(sender As Object, e As EventArgs) Handles start_shift_btn.Click
        Try
            ' التحقق من صحة البيانات
            If Not ValidateData() Then
                Return
            End If
            
            ' بدء الشيفت الجديد
            If StartNewShift() Then
                XtraMessageBox.Show("تم بدء الشيفت بنجاح!" & vbCrLf & 
                                   "كود الشيفت: " & shift_code.Text, 
                                   "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Me.Close()
            End If
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في بدء الشيفت: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function ValidateData() As Boolean
        ' التحقق من اسم الكاشير
        If String.IsNullOrEmpty(cashier_name.Text.Trim()) Then
            XtraMessageBox.Show("يرجى إدخال اسم الكاشير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cashier_name.Focus()
            Return False
        End If
        
        ' التحقق من اسم المخزن
        If String.IsNullOrEmpty(store_name.Text.Trim()) Then
            XtraMessageBox.Show("يرجى إدخال اسم المخزن", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            store_name.Focus()
            Return False
        End If
        
        ' التحقق من مبلغ البداية
        If start_amount.Value < 0 Then
            XtraMessageBox.Show("مبلغ البداية لا يمكن أن يكون سالباً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            start_amount.Focus()
            Return False
        End If
        
        Return True
    End Function
    
    Private Function StartNewShift() As Boolean
        Try
            ' إنشاء كود الشيفت
            Dim todayDate As String = Format(Now, "yyyyMMdd")
            Dim shiftCount As Integer = GetTodayShiftCount() + 1
            shift_code.Text = todayDate & "-" & shiftCount.ToString("00")
            
            ' إدراج الشيفت الجديد
            Dim sql As String = "INSERT INTO shifts (shift_code, cashier_name, cashier_id, store_name, " &
                               "start_date, start_amount, notes, created_by, shift_status) " &
                               "VALUES (@shift_code, @cashier_name, @cashier_id, @store_name, " &
                               "@start_date, @start_amount, @notes, @created_by, N'مفتوح')"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                cmd.Parameters.AddWithValue("@shift_code", shift_code.Text)
                cmd.Parameters.AddWithValue("@cashier_name", cashier_name.Text)
                cmd.Parameters.AddWithValue("@cashier_id", Integer.Parse(cashier_id.Text))
                cmd.Parameters.AddWithValue("@store_name", store_name.Text)
                cmd.Parameters.AddWithValue("@start_date", Now)
                cmd.Parameters.AddWithValue("@start_amount", start_amount.Value)
                cmd.Parameters.AddWithValue("@notes", notes.Text)
                cmd.Parameters.AddWithValue("@created_by", cashier_name.Text)
                
                Dim result As Integer = cmd.ExecuteNonQuery()
                
                If result > 0 Then
                    ' الحصول على معرف الشيفت الجديد
                    current_shift_id = GetLastShiftId()
                    
                    ' تحديث المتغير العام للشيفت النشط
                    If main_page IsNot Nothing Then
                        main_page.current_active_shift_id = current_shift_id
                    End If
                    
                    Return True
                Else
                    Return False
                End If
            End Using
            
        Catch ex As Exception
            Throw New Exception("خطأ في إنشاء الشيفت: " & ex.Message)
        End Try
    End Function
    
    Private Function GetTodayShiftCount() As Integer
        Try
            Dim sql As String = "SELECT COUNT(*) FROM shifts WHERE CAST(start_date AS DATE) = CAST(GETDATE() AS DATE)"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                Dim result = cmd.ExecuteScalar()
                Return If(result IsNot Nothing, Convert.ToInt32(result), 0)
            End Using
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    Private Function GetLastShiftId() As Integer
        Try
            Dim sql As String = "SELECT TOP 1 shift_id FROM shifts ORDER BY shift_id DESC"
            
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                
                Dim result = cmd.ExecuteScalar()
                Return If(result IsNot Nothing, Convert.ToInt32(result), 0)
            End Using
            
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    Private Sub cancel_btn_Click(sender As Object, e As EventArgs) Handles cancel_btn.Click
        Me.Close()
    End Sub
    
    Private Sub start_amount_KeyPress(sender As Object, e As KeyPressEventArgs) Handles start_amount.KeyPress
        ' السماح بالأرقام والفاصلة العشرية فقط
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso e.KeyChar <> ","c AndAlso e.KeyChar <> Chr(8) Then
            e.Handled = True
        End If
    End Sub
    
    Private Sub start_amount_Enter(sender As Object, e As EventArgs) Handles start_amount.Enter
        start_amount.SelectAll()
    End Sub
    
End Class
