# دليل دمج نظام مراكز التكلفة

## ✅ تم إنجاز المهام التالية:

### 1. إضافة الملفات إلى المشروع الرئيسي:
- ✅ `CostCentersManagement.vb` - شاشة إدارة مراكز التكلفة
- ✅ `CostCentersManagement.Designer.vb` - تصميم الشاشة
- ✅ `CostCentersManagement.resx` - ملف الموارد
- ✅ `CostCenterHelper.vb` - كلاس مساعد للوظائف المشتركة

### 2. تحديث ملف المشروع:
- ✅ إضافة الملفات إلى `code store.vbproj`
- ✅ ربط ملف التصميم بالملف الرئيسي
- ✅ إضافة ملف الموارد

### 3. تحديث القوائم والشاشة الرئيسية:
- ✅ إضافة "مراكز التكلفة" إلى قائمة الإعدادات
- ✅ إضافة "تقارير مراكز التكلفة" إلى قائمة الحسابات
- ✅ إضافة بلاطة "مراكز التكلفة" في الشاشة الرئيسية

## 🚀 خطوات التشغيل:

### الخطوة 1: تشغيل سكريبت قاعدة البيانات
```sql
-- شغل ملف: database/CostCenters_Database_Script.sql
```

### الخطوة 2: إعادة بناء المشروع
```
Build → Rebuild Solution
```

### الخطوة 3: اختبار الشاشة
```
ملف → مراكز التكلفة
```

## 🔧 استخدام الكلاس المساعد في الشاشات الأخرى:

### إضافة مراكز التكلفة إلى شاشة الفواتير:

1. **إضافة ComboBox في التصميم:**
```vb
Friend WithEvents CostCenterCombo As DevExpress.XtraEditors.LookUpEdit
```

2. **تحميل البيانات في Load:**
```vb
Private Sub InvoiceForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
    ' تحميل مراكز التكلفة
    CostCenterHelper.LoadActiveCostCenters(CostCenterCombo, True)
End Sub
```

3. **حفظ مركز التكلفة مع الفاتورة:**
```vb
Private Sub SaveInvoice()
    ' ... كود حفظ الفاتورة ...
    
    ' حفظ مركز التكلفة
    If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
        CostCenterHelper.UpdateInvoiceCostCenter(invoiceId, Convert.ToInt32(CostCenterCombo.EditValue))
    End If
End Sub
```

### إضافة مراكز التكلفة إلى شاشة المصروفات:

```vb
' في Load Event
CostCenterHelper.LoadActiveCostCenters(CostCenterCombo, True)

' في Save Event
If CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue) Then
    CostCenterHelper.UpdateExpenseCostCenter(expenseId, Convert.ToInt32(CostCenterCombo.EditValue))
End If
```

## 📊 استخدام وظائف التقارير:

### حساب تكاليف مركز معين:
```vb
Dim startDate As Date = DateTimePicker1.Value
Dim endDate As Date = DateTimePicker2.Value
Dim costCenterId As Integer = 1

Dim expenses = CostCenterHelper.CalculateCostCenterExpenses(costCenterId, startDate, endDate)
Dim revenues = CostCenterHelper.CalculateCostCenterRevenues(costCenterId, startDate, endDate)
Dim netProfit = CostCenterHelper.CalculateCostCenterNetProfit(costCenterId, startDate, endDate)
```

### الحصول على ملخص شامل:
```vb
Dim summary = CostCenterHelper.GetCostCenterSummary(costCenterId, startDate, endDate)

MessageBox.Show($"مركز التكلفة: {summary.CostCenterName}" & vbCrLf &
                $"المصروفات: {summary.TotalExpenses:N0}" & vbCrLf &
                $"الإيرادات: {summary.TotalRevenues:N0}" & vbCrLf &
                $"صافي الربح: {summary.NetProfit:N0}")
```

## 🎨 تخصيص الألوان:

النظام يستخدم `ColorScheme` الموحد. يمكن تخصيص الألوان من خلال:

```vb
' في Load Event للشاشة
ColorScheme.ApplyButtonColors(YourButton, ColorScheme.PrimaryBlue, ColorScheme.PrimaryBlueHover)
```

## 🔒 إدارة الصلاحيات:

### صلاحية إدارة مراكز التكلفة:
```vb
If XtraForm1.m1.Checked = False Then
    XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
    Exit Sub
End If
```

### صلاحية تقارير مراكز التكلفة:
```vb
If XtraForm1.m15.Checked = False Then
    XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
    Exit Sub
End If
```

## 🗄️ هيكل قاعدة البيانات:

### الجدول الرئيسي: `cost_centers`
- `cost_center_id` - المعرف الفريد
- `cost_center_code` - كود مركز التكلفة
- `cost_center_name` - اسم مركز التكلفة
- `cost_center_type` - نوع المركز
- `budget_amount` - الميزانية المخططة
- `is_active` - حالة النشاط

### الجداول المحدثة:
تم إضافة `cost_center_id` إلى:
- `invoice_add` (فواتير المبيعات)
- `Expenses_add` (المصروفات)
- `Purchases_add` (المشتريات)
- `Revenues_add` (الإيرادات)

## ⚠️ نصائح مهمة:

1. **تأكد من تشغيل سكريبت قاعدة البيانات** قبل استخدام النظام
2. **استخدم الكلاس المساعد** بدلاً من كتابة استعلامات مباشرة
3. **تحقق من الصلاحيات** قبل الوصول للشاشات
4. **اربط جميع العمليات المالية** بمراكز التكلفة للحصول على تقارير دقيقة

## 🆘 حل المشاكل الشائعة:

### خطأ "Type 'CostCentersManagement' is not defined":
- تأكد من إضافة الملفات إلى المشروع
- أعد بناء المشروع (Rebuild Solution)

### خطأ في قاعدة البيانات:
- تأكد من تشغيل سكريبت قاعدة البيانات
- تحقق من الاتصال بقاعدة البيانات

### مشاكل في الصلاحيات:
- تأكد من تفعيل الصلاحيات المطلوبة للمستخدم

---

**النظام جاهز للاستخدام! 🎉**

للمزيد من التفاصيل، راجع:
- `COST_CENTERS_QUICK_SETUP.md` - دليل الإعداد السريع
- `cost_centers/COST_CENTERS_SYSTEM_README.md` - دليل شامل
