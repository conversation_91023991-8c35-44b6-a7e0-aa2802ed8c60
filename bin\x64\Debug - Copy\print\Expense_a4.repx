﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.Expense_a4, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="Expense_a4" Margins="0, 0, 10, 3" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="17.2" DataMember="Expense_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="10" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="3.125" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="PageFooterBand" Name="PageFooter" HeightF="26.04167">
      <Controls>
        <Item1 Ref="6" ControlType="XRPageInfo" Name="XrPageInfo2" TextAlignment="TopRight" SizeF="48.95837,23" LocationFloat="696.5209, 0.4791727" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="8" ControlType="XRLabel" Name="XrLabel26" RightToLeft="Yes" Text="رقم الصفحة :" TextAlignment="MiddleRight" SizeF="65.52075,25.08331" LocationFloat="745.4792, 0.4791727" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="10" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleRight" SizeF="192.6042,25.08332" LocationFloat="16.00006, 0.4791727" Font="Times New Roman, 10pt" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
    <Item5 Ref="12" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="132.875">
      <Controls>
        <Item1 Ref="13" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="526.5314,25" LocationFloat="300.4686, 50" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="14" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="15" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="16" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="526.5315,25" LocationFloat="300.4686, 25" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="17" Expression="[Expenses_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="18" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="19" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" SizeF="328.1249,23" LocationFloat="484.7763, 104.6666" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="20" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="21" UseFont="false" />
        </Item3>
        <Item4 Ref="22" ControlType="XRTable" Name="XrTable3" TextAlignment="MiddleCenter" SizeF="208.1145,50" LocationFloat="9.999998, 25" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
          <Rows>
            <Item1 Ref="23" ControlType="XRTableRow" Name="XrTableRow3" Weight="1">
              <Cells>
                <Item1 Ref="24" ControlType="XRTableCell" Name="XrTableCell12" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="25" Expression="[Expenses_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="27" ControlType="XRTableCell" Name="XrTableCell13" Weight="1" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="28" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="29" ControlType="XRTableRow" Name="XrTableRow4" Weight="1">
              <Cells>
                <Item1 Ref="30" ControlType="XRTableCell" Name="XrTableCell11" Weight="1" TextFormatString="{0:#,#}" Text="XrTableCell11" TextAlignment="MiddleLeft" Padding="2,2,0,0,100">
                  <ExpressionBindings>
                    <Item1 Ref="31" Expression="[total_Expenses]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UsePadding="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="33" ControlType="XRTableCell" Name="XrTableCell14" Weight="1" Text="الاجمالي" TextAlignment="MiddleRight" Padding="2,2,0,0,100">
                  <StylePriority Ref="34" UsePadding="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="35" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item5>
    <Item6 Ref="36" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="104.875">
      <Controls>
        <Item1 Ref="37" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="270.3541,31.33333" LocationFloat="542.2526, 11.62499" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="38" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="39" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="40" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="270.3541,41.04168" LocationFloat="542.2526, 42.95832" Font="Droid Arabic Kufi, 14pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="41" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="42" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="43" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="128.7499,104.875" LocationFloat="9.39325, 0">
          <ExpressionBindings>
            <Item1 Ref="44" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
      </Controls>
    </Item6>
    <Item7 Ref="45" ControlType="PageHeaderBand" Name="PageHeader" HeightF="207.8334">
      <Controls>
        <Item1 Ref="46" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="817.0001,33.33334" LocationFloat="10, 171.7917" Font="Droid Arabic Kufi, 9.75pt, charSet=0" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="47" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
              <Cells>
                <Item1 Ref="48" ControlType="XRTableCell" Name="XrTableCell4" Weight="1.8693602641183027" Text="الشرج" Padding="2,2,0,0,100">
                  <StylePriority Ref="49" UsePadding="false" />
                </Item1>
                <Item2 Ref="50" ControlType="XRTableCell" Name="XrTableCell2" Weight="1.1352824638308083" Text="المبلغ" Padding="2,2,0,0,100" />
                <Item3 Ref="51" ControlType="XRTableCell" Name="XrTableCell3" Weight="2.165779289787741" Text="نوع المصروف" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="White" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="52" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="53" ControlType="XRPictureBox" Name="XrPictureBox2" SizeF="183.3333,32.75" LocationFloat="87.61458, 31.33331">
          <ExpressionBindings>
            <Item1 Ref="54" Expression="[pic_baracode]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item2>
        <Item3 Ref="55" ControlType="XRPictureBox" Name="XrPictureBox1" Sizing="StretchImage" SizeF="100.0001,83.54168" LocationFloat="677.7187, 6.392598E-06">
          <ExpressionBindings>
            <Item1 Ref="56" Expression="[pic_qr]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
        <Item4 Ref="57" ControlType="XRLabel" Name="XrLabel19" Text="فاتورة مصروفات" TextAlignment="MiddleCenter" SizeF="198.5417,31.33332" LocationFloat="27.61456, 0" Font="Times New Roman, 16pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="58" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="59" ControlType="XRPageInfo" Name="XrPageInfo1" PageInfo="DateTime" TextAlignment="MiddleCenter" SizeF="198.5417,23" LocationFloat="27.61456, 76.58331" Padding="2,2,0,0,100">
          <StylePriority Ref="60" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="61" ControlType="XRLabel" Name="XrLabel8" RightToLeft="Yes" Text="التاريخ :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="252.7187, 128.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="62" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="63" ControlType="XRLabel" Name="XrLabel4" RightToLeft="Yes" Text="رقم الفاتورة :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="662.0939, 128.6667" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="64" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="65" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="225.1043,31.25004" LocationFloat="27.61456, 128.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="66" Expression="[Expenses_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="67" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="68" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="277.0833,31.24999" LocationFloat="385.0103, 128.6667" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="69" Expression="[Expenses_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="70" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="71" ControlType="XRLabel" Name="XrLabel10" RightToLeft="Yes" Text="المخزن :" TextAlignment="TopCenter" SizeF="132.2916,31.25" LocationFloat="662.0939, 94.27084" Font="Droid Arabic Kufi, 10pt" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="72" UseFont="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="73" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleRight" SizeF="277.0832,31.25" LocationFloat="385.0105, 94.27084" Font="Droid Arabic Kufi, 10pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="74" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="75" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item7>
    <Item8 Ref="76" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="77" ControlType="DetailBand" Name="Detail1" HeightF="25">
          <Controls>
            <Item1 Ref="78" ControlType="XRTable" Name="XrTable2" TextAlignment="MiddleCenter" SizeF="817.6069,25" LocationFloat="9.39325, 0" Font="Droid Arabic Kufi, 9.75pt, charSet=0" Borders="Bottom">
              <Rows>
                <Item1 Ref="79" ControlType="XRTableRow" Name="XrTableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="80" ControlType="XRTableCell" Name="XrTableCell6" Weight="2.9599219880162275" TextFormatString="{0:#,#}" Text="XrTableCell6" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="81" Expression="[Expense_print].[Expenses_deital]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item1>
                    <Item2 Ref="82" ControlType="XRTableCell" Name="XrTableCell7" Weight="1.7939082022366319" Text="XrTableCell7" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="83" Expression="[Expense_print].[Expenses_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item2>
                    <Item3 Ref="84" ControlType="XRTableCell" Name="XrTableCell8" Weight="3.42223806986298" Text="XrTableCell8" TextAlignment="MiddleRight" Font="Arial, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="85" Expression="[Expense_print].[Expenses_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="86" UseFont="false" UseTextAlignment="false" />
                    </Item3>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="87" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <StyleSheet>
    <Item1 Ref="88" Name="Title" BorderStyle="Inset" Font="Tahoma, 14pt" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="1" />
    <Item2 Ref="89" Name="DetailCaption3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" BackColor="Transparent" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item3 Ref="90" Name="DetailData3" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" />
    <Item4 Ref="91" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Tahoma, 8pt" ForeColor="Black" BackColor="255,231,231,231" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;Default" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item5 Ref="92" Name="DetailCaptionBackground3" BorderStyle="Inset" BackColor="Transparent" BorderColor="255,206,206,206" Sides="Top" StringFormat="Near;Near;0;None;Character;Default" BorderWidthSerializable="2" />
    <Item6 Ref="93" Name="PageInfo" BorderStyle="Inset" Padding="2,2,0,0,100" Font="Tahoma, 8pt, style=Bold" ForeColor="255,75,75,75" StringFormat="Near;Near;0;None;Character;Default" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>