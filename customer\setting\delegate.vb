﻿Imports System.Data.SqlClient

Public Class delegte

    Private Sub delegte_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        new_btn_Click(Nothing, Nothing)

    End Sub

    Sub fill_user()
        ListBox1.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from delegate order by delegate_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            ListBox1.Items.Add(dt.Rows(i).Item("delegate_name"))
        Next

    End Sub
    Function get_id(subname) As String
        Dim sql = "select * from delegate where delegate_name=N'" & (ListBox1.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("delegate_id") Else Return ""
    End Function

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ListBox1.SelectedIndexChanged
        TextBox2.Text = get_id(ListBox1.Text)
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged
        delegate_name.Text = ""
        Dim sql = "select * from delegate where delegate_id=N'" & (TextBox2.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            On Error Resume Next
            delegate_name.Text = dr!delegate_name
            delegate_earn.Text = dr!delegate_earn
            delegate_email.Text = dr!delegate_email
            delegate_notes.Text = dr!delegate_notes
            delegate_address.Text = dr!delegate_address
            delegate_phone.Text = dr!delegate_phone
            If dr!delegate_catsh = "علي الارباح" Then
                earn.Checked = True
            End If
            If dr!delegate_catsh = "علي المبيعات" Then
                invoice.Checked = True
            End If     
        End If
        save_btn.Enabled = False
        edit_btn.Enabled = True
        delet_btn.Enabled = True
    End Sub

    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
        Try
            Dim sql = "select * from delegate where delegate_id  = N'" & TextBox2.Text.Trim() & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)

                '========= بيانات اساسية============
                dr!delegate_name = delegate_name.Text
                dr!delegate_earn = delegate_earn.Text
                dr!delegate_email = delegate_email.Text
                dr!delegate_notes = delegate_notes.Text
                dr!delegate_address = delegate_address.Text
                dr!delegate_phone = delegate_phone.Text
                If earn.Checked = True Then
                    dr!delegate_catsh = earn.Text
                End If
                If invoice.Checked = True Then
                    dr!delegate_catsh = invoice.Text
                End If
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
new_btn_Click(Nothing, Nothing)
                My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
            End If
        Catch ex As Exception
            MsgBox("فشل تعديل المندوب اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If delegate_name.Text = "" Then
            MsgBox("أدخل اسم المندوب")
            delegate_name.Focus()
            Exit Sub
        End If
        Try
            Dim sql = "select * from delegate where delegate_name=N'" & (delegate_name.Text) & "' "
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                MsgBox("اسم المندوب موجود سابقا", MsgBoxStyle.Critical, "خطأ")
            Else
                Dim dr = dt.NewRow
                dr!delegate_name = delegate_name.Text
                dr!delegate_earn = delegate_earn.Text
                dr!delegate_email = delegate_email.Text
                dr!delegate_notes = delegate_notes.Text
                dr!delegate_address = delegate_address.Text
                dr!delegate_phone = delegate_phone.Text
                If earn.Checked = True Then
                    dr!delegate_catsh = earn.Text
                End If
                If invoice.Checked = True Then
                    dr!delegate_catsh = invoice.Text
                End If
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                new_btn_Click(Nothing, Nothing)
                My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
            End If
        Catch ex As Exception
            MsgBox("فشل حفظ المندوب اعد المحاولة", MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    Private Sub delet_btn_Click(sender As Object, e As EventArgs) Handles delet_btn.Click
        If MsgBox("هل انت متأكد بحذف المندوب المحدد؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        Dim sql = "select * from delegate where delegate_id  = N'" & TextBox2.Text.Trim() & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("فشل في جلب البيانات")
        Else
            Dim dr = dt.Rows(0)
            dr.Delete()
            Dim cmd As New SqlCommandBuilder(adp)
            adp.Update(dt)
           new_btn_Click(Nothing, Nothing)
            My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.WaitToComplete)
        End If
    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        delegate_name.Text = ""
        delegate_earn.Text = 0
        delegate_email.Text = ""
        delegate_notes.Text = ""
        delegate_address.Text = ""
        delegate_phone.Text = ""
        earn.Checked = True
        fill_user()
        delegate_name.Focus()
        save_btn.Enabled = True
        edit_btn.Enabled = False
        delet_btn.Enabled = False
    End Sub

    Private Sub close_btn_Click(sender As Object, e As EventArgs) Handles close_btn.Click
        Me.Dispose()
    End Sub
End Class