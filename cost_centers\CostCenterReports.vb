Imports DevExpress.XtraEditors
Imports DevExpress.XtraCharts
Imports System.Data.SqlClient
Imports System.Drawing

Public Class CostCenterReports
    
    Private Sub CostCenterReports_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تطبيق نظام الألوان
            ApplyColorScheme()
            
            ' تحميل مراكز التكلفة
            LoadCostCenters()
            
            ' تعيين التواريخ الافتراضية
            SetDefaultDates()
            
            ' تحميل البيانات الأولية
            LoadInitialData()
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub ApplyColorScheme()
        ' تطبيق الألوان من نظام الألوان الموحد
        Me.BackColor = ColorScheme.BackgroundLight
        
        ' تطبيق الألوان على الأزرار
        ColorScheme.ApplyButtonColors(SearchButton, ColorScheme.PrimaryBlue, ColorScheme.PrimaryBlueHover)
        ColorScheme.ApplyButtonColors(ExportButton, ColorScheme.SuccessGreen, ColorScheme.SuccessGreenHover)
    End Sub
    
    Private Sub LoadCostCenters()
        Try
            ' تحميل مراكز التكلفة في ComboBox
            CostCenterHelper.LoadActiveCostCenters(CostCenterCombo, True)
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تحميل مراكز التكلفة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub SetDefaultDates()
        ' تعيين التواريخ الافتراضية (أول الشهر الحالي إلى اليوم)
        StartDateEdit.EditValue = New Date(DateTime.Now.Year, DateTime.Now.Month, 1)
        EndDateEdit.EditValue = DateTime.Now.Date
    End Sub
    
    Private Sub LoadInitialData()
        ' تحميل البيانات الأولية لجميع مراكز التكلفة
        SearchButton_Click(Nothing, Nothing)
    End Sub
    
    Private Sub SearchButton_Click(sender As Object, e As EventArgs) Handles SearchButton.Click
        Try
            ' التحقق من صحة التواريخ
            If Not ValidateDates() Then
                Exit Sub
            End If
            
            ' تحميل البيانات
            LoadSummaryData()
            LoadDetailsData()
            LoadChartsData()
            
            XtraMessageBox.Show("تم تحميل البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            XtraMessageBox.Show("خطأ في البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function ValidateDates() As Boolean
        If StartDateEdit.EditValue Is Nothing Then
            XtraMessageBox.Show("يجب اختيار تاريخ البداية", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            StartDateEdit.Focus()
            Return False
        End If
        
        If EndDateEdit.EditValue Is Nothing Then
            XtraMessageBox.Show("يجب اختيار تاريخ النهاية", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            EndDateEdit.Focus()
            Return False
        End If
        
        If CDate(StartDateEdit.EditValue) > CDate(EndDateEdit.EditValue) Then
            XtraMessageBox.Show("تاريخ البداية يجب أن يكون أقل من تاريخ النهاية", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            StartDateEdit.Focus()
            Return False
        End If
        
        Return True
    End Function
    
    Private Sub LoadSummaryData()
        Try
            Dim startDate = CDate(StartDateEdit.EditValue)
            Dim endDate = CDate(EndDateEdit.EditValue)
            Dim costCenterId = If(CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue), 
                                Convert.ToInt32(CostCenterCombo.EditValue), 0)
            
            Dim sql As String
            If costCenterId > 0 Then
                ' تقرير لمركز تكلفة محدد
                sql = "SELECT cc.cost_center_code, cc.cost_center_name, " &
                     "ISNULL(expenses.total_expenses, 0) as total_expenses, " &
                     "ISNULL(revenues.total_revenues, 0) as total_revenues, " &
                     "ISNULL(revenues.total_revenues, 0) - ISNULL(expenses.total_expenses, 0) as net_profit, " &
                     "CASE WHEN ISNULL(revenues.total_revenues, 0) > 0 THEN " &
                     "((ISNULL(revenues.total_revenues, 0) - ISNULL(expenses.total_expenses, 0)) / ISNULL(revenues.total_revenues, 0)) * 100 " &
                     "ELSE 0 END as profit_margin " &
                     "FROM cost_centers cc " &
                     "LEFT JOIN (SELECT cost_center_id, SUM(total_Expenses) as total_expenses " &
                     "FROM Expenses_add WHERE Expenses_date BETWEEN @startDate AND @endDate GROUP BY cost_center_id) expenses " &
                     "ON cc.cost_center_id = expenses.cost_center_id " &
                     "LEFT JOIN (SELECT cost_center_id, SUM(total_invoice) as total_revenues " &
                     "FROM invoice_add WHERE invoice_date BETWEEN @startDate AND @endDate GROUP BY cost_center_id) revenues " &
                     "ON cc.cost_center_id = revenues.cost_center_id " &
                     "WHERE cc.cost_center_id = @costCenterId"
            Else
                ' تقرير لجميع مراكز التكلفة
                sql = "SELECT cc.cost_center_code, cc.cost_center_name, " &
                     "ISNULL(expenses.total_expenses, 0) as total_expenses, " &
                     "ISNULL(revenues.total_revenues, 0) as total_revenues, " &
                     "ISNULL(revenues.total_revenues, 0) - ISNULL(expenses.total_expenses, 0) as net_profit, " &
                     "CASE WHEN ISNULL(revenues.total_revenues, 0) > 0 THEN " &
                     "((ISNULL(revenues.total_revenues, 0) - ISNULL(expenses.total_expenses, 0)) / ISNULL(revenues.total_revenues, 0)) * 100 " &
                     "ELSE 0 END as profit_margin " &
                     "FROM cost_centers cc " &
                     "LEFT JOIN (SELECT cost_center_id, SUM(total_Expenses) as total_expenses " &
                     "FROM Expenses_add WHERE Expenses_date BETWEEN @startDate AND @endDate GROUP BY cost_center_id) expenses " &
                     "ON cc.cost_center_id = expenses.cost_center_id " &
                     "LEFT JOIN (SELECT cost_center_id, SUM(total_invoice) as total_revenues " &
                     "FROM invoice_add WHERE invoice_date BETWEEN @startDate AND @endDate GROUP BY cost_center_id) revenues " &
                     "ON cc.cost_center_id = revenues.cost_center_id " &
                     "WHERE cc.is_active = 1 ORDER BY cc.cost_center_code"
            End If
            
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@startDate", startDate)
            cmd.Parameters.AddWithValue("@endDate", endDate)
            If costCenterId > 0 Then
                cmd.Parameters.AddWithValue("@costCenterId", costCenterId)
            End If
            
            Dim adapter As New SqlDataAdapter(cmd)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            
            SummaryGrid.DataSource = dataTable
            
            ' تنسيق الأعمدة
            SummaryView.Columns("total_expenses").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            SummaryView.Columns("total_expenses").DisplayFormat.FormatString = "N0"
            
            SummaryView.Columns("total_revenues").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            SummaryView.Columns("total_revenues").DisplayFormat.FormatString = "N0"
            
            SummaryView.Columns("net_profit").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            SummaryView.Columns("net_profit").DisplayFormat.FormatString = "N0"
            
            SummaryView.Columns("profit_margin").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            SummaryView.Columns("profit_margin").DisplayFormat.FormatString = "N2"
            
        Catch ex As Exception
            Throw New Exception("خطأ في تحميل بيانات الملخص: " & ex.Message)
        End Try
    End Sub
    
    Private Sub LoadDetailsData()
        Try
            Dim startDate = CDate(StartDateEdit.EditValue)
            Dim endDate = CDate(EndDateEdit.EditValue)
            Dim costCenterId = If(CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue), 
                                Convert.ToInt32(CostCenterCombo.EditValue), 0)
            
            Dim sql = "SELECT transaction_date, transaction_type, transaction_number, description, amount, cost_center_name FROM (" &
                     "SELECT Expenses_date as transaction_date, 'مصروف' as transaction_type, " &
                     "Expenses_number as transaction_number, Expenses_name as description, " &
                     "total_Expenses as amount, cc.cost_center_name " &
                     "FROM Expenses_add e " &
                     "LEFT JOIN cost_centers cc ON e.cost_center_id = cc.cost_center_id " &
                     "WHERE e.Expenses_date BETWEEN @startDate AND @endDate " &
                     If(costCenterId > 0, "AND e.cost_center_id = @costCenterId ", "") &
                     "UNION ALL " &
                     "SELECT invoice_date as transaction_date, 'فاتورة مبيعات' as transaction_type, " &
                     "invoice_number as transaction_number, Accounts_name as description, " &
                     "total_invoice as amount, cc.cost_center_name " &
                     "FROM invoice_add i " &
                     "LEFT JOIN cost_centers cc ON i.cost_center_id = cc.cost_center_id " &
                     "WHERE i.invoice_date BETWEEN @startDate AND @endDate " &
                     If(costCenterId > 0, "AND i.cost_center_id = @costCenterId ", "") &
                     ") transactions ORDER BY transaction_date DESC"
            
            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@startDate", startDate)
            cmd.Parameters.AddWithValue("@endDate", endDate)
            If costCenterId > 0 Then
                cmd.Parameters.AddWithValue("@costCenterId", costCenterId)
            End If
            
            Dim adapter As New SqlDataAdapter(cmd)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            
            DetailsGrid.DataSource = dataTable
            
            ' تنسيق الأعمدة
            DetailsView.Columns("transaction_date").DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
            DetailsView.Columns("transaction_date").DisplayFormat.FormatString = "dd/MM/yyyy"
            
            DetailsView.Columns("amount").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            DetailsView.Columns("amount").DisplayFormat.FormatString = "N0"
            
        Catch ex As Exception
            Throw New Exception("خطأ في تحميل بيانات التفاصيل: " & ex.Message)
        End Try
    End Sub

    Private Sub LoadChartsData()
        Try
            SetupExpensesChart()
            SetupRevenuesChart()

        Catch ex As Exception
            Throw New Exception("خطأ في تحميل الرسوم البيانية: " & ex.Message)
        End Try
    End Sub

    Private Sub SetupExpensesChart()
        Try
            Dim startDate = CDate(StartDateEdit.EditValue)
            Dim endDate = CDate(EndDateEdit.EditValue)
            Dim costCenterId = If(CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue),
                                Convert.ToInt32(CostCenterCombo.EditValue), 0)

            ExpensesChart.Series.Clear()

            Dim series As New Series("المصروفات حسب مراكز التكلفة", ViewType.Column)
            series.View.Color = ColorScheme.DangerRed

            Dim sql As String
            If costCenterId > 0 Then
                sql = "SELECT cc.cost_center_name, ISNULL(SUM(e.total_Expenses), 0) as total_expenses " &
                     "FROM cost_centers cc " &
                     "LEFT JOIN Expenses_add e ON cc.cost_center_id = e.cost_center_id " &
                     "AND e.Expenses_date BETWEEN @startDate AND @endDate " &
                     "WHERE cc.cost_center_id = @costCenterId " &
                     "GROUP BY cc.cost_center_name"
            Else
                sql = "SELECT cc.cost_center_name, ISNULL(SUM(e.total_Expenses), 0) as total_expenses " &
                     "FROM cost_centers cc " &
                     "LEFT JOIN Expenses_add e ON cc.cost_center_id = e.cost_center_id " &
                     "AND e.Expenses_date BETWEEN @startDate AND @endDate " &
                     "WHERE cc.is_active = 1 " &
                     "GROUP BY cc.cost_center_name " &
                     "ORDER BY total_expenses DESC"
            End If

            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@startDate", startDate)
            cmd.Parameters.AddWithValue("@endDate", endDate)
            If costCenterId > 0 Then
                cmd.Parameters.AddWithValue("@costCenterId", costCenterId)
            End If

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                Dim centerName = reader.GetString("cost_center_name")
                Dim expenses = reader.GetDecimal("total_expenses")
                series.Points.Add(New SeriesPoint(centerName, expenses))
            End While
            reader.Close()

            ExpensesChart.Series.Add(series)

            ' تخصيص مظهر الرسم البياني
            ExpensesChart.Titles.Clear()
            ExpensesChart.Titles.Add(New ChartTitle() With {.Text = "المصروفات حسب مراكز التكلفة"})

        Catch ex As Exception
            ' في حالة الخطأ، إخفاء الرسم البياني
            ExpensesChart.Visible = False
        End Try
    End Sub

    Private Sub SetupRevenuesChart()
        Try
            Dim startDate = CDate(StartDateEdit.EditValue)
            Dim endDate = CDate(EndDateEdit.EditValue)
            Dim costCenterId = If(CostCenterCombo.EditValue IsNot Nothing AndAlso Not IsDBNull(CostCenterCombo.EditValue),
                                Convert.ToInt32(CostCenterCombo.EditValue), 0)

            RevenuesChart.Series.Clear()

            Dim series As New Series("الإيرادات حسب مراكز التكلفة", ViewType.Column)
            series.View.Color = ColorScheme.SuccessGreen

            Dim sql As String
            If costCenterId > 0 Then
                sql = "SELECT cc.cost_center_name, ISNULL(SUM(i.total_invoice), 0) as total_revenues " &
                     "FROM cost_centers cc " &
                     "LEFT JOIN invoice_add i ON cc.cost_center_id = i.cost_center_id " &
                     "AND i.invoice_date BETWEEN @startDate AND @endDate " &
                     "WHERE cc.cost_center_id = @costCenterId " &
                     "GROUP BY cc.cost_center_name"
            Else
                sql = "SELECT cc.cost_center_name, ISNULL(SUM(i.total_invoice), 0) as total_revenues " &
                     "FROM cost_centers cc " &
                     "LEFT JOIN invoice_add i ON cc.cost_center_id = i.cost_center_id " &
                     "AND i.invoice_date BETWEEN @startDate AND @endDate " &
                     "WHERE cc.is_active = 1 " &
                     "GROUP BY cc.cost_center_name " &
                     "ORDER BY total_revenues DESC"
            End If

            Dim cmd As New SqlCommand(sql, sqlconn)
            cmd.Parameters.AddWithValue("@startDate", startDate)
            cmd.Parameters.AddWithValue("@endDate", endDate)
            If costCenterId > 0 Then
                cmd.Parameters.AddWithValue("@costCenterId", costCenterId)
            End If

            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                Dim centerName = reader.GetString("cost_center_name")
                Dim revenues = reader.GetDecimal("total_revenues")
                series.Points.Add(New SeriesPoint(centerName, revenues))
            End While
            reader.Close()

            RevenuesChart.Series.Add(series)

            ' تخصيص مظهر الرسم البياني
            RevenuesChart.Titles.Clear()
            RevenuesChart.Titles.Add(New ChartTitle() With {.Text = "الإيرادات حسب مراكز التكلفة"})

        Catch ex As Exception
            ' في حالة الخطأ، إخفاء الرسم البياني
            RevenuesChart.Visible = False
        End Try
    End Sub

    Private Sub ExportButton_Click(sender As Object, e As EventArgs) Handles ExportButton.Click
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.Title = "تصدير تقرير مراكز التكلفة"
            saveDialog.FileName = "تقرير_مراكز_التكلفة_" & DateTime.Now.ToString("yyyy-MM-dd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ' تصدير البيانات حسب التبويب المختار
                If TabControl1.SelectedTabPage Is SummaryTab Then
                    SummaryGrid.ExportToXlsx(saveDialog.FileName)
                ElseIf TabControl1.SelectedTabPage Is DetailsTab Then
                    DetailsGrid.ExportToXlsx(saveDialog.FileName)
                End If

                XtraMessageBox.Show("تم تصدير التقرير بنجاح", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            XtraMessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

End Class
