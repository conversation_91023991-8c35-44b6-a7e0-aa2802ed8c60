﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="invoice_pos" RollPaper="true" Margins="0, 0, 0, 0" PaperKind="Custom" PageWidth="270" PageHeight="1100" ScriptLanguage="VisualBasic" Version="17.2" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="93.45831">
      <Controls>
        <Item1 Ref="6" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="169.136444,62.12497" LocationFloat="49.6656876, 0" />
        <Item2 Ref="7" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="266.999939,31.3333359" LocationFloat="0, 62.1249771" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="8" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item4>
    <Item5 Ref="9" ControlType="PageHeaderBand" Name="PageHeader" HeightF="118.428574">
      <Controls>
        <Item1 Ref="10" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="258.46347,14.45314" LocationFloat="8.536541, 85.49994" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="11" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="12" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="13" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleCenter" SizeF="84.82989,23.41665" LocationFloat="1.759999, 7.916674" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="14" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="15" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="16" ControlType="XRLabel" Name="XrLabel19" Text="فاتورة بيع" TextAlignment="MiddleCenter" SizeF="64.16669,31.33332" LocationFloat="94.81075, 0" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="17" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="18" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="64.16669,31.24999" LocationFloat="94.8108139, 31.33333" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="19" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="20" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="21" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="196.4531,22.916626" LocationFloat="0.2239529, 62.58332" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="22" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="23" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="24" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="70.32288,22.91664" LocationFloat="196.677048, 62.5833" Font="Droid Arabic Kufi, 8pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="25" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="26" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="91.18738,23.41665" LocationFloat="175.812592, 7.916677" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="27" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="28" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="29" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="267,17.06" LocationFloat="0, 101.368576" Font="Droid Arabic Kufi, 7pt" ForeColor="White" BackColor="Black" BorderColor="White">
          <Rows>
            <Item1 Ref="30" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
              <Cells>
                <Item1 Ref="31" ControlType="XRTableCell" Name="XrTableCell17" Weight="0.34842684818111214" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="32" UsePadding="false" />
                </Item1>
                <Item2 Ref="33" ControlType="XRTableCell" Name="XrTableCell33" Weight="0.30313313203298747" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="34" ControlType="XRTableCell" Name="XrTableCell34" Weight="0.2123364308083609" Text="الكمية" Font="Droid Arabic Kufi, 6.75pt, charSet=0" Padding="2,2,0,0,100">
                  <StylePriority Ref="35" UseFont="false" UsePadding="false" />
                </Item3>
                <Item4 Ref="36" ControlType="XRTableCell" Name="XrTableCell36" Weight="0.98377432283210875" Text="اسم الصنف" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100" BorderColor="White">
                  <StylePriority Ref="37" UseFont="false" />
                </Item4>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="38" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item8>
      </Controls>
    </Item5>
    <Item6 Ref="39" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="100.839737">
      <Controls>
        <Item1 Ref="40" ControlType="XRTable" Name="table1" AnchorVertical="Top" TextAlignment="MiddleCenter" SizeF="116.618721,34.82671" LocationFloat="0.223960876, 34.82671" Font="Droid Arabic Kufi, 7pt" Borders="All">
          <Rows>
            <Item1 Ref="41" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="42" ControlType="XRTableCell" Name="tableCell1" Weight="1" CanGrow="false" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100" Borders="Left, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="43" Expression="[pay]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="44" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="45" ControlType="XRTableCell" Name="tableCell2" Weight="1" CanGrow="false" Text="المدفوع" TextAlignment="MiddleRight" Padding="2,2,0,0,100" Borders="Left, Right, Bottom">
                  <StylePriority Ref="46" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="47" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="48" ControlType="XRTableCell" Name="tableCell3" Weight="1" CanGrow="false" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" BorderColor="Black" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="49" Expression="[new_balace]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="50" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="51" ControlType="XRTableCell" Name="tableCell4" Weight="1" CanGrow="false" Text="المتبقي" TextAlignment="MiddleRight" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" Borders="All">
                  <StylePriority Ref="52" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="53" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="54" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="156.3242,13.03909" LocationFloat="0.22395964, 87.8006439" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="55" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="56" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="156.3242,13.03906" LocationFloat="0.22395964, 74.76165" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="58" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleLeft" SizeF="84.58365,13.0390472" LocationFloat="156.54808, 74.76165" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="60" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleLeft" SizeF="84.58365,13.03909" LocationFloat="156.54808, 87.8006439" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="62" ControlType="XRTable" Name="XrTable7" AnchorVertical="Top" TextAlignment="MiddleCenter" SizeF="116.618721,34.82671" LocationFloat="0.22395964, 0" Font="Droid Arabic Kufi, 7pt" Borders="All">
          <Rows>
            <Item1 Ref="63" ControlType="XRTableRow" Name="XrTableRow9" Weight="1">
              <Cells>
                <Item1 Ref="64" ControlType="XRTableCell" Name="XrTableCell6" Weight="1" CanGrow="false" TextFormatString="{0:n2}" Text="XrTableCell12" TextAlignment="MiddleLeft" Padding="2,2,0,0,100" Borders="Left, Right, Bottom">
                  <Summary Ref="65" Running="Report" />
                  <ExpressionBindings>
                    <Item1 Ref="66" Expression="[item_count]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="67" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="68" ControlType="XRTableCell" Name="XrTableCell7" Weight="1" CanGrow="false" Text="عدد الاصناف" TextAlignment="MiddleRight" Padding="2,2,0,0,100" Borders="Left, Right, Bottom">
                  <StylePriority Ref="69" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item1>
            <Item2 Ref="70" ControlType="XRTableRow" Name="XrTableRow13" Weight="1">
              <Cells>
                <Item1 Ref="71" ControlType="XRTableCell" Name="XrTableCell10" Weight="1" CanGrow="false" TextFormatString="{0:n2}" Text="XrTableCell11" TextAlignment="MiddleLeft" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" BorderColor="Black" Borders="All">
                  <Summary Ref="72" Running="Report" />
                  <ExpressionBindings>
                    <Item1 Ref="73" Expression="[item_total]" PropertyName="Text" EventName="BeforePrint" />
                  </ExpressionBindings>
                  <StylePriority Ref="74" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="75" ControlType="XRTableCell" Name="XrTableCell15" Weight="1" CanGrow="false" Text="الاجمالي" TextAlignment="MiddleRight" ForeColor="Black" BackColor="White" Padding="2,2,0,0,100" Borders="All">
                  <StylePriority Ref="76" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
              </Cells>
            </Item2>
          </Rows>
          <StylePriority Ref="77" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
      </Controls>
    </Item6>
    <Item7 Ref="78" ControlType="PageFooterBand" Name="PageFooter" HeightF="16.07827">
      <Controls>
        <Item1 Ref="79" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleCenter" SizeF="192.6042,15.58078" LocationFloat="26.1979, 0.4974952" Font="Times New Roman, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item7>
    <Item8 Ref="81" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="82" ControlType="DetailBand" Name="Detail1" HeightF="17.06">
          <Controls>
            <Item1 Ref="83" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="267,17.06" LocationFloat="0.2239529, 0" Font="Droid Arabic Kufi, 7pt" ForeColor="Black" BackColor="White" BorderColor="Black">
              <Rows>
                <Item1 Ref="84" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
                  <Cells>
                    <Item1 Ref="85" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.34842684818111214" TextFormatString="{0:n2}" Text="الاجمالي" Padding="2,2,0,0,100" Borders="All">
                      <ExpressionBindings>
                        <Item1 Ref="86" Expression="[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="87" UsePadding="false" UseBorders="false" />
                    </Item1>
                    <Item2 Ref="88" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.30313313203298747" TextFormatString="{0:n2}" Text="السعر" Padding="2,2,0,0,100" Borders="All">
                      <ExpressionBindings>
                        <Item1 Ref="89" Expression="[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="90" UseBorders="false" />
                    </Item2>
                    <Item3 Ref="91" ControlType="XRTableCell" Name="XrTableCell3" Weight="0.21078670135609673" TextFormatString="{0:n2}" Text="الكمية" Padding="2,2,0,0,100" Borders="All">
                      <ExpressionBindings>
                        <Item1 Ref="92" Expression="[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="93" UsePadding="false" UseBorders="false" />
                    </Item3>
                    <Item4 Ref="94" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.985324052284373" Text="اسم الصنف" TextAlignment="MiddleRight" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100" BorderColor="Black" Borders="All">
                      <ExpressionBindings>
                        <Item1 Ref="95" Expression="[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="96" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="97" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>