Imports System.Data.SqlClient
Imports DevExpress.XtraEditors

Public Class TestAdvancedReports
    Inherits XtraForm

    Private btnTestReports As SimpleButton
    Private btnCreateSampleData As SimpleButton
    Private btnClearData As SimpleButton
    Private memoResults As DevExpress.XtraEditors.MemoEdit

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "اختبار التقارير المتقدمة لمراكز التكلفة"
        Me.Size = New Size(800, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.RightToLeft = RightToLeft.Yes

        ' زر اختبار التقارير
        btnTestReports = New SimpleButton()
        btnTestReports.Text = "اختبار التقارير المتقدمة"
        btnTestReports.Location = New Point(20, 20)
        btnTestReports.Size = New Size(200, 30)
        AddHandler btnTestReports.Click, AddressOf btnTestReports_Click
        Me.Controls.Add(btnTestReports)

        ' زر إنشاء بيانات تجريبية
        btnCreateSampleData = New SimpleButton()
        btnCreateSampleData.Text = "إنشاء بيانات تجريبية"
        btnCreateSampleData.Location = New Point(240, 20)
        btnCreateSampleData.Size = New Size(200, 30)
        AddHandler btnCreateSampleData.Click, AddressOf btnCreateSampleData_Click
        Me.Controls.Add(btnCreateSampleData)

        ' زر مسح البيانات
        btnClearData = New SimpleButton()
        btnClearData.Text = "مسح البيانات التجريبية"
        btnClearData.Location = New Point(460, 20)
        btnClearData.Size = New Size(200, 30)
        AddHandler btnClearData.Click, AddressOf btnClearData_Click
        Me.Controls.Add(btnClearData)

        ' منطقة النتائج
        memoResults = New DevExpress.XtraEditors.MemoEdit()
        memoResults.Location = New Point(20, 70)
        memoResults.Size = New Size(740, 500)
        memoResults.Properties.ReadOnly = True
        memoResults.Properties.ScrollBars = ScrollBars.Both
        Me.Controls.Add(memoResults)
    End Sub

    Private Sub btnTestReports_Click(sender As Object, e As EventArgs)
        Try
            memoResults.Text = "جاري اختبار التقارير المتقدمة..." & vbCrLf

            ' اختبار فتح التقارير
            Dim f As New AdvancedCostCenterReports()
            f.Show()

            memoResults.Text += "✅ تم فتح التقارير المتقدمة بنجاح!" & vbCrLf
            memoResults.Text += "📊 التقارير المتاحة:" & vbCrLf
            memoResults.Text += "   1. تحليل التكاليف حسب مركز التكلفة" & vbCrLf
            memoResults.Text += "   2. مقارنة موازنة مركز تكلفة مقابل الفعلي" & vbCrLf
            memoResults.Text += "   3. أرباح وخسائر على مستوى مركز تكلفة" & vbCrLf
            memoResults.Text += "   4. توزيع المصروفات حسب مراكز التكلفة" & vbCrLf & vbCrLf

            ' اختبار الاتصال بقاعدة البيانات
            TestDatabaseConnection()

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في اختبار التقارير: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub TestDatabaseConnection()
        Try
            memoResults.Text += "🔍 اختبار الاتصال بقاعدة البيانات..." & vbCrLf

            ' اختبار جدول مراكز التكلفة
            Dim costCentersCount As Integer = GetTableCount("cost_centers")
            memoResults.Text += $"📋 عدد مراكز التكلفة: {costCentersCount}" & vbCrLf

            ' اختبار جدول الفواتير
            Dim invoicesCount As Integer = GetTableCount("invoice_add")
            memoResults.Text += $"🧾 عدد فواتير المبيعات: {invoicesCount}" & vbCrLf

            ' اختبار جدول المصروفات
            Dim expensesCount As Integer = GetTableCount("Expenses_add")
            memoResults.Text += $"💰 عدد المصروفات: {expensesCount}" & vbCrLf

            ' اختبار جدول الموازنة
            Dim budgetCount As Integer = GetTableCount("cost_center_budget")
            memoResults.Text += $"📊 عدد بنود الموازنة: {budgetCount}" & vbCrLf & vbCrLf

            If costCentersCount = 0 Then
                memoResults.Text += "⚠️ تحذير: لا توجد مراكز تكلفة. استخدم زر 'إنشاء بيانات تجريبية'" & vbCrLf
            End If

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في اختبار قاعدة البيانات: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Function GetTableCount(tableName As String) As Integer
        Try
            Dim sql As String = $"SELECT COUNT(*) FROM {tableName}"
            Using cmd As New SqlCommand(sql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                Return Convert.ToInt32(cmd.ExecuteScalar())
            End Using
        Catch
            Return 0
        End Try
    End Function

    Private Sub btnCreateSampleData_Click(sender As Object, e As EventArgs)
        Try
            memoResults.Text = "جاري إنشاء بيانات تجريبية..." & vbCrLf

            ' إنشاء مراكز التكلفة التجريبية
            CreateSampleCostCenters()

            ' إنشاء بيانات الموازنة
            CreateSampleBudget()

            ' إنشاء فواتير تجريبية
            CreateSampleInvoices()

            ' إنشاء مصروفات تجريبية
            CreateSampleExpenses()

            memoResults.Text += "✅ تم إنشاء البيانات التجريبية بنجاح!" & vbCrLf
            memoResults.Text += "📊 يمكنك الآن اختبار التقارير المتقدمة" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في إنشاء البيانات التجريبية: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub CreateSampleCostCenters()
        Try
            ' إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
            Dim createTableSql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_centers' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_centers (
                        cost_center_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_code NVARCHAR(20) NOT NULL,
                        cost_center_name NVARCHAR(100) NOT NULL,
                        description NVARCHAR(255),
                        is_active BIT DEFAULT 1,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                END"

            Using cmd As New SqlCommand(createTableSql, sqlconn)
                If sqlconn.State = ConnectionState.Closed Then
                    sqlconn.Open()
                End If
                cmd.ExecuteNonQuery()
            End Using

            ' إدراج مراكز تكلفة تجريبية
            Dim insertSql As String = "
                IF NOT EXISTS (SELECT * FROM cost_centers WHERE cost_center_code = 'CC001')
                BEGIN
                    INSERT INTO cost_centers (cost_center_code, cost_center_name, description) VALUES
                    ('CC001', 'فرع الرئيسي', 'المكتب الرئيسي للشركة'),
                    ('CC002', 'فرع الشمال', 'فرع المنطقة الشمالية'),
                    ('CC003', 'فرع الجنوب', 'فرع المنطقة الجنوبية'),
                    ('CC004', 'قسم التسويق', 'قسم التسويق والمبيعات'),
                    ('CC005', 'قسم الإنتاج', 'قسم الإنتاج والتصنيع');
                END"

            Using cmd As New SqlCommand(insertSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم إنشاء مراكز التكلفة التجريبية" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في إنشاء مراكز التكلفة: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub CreateSampleBudget()
        Try
            ' إنشاء جدول الموازنة
            Dim createBudgetSql As String = "
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cost_center_budget' AND xtype='U')
                BEGIN
                    CREATE TABLE cost_center_budget (
                        budget_id INT IDENTITY(1,1) PRIMARY KEY,
                        cost_center_id INT NOT NULL,
                        budget_year INT NOT NULL,
                        budget_amount DECIMAL(18,2) NOT NULL DEFAULT 0,
                        created_date DATETIME DEFAULT GETDATE()
                    );
                END"

            Using cmd As New SqlCommand(createBudgetSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            ' إدراج بيانات الموازنة
            Dim insertBudgetSql As String = "
                DELETE FROM cost_center_budget WHERE budget_year = YEAR(GETDATE());
                
                INSERT INTO cost_center_budget (cost_center_id, budget_year, budget_amount)
                SELECT cost_center_id, YEAR(GETDATE()), 
                    CASE cost_center_id 
                        WHEN 1 THEN 100000
                        WHEN 2 THEN 75000
                        WHEN 3 THEN 80000
                        WHEN 4 THEN 50000
                        WHEN 5 THEN 120000
                        ELSE 60000
                    END
                FROM cost_centers WHERE is_active = 1;"

            Using cmd As New SqlCommand(insertBudgetSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم إنشاء بيانات الموازنة التجريبية" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في إنشاء الموازنة: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub CreateSampleInvoices()
        Try
            ' التحقق من وجود عمود cost_center_id في جدول الفواتير
            Dim checkColumnSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = 'invoice_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE invoice_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(checkColumnSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم تحديث جدول الفواتير" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "⚠️ تحذير في تحديث الفواتير: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub CreateSampleExpenses()
        Try
            ' التحقق من وجود عمود cost_center_id في جدول المصروفات
            Dim checkColumnSql As String = "
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = 'Expenses_add' AND COLUMN_NAME = 'cost_center_id')
                BEGIN
                    ALTER TABLE Expenses_add ADD cost_center_id INT;
                END"

            Using cmd As New SqlCommand(checkColumnSql, sqlconn)
                cmd.ExecuteNonQuery()
            End Using

            memoResults.Text += "✅ تم تحديث جدول المصروفات" & vbCrLf

        Catch ex As Exception
            memoResults.Text += "⚠️ تحذير في تحديث المصروفات: " & ex.Message & vbCrLf
        End Try
    End Sub

    Private Sub btnClearData_Click(sender As Object, e As EventArgs)
        Try
            If XtraMessageBox.Show("هل تريد مسح جميع البيانات التجريبية؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                
                memoResults.Text = "جاري مسح البيانات التجريبية..." & vbCrLf

                ' مسح بيانات الموازنة
                Dim deleteBudgetSql As String = "DELETE FROM cost_center_budget WHERE budget_year = YEAR(GETDATE())"
                Using cmd As New SqlCommand(deleteBudgetSql, sqlconn)
                    If sqlconn.State = ConnectionState.Closed Then
                        sqlconn.Open()
                    End If
                    cmd.ExecuteNonQuery()
                End Using

                ' مسح مراكز التكلفة التجريبية
                Dim deleteCostCentersSql As String = "DELETE FROM cost_centers WHERE cost_center_code LIKE 'CC%'"
                Using cmd As New SqlCommand(deleteCostCentersSql, sqlconn)
                    cmd.ExecuteNonQuery()
                End Using

                memoResults.Text += "✅ تم مسح البيانات التجريبية بنجاح!" & vbCrLf
            End If

        Catch ex As Exception
            memoResults.Text += "❌ خطأ في مسح البيانات: " & ex.Message & vbCrLf
        End Try
    End Sub
End Class
