﻿Imports System.Management
Imports DevExpress.XtraBars
Imports DevExpress.XtraTabbedMdi
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Data.SqlClient
Imports System.IO
Imports System.Threading
Imports System.Net
Imports Security.FingerPrint
Imports System.Data.OleDb
Imports MicroVisionSerial.Encryption.Activation

Public Class XtraForm1
    Private rng As New Random
    Dim cmd As New SqlCommand
    Private sqlconn2 As New SqlConnection
    Private dtt As DataTable
    Dim web As New WebClient
    'Dim ConStr As String = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=|DataDirectory|\setting.mdb;Jet OLEDB:Database Password=spaxet2020"
    Dim Conn As New OleDbConnection(ConStr)
    Public Sub New()
        InitializeComponent()
        ' Thread.CurrentThread.CurrentCulture = New System.Globalization.CultureInfo("ar")
        Thread.CurrentThread.CurrentUICulture = New System.Globalization.CultureInfo("ar")
        '            #End Region ' #frlocalize
    End Sub
    Private Sub حركةالعملاءToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a13.Click
        If m13.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        cus_trans_show.ShowDialog()
    End Sub

    Private Sub سندأضافةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a18.Click
        If m18.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As tax_customer = New tax_customer()
        f.Text = "سند اضافة"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub سندخصمToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a17.Click
        If m17.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As descound_customer = New descound_customer()
        f.Text = "سند خصم"
        f.MdiParent = Me
        f.Show()
        f.customername.Focus()
    End Sub
    Private Sub شاشةالموردينToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a22.Click
        If m26.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As importer_show = New importer_show()
        f.Text = "فهرس الموردين"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub جردالاصنافToolStripMenuItem_Click(sender As Object, e As EventArgs)
        Dim f As item_show = New item_show()

        f.Text = "الرأيسية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub اتلافالاصنافToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a42.Click
        If m49.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As damage_item = New damage_item()
        f.Text = "اتلاف اصناف "
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p28_Click(sender As Object, e As EventArgs)
        Dim f As users = New users()

        f.Text = "الرأيسية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub p29_Click(sender As Object, e As EventArgs)
        Dim f As backup = New backup()

        f.Text = "الرأيسية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub اعدادالاتصالToolStripMenuItem_Click(sender As Object, e As EventArgs)
        Dim f As frmdatabase = New frmdatabase()

        f.Text = "الرأيسية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub اعدادالفاتورةToolStripMenuItem_Click(sender As Object, e As EventArgs)
        Dim f As setting_fatora = New setting_fatora()

        f.Text = "الرئيسية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub XtraForm1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        الرأيسيةToolStripMenuItem_Click(Nothing, Nothing)
        Try
            dtt = New DataTable
            dtt.Columns.Add("اللإشعارات")
            dgv.DataSource = dtt
        Catch ex As Exception

        End Try

        start_bac()
        fill_tax()
 
    End Sub
    Sub start_bac()
        Try
            If My.Settings.f1 = True Then
                Timer3.Start()
                Timer3.Interval = Val(My.Settings.f2) * 60000
            Else
                Timer3.Stop()
            End If
        Catch ex As Exception

        End Try
       
    End Sub
    Sub fill_p1()
        Try
            Dim adp As New SqlDataAdapter("select (cusname),(Accounts_balace),(CusCreditlimit) from [dbo].[customer] where [Accounts_balace] >= [CusCreditlimit]", sqlconn)

            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            dgv_p1.AutoGenerateColumns = False
            dgv_p1.DataSource = dt
        Catch ex As Exception
        End Try

    End Sub
    Sub fill_p2()
        Dim adp As New SqlDataAdapter("select (cusname),(Accounts_balace) from  customer where Accounts_balace >=N'" & (p11.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_p2.AutoGenerateColumns = False
        dgv_p2.DataSource = dt
    End Sub
    Sub fill_p3()

        Dim adp As New SqlDataAdapter("select * from  importer where Accounts_balace <=N'" & (Val(p12.Text)) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_p3.AutoGenerateColumns = False
        dgv_p3.DataSource = dt
    End Sub
    Sub fill_p4()

        Dim adp As New SqlDataAdapter("select (total_Expenses) from Expenses_add  where store=N'" & (store.Text) & "' and Expenses_date>='" & Format(DateAdd("m", -1, Now.Date), "yyy/MM/dd") & "'  and Expenses_date<='" & Format(Now.Date, "yyy/MM/dd") & "' order by Expenses_date", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        GridControl1.DataSource = dt
    End Sub
    Sub fill_p5()

        Dim adp As New SqlDataAdapter("select (balace),(treasury_name) from treasury_name where balace < 0", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_p4.AutoGenerateColumns = False
        dgv_p4.DataSource = dt
    End Sub
    Sub fill_p6()

        Dim adp As New SqlDataAdapter("select (Accounts_balace),(cusname) from  customer where Accounts_balace < 0", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_p5.AutoGenerateColumns = False
        dgv_p5.DataSource = dt
    End Sub
    Sub fill_p7()

        Dim adp As New SqlDataAdapter("select (item_count),(item_name) from expire_item where item_date<='" & (Now.Date) & "' and item_count>N'" & 0 & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_p6.AutoGenerateColumns = False
        dgv_p6.DataSource = dt
    End Sub

    Sub fill_tax()
        Try
            Dim sql = "select * from tax where id=N'" & (1) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            If dr!p1 = True Then
                p2.Checked = dr!p2
                p3.Checked = dr!p3
                p4.Checked = dr!p4
                p5.Checked = dr!p5
                p6.Checked = dr!p6
                p7.Checked = dr!p7
                p8.Checked = dr!p8
                p10.Text = dr!p10
                p11.Text = dr!p11
                p12.Text = dr!p12
                p13.Text = dr!p13
                p12.Text = Val(p12.Text) * -1
                Timer2.Start()
                Timer2.Interval = Val(p10.Text) * 60000
            Else
                Timer2.Stop()
                Exit Sub
            End If
          

          

        Catch ex As Exception

        End Try
     
    End Sub


    Sub fill_printer_dize()
        Try
            Dim sql = "select * from ptinter_pro where windows_name='" & (System.Windows.Forms.SystemInformation.ComputerName) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            If dr!print_directry = True Then
                RadioButton2.Checked = True
            Else
                RadioButton1.Checked = True
            End If
            printer_name.Text = dr!printer_name
            report_size.Text = dr!report_size
            report_size_2.Text = dr!report_size_2
            printer_defult.Text = dr!printer_defult
            printer_defult_2.Text = dr!printer_defult_2
            printer1.Text = dr!printer1
            printer2.Text = dr!printer2
            printer3.Text = dr!printer3
            printer4.Text = dr!printer4
            printer5.Text = dr!printer5
            printer6.Text = dr!printer6

            printer12.Text = dr!printer12
            printer22.Text = dr!printer22
            printer32.Text = dr!printer32
            printer42.Text = dr!printer42
            printer52.Text = dr!printer52
            printer62.Text = dr!printer62
            paper1.Text = dr!paper1
            paper2.Text = dr!paper2
        Catch ex As Exception

        End Try

    End Sub

    Sub show_set_fator()
        Try
            Dim sql = "select * from inv_setting where code=N'" & (1) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            w1.Text = dr!w1
            w2.Text = dr!w2
            w3.Text = dr!w3
            w4.Text = dr!w4
            w5.Text = dr!w5
            w6.Text = dr!w6
            w7.Text = dr!w7
            w8.Text = dr!w8
            w9.Text = dr!w9
            w10.Text = dr!w10
            w11.Text = dr!w11
            w12.Text = dr!w12
            w13.Text = dr!w13
            w14.Text = dr!w14
            w15.Text = dr!w15
            w16.Text = dr!w16
            w17.Text = dr!w17
            w18.Text = dr!w18
            w19.Text = dr!w19
            d20.Text = dr!w20
            d21.Text = dr!w21
            d22.Text = dr!w22

            curn1.Text = dr!w23
            curn2.Text = dr!w24
            w_desc.Text = dr!w_desc
            w_tax.Text = dr!w_tax
            sale_buy.Text = dr!sale_buy
            sale_buy2.Text = dr!sale_buy2
            tax_invo.Text = dr!tax_invo
            tax_pur.Text = dr!tax_pur

            balance_check.Text = dr!balance_check
            balance_w1.Text = dr!balance_w1
            balance_w2.Text = dr!balance_w2
        Catch ex As Exception

        End Try
      

    End Sub
    Private Sub الرأيسيةToolStripMenuItem_Click(sender As Object, e As EventArgs)
        Dim f As main_page = New main_page()
        f.Text = "الرئيسية"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs)
        End
    End Sub

    Private Sub اعدادتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a1.Click
        If m1.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As setting_fatora = New setting_fatora()
        f.Text = "اعدادات "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub سندأضافةToolStripMenuItem_Click_1(sender As Object, e As EventArgs) Handles a21.Click
        If m21.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As tax_show = New tax_show()
        f.Text = "  مراجعة الاضافة "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub مراجعةالتوالفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a46.Click
        If m53.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As damage_show = New damage_show()
        f.Text = "  مراجعة التوالف "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub p13_Click(sender As Object, e As EventArgs) Handles a43.Click
        If m50.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Drawings_item = New Drawings_item()
        f.Text = "المسحوبات"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub تسويةجرديةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a41.Click

        Dim f As inventory_item = New inventory_item()
        f.Text = "  تسوية جردية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub مراجعةالتسوياتالجرديةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a45.Click
        If m52.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As inventory_show = New inventory_show()
        f.Text = "  مراجعة التسويات الجردية "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub تحويلمخازنToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a40.Click
        If m48.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As trans_item = New trans_item()
        f.Text = "  تحويلات مخازن "
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub مراجعةالتحويلاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a44.Click
        If m51.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As trans_show = New trans_show()
        f.Text = "  مراجعة التحويلات"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub p22_Click(sender As Object, e As EventArgs) Handles a48.Click
        If m60.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        Dim f As Purchases_add = New Purchases_add()
        f.Text = "  فاتورة مشتريات"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub المشترياتToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles a50.Click
        If m62.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_show = New Purchases_show()
        f.Text = "  مراجعة المشتريات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p24_Click(sender As Object, e As EventArgs) Handles a52.Click
        If m68.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_add = New invoice_add()
        f.Text = "فاتورة مبيعات"
        f.MdiParent = Me
        f.Show()
        f.TextEdit1.Focus()
    End Sub

    Private Sub المبيعاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a54.Click
        If m70.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_show = New invoice_show()
        f.Text = "  مراجعة المبيعات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p23_Click(sender As Object, e As EventArgs) Handles a49.Click
        If m61.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_back = New Purchases_back()
        f.Text = "  مرتجع مشتريات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub مرتجعالمشترياتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a51.Click
        If m63.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_back_show = New Purchases_back_show()
        f.Text = "  مراجعة مرتجع المبيعات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p25_Click(sender As Object, e As EventArgs) Handles a53.Click
        If m69.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        item_bac.ShowDialog()
    End Sub

    Private Sub مرتجعالمبيعاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a55.Click
        If m71.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_back_show = New invoice_back_show()
        f.Text = "مرتجع المبيعات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p26_Click(sender As Object, e As EventArgs) Handles a56.Click
        If m77.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Treasury = New Treasury()
        f.Text = "الخزينة"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p1_Click(sender As Object, e As EventArgs) Handles a8.Click
        If m8.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_show = New customer_show()
        f.Text = "فهرس العملاء"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub سندخصمToolStripMenuItem_Click_1(sender As Object, e As EventArgs) Handles a20.Click
        If m20.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As discound_show = New discound_show()
        f.Text = "  مراجعة الخصم "
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub كشفتفصيليToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a14.Click
        If m14.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_Detailed = New customer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub احصائياتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a15.Click

        If m15.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Statistic_customer = New Statistic_customer()
        f.Text = "احصائيات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub p7_Click(sender As Object, e As EventArgs) Handles a23.Click
        If m27.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
       imp_trans_show.ShowDialog()
    End Sub

    Private Sub p9_Click(sender As Object, e As EventArgs) Handles a26.Click
        If m30.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As descound_importer = New descound_importer()
        f.Text = "سند خصم"
        f.MdiParent = Me
        f.Show()
        f.customername.Focus()
    End Sub
    Private Sub سندخصمToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles a29.Click
        If m33.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As discound_show_imp = New discound_show_imp()
        f.Text = "مراجعة الخصم"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub p10_Click(sender As Object, e As EventArgs) Handles a27.Click
        If m31.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As tax_importer = New tax_importer()
        f.Text = "سند اضافة"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub سندأضافةToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles a30.Click
        If m34.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As taximp_show = New taximp_show()
        f.Text = "مراجعة الاضافة"""
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub حركةالاصنافToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a37.Click
 item_trans_show.ShowDialog()

    End Sub
    Private Sub بياناتالمخازنToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a2.Click
        If m2.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        set_store.Show()
    End Sub

    Private Sub بياناتالخزائنToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a3.Click
        If m3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Treasury_add.Show()

    End Sub
    Private Sub Button1_Click(sender As Object, e As EventArgs)
        If XtraMessageBox.Show("هل تريد الخروج من البرنامج", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then

            End
        End If
    End Sub
    Private Sub Button4_Click(sender As Object, e As EventArgs)
        System.Diagnostics.Process.Start("CALC")
    End Sub
    Private Sub الاصنافالبديلةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a36.Click
        If m44.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Alternative_item = New Alternative_item()
        f.Text = "الاصناف البديلة"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub ToolStripMenuItem41_Click(sender As Object, e As EventArgs) Handles a47.Click
        Dim f As Drawings_show = New Drawings_show()
        f.Text = "مراجعة المسحوبات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub كشفتفصيليToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles a38.Click
        If m46.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_Detailed = New item_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub ToolStripMenuItem31_Click(sender As Object, e As EventArgs) Handles a39.Click
        If m47.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Statistic_item = New Statistic_item()
        f.Text = " احصائيات صنف"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub المندوبينToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a9.Click
        If m9.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        delegte.Show()
    End Sub
    Private Sub المصروفاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a57.Click
        If m78.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_add = New Expenses_add()
        f.Text = "المصروفان"
        f.MdiParent = Me
        f.Show()
    End Sub
    Private Sub التقاريرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a60.Click
        Dim f As report_show = New report_show()
        f.Text = "التقارير"
        f.MdiParent = Me
        f.Show()

    End Sub

    Private Sub المجموعاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a10.Click
        If m10.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        group.Show()
    End Sub

    Private Sub المدنToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a11.Click
        If m11.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        city.Show()
    End Sub

    Private Sub المحافظاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a12.Click
        If m12.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Governorate.Show()
    End Sub

    Private Sub نسبةالمندوبينToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a58.Click
        If m79.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As dele_money = New dele_money()
        f.Text = "نسبة المندوبين"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub كشفتفصيليToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles a24.Click
        If m28.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As importer_Detailed = New importer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = Me
        f.Show()
        f.importername.Focus()
    End Sub
    Private Sub XtraForm1_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If XtraMessageBox.Show("هل تريد الخروج من البرنامج", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.Yes Then
            e.Cancel = True
        Else
            End
            Application.Exit()
        End If
    End Sub


    Private Sub النسخالاToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a6.Click
        If m6.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        backup.Show()
    End Sub

    Private Sub استرجاعنسخةاحتياطةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a7.Click
        If m7.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        recovey.Show()
    End Sub

    Private Sub المستخدمينToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles a4.Click
        If m4.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        users.Show()
    End Sub
    Private Sub التصنيفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a32.Click
        If m40.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Category.Show()
    End Sub
    Private Sub الشركاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a34.Click
        If m42.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        company.Show()
    End Sub

    Private Sub المجموعةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles a35.Click
        If m43.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        contact.Show()
    End Sub

    Private Sub المصروفاتToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles a59.Click
        If m80.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_show = New Expenses_show()
        f.Text = "مراجعة المصروفات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub a5_Click(sender As Object, e As EventArgs) Handles a5.Click
        If m5.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Power_user.Show()
    End Sub

    Private Sub a31_Click(sender As Object, e As EventArgs) Handles a31.Click
        If m39.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_show = New item_show()
        f.Text = "فهرس الاصناف "
        f.MdiParent = Me
        f.Show()
    End Sub



    Private Sub اتصالقاعدةالبياناتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles اتصالقاعدةالبياناتToolStripMenuItem.Click
        frmdatabase.Show()
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs)
        help_system.Show()
    End Sub
    Private Sub XtraForm1_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown

        If e.Control = True And e.KeyCode = Keys.I Then
            شاشةالموردينToolStripMenuItem_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.Control = True And e.KeyCode = Keys.E Then
            p1_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.Control = True And e.KeyCode = Keys.W Then
            p22_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.Control = True And e.KeyCode = Keys.H Then
            p23_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.Control = True And e.KeyCode = Keys.Q Then
            p24_Click(Nothing, Nothing)
            Exit Sub
        End If
        If e.Control = True And e.KeyCode = Keys.M Then
            p25_Click(Nothing, Nothing)
            Exit Sub
        End If
    End Sub


    Private Sub بيانأسعارToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u17.Click
        If o17.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As suplier_add = New suplier_add()
        f.Text = "عرض أسعار"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub بيانأسعارToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles u19.Click
        If o19.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As suplier_show = New suplier_show()
        f.Text = "مراجعة بيان أسعار"
        f.MdiParent = Me
        f.Show()
    End Sub
 
    Private Sub أضافةموظفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u25.Click
        If w25.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Employee_show = New Employee_show()
        f.Text = "فهري الموظفين"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub الادارةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles الادارةToolStripMenuItem.Click
        emp_manged.ShowDialog()
    End Sub

    Private Sub المرتباتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles المرتباتToolStripMenuItem.Click
        If w26.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_emp = New cashing_emp()
        f.Text = "المرتبات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub الوردياتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u28.Click
        If w28.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Rosacea.ShowDialog()
    End Sub

    Private Sub السلفToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u29.Click
        If w29.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As ancestor_show1 = New ancestor_show1()
        f.Text = "السلف"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub HglhToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u30.Click
        If w30.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Rewards_show = New Rewards_show()
        f.Text = "المكافأة"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub خصوماتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u31.Click
        If w31.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Discounts_show = New Discounts_show()
        f.Text = "خصومات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub الاجازاتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u32.Click
        If w32.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As holiday_show = New holiday_show()
        f.Text = "الاجازات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub القسمToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles القسمToolStripMenuItem.Click
        emp_Section.ShowDialog()
    End Sub

    Private Sub الوظيفةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles الوظيفةToolStripMenuItem.Click
        emp_Functioned.ShowDialog()
    End Sub

    Private Sub تسجيلToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u27.Click
        If w27.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Attending_leaving.ShowDialog()
    End Sub

    Private Sub الحضوروالانصرافToolStripMenuItem1_Click(sender As Object, e As EventArgs)
        Attending_show.Show()
    End Sub

    Private Sub ToolStripMenuItem3_Click(sender As Object, e As EventArgs)
        cashing_emp_show.Show()
    End Sub

    Private Sub كشفحسابToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles كشفحسابToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "\cus_trnas_print_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)

    End Sub

    Private Sub سندخصمToolStripMenuItem_Click_2(sender As Object, e As EventArgs) Handles سندخصمToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/desc_cus_s4vb.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub سنداضافةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles سنداضافةToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/cus_trnas_print_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub كشفحسابToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles كشفحسابToolStripMenuItem1.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/imp_trans_print_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub سندخصمToolStripMenuItem1_Click_1(sender As Object, e As EventArgs) Handles سندخصمToolStripMenuItem1.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/desc_imp_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub سنداضافةToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles سنداضافةToolStripMenuItem1.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/tax_imp_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub حركةالاصنافToolStripMenuItem_Click_1(sender As Object, e As EventArgs) Handles حركةالاصنافToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/item_trnas_print_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub تحويلاتمباشرةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تحويلاتمباشرةToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/trans_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub تسويةجرديةToolStripMenuItem_Click_1(sender As Object, e As EventArgs) Handles تسويةجرديةToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/inventory_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub إذنالهالكToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles إذنالهالكToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/damage_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub مسحوباتشخصيةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles مسحوباتشخصيةToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/Drawings_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub فاتورةمشترياتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles فاتورةمشترياتToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/Purchases_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub
    Private Sub مرتجعمشترياتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles مرتجعمشترياتToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/Purchases_back_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل1ToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل2ToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem2.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_a4_2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل1ToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem1.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_a5.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل2ToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem1.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_a5_2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل1ToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem2.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_pos.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل2ToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_pos_2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل1ToolStripMenuItem9_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem9.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/invoice_back_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub المصروفاتToolStripMenuItem_Click_1(sender As Object, e As EventArgs) Handles المصروفاتToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/Expense_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub تحصيلالفواتيرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تحصيلالفواتيرToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/cashing_cus_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub سدادالفواتيرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles سدادالفواتيرToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/cashing_imp_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub الاصنافToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles الاصنافToolStripMenuItem.Click
        If o2.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        item_export.Show()
    End Sub

    Private Sub تحصيلفواتيرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تحصيلفواتيرToolStripMenuItem.Click
        If m16.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_customer = New cashing_customer()
        f.Text = "تحصيل الفواتير"
        f.MdiParent = Me
        f.Show()
        f.Accounts_name.Focus()
    End Sub

    Private Sub سدادفواتيرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles سدادفواتيرToolStripMenuItem.Click
        If m29.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_importer = New cashing_importer()
        f.Text = "سداد الفواتير"
        f.MdiParent = Me
        f.Show()
        f.Accounts_name.Focus()
    End Sub

    Private Sub تحصيلالفواتيرToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles تحصيلالفواتيرToolStripMenuItem1.Click
        If m19.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_show = New cashing_show()
        f.Text = "مراجعة الفبض "
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub سدادالفواتيرToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles سدادالفواتيرToolStripMenuItem1.Click
        If m19.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_imp_show = New cashing_imp_show()
        f.Text = "مراجعة سداد الفواتير "
        f.MdiParent = Me
        f.Show()
    End Sub


    Private Sub حجزفاتورةToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles u16.Click
        If o16.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Reservation_add = New Reservation_add()
        f.Text = "حجز فاتورة"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub طلبيةمشترياتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u14.Click
        If o14.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If

        Dim f As Purchases_order = New Purchases_order()
        f.Text = "طلبية مشتريات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub طلبيةشراءToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u15.Click
        If o15.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As order_show = New order_show()
        f.Text = "مراجعة طلبيات الشراء"
        f.MdiParent = Me
        f.Show()
    End Sub


    Private Sub حجزفاتورةToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles u18.Click
        If o18.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Reservation_show = New Reservation_show()
        f.Text = "مراجعة حجز الفاتورة"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub العملاءToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles العملاءToolStripMenuItem2.Click
        If o2.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        customer_export.Show()
    End Sub

    Private Sub زيارةالعملاءToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u10.Click
        If o10.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        cus_visitor.Show()
    End Sub

    Private Sub تواريخالصلاحيةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u12.Click
        If o12.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        expire_item.Show()
    End Sub

    Private Sub الارقامالتسلسيةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u13.Click
        If o13.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        serial_item.Show()
    End Sub

    Private Sub حذفقاعدةالبياناتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u1.Click
        If o1.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        format_data.ShowDialog()
    End Sub

    Private Sub تحويلنقديةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles تحويلنقديةToolStripMenuItem.Click
        If m38.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        trans_money.ShowDialog()
    End Sub

    Private Sub الايرداتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u21.Click
        If w21.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Revenues_add = New Revenues_add()
        f.Text = "الايردادت"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub الضرائبToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u22.Click
        If w22.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        pay_tax.Show()
    End Sub

    Private Sub الايردادتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u50.Click
        If w21.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Revenues_show = New Revenues_show()
        f.Text = "مراجعة الايرادات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub الموردينToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles الموردينToolStripMenuItem2.Click
        If o2.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        importer_export.Show()
    End Sub

    Private Sub المرتباتToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles u34.Click

        If w34.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_emp_show = New cashing_emp_show()
        f.Text = "مراجعة المرتبات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub طباعةباركودToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles طباعةباركودToolStripMenuItem.Click
        Dim f As frmbarcode = New frmbarcode()
        f.Text = "طباعة باركود"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub مراجعةToolStripMenuItem3_Click(sender As Object, e As EventArgs) Handles u33.Click
        If w33.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Attending_show = New Attending_show()
        f.Text = "مراجعة الحضور"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub نسبةالمهندسينToolStripMenuItem_Click(sender As Object, e As EventArgs)

    End Sub

    Private Sub مصروفاتتأسيسيةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles u20.Click
        If w20.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_add22 = New Expenses_add22()
        f.Text = "المصروفات التأسيسية"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub u23_Click(sender As Object, e As EventArgs) Handles u23.Click
        If w23.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_show2 = New Expenses_show2()
        f.Text = "مراجعة مصورفات تأسيسية"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub u24_Click(sender As Object, e As EventArgs) Handles u24.Click

    End Sub

    Private Sub BarButtonItem1_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem1.ItemClick
        If m70.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_show = New invoice_show()
        f.Text = "  مراجعة المبيعات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub BarButtonItem2_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem2.ItemClick
        If m71.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As invoice_back_show = New invoice_back_show()
        f.Text = "مرتجع المبيعات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub BarButtonItem3_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem3.ItemClick
        If m62.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_show = New Purchases_show()
        f.Text = "  مراجعة المشتريات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub BarButtonItem4_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem4.ItemClick
        If m63.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Purchases_back_show = New Purchases_back_show()
        f.Text = "  مراجعة مرتجع المبيعات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub BarButtonItem5_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem5.ItemClick
          If m13.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        cus_trans_show.ShowDialog()
    End Sub

    Private Sub BarButtonItem6_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem6.ItemClick
        If m27.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
       imp_trans_show.ShowDialog()
    End Sub

    Private Sub BarButtonItem7_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem7.ItemClick
        If m80.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Expenses_show = New Expenses_show()
        f.Text = "مراجعة المصروفات"
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub BarButtonItem8_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem8.ItemClick
        If o19.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As suplier_show = New suplier_show()
        f.Text = "مراجعة بيان أسعار"
        f.MdiParent = Me
        f.Show()
    End Sub


    Private Sub فاتورةمشترياتCtrlpToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles فاتورةمشترياتCtrlpToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/Purchases_order_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub
    Private Sub باركودالاصنافToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles باركودالاصنافToolStripMenuItem.Click
        barcode_export.Show()
    End Sub

    Private Sub الاصولالثابتهToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles الاصولالثابتهToolStripMenuItem.Click
        Destruction_ethol.Show()
    End Sub

    Private Sub اهلاكالاصولToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles اهلاكالاصولToolStripMenuItem.Click
        Fixed_assets.Show()
    End Sub

    Private Sub شكل1ToolStripMenuItem3_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem3.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/suplier_a4.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل2ToolStripMenuItem3_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem3.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/suplier_a4_2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل1ToolStripMenuItem4_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem4.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/suplier_a5.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل2ToolStripMenuItem4_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem4.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/suplier_a5_2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل1ToolStripMenuItem5_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem5.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/suplier_pos.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub شكل2ToolStripMenuItem5_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem5.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/suplier_pos_2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub


    Private Sub Timer2_Tick(sender As Object, e As EventArgs) Handles Timer2.Tick

        If p2.Checked = True Then
            fill_p1()
        End If
        If p3.Checked = True Then

            fill_p2()
        End If
        If p4.Checked = True Then
            fill_p3()
        End If
        If p5.Checked = True Then
            fill_p4()
        End If
        If p6.Checked = True Then
            fill_p5()
        End If
        If p7.Checked = True Then
            fill_p6()
        End If
        If p8.Checked = True Then
            fill_p7()
        End If

        Try
            dtt.Clear()
        Catch ex As Exception

        End Try
        If dgv_p1.RowCount > 0 Then
            For i = 0 To dgv_p1.Rows.Count - 1
                Dim dr As DataRow = dtt.NewRow()
                dr(0) = "العميل " + dgv_p1.Rows(i).Cells(0).Value + " تخطي الحد الائتماني"
                dtt.Rows.Add(dr)
                GridControl2.DataSource = dtt
            Next
        End If
        If dgv_p2.RowCount > 0 Then

            For i = 0 To dgv_p2.Rows.Count - 1
                Dim dr As DataRow = dtt.NewRow()
                dr(0) = "رصيد العميل " + dgv_p2.Rows(i).Cells(0).Value + " تخطي " + p11.Text
                dtt.Rows.Add(dr)
                GridControl2.DataSource = dtt
            Next
        End If
        If dgv_p3.RowCount > 0 Then

            For i = 0 To dgv_p3.Rows.Count - 1
                Dim dr As DataRow = dtt.NewRow()
                dr(0) = "رصيد المورد " + dgv_p3.Rows(i).Cells(0).Value + " تخطي " + p12.Text
                dtt.Rows.Add(dr)
                GridControl2.DataSource = dtt
            Next
        End If
        If p13.Text < GridView1.Columns("total_Expenses").SummaryItem.SummaryValue Then
            Dim dr As DataRow = dtt.NewRow()
            dr(0) = "مصروفات الشهر تخطت الحد الادني"
            dtt.Rows.Add(dr)
            GridControl2.DataSource = dtt
        End If
        If dgv_p4.RowCount > 0 Then


            For i = 0 To dgv_p4.Rows.Count - 1
                Dim dr As DataRow = dtt.NewRow()
                dr(0) = "رصيد الخزينة " + dgv_p4.Rows(i).Cells(0).Value + " بالسالب "
                dtt.Rows.Add(dr)
                GridControl2.DataSource = dtt
            Next
        End If
        If dgv_p5.RowCount > 0 Then

            For i = 0 To dgv_p5.Rows.Count - 1
                Dim dr As DataRow = dtt.NewRow()
                dr(0) = "رصيد العميل" + dgv_p5.Rows(i).Cells(0).Value + " بالسالب "
                dtt.Rows.Add(dr)
                GridControl2.DataSource = dtt
            Next
        End If
        If dgv_p6.RowCount > 0 Then

            For i = 0 To dgv_p6.Rows.Count - 1
                Dim dr As DataRow = dtt.NewRow()
                dr(0) = "الصنف" + dgv_p6.Rows(i).Cells(0).Value + " يوجد له كمية منتهية الصلاحية "
                dtt.Rows.Add(dr)
                GridControl2.DataSource = dtt
            Next
        End If
        If GridView3.RowCount > 0 Then
            PictureBox1.BringToFront()
        Else
            PictureBox3.BringToFront()
        End If

    End Sub
    Private Sub open_con()


    End Sub

    Private Sub Timer3_Tick(sender As Object, e As EventArgs) Handles Timer3.Tick
        Try

            Me.Cursor = Cursors.WaitCursor
            Dim FIL As String
            Dim STT As String
            FIL = My.Settings.f3 + "\\spaxet " + DateTime.Now.ToString("yyyy_MM_dd", New System.Globalization.CultureInfo("en-US")) + "__" + DateTime.Now.ToString("hh_mm tt")
            STT = "Backup Database data to Disk='" + FIL + ".bak'"
            cmd = New SqlCommand(STT, sqlconn)
            If sqlconn.State = ConnectionState.Open Then
                sqlconn.Close()
            End If
            sqlconn.Open()
            cmd.ExecuteNonQuery()
            sqlconn.Close()
            Me.Cursor = Cursors.Default
        Catch ex As Exception

        End Try
    End Sub

    Private Sub طباعةباركودToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles طباعةباركودToolStripMenuItem1.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/barcode_pos.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub طباعةباركودمتوسطToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles طباعةباركودمتوسطToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/barcode_pos2.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub طباعةباركودكبيرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles طباعةباركودكبيرToolStripMenuItem.Click
        If o3.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim FilePath As String = (Application.StartupPath + "\print" + "/barcode_pos3.repx")
        Form2.Show()
        Form2.ReportDesigner1.OpenReport(FilePath)
    End Sub

    Private Sub الشركاءToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles الشركاءToolStripMenuItem1.Click
        Partners_add.Show()
    End Sub

    Private Sub Timer4_Tick(sender As Object, e As EventArgs) Handles Timer4.Tick
        show_set_fator()

        Timer4.Stop()
    End Sub

    Private Sub Timer5_Tick(sender As Object, e As EventArgs) Handles Timer5.Tick
        fill_printer_dize()
        Timer5.Stop()
    End Sub

    Private Sub PictureBox3_Click(sender As Object, e As EventArgs) Handles PictureBox3.Click
        If GridControl2.Visible = False Then
            GridControl2.Visible = True
        ElseIf GridControl2.Visible = True Then
            GridControl2.Visible = False
        End If
    End Sub

    Private Sub PictureBox1_Click(sender As Object, e As EventArgs) Handles PictureBox1.Click
        If GridControl2.Visible = False Then
            GridControl2.Visible = True
        ElseIf GridControl2.Visible = True Then
            GridControl2.Visible = False
        End If
    End Sub
    Private Sub فاتورةبيعبالمسToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles فاتورةبيعبالمسToolStripMenuItem.Click
        If m68.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        pos_add.Show()
    End Sub

    Private Sub نسبةالشركاءToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles نسبةالشركاءToolStripMenuItem.Click
        Dim f As partners_show = New partners_show()
        f.Text = "نسبة الشركاء"
        f.MdiParent = Me
        f.Show()
    End Sub


    Private Sub Timer6_Tick(sender As Object, e As EventArgs) Handles Timer6.Tick
        Try
            Dim sql = "select * from section where code=N'" & (1) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            Dim dr = dt.Rows(0)
            sec_name.Text = dr!sec_name
            sec_phone.Text = dr!sec_phone
            sec_address.Text = dr!sec_address
            sec_email.Text = dr!sec_email
            sec_web.Text = dr!sec_web
            sec_number.Text = dr!sec_number
            sec_s1.Text = dr!sec_s1
            sec_s2.Text = dr!sec_s2
        Catch ex As Exception

        End Try
     
        Timer6.Stop()
    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick

    End Sub

    Private Sub الوحداتToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles الوحداتToolStripMenuItem.Click
        whada.ShowDialog()

    End Sub

    Private Sub كاشيرToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles كاشيرToolStripMenuItem.Click

        Dim f As pos_add2 = New pos_add2()
        f.Text = "فاتورة مبيعات"
        f.MdiParent = Me
        f.Show()
        f.TextEdit1.Focus()
    End Sub



    Private Sub btn_srore_data_Click(sender As Object, e As EventArgs) Handles btn_srore_data.Click
        If m1.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As frm_store_data = New frm_store_data()
        f.Text = "اعدادات "
        f.MdiParent = Me
        f.Show()
    End Sub

    Private Sub مراكزالتكلفةToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles مراكزالتكلفةToolStripMenuItem.Click
        If m1.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As CostCentersManagement = New CostCentersManagement()
        f.Text = "اعدادات "
        f.MdiParent = Me
        f.Show()
    End Sub
End Class