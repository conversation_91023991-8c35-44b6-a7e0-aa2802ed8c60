﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class report_show
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(report_show))
        Me.XtraTabControl2 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage3 = New DevExpress.XtraTab.XtraTabPage()
        Me.SimpleButton109 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton110 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton111 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label45 = New System.Windows.Forms.Label()
        Me.SimpleButton19 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton20 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton21 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.SimpleButton16 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton17 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton18 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.SimpleButton13 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton14 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton15 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.SimpleButton12 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton9 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton10 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton11 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.SimpleButton6 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton7 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton8 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton4 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.cus_balace = New DevExpress.XtraEditors.SimpleButton()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.XtraTabPage4 = New DevExpress.XtraTab.XtraTabPage()
        Me.SimpleButton112 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton113 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton114 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label46 = New System.Windows.Forms.Label()
        Me.SimpleButton26 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton27 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton28 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.SimpleButton22 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton23 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton24 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.XtraTabPage5 = New DevExpress.XtraTab.XtraTabPage()
        Me.SimpleButton117 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton54 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton55 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton56 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.SimpleButton51 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton52 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton53 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.SimpleButton48 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton49 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton50 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.SimpleButton45 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton46 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton47 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.SimpleButton44 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton32 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton42 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton43 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.SimpleButton41 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton40 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton39 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton38 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton37 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton36 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton33 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton34 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton35 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.SimpleButton31 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton25 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton29 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton30 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.XtraTabPage6 = New DevExpress.XtraTab.XtraTabPage()
        Me.SimpleButton93 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton94 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label34 = New System.Windows.Forms.Label()
        Me.SimpleButton89 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton90 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton91 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton92 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label32 = New System.Windows.Forms.Label()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.SimpleButton87 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton88 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.SimpleButton85 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton86 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.SimpleButton83 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.SimpleButton79 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton80 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton81 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton82 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.SimpleButton78 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton77 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton76 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.SimpleButton73 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton75 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.SimpleButton72 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton67 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton70 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton69 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.SimpleButton64 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton66 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.SimpleButton63 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton57 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton58 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton62 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.SimpleButton59 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton61 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.XtraTabPage7 = New DevExpress.XtraTab.XtraTabPage()
        Me.SimpleButton60 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label35 = New System.Windows.Forms.Label()
        Me.Label36 = New System.Windows.Forms.Label()
        Me.SimpleButton65 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton71 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton74 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton95 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label37 = New System.Windows.Forms.Label()
        Me.SimpleButton96 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton97 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label38 = New System.Windows.Forms.Label()
        Me.SimpleButton68 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.Label39 = New System.Windows.Forms.Label()
        Me.SimpleButton101 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton102 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton103 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton104 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label40 = New System.Windows.Forms.Label()
        Me.SimpleButton105 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton106 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label41 = New System.Windows.Forms.Label()
        Me.XtraTabPage8 = New DevExpress.XtraTab.XtraTabPage()
        Me.SimpleButton125 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton123 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton124 = New DevExpress.XtraEditors.SimpleButton()
        Me.CostCenterReportBtn = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelCostCenter = New System.Windows.Forms.Label()
        Me.Label48 = New System.Windows.Forms.Label()
        Me.SimpleButton121 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton122 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.SimpleButton115 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton120 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton119 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton118 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton116 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton108 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton107 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton84 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label44 = New System.Windows.Forms.Label()
        Me.SimpleButton100 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label43 = New System.Windows.Forms.Label()
        Me.SimpleButton98 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton99 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label42 = New System.Windows.Forms.Label()
        Me.Label47 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl2.SuspendLayout()
        Me.XtraTabPage3.SuspendLayout()
        Me.XtraTabPage4.SuspendLayout()
        Me.XtraTabPage5.SuspendLayout()
        Me.XtraTabPage6.SuspendLayout()
        Me.XtraTabPage7.SuspendLayout()
        Me.XtraTabPage8.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'XtraTabControl2
        '
        Me.XtraTabControl2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl2.HeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Right
        Me.XtraTabControl2.HeaderOrientation = DevExpress.XtraTab.TabOrientation.Horizontal
        Me.XtraTabControl2.Location = New System.Drawing.Point(-1, 48)
        Me.XtraTabControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl2.MultiLine = DevExpress.Utils.DefaultBoolean.[True]
        Me.XtraTabControl2.Name = "XtraTabControl2"
        Me.XtraTabControl2.PageImagePosition = DevExpress.XtraTab.TabPageImagePosition.Far
        Me.XtraTabControl2.SelectedTabPage = Me.XtraTabPage3
        Me.XtraTabControl2.Size = New System.Drawing.Size(1015, 446)
        Me.XtraTabControl2.TabIndex = 186
        Me.XtraTabControl2.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage3, Me.XtraTabPage4, Me.XtraTabPage5, Me.XtraTabPage6, Me.XtraTabPage7, Me.XtraTabPage8})
        '
        'XtraTabPage3
        '
        Me.XtraTabPage3.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.XtraTabPage3.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage3.Appearance.Header.Options.UseTextOptions = True
        Me.XtraTabPage3.Appearance.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.XtraTabPage3.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton109)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton110)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton111)
        Me.XtraTabPage3.Controls.Add(Me.Label45)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton19)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton20)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton21)
        Me.XtraTabPage3.Controls.Add(Me.Label7)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton16)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton17)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton18)
        Me.XtraTabPage3.Controls.Add(Me.Label6)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton13)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton14)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton15)
        Me.XtraTabPage3.Controls.Add(Me.Label5)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton12)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton9)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton10)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton11)
        Me.XtraTabPage3.Controls.Add(Me.Label4)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton6)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton7)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton8)
        Me.XtraTabPage3.Controls.Add(Me.Label2)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton3)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton4)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton5)
        Me.XtraTabPage3.Controls.Add(Me.Label1)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton2)
        Me.XtraTabPage3.Controls.Add(Me.SimpleButton1)
        Me.XtraTabPage3.Controls.Add(Me.cus_balace)
        Me.XtraTabPage3.Controls.Add(Me.Label17)
        Me.XtraTabPage3.ImageOptions.Image = CType(resources.GetObject("XtraTabPage3.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage3.Name = "XtraTabPage3"
        Me.XtraTabPage3.Size = New System.Drawing.Size(921, 444)
        Me.XtraTabPage3.Text = "العملاء"
        '
        'SimpleButton109
        '
        Me.SimpleButton109.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton109.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton109.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton109.Appearance.Options.UseBackColor = True
        Me.SimpleButton109.Appearance.Options.UseFont = True
        Me.SimpleButton109.Appearance.Options.UseTextOptions = True
        Me.SimpleButton109.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton109.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton109.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton109.Location = New System.Drawing.Point(433, 335)
        Me.SimpleButton109.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton109.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton109.Name = "SimpleButton109"
        Me.SimpleButton109.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton109.TabIndex = 279
        Me.SimpleButton109.Text = "تم سددها"
        '
        'SimpleButton110
        '
        Me.SimpleButton110.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton110.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton110.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton110.Appearance.Options.UseBackColor = True
        Me.SimpleButton110.Appearance.Options.UseFont = True
        Me.SimpleButton110.Appearance.Options.UseTextOptions = True
        Me.SimpleButton110.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton110.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton110.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton110.Location = New System.Drawing.Point(433, 296)
        Me.SimpleButton110.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton110.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton110.Name = "SimpleButton110"
        Me.SimpleButton110.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton110.TabIndex = 278
        Me.SimpleButton110.Text = "الواجب سددها"
        '
        'SimpleButton111
        '
        Me.SimpleButton111.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton111.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton111.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton111.Appearance.Options.UseBackColor = True
        Me.SimpleButton111.Appearance.Options.UseFont = True
        Me.SimpleButton111.Appearance.Options.UseTextOptions = True
        Me.SimpleButton111.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton111.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton111.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton111.Location = New System.Drawing.Point(433, 257)
        Me.SimpleButton111.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton111.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton111.Name = "SimpleButton111"
        Me.SimpleButton111.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton111.TabIndex = 277
        Me.SimpleButton111.Text = "المتأخرة"
        '
        'Label45
        '
        Me.Label45.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label45.AutoSize = True
        Me.Label45.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label45.ForeColor = System.Drawing.Color.Maroon
        Me.Label45.Location = New System.Drawing.Point(482, 221)
        Me.Label45.Name = "Label45"
        Me.Label45.Size = New System.Drawing.Size(48, 16)
        Me.Label45.TabIndex = 276
        Me.Label45.Text = "الشيكات"
        '
        'SimpleButton19
        '
        Me.SimpleButton19.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton19.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton19.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton19.Appearance.Options.UseBackColor = True
        Me.SimpleButton19.Appearance.Options.UseFont = True
        Me.SimpleButton19.Appearance.Options.UseTextOptions = True
        Me.SimpleButton19.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton19.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton19.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton19.Location = New System.Drawing.Point(594, 335)
        Me.SimpleButton19.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton19.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton19.Name = "SimpleButton19"
        Me.SimpleButton19.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton19.TabIndex = 275
        Me.SimpleButton19.Text = "مقارنة ربح"
        '
        'SimpleButton20
        '
        Me.SimpleButton20.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton20.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton20.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton20.Appearance.Options.UseBackColor = True
        Me.SimpleButton20.Appearance.Options.UseFont = True
        Me.SimpleButton20.Appearance.Options.UseTextOptions = True
        Me.SimpleButton20.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton20.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton20.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton20.Location = New System.Drawing.Point(594, 296)
        Me.SimpleButton20.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton20.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton20.Name = "SimpleButton20"
        Me.SimpleButton20.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton20.TabIndex = 274
        Me.SimpleButton20.Text = "الاقل ربح"
        '
        'SimpleButton21
        '
        Me.SimpleButton21.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton21.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton21.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton21.Appearance.Options.UseBackColor = True
        Me.SimpleButton21.Appearance.Options.UseFont = True
        Me.SimpleButton21.Appearance.Options.UseTextOptions = True
        Me.SimpleButton21.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton21.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton21.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton21.Location = New System.Drawing.Point(594, 257)
        Me.SimpleButton21.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton21.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton21.Name = "SimpleButton21"
        Me.SimpleButton21.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton21.TabIndex = 273
        Me.SimpleButton21.Text = "الاكثر ربح"
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.Maroon
        Me.Label7.Location = New System.Drawing.Point(643, 221)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(43, 16)
        Me.Label7.TabIndex = 272
        Me.Label7.Text = "المندوب"
        '
        'SimpleButton16
        '
        Me.SimpleButton16.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton16.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton16.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton16.Appearance.Options.UseBackColor = True
        Me.SimpleButton16.Appearance.Options.UseFont = True
        Me.SimpleButton16.Appearance.Options.UseTextOptions = True
        Me.SimpleButton16.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton16.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton16.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton16.Location = New System.Drawing.Point(111, 150)
        Me.SimpleButton16.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton16.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton16.Name = "SimpleButton16"
        Me.SimpleButton16.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton16.TabIndex = 271
        Me.SimpleButton16.Text = "مقارنة ربح"
        '
        'SimpleButton17
        '
        Me.SimpleButton17.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton17.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton17.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton17.Appearance.Options.UseBackColor = True
        Me.SimpleButton17.Appearance.Options.UseFont = True
        Me.SimpleButton17.Appearance.Options.UseTextOptions = True
        Me.SimpleButton17.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton17.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton17.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton17.Location = New System.Drawing.Point(111, 111)
        Me.SimpleButton17.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton17.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton17.Name = "SimpleButton17"
        Me.SimpleButton17.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton17.TabIndex = 270
        Me.SimpleButton17.Text = "الاقل ربح"
        '
        'SimpleButton18
        '
        Me.SimpleButton18.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton18.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton18.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton18.Appearance.Options.UseBackColor = True
        Me.SimpleButton18.Appearance.Options.UseFont = True
        Me.SimpleButton18.Appearance.Options.UseTextOptions = True
        Me.SimpleButton18.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton18.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton18.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton18.Location = New System.Drawing.Point(111, 72)
        Me.SimpleButton18.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton18.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton18.Name = "SimpleButton18"
        Me.SimpleButton18.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton18.TabIndex = 269
        Me.SimpleButton18.Text = "الاكثر ربح"
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.Maroon
        Me.Label6.Location = New System.Drawing.Point(157, 30)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(50, 16)
        Me.Label6.TabIndex = 268
        Me.Label6.Text = "المحافظة"
        '
        'SimpleButton13
        '
        Me.SimpleButton13.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton13.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton13.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton13.Appearance.Options.UseBackColor = True
        Me.SimpleButton13.Appearance.Options.UseFont = True
        Me.SimpleButton13.Appearance.Options.UseTextOptions = True
        Me.SimpleButton13.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton13.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton13.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton13.Location = New System.Drawing.Point(272, 150)
        Me.SimpleButton13.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton13.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton13.Name = "SimpleButton13"
        Me.SimpleButton13.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton13.TabIndex = 267
        Me.SimpleButton13.Text = "مقارنة ربح"
        '
        'SimpleButton14
        '
        Me.SimpleButton14.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton14.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton14.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton14.Appearance.Options.UseBackColor = True
        Me.SimpleButton14.Appearance.Options.UseFont = True
        Me.SimpleButton14.Appearance.Options.UseTextOptions = True
        Me.SimpleButton14.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton14.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton14.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton14.Location = New System.Drawing.Point(272, 111)
        Me.SimpleButton14.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton14.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton14.Name = "SimpleButton14"
        Me.SimpleButton14.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton14.TabIndex = 266
        Me.SimpleButton14.Text = "الاقل ربح"
        '
        'SimpleButton15
        '
        Me.SimpleButton15.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton15.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton15.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton15.Appearance.Options.UseBackColor = True
        Me.SimpleButton15.Appearance.Options.UseFont = True
        Me.SimpleButton15.Appearance.Options.UseTextOptions = True
        Me.SimpleButton15.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton15.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton15.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton15.Location = New System.Drawing.Point(272, 72)
        Me.SimpleButton15.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton15.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton15.Name = "SimpleButton15"
        Me.SimpleButton15.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton15.TabIndex = 265
        Me.SimpleButton15.Text = "الاكثر ربح"
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.Maroon
        Me.Label5.Location = New System.Drawing.Point(318, 30)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(37, 16)
        Me.Label5.TabIndex = 264
        Me.Label5.Text = "المدينة"
        '
        'SimpleButton12
        '
        Me.SimpleButton12.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton12.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton12.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton12.Appearance.Options.UseBackColor = True
        Me.SimpleButton12.Appearance.Options.UseFont = True
        Me.SimpleButton12.Appearance.Options.UseTextOptions = True
        Me.SimpleButton12.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton12.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton12.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton12.Location = New System.Drawing.Point(753, 337)
        Me.SimpleButton12.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton12.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton12.Name = "SimpleButton12"
        Me.SimpleButton12.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton12.TabIndex = 263
        Me.SimpleButton12.Text = "احصائيات"
        '
        'SimpleButton9
        '
        Me.SimpleButton9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton9.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton9.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton9.Appearance.Options.UseBackColor = True
        Me.SimpleButton9.Appearance.Options.UseFont = True
        Me.SimpleButton9.Appearance.Options.UseTextOptions = True
        Me.SimpleButton9.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton9.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton9.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton9.Location = New System.Drawing.Point(753, 375)
        Me.SimpleButton9.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton9.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton9.Name = "SimpleButton9"
        Me.SimpleButton9.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton9.TabIndex = 262
        Me.SimpleButton9.Text = "اصناف عميل"
        '
        'SimpleButton10
        '
        Me.SimpleButton10.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton10.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton10.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton10.Appearance.Options.UseBackColor = True
        Me.SimpleButton10.Appearance.Options.UseFont = True
        Me.SimpleButton10.Appearance.Options.UseTextOptions = True
        Me.SimpleButton10.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton10.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton10.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton10.Location = New System.Drawing.Point(753, 297)
        Me.SimpleButton10.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton10.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton10.Name = "SimpleButton10"
        Me.SimpleButton10.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton10.TabIndex = 261
        Me.SimpleButton10.Text = "كشف تفصيلي"
        '
        'SimpleButton11
        '
        Me.SimpleButton11.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton11.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton11.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton11.Appearance.Options.UseBackColor = True
        Me.SimpleButton11.Appearance.Options.UseFont = True
        Me.SimpleButton11.Appearance.Options.UseTextOptions = True
        Me.SimpleButton11.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton11.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton11.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton11.Location = New System.Drawing.Point(753, 257)
        Me.SimpleButton11.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton11.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton11.Name = "SimpleButton11"
        Me.SimpleButton11.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton11.TabIndex = 260
        Me.SimpleButton11.Text = "كشف حساب"
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.Maroon
        Me.Label4.Location = New System.Drawing.Point(802, 221)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(38, 16)
        Me.Label4.TabIndex = 259
        Me.Label4.Text = "العملاء"
        '
        'SimpleButton6
        '
        Me.SimpleButton6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton6.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton6.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton6.Appearance.Options.UseBackColor = True
        Me.SimpleButton6.Appearance.Options.UseFont = True
        Me.SimpleButton6.Appearance.Options.UseTextOptions = True
        Me.SimpleButton6.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton6.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton6.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton6.Location = New System.Drawing.Point(433, 150)
        Me.SimpleButton6.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton6.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton6.Name = "SimpleButton6"
        Me.SimpleButton6.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton6.TabIndex = 258
        Me.SimpleButton6.Text = "مقارنة ربح"
        '
        'SimpleButton7
        '
        Me.SimpleButton7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton7.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton7.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton7.Appearance.Options.UseBackColor = True
        Me.SimpleButton7.Appearance.Options.UseFont = True
        Me.SimpleButton7.Appearance.Options.UseTextOptions = True
        Me.SimpleButton7.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton7.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton7.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton7.Location = New System.Drawing.Point(433, 111)
        Me.SimpleButton7.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton7.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton7.Name = "SimpleButton7"
        Me.SimpleButton7.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton7.TabIndex = 257
        Me.SimpleButton7.Text = "الاقل ربح"
        '
        'SimpleButton8
        '
        Me.SimpleButton8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton8.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton8.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton8.Appearance.Options.UseBackColor = True
        Me.SimpleButton8.Appearance.Options.UseFont = True
        Me.SimpleButton8.Appearance.Options.UseTextOptions = True
        Me.SimpleButton8.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton8.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton8.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton8.Location = New System.Drawing.Point(433, 72)
        Me.SimpleButton8.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton8.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton8.Name = "SimpleButton8"
        Me.SimpleButton8.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton8.TabIndex = 256
        Me.SimpleButton8.Text = "الاكثر ربح"
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.Maroon
        Me.Label2.Location = New System.Drawing.Point(479, 30)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(51, 16)
        Me.Label2.TabIndex = 255
        Me.Label2.Text = "المجموعة"
        '
        'SimpleButton3
        '
        Me.SimpleButton3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton3.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseFont = True
        Me.SimpleButton3.Appearance.Options.UseTextOptions = True
        Me.SimpleButton3.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton3.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton3.Location = New System.Drawing.Point(594, 150)
        Me.SimpleButton3.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton3.TabIndex = 254
        Me.SimpleButton3.Text = "مقارنة ربح"
        '
        'SimpleButton4
        '
        Me.SimpleButton4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton4.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton4.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton4.Appearance.Options.UseBackColor = True
        Me.SimpleButton4.Appearance.Options.UseFont = True
        Me.SimpleButton4.Appearance.Options.UseTextOptions = True
        Me.SimpleButton4.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton4.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton4.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton4.Location = New System.Drawing.Point(594, 111)
        Me.SimpleButton4.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton4.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton4.Name = "SimpleButton4"
        Me.SimpleButton4.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton4.TabIndex = 253
        Me.SimpleButton4.Text = "الاقل ربح"
        '
        'SimpleButton5
        '
        Me.SimpleButton5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton5.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseFont = True
        Me.SimpleButton5.Appearance.Options.UseTextOptions = True
        Me.SimpleButton5.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton5.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton5.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton5.Location = New System.Drawing.Point(594, 72)
        Me.SimpleButton5.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton5.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton5.TabIndex = 252
        Me.SimpleButton5.Text = "الاكثر ربح"
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.Maroon
        Me.Label1.Location = New System.Drawing.Point(643, 30)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(41, 16)
        Me.Label1.TabIndex = 251
        Me.Label1.Text = "الارباح"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseFont = True
        Me.SimpleButton2.Appearance.Options.UseTextOptions = True
        Me.SimpleButton2.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton2.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton2.Location = New System.Drawing.Point(753, 150)
        Me.SimpleButton2.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton2.TabIndex = 250
        Me.SimpleButton2.Text = "الرصيد بالسالب"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseTextOptions = True
        Me.SimpleButton1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton1.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton1.Location = New System.Drawing.Point(753, 111)
        Me.SimpleButton1.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton1.TabIndex = 249
        Me.SimpleButton1.Text = "ميزان مراجعة العملاء"
        '
        'cus_balace
        '
        Me.cus_balace.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cus_balace.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.cus_balace.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cus_balace.Appearance.Options.UseBackColor = True
        Me.cus_balace.Appearance.Options.UseFont = True
        Me.cus_balace.Appearance.Options.UseTextOptions = True
        Me.cus_balace.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.cus_balace.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cus_balace.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.cus_balace.Location = New System.Drawing.Point(753, 72)
        Me.cus_balace.LookAndFeel.SkinName = "VS2010"
        Me.cus_balace.LookAndFeel.UseDefaultLookAndFeel = False
        Me.cus_balace.Name = "cus_balace"
        Me.cus_balace.Size = New System.Drawing.Size(143, 31)
        Me.cus_balace.TabIndex = 248
        Me.cus_balace.Text = "ارصدة العملاء"
        '
        'Label17
        '
        Me.Label17.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label17.ForeColor = System.Drawing.Color.Maroon
        Me.Label17.Location = New System.Drawing.Point(802, 30)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(38, 16)
        Me.Label17.TabIndex = 246
        Me.Label17.Text = "الرصيد"
        '
        'XtraTabPage4
        '
        Me.XtraTabPage4.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.XtraTabPage4.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage4.Appearance.Header.Options.UseTextOptions = True
        Me.XtraTabPage4.Appearance.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton112)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton113)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton114)
        Me.XtraTabPage4.Controls.Add(Me.Label46)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton26)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton27)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton28)
        Me.XtraTabPage4.Controls.Add(Me.Label9)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton22)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton23)
        Me.XtraTabPage4.Controls.Add(Me.SimpleButton24)
        Me.XtraTabPage4.Controls.Add(Me.Label8)
        Me.XtraTabPage4.ImageOptions.Image = CType(resources.GetObject("XtraTabPage4.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage4.Name = "XtraTabPage4"
        Me.XtraTabPage4.Size = New System.Drawing.Size(921, 444)
        Me.XtraTabPage4.Text = "الموردين"
        '
        'SimpleButton112
        '
        Me.SimpleButton112.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton112.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton112.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton112.Appearance.Options.UseBackColor = True
        Me.SimpleButton112.Appearance.Options.UseFont = True
        Me.SimpleButton112.Appearance.Options.UseTextOptions = True
        Me.SimpleButton112.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton112.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton112.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton112.Location = New System.Drawing.Point(399, 149)
        Me.SimpleButton112.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton112.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton112.Name = "SimpleButton112"
        Me.SimpleButton112.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton112.TabIndex = 283
        Me.SimpleButton112.Text = "تم سددها"
        '
        'SimpleButton113
        '
        Me.SimpleButton113.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton113.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton113.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton113.Appearance.Options.UseBackColor = True
        Me.SimpleButton113.Appearance.Options.UseFont = True
        Me.SimpleButton113.Appearance.Options.UseTextOptions = True
        Me.SimpleButton113.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton113.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton113.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton113.Location = New System.Drawing.Point(399, 110)
        Me.SimpleButton113.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton113.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton113.Name = "SimpleButton113"
        Me.SimpleButton113.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton113.TabIndex = 282
        Me.SimpleButton113.Text = "الواجب سددها"
        '
        'SimpleButton114
        '
        Me.SimpleButton114.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton114.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton114.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton114.Appearance.Options.UseBackColor = True
        Me.SimpleButton114.Appearance.Options.UseFont = True
        Me.SimpleButton114.Appearance.Options.UseTextOptions = True
        Me.SimpleButton114.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton114.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton114.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton114.Location = New System.Drawing.Point(399, 71)
        Me.SimpleButton114.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton114.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton114.Name = "SimpleButton114"
        Me.SimpleButton114.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton114.TabIndex = 281
        Me.SimpleButton114.Text = "المتأخرة"
        '
        'Label46
        '
        Me.Label46.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label46.AutoSize = True
        Me.Label46.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label46.ForeColor = System.Drawing.Color.Maroon
        Me.Label46.Location = New System.Drawing.Point(448, 35)
        Me.Label46.Name = "Label46"
        Me.Label46.Size = New System.Drawing.Size(48, 16)
        Me.Label46.TabIndex = 280
        Me.Label46.Text = "الشيكات"
        '
        'SimpleButton26
        '
        Me.SimpleButton26.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton26.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton26.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton26.Appearance.Options.UseBackColor = True
        Me.SimpleButton26.Appearance.Options.UseFont = True
        Me.SimpleButton26.Appearance.Options.UseTextOptions = True
        Me.SimpleButton26.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton26.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton26.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton26.Location = New System.Drawing.Point(559, 149)
        Me.SimpleButton26.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton26.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton26.Name = "SimpleButton26"
        Me.SimpleButton26.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton26.TabIndex = 267
        Me.SimpleButton26.Text = "اصناف مورد"
        '
        'SimpleButton27
        '
        Me.SimpleButton27.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton27.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton27.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton27.Appearance.Options.UseBackColor = True
        Me.SimpleButton27.Appearance.Options.UseFont = True
        Me.SimpleButton27.Appearance.Options.UseTextOptions = True
        Me.SimpleButton27.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton27.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton27.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton27.Location = New System.Drawing.Point(559, 111)
        Me.SimpleButton27.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton27.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton27.Name = "SimpleButton27"
        Me.SimpleButton27.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton27.TabIndex = 266
        Me.SimpleButton27.Text = "كشف تفصيلي"
        '
        'SimpleButton28
        '
        Me.SimpleButton28.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton28.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton28.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton28.Appearance.Options.UseBackColor = True
        Me.SimpleButton28.Appearance.Options.UseFont = True
        Me.SimpleButton28.Appearance.Options.UseTextOptions = True
        Me.SimpleButton28.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton28.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton28.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton28.Location = New System.Drawing.Point(559, 71)
        Me.SimpleButton28.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton28.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton28.Name = "SimpleButton28"
        Me.SimpleButton28.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton28.TabIndex = 265
        Me.SimpleButton28.Text = "كشف حساب"
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.Maroon
        Me.Label9.Location = New System.Drawing.Point(608, 35)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(47, 16)
        Me.Label9.TabIndex = 264
        Me.Label9.Text = "الموردين"
        '
        'SimpleButton22
        '
        Me.SimpleButton22.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton22.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton22.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton22.Appearance.Options.UseBackColor = True
        Me.SimpleButton22.Appearance.Options.UseFont = True
        Me.SimpleButton22.Appearance.Options.UseTextOptions = True
        Me.SimpleButton22.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton22.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton22.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton22.Location = New System.Drawing.Point(729, 149)
        Me.SimpleButton22.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton22.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton22.Name = "SimpleButton22"
        Me.SimpleButton22.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton22.TabIndex = 254
        Me.SimpleButton22.Text = "الرصيد بالموجب"
        '
        'SimpleButton23
        '
        Me.SimpleButton23.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton23.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton23.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton23.Appearance.Options.UseBackColor = True
        Me.SimpleButton23.Appearance.Options.UseFont = True
        Me.SimpleButton23.Appearance.Options.UseTextOptions = True
        Me.SimpleButton23.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton23.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton23.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton23.Location = New System.Drawing.Point(729, 110)
        Me.SimpleButton23.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton23.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton23.Name = "SimpleButton23"
        Me.SimpleButton23.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton23.TabIndex = 253
        Me.SimpleButton23.Text = "ميزان مراجعة الموردين"
        '
        'SimpleButton24
        '
        Me.SimpleButton24.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton24.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton24.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton24.Appearance.Options.UseBackColor = True
        Me.SimpleButton24.Appearance.Options.UseFont = True
        Me.SimpleButton24.Appearance.Options.UseTextOptions = True
        Me.SimpleButton24.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton24.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton24.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton24.Location = New System.Drawing.Point(729, 71)
        Me.SimpleButton24.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton24.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton24.Name = "SimpleButton24"
        Me.SimpleButton24.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton24.TabIndex = 252
        Me.SimpleButton24.Text = "ارصدة الموردين"
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.Maroon
        Me.Label8.Location = New System.Drawing.Point(778, 29)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(38, 16)
        Me.Label8.TabIndex = 251
        Me.Label8.Text = "الرصيد"
        '
        'XtraTabPage5
        '
        Me.XtraTabPage5.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.XtraTabPage5.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage5.Appearance.Header.Options.UseTextOptions = True
        Me.XtraTabPage5.Appearance.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton117)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton54)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton55)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton56)
        Me.XtraTabPage5.Controls.Add(Me.Label16)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton51)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton52)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton53)
        Me.XtraTabPage5.Controls.Add(Me.Label15)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton48)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton49)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton50)
        Me.XtraTabPage5.Controls.Add(Me.Label14)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton45)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton46)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton47)
        Me.XtraTabPage5.Controls.Add(Me.Label13)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton44)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton32)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton42)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton43)
        Me.XtraTabPage5.Controls.Add(Me.Label12)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton41)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton40)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton39)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton38)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton37)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton36)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton33)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton34)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton35)
        Me.XtraTabPage5.Controls.Add(Me.Label11)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton31)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton25)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton29)
        Me.XtraTabPage5.Controls.Add(Me.SimpleButton30)
        Me.XtraTabPage5.Controls.Add(Me.Label10)
        Me.XtraTabPage5.ImageOptions.Image = CType(resources.GetObject("XtraTabPage5.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage5.Name = "XtraTabPage5"
        Me.XtraTabPage5.Size = New System.Drawing.Size(921, 444)
        Me.XtraTabPage5.Text = "المخازن"
        '
        'SimpleButton117
        '
        Me.SimpleButton117.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton117.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton117.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton117.Appearance.Options.UseBackColor = True
        Me.SimpleButton117.Appearance.Options.UseFont = True
        Me.SimpleButton117.Appearance.Options.UseTextOptions = True
        Me.SimpleButton117.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton117.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton117.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton117.Location = New System.Drawing.Point(736, 258)
        Me.SimpleButton117.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton117.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton117.Name = "SimpleButton117"
        Me.SimpleButton117.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton117.TabIndex = 288
        Me.SimpleButton117.Text = "اصناف منتهية الصلاحية"
        '
        'SimpleButton54
        '
        Me.SimpleButton54.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton54.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton54.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton54.Appearance.Options.UseBackColor = True
        Me.SimpleButton54.Appearance.Options.UseFont = True
        Me.SimpleButton54.Appearance.Options.UseTextOptions = True
        Me.SimpleButton54.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton54.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton54.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton54.Location = New System.Drawing.Point(222, 335)
        Me.SimpleButton54.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton54.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton54.Name = "SimpleButton54"
        Me.SimpleButton54.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton54.TabIndex = 287
        Me.SimpleButton54.Text = "مقارنة ربح"
        '
        'SimpleButton55
        '
        Me.SimpleButton55.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton55.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton55.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton55.Appearance.Options.UseBackColor = True
        Me.SimpleButton55.Appearance.Options.UseFont = True
        Me.SimpleButton55.Appearance.Options.UseTextOptions = True
        Me.SimpleButton55.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton55.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton55.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton55.Location = New System.Drawing.Point(222, 296)
        Me.SimpleButton55.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton55.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton55.Name = "SimpleButton55"
        Me.SimpleButton55.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton55.TabIndex = 286
        Me.SimpleButton55.Text = "الاقل ربح"
        '
        'SimpleButton56
        '
        Me.SimpleButton56.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton56.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton56.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton56.Appearance.Options.UseBackColor = True
        Me.SimpleButton56.Appearance.Options.UseFont = True
        Me.SimpleButton56.Appearance.Options.UseTextOptions = True
        Me.SimpleButton56.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton56.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton56.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton56.Location = New System.Drawing.Point(222, 257)
        Me.SimpleButton56.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton56.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton56.Name = "SimpleButton56"
        Me.SimpleButton56.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton56.TabIndex = 285
        Me.SimpleButton56.Text = "الاكثر ربح"
        '
        'Label16
        '
        Me.Label16.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.ForeColor = System.Drawing.Color.Maroon
        Me.Label16.Location = New System.Drawing.Point(247, 220)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(86, 16)
        Me.Label16.TabIndex = 284
        Me.Label16.Text = "المخازن / الفروع"
        '
        'SimpleButton51
        '
        Me.SimpleButton51.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton51.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton51.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton51.Appearance.Options.UseBackColor = True
        Me.SimpleButton51.Appearance.Options.UseFont = True
        Me.SimpleButton51.Appearance.Options.UseTextOptions = True
        Me.SimpleButton51.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton51.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton51.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton51.Location = New System.Drawing.Point(393, 335)
        Me.SimpleButton51.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton51.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton51.Name = "SimpleButton51"
        Me.SimpleButton51.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton51.TabIndex = 283
        Me.SimpleButton51.Text = "مقارنة ربح"
        '
        'SimpleButton52
        '
        Me.SimpleButton52.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton52.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton52.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton52.Appearance.Options.UseBackColor = True
        Me.SimpleButton52.Appearance.Options.UseFont = True
        Me.SimpleButton52.Appearance.Options.UseTextOptions = True
        Me.SimpleButton52.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton52.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton52.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton52.Location = New System.Drawing.Point(393, 296)
        Me.SimpleButton52.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton52.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton52.Name = "SimpleButton52"
        Me.SimpleButton52.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton52.TabIndex = 282
        Me.SimpleButton52.Text = "الاقل ربح"
        '
        'SimpleButton53
        '
        Me.SimpleButton53.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton53.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton53.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton53.Appearance.Options.UseBackColor = True
        Me.SimpleButton53.Appearance.Options.UseFont = True
        Me.SimpleButton53.Appearance.Options.UseTextOptions = True
        Me.SimpleButton53.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton53.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton53.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton53.Location = New System.Drawing.Point(393, 257)
        Me.SimpleButton53.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton53.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton53.Name = "SimpleButton53"
        Me.SimpleButton53.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton53.TabIndex = 281
        Me.SimpleButton53.Text = "الاكثر ربح"
        '
        'Label15
        '
        Me.Label15.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.ForeColor = System.Drawing.Color.Maroon
        Me.Label15.Location = New System.Drawing.Point(430, 220)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(60, 16)
        Me.Label15.TabIndex = 280
        Me.Label15.Text = "المجموعات"
        '
        'SimpleButton48
        '
        Me.SimpleButton48.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton48.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton48.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton48.Appearance.Options.UseBackColor = True
        Me.SimpleButton48.Appearance.Options.UseFont = True
        Me.SimpleButton48.Appearance.Options.UseTextOptions = True
        Me.SimpleButton48.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton48.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton48.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton48.Location = New System.Drawing.Point(50, 147)
        Me.SimpleButton48.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton48.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton48.Name = "SimpleButton48"
        Me.SimpleButton48.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton48.TabIndex = 279
        Me.SimpleButton48.Text = "مقارنة ربح"
        '
        'SimpleButton49
        '
        Me.SimpleButton49.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton49.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton49.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton49.Appearance.Options.UseBackColor = True
        Me.SimpleButton49.Appearance.Options.UseFont = True
        Me.SimpleButton49.Appearance.Options.UseTextOptions = True
        Me.SimpleButton49.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton49.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton49.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton49.Location = New System.Drawing.Point(50, 108)
        Me.SimpleButton49.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton49.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton49.Name = "SimpleButton49"
        Me.SimpleButton49.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton49.TabIndex = 278
        Me.SimpleButton49.Text = "الاقل ربح"
        '
        'SimpleButton50
        '
        Me.SimpleButton50.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton50.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton50.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton50.Appearance.Options.UseBackColor = True
        Me.SimpleButton50.Appearance.Options.UseFont = True
        Me.SimpleButton50.Appearance.Options.UseTextOptions = True
        Me.SimpleButton50.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton50.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton50.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton50.Location = New System.Drawing.Point(50, 69)
        Me.SimpleButton50.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton50.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton50.Name = "SimpleButton50"
        Me.SimpleButton50.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton50.TabIndex = 277
        Me.SimpleButton50.Text = "الاكثر ربح"
        '
        'Label14
        '
        Me.Label14.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.ForeColor = System.Drawing.Color.Maroon
        Me.Label14.Location = New System.Drawing.Point(99, 27)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(45, 16)
        Me.Label14.TabIndex = 276
        Me.Label14.Text = "التصنيف"
        '
        'SimpleButton45
        '
        Me.SimpleButton45.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton45.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton45.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton45.Appearance.Options.UseBackColor = True
        Me.SimpleButton45.Appearance.Options.UseFont = True
        Me.SimpleButton45.Appearance.Options.UseTextOptions = True
        Me.SimpleButton45.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton45.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton45.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton45.Location = New System.Drawing.Point(222, 147)
        Me.SimpleButton45.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton45.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton45.Name = "SimpleButton45"
        Me.SimpleButton45.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton45.TabIndex = 275
        Me.SimpleButton45.Text = "مقارنة ربح"
        '
        'SimpleButton46
        '
        Me.SimpleButton46.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton46.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton46.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton46.Appearance.Options.UseBackColor = True
        Me.SimpleButton46.Appearance.Options.UseFont = True
        Me.SimpleButton46.Appearance.Options.UseTextOptions = True
        Me.SimpleButton46.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton46.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton46.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton46.Location = New System.Drawing.Point(222, 108)
        Me.SimpleButton46.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton46.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton46.Name = "SimpleButton46"
        Me.SimpleButton46.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton46.TabIndex = 274
        Me.SimpleButton46.Text = "الاقل ربح"
        '
        'SimpleButton47
        '
        Me.SimpleButton47.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton47.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton47.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton47.Appearance.Options.UseBackColor = True
        Me.SimpleButton47.Appearance.Options.UseFont = True
        Me.SimpleButton47.Appearance.Options.UseTextOptions = True
        Me.SimpleButton47.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton47.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton47.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton47.Location = New System.Drawing.Point(222, 69)
        Me.SimpleButton47.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton47.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton47.Name = "SimpleButton47"
        Me.SimpleButton47.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton47.TabIndex = 273
        Me.SimpleButton47.Text = "الاكثر ربح"
        '
        'Label13
        '
        Me.Label13.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.ForeColor = System.Drawing.Color.Maroon
        Me.Label13.Location = New System.Drawing.Point(271, 27)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(50, 16)
        Me.Label13.TabIndex = 272
        Me.Label13.Text = "الشركات"
        '
        'SimpleButton44
        '
        Me.SimpleButton44.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton44.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton44.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton44.Appearance.Options.UseBackColor = True
        Me.SimpleButton44.Appearance.Options.UseFont = True
        Me.SimpleButton44.Appearance.Options.UseTextOptions = True
        Me.SimpleButton44.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton44.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton44.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton44.Location = New System.Drawing.Point(736, 221)
        Me.SimpleButton44.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton44.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton44.Name = "SimpleButton44"
        Me.SimpleButton44.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton44.TabIndex = 271
        Me.SimpleButton44.Text = "اصناف تسبب خسارة"
        '
        'SimpleButton32
        '
        Me.SimpleButton32.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton32.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton32.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton32.Appearance.Options.UseBackColor = True
        Me.SimpleButton32.Appearance.Options.UseFont = True
        Me.SimpleButton32.Appearance.Options.UseTextOptions = True
        Me.SimpleButton32.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton32.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton32.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton32.Location = New System.Drawing.Point(393, 147)
        Me.SimpleButton32.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton32.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton32.Name = "SimpleButton32"
        Me.SimpleButton32.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton32.TabIndex = 270
        Me.SimpleButton32.Text = "مقارنة ربح"
        '
        'SimpleButton42
        '
        Me.SimpleButton42.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton42.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton42.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton42.Appearance.Options.UseBackColor = True
        Me.SimpleButton42.Appearance.Options.UseFont = True
        Me.SimpleButton42.Appearance.Options.UseTextOptions = True
        Me.SimpleButton42.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton42.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton42.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton42.Location = New System.Drawing.Point(393, 108)
        Me.SimpleButton42.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton42.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton42.Name = "SimpleButton42"
        Me.SimpleButton42.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton42.TabIndex = 269
        Me.SimpleButton42.Text = "الاقل ربح"
        '
        'SimpleButton43
        '
        Me.SimpleButton43.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton43.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton43.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton43.Appearance.Options.UseBackColor = True
        Me.SimpleButton43.Appearance.Options.UseFont = True
        Me.SimpleButton43.Appearance.Options.UseTextOptions = True
        Me.SimpleButton43.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton43.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton43.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton43.Location = New System.Drawing.Point(393, 69)
        Me.SimpleButton43.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton43.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton43.Name = "SimpleButton43"
        Me.SimpleButton43.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton43.TabIndex = 268
        Me.SimpleButton43.Text = "الاكثر ربح"
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.ForeColor = System.Drawing.Color.Maroon
        Me.Label12.Location = New System.Drawing.Point(442, 27)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(41, 16)
        Me.Label12.TabIndex = 267
        Me.Label12.Text = "الارباح"
        '
        'SimpleButton41
        '
        Me.SimpleButton41.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton41.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton41.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton41.Appearance.Options.UseBackColor = True
        Me.SimpleButton41.Appearance.Options.UseFont = True
        Me.SimpleButton41.Appearance.Options.UseTextOptions = True
        Me.SimpleButton41.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton41.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton41.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton41.Location = New System.Drawing.Point(559, 331)
        Me.SimpleButton41.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton41.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton41.Name = "SimpleButton41"
        Me.SimpleButton41.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton41.TabIndex = 266
        Me.SimpleButton41.Text = "اجمالي هالك الاصناف"
        '
        'SimpleButton40
        '
        Me.SimpleButton40.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton40.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton40.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton40.Appearance.Options.UseBackColor = True
        Me.SimpleButton40.Appearance.Options.UseFont = True
        Me.SimpleButton40.Appearance.Options.UseTextOptions = True
        Me.SimpleButton40.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton40.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton40.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton40.Location = New System.Drawing.Point(559, 368)
        Me.SimpleButton40.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton40.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton40.Name = "SimpleButton40"
        Me.SimpleButton40.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton40.TabIndex = 265
        Me.SimpleButton40.Text = "اجمالي مسحوبات الاصناف"
        '
        'SimpleButton39
        '
        Me.SimpleButton39.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton39.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton39.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton39.Appearance.Options.UseBackColor = True
        Me.SimpleButton39.Appearance.Options.UseFont = True
        Me.SimpleButton39.Appearance.Options.UseTextOptions = True
        Me.SimpleButton39.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton39.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton39.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton39.Location = New System.Drawing.Point(559, 294)
        Me.SimpleButton39.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton39.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton39.Name = "SimpleButton39"
        Me.SimpleButton39.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton39.TabIndex = 264
        Me.SimpleButton39.Text = "اجمالي تحويلات الاصناف"
        '
        'SimpleButton38
        '
        Me.SimpleButton38.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton38.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton38.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton38.Appearance.Options.UseBackColor = True
        Me.SimpleButton38.Appearance.Options.UseFont = True
        Me.SimpleButton38.Appearance.Options.UseTextOptions = True
        Me.SimpleButton38.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton38.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton38.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton38.Location = New System.Drawing.Point(559, 257)
        Me.SimpleButton38.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton38.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton38.Name = "SimpleButton38"
        Me.SimpleButton38.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton38.TabIndex = 263
        Me.SimpleButton38.Text = "اجمالي جرد الاصناف"
        '
        'SimpleButton37
        '
        Me.SimpleButton37.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton37.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton37.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton37.Appearance.Options.UseBackColor = True
        Me.SimpleButton37.Appearance.Options.UseFont = True
        Me.SimpleButton37.Appearance.Options.UseTextOptions = True
        Me.SimpleButton37.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton37.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton37.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton37.Location = New System.Drawing.Point(559, 220)
        Me.SimpleButton37.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton37.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton37.Name = "SimpleButton37"
        Me.SimpleButton37.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton37.TabIndex = 262
        Me.SimpleButton37.Text = "اجمالي مشتريات الاصناف"
        '
        'SimpleButton36
        '
        Me.SimpleButton36.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton36.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton36.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton36.Appearance.Options.UseBackColor = True
        Me.SimpleButton36.Appearance.Options.UseFont = True
        Me.SimpleButton36.Appearance.Options.UseTextOptions = True
        Me.SimpleButton36.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton36.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton36.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton36.Location = New System.Drawing.Point(559, 183)
        Me.SimpleButton36.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton36.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton36.Name = "SimpleButton36"
        Me.SimpleButton36.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton36.TabIndex = 261
        Me.SimpleButton36.Text = "اجمالي مبيعات الاصناف"
        '
        'SimpleButton33
        '
        Me.SimpleButton33.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton33.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton33.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton33.Appearance.Options.UseBackColor = True
        Me.SimpleButton33.Appearance.Options.UseFont = True
        Me.SimpleButton33.Appearance.Options.UseTextOptions = True
        Me.SimpleButton33.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton33.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton33.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton33.Location = New System.Drawing.Point(559, 146)
        Me.SimpleButton33.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton33.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton33.Name = "SimpleButton33"
        Me.SimpleButton33.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton33.TabIndex = 259
        Me.SimpleButton33.Text = "احصائيات اصناف"
        '
        'SimpleButton34
        '
        Me.SimpleButton34.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton34.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton34.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton34.Appearance.Options.UseBackColor = True
        Me.SimpleButton34.Appearance.Options.UseFont = True
        Me.SimpleButton34.Appearance.Options.UseTextOptions = True
        Me.SimpleButton34.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton34.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton34.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton34.Location = New System.Drawing.Point(559, 107)
        Me.SimpleButton34.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton34.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton34.Name = "SimpleButton34"
        Me.SimpleButton34.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton34.TabIndex = 258
        Me.SimpleButton34.Text = "كشف تفصيلي"
        '
        'SimpleButton35
        '
        Me.SimpleButton35.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton35.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton35.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton35.Appearance.Options.UseBackColor = True
        Me.SimpleButton35.Appearance.Options.UseFont = True
        Me.SimpleButton35.Appearance.Options.UseTextOptions = True
        Me.SimpleButton35.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton35.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton35.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton35.Location = New System.Drawing.Point(559, 69)
        Me.SimpleButton35.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton35.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton35.Name = "SimpleButton35"
        Me.SimpleButton35.Size = New System.Drawing.Size(152, 31)
        Me.SimpleButton35.TabIndex = 257
        Me.SimpleButton35.Text = "حركة الاصناف"
        '
        'Label11
        '
        Me.Label11.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.ForeColor = System.Drawing.Color.Maroon
        Me.Label11.Location = New System.Drawing.Point(608, 27)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(46, 16)
        Me.Label11.TabIndex = 256
        Me.Label11.Text = "الاصناف"
        '
        'SimpleButton31
        '
        Me.SimpleButton31.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton31.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton31.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton31.Appearance.Options.UseBackColor = True
        Me.SimpleButton31.Appearance.Options.UseFont = True
        Me.SimpleButton31.Appearance.Options.UseTextOptions = True
        Me.SimpleButton31.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton31.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton31.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton31.Location = New System.Drawing.Point(736, 184)
        Me.SimpleButton31.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton31.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton31.Name = "SimpleButton31"
        Me.SimpleButton31.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton31.TabIndex = 255
        Me.SimpleButton31.Text = "الكمية السالبة"
        '
        'SimpleButton25
        '
        Me.SimpleButton25.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton25.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton25.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton25.Appearance.Options.UseBackColor = True
        Me.SimpleButton25.Appearance.Options.UseFont = True
        Me.SimpleButton25.Appearance.Options.UseTextOptions = True
        Me.SimpleButton25.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton25.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton25.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton25.Location = New System.Drawing.Point(736, 147)
        Me.SimpleButton25.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton25.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton25.Name = "SimpleButton25"
        Me.SimpleButton25.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton25.TabIndex = 254
        Me.SimpleButton25.Text = "الكمية الصفرية"
        '
        'SimpleButton29
        '
        Me.SimpleButton29.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton29.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton29.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton29.Appearance.Options.UseBackColor = True
        Me.SimpleButton29.Appearance.Options.UseFont = True
        Me.SimpleButton29.Appearance.Options.UseTextOptions = True
        Me.SimpleButton29.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton29.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton29.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton29.Location = New System.Drawing.Point(736, 108)
        Me.SimpleButton29.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton29.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton29.Name = "SimpleButton29"
        Me.SimpleButton29.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton29.TabIndex = 253
        Me.SimpleButton29.Text = "تجاوزت  الحد الادني"
        '
        'SimpleButton30
        '
        Me.SimpleButton30.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton30.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton30.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton30.Appearance.Options.UseBackColor = True
        Me.SimpleButton30.Appearance.Options.UseFont = True
        Me.SimpleButton30.Appearance.Options.UseTextOptions = True
        Me.SimpleButton30.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton30.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton30.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton30.Location = New System.Drawing.Point(736, 69)
        Me.SimpleButton30.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton30.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton30.Name = "SimpleButton30"
        Me.SimpleButton30.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton30.TabIndex = 252
        Me.SimpleButton30.Text = "الاصناف الراكدة"
        '
        'Label10
        '
        Me.Label10.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.ForeColor = System.Drawing.Color.Maroon
        Me.Label10.Location = New System.Drawing.Point(785, 27)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(49, 16)
        Me.Label10.TabIndex = 251
        Me.Label10.Text = "التنبيهات"
        '
        'XtraTabPage6
        '
        Me.XtraTabPage6.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.XtraTabPage6.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage6.Appearance.Header.Options.UseTextOptions = True
        Me.XtraTabPage6.Appearance.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton93)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton94)
        Me.XtraTabPage6.Controls.Add(Me.Label34)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton89)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton90)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton91)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton92)
        Me.XtraTabPage6.Controls.Add(Me.Label32)
        Me.XtraTabPage6.Controls.Add(Me.Label33)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton87)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton88)
        Me.XtraTabPage6.Controls.Add(Me.Label31)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton85)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton86)
        Me.XtraTabPage6.Controls.Add(Me.Label30)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton83)
        Me.XtraTabPage6.Controls.Add(Me.Label29)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton79)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton80)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton81)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton82)
        Me.XtraTabPage6.Controls.Add(Me.Label28)
        Me.XtraTabPage6.Controls.Add(Me.Label27)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton78)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton77)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton76)
        Me.XtraTabPage6.Controls.Add(Me.Label26)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton73)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton75)
        Me.XtraTabPage6.Controls.Add(Me.Label24)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton72)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton67)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton70)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton69)
        Me.XtraTabPage6.Controls.Add(Me.Label22)
        Me.XtraTabPage6.Controls.Add(Me.Label23)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton64)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton66)
        Me.XtraTabPage6.Controls.Add(Me.Label21)
        Me.XtraTabPage6.Controls.Add(Me.Label20)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton63)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton57)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton58)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton62)
        Me.XtraTabPage6.Controls.Add(Me.Label19)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton59)
        Me.XtraTabPage6.Controls.Add(Me.SimpleButton61)
        Me.XtraTabPage6.Controls.Add(Me.Label18)
        Me.XtraTabPage6.ImageOptions.Image = CType(resources.GetObject("XtraTabPage6.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage6.Name = "XtraTabPage6"
        Me.XtraTabPage6.Size = New System.Drawing.Size(921, 444)
        Me.XtraTabPage6.Text = "المبيعات"
        '
        'SimpleButton93
        '
        Me.SimpleButton93.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton93.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton93.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton93.Appearance.Options.UseBackColor = True
        Me.SimpleButton93.Appearance.Options.UseFont = True
        Me.SimpleButton93.Appearance.Options.UseTextOptions = True
        Me.SimpleButton93.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton93.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton93.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton93.Location = New System.Drawing.Point(183, 350)
        Me.SimpleButton93.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton93.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton93.Name = "SimpleButton93"
        Me.SimpleButton93.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton93.TabIndex = 331
        Me.SimpleButton93.Text = "مقارنة م.المبيعات"
        '
        'SimpleButton94
        '
        Me.SimpleButton94.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton94.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton94.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton94.Appearance.Options.UseBackColor = True
        Me.SimpleButton94.Appearance.Options.UseFont = True
        Me.SimpleButton94.Appearance.Options.UseTextOptions = True
        Me.SimpleButton94.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton94.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton94.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton94.Location = New System.Drawing.Point(183, 311)
        Me.SimpleButton94.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton94.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton94.Name = "SimpleButton94"
        Me.SimpleButton94.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton94.TabIndex = 330
        Me.SimpleButton94.Text = "الاكثر م.المبيعات"
        '
        'Label34
        '
        Me.Label34.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label34.AutoSize = True
        Me.Label34.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label34.ForeColor = System.Drawing.Color.Maroon
        Me.Label34.Location = New System.Drawing.Point(224, 283)
        Me.Label34.Name = "Label34"
        Me.Label34.Size = New System.Drawing.Size(43, 16)
        Me.Label34.TabIndex = 329
        Me.Label34.Text = "المندوب"
        '
        'SimpleButton89
        '
        Me.SimpleButton89.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton89.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton89.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton89.Appearance.Options.UseBackColor = True
        Me.SimpleButton89.Appearance.Options.UseFont = True
        Me.SimpleButton89.Appearance.Options.UseTextOptions = True
        Me.SimpleButton89.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton89.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton89.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton89.Location = New System.Drawing.Point(28, 302)
        Me.SimpleButton89.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton89.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton89.Name = "SimpleButton89"
        Me.SimpleButton89.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton89.TabIndex = 327
        Me.SimpleButton89.Text = "مقارنة المبيعات"
        '
        'SimpleButton90
        '
        Me.SimpleButton90.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton90.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton90.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton90.Appearance.Options.UseBackColor = True
        Me.SimpleButton90.Appearance.Options.UseFont = True
        Me.SimpleButton90.Appearance.Options.UseTextOptions = True
        Me.SimpleButton90.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton90.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton90.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton90.Location = New System.Drawing.Point(28, 398)
        Me.SimpleButton90.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton90.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton90.Name = "SimpleButton90"
        Me.SimpleButton90.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton90.TabIndex = 328
        Me.SimpleButton90.Text = "مقارنة المبيعات"
        '
        'SimpleButton91
        '
        Me.SimpleButton91.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton91.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton91.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton91.Appearance.Options.UseBackColor = True
        Me.SimpleButton91.Appearance.Options.UseFont = True
        Me.SimpleButton91.Appearance.Options.UseTextOptions = True
        Me.SimpleButton91.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton91.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton91.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton91.Location = New System.Drawing.Point(28, 265)
        Me.SimpleButton91.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton91.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton91.Name = "SimpleButton91"
        Me.SimpleButton91.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton91.TabIndex = 325
        Me.SimpleButton91.Text = "الاكثر مبيعا"
        '
        'SimpleButton92
        '
        Me.SimpleButton92.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton92.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton92.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton92.Appearance.Options.UseBackColor = True
        Me.SimpleButton92.Appearance.Options.UseFont = True
        Me.SimpleButton92.Appearance.Options.UseTextOptions = True
        Me.SimpleButton92.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton92.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton92.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton92.Location = New System.Drawing.Point(28, 361)
        Me.SimpleButton92.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton92.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton92.Name = "SimpleButton92"
        Me.SimpleButton92.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton92.TabIndex = 326
        Me.SimpleButton92.Text = "الاكثر مبيعا"
        '
        'Label32
        '
        Me.Label32.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label32.AutoSize = True
        Me.Label32.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label32.ForeColor = System.Drawing.Color.Maroon
        Me.Label32.Location = New System.Drawing.Point(69, 333)
        Me.Label32.Name = "Label32"
        Me.Label32.Size = New System.Drawing.Size(50, 16)
        Me.Label32.TabIndex = 324
        Me.Label32.Text = "المحافظة"
        '
        'Label33
        '
        Me.Label33.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label33.AutoSize = True
        Me.Label33.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label33.ForeColor = System.Drawing.Color.Maroon
        Me.Label33.Location = New System.Drawing.Point(82, 238)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(37, 16)
        Me.Label33.TabIndex = 323
        Me.Label33.Text = "المدينة"
        '
        'SimpleButton87
        '
        Me.SimpleButton87.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton87.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton87.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton87.Appearance.Options.UseBackColor = True
        Me.SimpleButton87.Appearance.Options.UseFont = True
        Me.SimpleButton87.Appearance.Options.UseTextOptions = True
        Me.SimpleButton87.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton87.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton87.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton87.Location = New System.Drawing.Point(28, 202)
        Me.SimpleButton87.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton87.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton87.Name = "SimpleButton87"
        Me.SimpleButton87.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton87.TabIndex = 322
        Me.SimpleButton87.Text = "مقارنة المبيعات"
        '
        'SimpleButton88
        '
        Me.SimpleButton88.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton88.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton88.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton88.Appearance.Options.UseBackColor = True
        Me.SimpleButton88.Appearance.Options.UseFont = True
        Me.SimpleButton88.Appearance.Options.UseTextOptions = True
        Me.SimpleButton88.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton88.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton88.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton88.Location = New System.Drawing.Point(28, 165)
        Me.SimpleButton88.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton88.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton88.Name = "SimpleButton88"
        Me.SimpleButton88.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton88.TabIndex = 321
        Me.SimpleButton88.Text = "الاكثر مبيعا"
        '
        'Label31
        '
        Me.Label31.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label31.AutoSize = True
        Me.Label31.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label31.ForeColor = System.Drawing.Color.Maroon
        Me.Label31.Location = New System.Drawing.Point(75, 137)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(51, 16)
        Me.Label31.TabIndex = 320
        Me.Label31.Text = "المجموعة"
        '
        'SimpleButton85
        '
        Me.SimpleButton85.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton85.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton85.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton85.Appearance.Options.UseBackColor = True
        Me.SimpleButton85.Appearance.Options.UseFont = True
        Me.SimpleButton85.Appearance.Options.UseTextOptions = True
        Me.SimpleButton85.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton85.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton85.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton85.Location = New System.Drawing.Point(28, 99)
        Me.SimpleButton85.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton85.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton85.Name = "SimpleButton85"
        Me.SimpleButton85.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton85.TabIndex = 319
        Me.SimpleButton85.Text = "مقارنة م.مبيعات"
        '
        'SimpleButton86
        '
        Me.SimpleButton86.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton86.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton86.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton86.Appearance.Options.UseBackColor = True
        Me.SimpleButton86.Appearance.Options.UseFont = True
        Me.SimpleButton86.Appearance.Options.UseTextOptions = True
        Me.SimpleButton86.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton86.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton86.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton86.Location = New System.Drawing.Point(28, 62)
        Me.SimpleButton86.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton86.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton86.Name = "SimpleButton86"
        Me.SimpleButton86.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton86.TabIndex = 318
        Me.SimpleButton86.Text = "المخزن الاكثر م.مبيعا"
        '
        'Label30
        '
        Me.Label30.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label30.AutoSize = True
        Me.Label30.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.ForeColor = System.Drawing.Color.Maroon
        Me.Label30.Location = New System.Drawing.Point(54, 34)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(86, 16)
        Me.Label30.TabIndex = 317
        Me.Label30.Text = "المخازن / الفروع"
        '
        'SimpleButton83
        '
        Me.SimpleButton83.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton83.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton83.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton83.Appearance.Options.UseBackColor = True
        Me.SimpleButton83.Appearance.Options.UseFont = True
        Me.SimpleButton83.Appearance.Options.UseTextOptions = True
        Me.SimpleButton83.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton83.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton83.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton83.Location = New System.Drawing.Point(183, 243)
        Me.SimpleButton83.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton83.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton83.Name = "SimpleButton83"
        Me.SimpleButton83.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton83.TabIndex = 315
        Me.SimpleButton83.Text = "مقارنة م.المبيعات"
        '
        'Label29
        '
        Me.Label29.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label29.AutoSize = True
        Me.Label29.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label29.ForeColor = System.Drawing.Color.Maroon
        Me.Label29.Location = New System.Drawing.Point(229, 215)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(38, 16)
        Me.Label29.TabIndex = 314
        Me.Label29.Text = "مقارنة"
        '
        'SimpleButton79
        '
        Me.SimpleButton79.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton79.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton79.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton79.Appearance.Options.UseBackColor = True
        Me.SimpleButton79.Appearance.Options.UseFont = True
        Me.SimpleButton79.Appearance.Options.UseTextOptions = True
        Me.SimpleButton79.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton79.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton79.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton79.Location = New System.Drawing.Point(183, 178)
        Me.SimpleButton79.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton79.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton79.Name = "SimpleButton79"
        Me.SimpleButton79.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton79.TabIndex = 313
        Me.SimpleButton79.Text = "اجمالي م.مبيعات"
        '
        'SimpleButton80
        '
        Me.SimpleButton80.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton80.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton80.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton80.Appearance.Options.UseBackColor = True
        Me.SimpleButton80.Appearance.Options.UseFont = True
        Me.SimpleButton80.Appearance.Options.UseTextOptions = True
        Me.SimpleButton80.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton80.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton80.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton80.Location = New System.Drawing.Point(183, 141)
        Me.SimpleButton80.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton80.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton80.Name = "SimpleButton80"
        Me.SimpleButton80.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton80.TabIndex = 312
        Me.SimpleButton80.Text = "السنة"
        '
        'SimpleButton81
        '
        Me.SimpleButton81.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton81.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton81.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton81.Appearance.Options.UseBackColor = True
        Me.SimpleButton81.Appearance.Options.UseFont = True
        Me.SimpleButton81.Appearance.Options.UseTextOptions = True
        Me.SimpleButton81.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton81.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton81.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton81.Location = New System.Drawing.Point(183, 101)
        Me.SimpleButton81.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton81.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton81.Name = "SimpleButton81"
        Me.SimpleButton81.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton81.TabIndex = 311
        Me.SimpleButton81.Text = "الشهر"
        '
        'SimpleButton82
        '
        Me.SimpleButton82.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton82.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton82.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton82.Appearance.Options.UseBackColor = True
        Me.SimpleButton82.Appearance.Options.UseFont = True
        Me.SimpleButton82.Appearance.Options.UseTextOptions = True
        Me.SimpleButton82.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton82.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton82.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton82.Location = New System.Drawing.Point(183, 62)
        Me.SimpleButton82.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton82.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton82.Name = "SimpleButton82"
        Me.SimpleButton82.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton82.TabIndex = 310
        Me.SimpleButton82.Text = "اليوم"
        '
        'Label28
        '
        Me.Label28.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label28.AutoSize = True
        Me.Label28.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label28.ForeColor = System.Drawing.Color.Maroon
        Me.Label28.Location = New System.Drawing.Point(211, 34)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(82, 16)
        Me.Label28.TabIndex = 309
        Me.Label28.Text = "مرتجع المبيعات"
        '
        'Label27
        '
        Me.Label27.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label27.BackColor = System.Drawing.Color.RoyalBlue
        Me.Label27.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label27.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label27.ForeColor = System.Drawing.Color.White
        Me.Label27.Location = New System.Drawing.Point(6, 1)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(342, 32)
        Me.Label27.TabIndex = 308
        Me.Label27.Text = "مرتجع المبيعات"
        Me.Label27.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'SimpleButton78
        '
        Me.SimpleButton78.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton78.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton78.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton78.Appearance.Options.UseBackColor = True
        Me.SimpleButton78.Appearance.Options.UseFont = True
        Me.SimpleButton78.Appearance.Options.UseTextOptions = True
        Me.SimpleButton78.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton78.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton78.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton78.Location = New System.Drawing.Point(731, 311)
        Me.SimpleButton78.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton78.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton78.Name = "SimpleButton78"
        Me.SimpleButton78.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton78.TabIndex = 307
        Me.SimpleButton78.Text = "مقارنة ارباح المخازن"
        '
        'SimpleButton77
        '
        Me.SimpleButton77.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton77.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton77.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton77.Appearance.Options.UseBackColor = True
        Me.SimpleButton77.Appearance.Options.UseFont = True
        Me.SimpleButton77.Appearance.Options.UseTextOptions = True
        Me.SimpleButton77.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton77.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton77.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton77.Location = New System.Drawing.Point(731, 274)
        Me.SimpleButton77.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton77.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton77.Name = "SimpleButton77"
        Me.SimpleButton77.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton77.TabIndex = 306
        Me.SimpleButton77.Text = "مقارنة الارباح"
        '
        'SimpleButton76
        '
        Me.SimpleButton76.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton76.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton76.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton76.Appearance.Options.UseBackColor = True
        Me.SimpleButton76.Appearance.Options.UseFont = True
        Me.SimpleButton76.Appearance.Options.UseTextOptions = True
        Me.SimpleButton76.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton76.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton76.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton76.Location = New System.Drawing.Point(731, 237)
        Me.SimpleButton76.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton76.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton76.Name = "SimpleButton76"
        Me.SimpleButton76.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton76.TabIndex = 305
        Me.SimpleButton76.Text = "مقارنة المبيعات"
        '
        'Label26
        '
        Me.Label26.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label26.AutoSize = True
        Me.Label26.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.Maroon
        Me.Label26.Location = New System.Drawing.Point(771, 209)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(64, 16)
        Me.Label26.TabIndex = 304
        Me.Label26.Text = "مقارنة عامة"
        '
        'SimpleButton73
        '
        Me.SimpleButton73.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton73.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton73.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton73.Appearance.Options.UseBackColor = True
        Me.SimpleButton73.Appearance.Options.UseFont = True
        Me.SimpleButton73.Appearance.Options.UseTextOptions = True
        Me.SimpleButton73.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton73.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton73.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton73.Location = New System.Drawing.Point(737, 404)
        Me.SimpleButton73.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton73.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton73.Name = "SimpleButton73"
        Me.SimpleButton73.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton73.TabIndex = 297
        Me.SimpleButton73.Text = "مقارنة المبيعات"
        '
        'SimpleButton75
        '
        Me.SimpleButton75.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton75.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton75.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton75.Appearance.Options.UseBackColor = True
        Me.SimpleButton75.Appearance.Options.UseFont = True
        Me.SimpleButton75.Appearance.Options.UseTextOptions = True
        Me.SimpleButton75.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton75.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton75.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton75.Location = New System.Drawing.Point(737, 365)
        Me.SimpleButton75.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton75.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton75.Name = "SimpleButton75"
        Me.SimpleButton75.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton75.TabIndex = 295
        Me.SimpleButton75.Text = "الاكثر مبيعا"
        '
        'Label24
        '
        Me.Label24.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.ForeColor = System.Drawing.Color.Maroon
        Me.Label24.Location = New System.Drawing.Point(778, 342)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(43, 16)
        Me.Label24.TabIndex = 294
        Me.Label24.Text = "المندوب"
        '
        'SimpleButton72
        '
        Me.SimpleButton72.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton72.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton72.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton72.Appearance.Options.UseBackColor = True
        Me.SimpleButton72.Appearance.Options.UseFont = True
        Me.SimpleButton72.Appearance.Options.UseTextOptions = True
        Me.SimpleButton72.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton72.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton72.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton72.Location = New System.Drawing.Point(575, 304)
        Me.SimpleButton72.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton72.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton72.Name = "SimpleButton72"
        Me.SimpleButton72.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton72.TabIndex = 293
        Me.SimpleButton72.Text = "مقارنة المبيعات"
        '
        'SimpleButton67
        '
        Me.SimpleButton67.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton67.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton67.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton67.Appearance.Options.UseBackColor = True
        Me.SimpleButton67.Appearance.Options.UseFont = True
        Me.SimpleButton67.Appearance.Options.UseTextOptions = True
        Me.SimpleButton67.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton67.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton67.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton67.Location = New System.Drawing.Point(575, 400)
        Me.SimpleButton67.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton67.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton67.Name = "SimpleButton67"
        Me.SimpleButton67.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton67.TabIndex = 293
        Me.SimpleButton67.Text = "مقارنة المبيعات"
        '
        'SimpleButton70
        '
        Me.SimpleButton70.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton70.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton70.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton70.Appearance.Options.UseBackColor = True
        Me.SimpleButton70.Appearance.Options.UseFont = True
        Me.SimpleButton70.Appearance.Options.UseTextOptions = True
        Me.SimpleButton70.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton70.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton70.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton70.Location = New System.Drawing.Point(575, 267)
        Me.SimpleButton70.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton70.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton70.Name = "SimpleButton70"
        Me.SimpleButton70.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton70.TabIndex = 291
        Me.SimpleButton70.Text = "الاكثر مبيعا"
        '
        'SimpleButton69
        '
        Me.SimpleButton69.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton69.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton69.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton69.Appearance.Options.UseBackColor = True
        Me.SimpleButton69.Appearance.Options.UseFont = True
        Me.SimpleButton69.Appearance.Options.UseTextOptions = True
        Me.SimpleButton69.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton69.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton69.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton69.Location = New System.Drawing.Point(575, 363)
        Me.SimpleButton69.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton69.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton69.Name = "SimpleButton69"
        Me.SimpleButton69.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton69.TabIndex = 291
        Me.SimpleButton69.Text = "الاكثر مبيعا"
        '
        'Label22
        '
        Me.Label22.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label22.AutoSize = True
        Me.Label22.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.ForeColor = System.Drawing.Color.Maroon
        Me.Label22.Location = New System.Drawing.Point(616, 335)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(50, 16)
        Me.Label22.TabIndex = 290
        Me.Label22.Text = "المحافظة"
        '
        'Label23
        '
        Me.Label23.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label23.ForeColor = System.Drawing.Color.Maroon
        Me.Label23.Location = New System.Drawing.Point(629, 240)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(37, 16)
        Me.Label23.TabIndex = 286
        Me.Label23.Text = "المدينة"
        '
        'SimpleButton64
        '
        Me.SimpleButton64.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton64.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton64.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton64.Appearance.Options.UseBackColor = True
        Me.SimpleButton64.Appearance.Options.UseFont = True
        Me.SimpleButton64.Appearance.Options.UseTextOptions = True
        Me.SimpleButton64.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton64.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton64.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton64.Location = New System.Drawing.Point(575, 204)
        Me.SimpleButton64.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton64.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton64.Name = "SimpleButton64"
        Me.SimpleButton64.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton64.TabIndex = 285
        Me.SimpleButton64.Text = "مقارنة المبيعات"
        '
        'SimpleButton66
        '
        Me.SimpleButton66.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton66.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton66.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton66.Appearance.Options.UseBackColor = True
        Me.SimpleButton66.Appearance.Options.UseFont = True
        Me.SimpleButton66.Appearance.Options.UseTextOptions = True
        Me.SimpleButton66.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton66.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton66.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton66.Location = New System.Drawing.Point(575, 167)
        Me.SimpleButton66.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton66.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton66.Name = "SimpleButton66"
        Me.SimpleButton66.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton66.TabIndex = 283
        Me.SimpleButton66.Text = "الاكثر مبيعا"
        '
        'Label21
        '
        Me.Label21.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.ForeColor = System.Drawing.Color.Maroon
        Me.Label21.Location = New System.Drawing.Point(622, 139)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(51, 16)
        Me.Label21.TabIndex = 282
        Me.Label21.Text = "المجموعة"
        '
        'Label20
        '
        Me.Label20.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label20.BackColor = System.Drawing.Color.RoyalBlue
        Me.Label20.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label20.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.White
        Me.Label20.Location = New System.Drawing.Point(538, 1)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(363, 32)
        Me.Label20.TabIndex = 281
        Me.Label20.Text = "المبيعات"
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'SimpleButton63
        '
        Me.SimpleButton63.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton63.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton63.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton63.Appearance.Options.UseBackColor = True
        Me.SimpleButton63.Appearance.Options.UseFont = True
        Me.SimpleButton63.Appearance.Options.UseTextOptions = True
        Me.SimpleButton63.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton63.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton63.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton63.Location = New System.Drawing.Point(731, 173)
        Me.SimpleButton63.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton63.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton63.Name = "SimpleButton63"
        Me.SimpleButton63.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton63.TabIndex = 280
        Me.SimpleButton63.Text = "اجمالي مبيعات"
        '
        'SimpleButton57
        '
        Me.SimpleButton57.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton57.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton57.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton57.Appearance.Options.UseBackColor = True
        Me.SimpleButton57.Appearance.Options.UseFont = True
        Me.SimpleButton57.Appearance.Options.UseTextOptions = True
        Me.SimpleButton57.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton57.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton57.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton57.Location = New System.Drawing.Point(731, 136)
        Me.SimpleButton57.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton57.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton57.Name = "SimpleButton57"
        Me.SimpleButton57.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton57.TabIndex = 279
        Me.SimpleButton57.Text = "السنة"
        '
        'SimpleButton58
        '
        Me.SimpleButton58.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton58.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton58.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton58.Appearance.Options.UseBackColor = True
        Me.SimpleButton58.Appearance.Options.UseFont = True
        Me.SimpleButton58.Appearance.Options.UseTextOptions = True
        Me.SimpleButton58.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton58.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton58.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton58.Location = New System.Drawing.Point(731, 97)
        Me.SimpleButton58.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton58.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton58.Name = "SimpleButton58"
        Me.SimpleButton58.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton58.TabIndex = 278
        Me.SimpleButton58.Text = "الشهر"
        '
        'SimpleButton62
        '
        Me.SimpleButton62.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton62.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton62.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton62.Appearance.Options.UseBackColor = True
        Me.SimpleButton62.Appearance.Options.UseFont = True
        Me.SimpleButton62.Appearance.Options.UseTextOptions = True
        Me.SimpleButton62.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton62.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton62.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton62.Location = New System.Drawing.Point(731, 58)
        Me.SimpleButton62.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton62.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton62.Name = "SimpleButton62"
        Me.SimpleButton62.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton62.TabIndex = 277
        Me.SimpleButton62.Text = "اليوم"
        '
        'Label19
        '
        Me.Label19.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.ForeColor = System.Drawing.Color.Maroon
        Me.Label19.Location = New System.Drawing.Point(771, 30)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(47, 16)
        Me.Label19.TabIndex = 276
        Me.Label19.Text = "المبيعات"
        '
        'SimpleButton59
        '
        Me.SimpleButton59.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton59.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton59.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton59.Appearance.Options.UseBackColor = True
        Me.SimpleButton59.Appearance.Options.UseFont = True
        Me.SimpleButton59.Appearance.Options.UseTextOptions = True
        Me.SimpleButton59.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton59.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton59.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton59.Location = New System.Drawing.Point(575, 97)
        Me.SimpleButton59.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton59.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton59.Name = "SimpleButton59"
        Me.SimpleButton59.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton59.TabIndex = 275
        Me.SimpleButton59.Text = "مقارنة مبيعات"
        '
        'SimpleButton61
        '
        Me.SimpleButton61.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton61.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton61.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton61.Appearance.Options.UseBackColor = True
        Me.SimpleButton61.Appearance.Options.UseFont = True
        Me.SimpleButton61.Appearance.Options.UseTextOptions = True
        Me.SimpleButton61.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton61.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton61.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton61.Location = New System.Drawing.Point(575, 60)
        Me.SimpleButton61.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton61.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton61.Name = "SimpleButton61"
        Me.SimpleButton61.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton61.TabIndex = 273
        Me.SimpleButton61.Text = "المخزن الاكثر مبيعا"
        '
        'Label18
        '
        Me.Label18.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.ForeColor = System.Drawing.Color.Maroon
        Me.Label18.Location = New System.Drawing.Point(601, 32)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(86, 16)
        Me.Label18.TabIndex = 272
        Me.Label18.Text = "المخازن / الفروع"
        '
        'XtraTabPage7
        '
        Me.XtraTabPage7.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.XtraTabPage7.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage7.Appearance.Header.Options.UseTextOptions = True
        Me.XtraTabPage7.Appearance.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton60)
        Me.XtraTabPage7.Controls.Add(Me.Label35)
        Me.XtraTabPage7.Controls.Add(Me.Label36)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton65)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton71)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton74)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton95)
        Me.XtraTabPage7.Controls.Add(Me.Label37)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton96)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton97)
        Me.XtraTabPage7.Controls.Add(Me.Label38)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton68)
        Me.XtraTabPage7.Controls.Add(Me.Label25)
        Me.XtraTabPage7.Controls.Add(Me.Label39)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton101)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton102)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton103)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton104)
        Me.XtraTabPage7.Controls.Add(Me.Label40)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton105)
        Me.XtraTabPage7.Controls.Add(Me.SimpleButton106)
        Me.XtraTabPage7.Controls.Add(Me.Label41)
        Me.XtraTabPage7.ImageOptions.Image = CType(resources.GetObject("XtraTabPage7.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage7.Name = "XtraTabPage7"
        Me.XtraTabPage7.Size = New System.Drawing.Size(921, 444)
        Me.XtraTabPage7.Text = "المشتريات"
        '
        'SimpleButton60
        '
        Me.SimpleButton60.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton60.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton60.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton60.Appearance.Options.UseBackColor = True
        Me.SimpleButton60.Appearance.Options.UseFont = True
        Me.SimpleButton60.Appearance.Options.UseTextOptions = True
        Me.SimpleButton60.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton60.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton60.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton60.Location = New System.Drawing.Point(430, 256)
        Me.SimpleButton60.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton60.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton60.Name = "SimpleButton60"
        Me.SimpleButton60.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton60.TabIndex = 341
        Me.SimpleButton60.Text = "مقارنة م.مشتريات"
        '
        'Label35
        '
        Me.Label35.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label35.AutoSize = True
        Me.Label35.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label35.ForeColor = System.Drawing.Color.Maroon
        Me.Label35.Location = New System.Drawing.Point(470, 228)
        Me.Label35.Name = "Label35"
        Me.Label35.Size = New System.Drawing.Size(64, 16)
        Me.Label35.TabIndex = 340
        Me.Label35.Text = "مقارنة عامة"
        '
        'Label36
        '
        Me.Label36.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label36.BackColor = System.Drawing.Color.RoyalBlue
        Me.Label36.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label36.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label36.ForeColor = System.Drawing.Color.White
        Me.Label36.Location = New System.Drawing.Point(402, 13)
        Me.Label36.Name = "Label36"
        Me.Label36.Size = New System.Drawing.Size(198, 32)
        Me.Label36.TabIndex = 339
        Me.Label36.Text = "مشرتجع المشتريات"
        Me.Label36.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'SimpleButton65
        '
        Me.SimpleButton65.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton65.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton65.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton65.Appearance.Options.UseBackColor = True
        Me.SimpleButton65.Appearance.Options.UseFont = True
        Me.SimpleButton65.Appearance.Options.UseTextOptions = True
        Me.SimpleButton65.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton65.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton65.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton65.Location = New System.Drawing.Point(430, 192)
        Me.SimpleButton65.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton65.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton65.Name = "SimpleButton65"
        Me.SimpleButton65.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton65.TabIndex = 338
        Me.SimpleButton65.Text = "اجمالي م.مشتريات"
        '
        'SimpleButton71
        '
        Me.SimpleButton71.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton71.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton71.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton71.Appearance.Options.UseBackColor = True
        Me.SimpleButton71.Appearance.Options.UseFont = True
        Me.SimpleButton71.Appearance.Options.UseTextOptions = True
        Me.SimpleButton71.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton71.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton71.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton71.Location = New System.Drawing.Point(430, 155)
        Me.SimpleButton71.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton71.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton71.Name = "SimpleButton71"
        Me.SimpleButton71.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton71.TabIndex = 337
        Me.SimpleButton71.Text = "السنة"
        '
        'SimpleButton74
        '
        Me.SimpleButton74.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton74.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton74.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton74.Appearance.Options.UseBackColor = True
        Me.SimpleButton74.Appearance.Options.UseFont = True
        Me.SimpleButton74.Appearance.Options.UseTextOptions = True
        Me.SimpleButton74.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton74.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton74.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton74.Location = New System.Drawing.Point(430, 116)
        Me.SimpleButton74.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton74.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton74.Name = "SimpleButton74"
        Me.SimpleButton74.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton74.TabIndex = 336
        Me.SimpleButton74.Text = "الشهر"
        '
        'SimpleButton95
        '
        Me.SimpleButton95.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton95.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton95.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton95.Appearance.Options.UseBackColor = True
        Me.SimpleButton95.Appearance.Options.UseFont = True
        Me.SimpleButton95.Appearance.Options.UseTextOptions = True
        Me.SimpleButton95.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton95.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton95.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton95.Location = New System.Drawing.Point(430, 77)
        Me.SimpleButton95.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton95.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton95.Name = "SimpleButton95"
        Me.SimpleButton95.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton95.TabIndex = 335
        Me.SimpleButton95.Text = "اليوم"
        '
        'Label37
        '
        Me.Label37.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label37.AutoSize = True
        Me.Label37.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label37.ForeColor = System.Drawing.Color.Maroon
        Me.Label37.Location = New System.Drawing.Point(470, 49)
        Me.Label37.Name = "Label37"
        Me.Label37.Size = New System.Drawing.Size(64, 16)
        Me.Label37.TabIndex = 334
        Me.Label37.Text = "م.المشتريات"
        '
        'SimpleButton96
        '
        Me.SimpleButton96.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton96.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton96.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton96.Appearance.Options.UseBackColor = True
        Me.SimpleButton96.Appearance.Options.UseFont = True
        Me.SimpleButton96.Appearance.Options.UseTextOptions = True
        Me.SimpleButton96.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton96.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton96.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton96.Location = New System.Drawing.Point(430, 354)
        Me.SimpleButton96.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton96.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton96.Name = "SimpleButton96"
        Me.SimpleButton96.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton96.TabIndex = 333
        Me.SimpleButton96.Text = "مقارنة م.مشتريات"
        '
        'SimpleButton97
        '
        Me.SimpleButton97.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton97.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton97.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton97.Appearance.Options.UseBackColor = True
        Me.SimpleButton97.Appearance.Options.UseFont = True
        Me.SimpleButton97.Appearance.Options.UseTextOptions = True
        Me.SimpleButton97.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton97.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton97.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton97.Location = New System.Drawing.Point(430, 317)
        Me.SimpleButton97.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton97.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton97.Name = "SimpleButton97"
        Me.SimpleButton97.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton97.TabIndex = 332
        Me.SimpleButton97.Text = "المخزن الاكثر م.شراء"
        '
        'Label38
        '
        Me.Label38.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label38.AutoSize = True
        Me.Label38.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label38.ForeColor = System.Drawing.Color.Maroon
        Me.Label38.Location = New System.Drawing.Point(456, 289)
        Me.Label38.Name = "Label38"
        Me.Label38.Size = New System.Drawing.Size(86, 16)
        Me.Label38.TabIndex = 331
        Me.Label38.Text = "المخازن / الفروع"
        '
        'SimpleButton68
        '
        Me.SimpleButton68.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton68.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton68.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton68.Appearance.Options.UseBackColor = True
        Me.SimpleButton68.Appearance.Options.UseFont = True
        Me.SimpleButton68.Appearance.Options.UseTextOptions = True
        Me.SimpleButton68.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton68.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton68.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton68.Location = New System.Drawing.Point(727, 256)
        Me.SimpleButton68.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton68.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton68.Name = "SimpleButton68"
        Me.SimpleButton68.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton68.TabIndex = 330
        Me.SimpleButton68.Text = "مقارنة مشتريات"
        '
        'Label25
        '
        Me.Label25.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label25.AutoSize = True
        Me.Label25.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label25.ForeColor = System.Drawing.Color.Maroon
        Me.Label25.Location = New System.Drawing.Point(767, 228)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(64, 16)
        Me.Label25.TabIndex = 329
        Me.Label25.Text = "مقارنة عامة"
        '
        'Label39
        '
        Me.Label39.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label39.BackColor = System.Drawing.Color.RoyalBlue
        Me.Label39.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Label39.Font = New System.Drawing.Font("Microsoft Sans Serif", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label39.ForeColor = System.Drawing.Color.White
        Me.Label39.Location = New System.Drawing.Point(699, 13)
        Me.Label39.Name = "Label39"
        Me.Label39.Size = New System.Drawing.Size(198, 32)
        Me.Label39.TabIndex = 316
        Me.Label39.Text = "المشتريات"
        Me.Label39.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'SimpleButton101
        '
        Me.SimpleButton101.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton101.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton101.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton101.Appearance.Options.UseBackColor = True
        Me.SimpleButton101.Appearance.Options.UseFont = True
        Me.SimpleButton101.Appearance.Options.UseTextOptions = True
        Me.SimpleButton101.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton101.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton101.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton101.Location = New System.Drawing.Point(727, 192)
        Me.SimpleButton101.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton101.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton101.Name = "SimpleButton101"
        Me.SimpleButton101.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton101.TabIndex = 315
        Me.SimpleButton101.Text = "اجمالي مشتريات"
        '
        'SimpleButton102
        '
        Me.SimpleButton102.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton102.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton102.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton102.Appearance.Options.UseBackColor = True
        Me.SimpleButton102.Appearance.Options.UseFont = True
        Me.SimpleButton102.Appearance.Options.UseTextOptions = True
        Me.SimpleButton102.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton102.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton102.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton102.Location = New System.Drawing.Point(727, 155)
        Me.SimpleButton102.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton102.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton102.Name = "SimpleButton102"
        Me.SimpleButton102.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton102.TabIndex = 314
        Me.SimpleButton102.Text = "السنة"
        '
        'SimpleButton103
        '
        Me.SimpleButton103.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton103.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton103.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton103.Appearance.Options.UseBackColor = True
        Me.SimpleButton103.Appearance.Options.UseFont = True
        Me.SimpleButton103.Appearance.Options.UseTextOptions = True
        Me.SimpleButton103.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton103.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton103.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton103.Location = New System.Drawing.Point(727, 116)
        Me.SimpleButton103.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton103.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton103.Name = "SimpleButton103"
        Me.SimpleButton103.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton103.TabIndex = 313
        Me.SimpleButton103.Text = "الشهر"
        '
        'SimpleButton104
        '
        Me.SimpleButton104.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton104.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton104.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton104.Appearance.Options.UseBackColor = True
        Me.SimpleButton104.Appearance.Options.UseFont = True
        Me.SimpleButton104.Appearance.Options.UseTextOptions = True
        Me.SimpleButton104.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton104.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton104.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton104.Location = New System.Drawing.Point(727, 77)
        Me.SimpleButton104.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton104.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton104.Name = "SimpleButton104"
        Me.SimpleButton104.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton104.TabIndex = 312
        Me.SimpleButton104.Text = "اليوم"
        '
        'Label40
        '
        Me.Label40.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label40.AutoSize = True
        Me.Label40.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label40.ForeColor = System.Drawing.Color.Maroon
        Me.Label40.Location = New System.Drawing.Point(767, 49)
        Me.Label40.Name = "Label40"
        Me.Label40.Size = New System.Drawing.Size(56, 16)
        Me.Label40.TabIndex = 311
        Me.Label40.Text = "المشتريات"
        '
        'SimpleButton105
        '
        Me.SimpleButton105.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton105.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton105.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton105.Appearance.Options.UseBackColor = True
        Me.SimpleButton105.Appearance.Options.UseFont = True
        Me.SimpleButton105.Appearance.Options.UseTextOptions = True
        Me.SimpleButton105.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton105.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton105.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton105.Location = New System.Drawing.Point(727, 354)
        Me.SimpleButton105.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton105.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton105.Name = "SimpleButton105"
        Me.SimpleButton105.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton105.TabIndex = 310
        Me.SimpleButton105.Text = "مقارنة مشتريات"
        '
        'SimpleButton106
        '
        Me.SimpleButton106.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton106.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton106.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton106.Appearance.Options.UseBackColor = True
        Me.SimpleButton106.Appearance.Options.UseFont = True
        Me.SimpleButton106.Appearance.Options.UseTextOptions = True
        Me.SimpleButton106.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton106.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton106.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton106.Location = New System.Drawing.Point(727, 317)
        Me.SimpleButton106.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton106.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton106.Name = "SimpleButton106"
        Me.SimpleButton106.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton106.TabIndex = 309
        Me.SimpleButton106.Text = "المخزن الاكثر للشراء"
        '
        'Label41
        '
        Me.Label41.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label41.AutoSize = True
        Me.Label41.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label41.ForeColor = System.Drawing.Color.Maroon
        Me.Label41.Location = New System.Drawing.Point(753, 289)
        Me.Label41.Name = "Label41"
        Me.Label41.Size = New System.Drawing.Size(86, 16)
        Me.Label41.TabIndex = 308
        Me.Label41.Text = "المخازن / الفروع"
        '
        'XtraTabPage8
        '
        Me.XtraTabPage8.Appearance.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.XtraTabPage8.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage8.Appearance.Header.Options.UseTextOptions = True
        Me.XtraTabPage8.Appearance.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.XtraTabPage8.Controls.Add(Me.LabelCostCenter)
        Me.XtraTabPage8.Controls.Add(Me.CostCenterReportBtn)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton125)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton123)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton124)
        Me.XtraTabPage8.Controls.Add(Me.Label48)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton121)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton122)
        Me.XtraTabPage8.Controls.Add(Me.Label3)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton115)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton120)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton119)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton118)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton116)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton108)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton107)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton84)
        Me.XtraTabPage8.Controls.Add(Me.Label44)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton100)
        Me.XtraTabPage8.Controls.Add(Me.Label43)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton98)
        Me.XtraTabPage8.Controls.Add(Me.SimpleButton99)
        Me.XtraTabPage8.Controls.Add(Me.Label42)
        Me.XtraTabPage8.ImageOptions.Image = CType(resources.GetObject("XtraTabPage8.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage8.Name = "XtraTabPage8"
        Me.XtraTabPage8.Size = New System.Drawing.Size(921, 444)
        Me.XtraTabPage8.Text = "الحسابات"
        '
        'SimpleButton125
        '
        Me.SimpleButton125.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton125.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton125.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton125.Appearance.Options.UseBackColor = True
        Me.SimpleButton125.Appearance.Options.UseFont = True
        Me.SimpleButton125.Appearance.Options.UseTextOptions = True
        Me.SimpleButton125.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton125.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton125.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton125.Location = New System.Drawing.Point(592, 192)
        Me.SimpleButton125.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton125.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton125.Name = "SimpleButton125"
        Me.SimpleButton125.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton125.TabIndex = 285
        Me.SimpleButton125.Text = "ميزانية عمومية"
        '
        'SimpleButton123
        '
        Me.SimpleButton123.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton123.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton123.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton123.Appearance.Options.UseBackColor = True
        Me.SimpleButton123.Appearance.Options.UseFont = True
        Me.SimpleButton123.Appearance.Options.UseTextOptions = True
        Me.SimpleButton123.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton123.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton123.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton123.Location = New System.Drawing.Point(275, 76)
        Me.SimpleButton123.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton123.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton123.Name = "SimpleButton123"
        Me.SimpleButton123.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton123.TabIndex = 284
        Me.SimpleButton123.Text = "اجمالي الايرادات"
        '
        'SimpleButton124
        '
        Me.SimpleButton124.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton124.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton124.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton124.Appearance.Options.UseBackColor = True
        Me.SimpleButton124.Appearance.Options.UseFont = True
        Me.SimpleButton124.Appearance.Options.UseTextOptions = True
        Me.SimpleButton124.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton124.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton124.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton124.Location = New System.Drawing.Point(275, 36)
        Me.SimpleButton124.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton124.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton124.Name = "SimpleButton124"
        Me.SimpleButton124.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton124.TabIndex = 283
        Me.SimpleButton124.Text = "مراجعة الايرادات"
        '
        'Label48
        '
        Me.Label48.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label48.AutoSize = True
        Me.Label48.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label48.ForeColor = System.Drawing.Color.Maroon
        Me.Label48.Location = New System.Drawing.Point(316, 8)
        Me.Label48.Name = "Label48"
        Me.Label48.Size = New System.Drawing.Size(47, 16)
        Me.Label48.TabIndex = 282
        Me.Label48.Text = "الايرادات"
        '
        'SimpleButton121
        '
        Me.SimpleButton121.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton121.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton121.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton121.Appearance.Options.UseBackColor = True
        Me.SimpleButton121.Appearance.Options.UseFont = True
        Me.SimpleButton121.Appearance.Options.UseTextOptions = True
        Me.SimpleButton121.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton121.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton121.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton121.Location = New System.Drawing.Point(434, 76)
        Me.SimpleButton121.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton121.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton121.Name = "SimpleButton121"
        Me.SimpleButton121.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton121.TabIndex = 281
        Me.SimpleButton121.Text = "اجمالي المصروفات"
        '
        'SimpleButton122
        '
        Me.SimpleButton122.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton122.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton122.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton122.Appearance.Options.UseBackColor = True
        Me.SimpleButton122.Appearance.Options.UseFont = True
        Me.SimpleButton122.Appearance.Options.UseTextOptions = True
        Me.SimpleButton122.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton122.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton122.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton122.Location = New System.Drawing.Point(434, 36)
        Me.SimpleButton122.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton122.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton122.Name = "SimpleButton122"
        Me.SimpleButton122.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton122.TabIndex = 280
        Me.SimpleButton122.Text = "مراجعة المصروفات"
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.Maroon
        Me.Label3.Location = New System.Drawing.Point(434, 8)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(111, 16)
        Me.Label3.TabIndex = 279
        Me.Label3.Text = "المصروفات التأسيسية"
        '
        'SimpleButton115
        '
        Me.SimpleButton115.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton115.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton115.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton115.Appearance.Options.UseBackColor = True
        Me.SimpleButton115.Appearance.Options.UseFont = True
        Me.SimpleButton115.Appearance.Options.UseTextOptions = True
        Me.SimpleButton115.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton115.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton115.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton115.Location = New System.Drawing.Point(752, 377)
        Me.SimpleButton115.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton115.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton115.Name = "SimpleButton115"
        Me.SimpleButton115.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton115.TabIndex = 278
        Me.SimpleButton115.Text = "تقييم الشركة"
        '
        'SimpleButton120
        '
        Me.SimpleButton120.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton120.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton120.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton120.Appearance.Options.UseBackColor = True
        Me.SimpleButton120.Appearance.Options.UseFont = True
        Me.SimpleButton120.Appearance.Options.UseTextOptions = True
        Me.SimpleButton120.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton120.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton120.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton120.Location = New System.Drawing.Point(752, 303)
        Me.SimpleButton120.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton120.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton120.Name = "SimpleButton120"
        Me.SimpleButton120.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton120.TabIndex = 277
        Me.SimpleButton120.Text = "ميزان مراجعة"
        '
        'SimpleButton119
        '
        Me.SimpleButton119.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton119.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton119.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton119.Appearance.Options.UseBackColor = True
        Me.SimpleButton119.Appearance.Options.UseFont = True
        Me.SimpleButton119.Appearance.Options.UseTextOptions = True
        Me.SimpleButton119.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton119.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton119.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton119.Location = New System.Drawing.Point(752, 266)
        Me.SimpleButton119.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton119.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton119.Name = "SimpleButton119"
        Me.SimpleButton119.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton119.TabIndex = 276
        Me.SimpleButton119.Text = "اهلاكات الاصول"
        '
        'SimpleButton118
        '
        Me.SimpleButton118.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton118.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton118.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton118.Appearance.Options.UseBackColor = True
        Me.SimpleButton118.Appearance.Options.UseFont = True
        Me.SimpleButton118.Appearance.Options.UseTextOptions = True
        Me.SimpleButton118.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton118.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton118.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton118.Location = New System.Drawing.Point(752, 229)
        Me.SimpleButton118.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton118.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton118.Name = "SimpleButton118"
        Me.SimpleButton118.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton118.TabIndex = 275
        Me.SimpleButton118.Text = "الاصول"
        '
        'SimpleButton116
        '
        Me.SimpleButton116.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton116.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton116.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton116.Appearance.Options.UseBackColor = True
        Me.SimpleButton116.Appearance.Options.UseFont = True
        Me.SimpleButton116.Appearance.Options.UseTextOptions = True
        Me.SimpleButton116.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton116.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton116.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton116.Location = New System.Drawing.Point(752, 192)
        Me.SimpleButton116.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton116.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton116.Name = "SimpleButton116"
        Me.SimpleButton116.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton116.TabIndex = 274
        Me.SimpleButton116.Text = "قائمة الدخل"
        '
        'SimpleButton108
        '
        Me.SimpleButton108.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton108.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton108.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton108.Appearance.Options.UseBackColor = True
        Me.SimpleButton108.Appearance.Options.UseFont = True
        Me.SimpleButton108.Appearance.Options.UseTextOptions = True
        Me.SimpleButton108.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton108.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton108.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton108.Location = New System.Drawing.Point(752, 111)
        Me.SimpleButton108.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton108.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton108.Name = "SimpleButton108"
        Me.SimpleButton108.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton108.TabIndex = 272
        Me.SimpleButton108.Text = "مقارنة ربح"
        '
        'SimpleButton107
        '
        Me.SimpleButton107.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton107.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton107.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton107.Appearance.Options.UseBackColor = True
        Me.SimpleButton107.Appearance.Options.UseFont = True
        Me.SimpleButton107.Appearance.Options.UseTextOptions = True
        Me.SimpleButton107.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton107.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton107.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton107.Location = New System.Drawing.Point(752, 74)
        Me.SimpleButton107.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton107.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton107.Name = "SimpleButton107"
        Me.SimpleButton107.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton107.TabIndex = 271
        Me.SimpleButton107.Text = "الاقل ربح"
        '
        'SimpleButton84
        '
        Me.SimpleButton84.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton84.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton84.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton84.Appearance.Options.UseBackColor = True
        Me.SimpleButton84.Appearance.Options.UseFont = True
        Me.SimpleButton84.Appearance.Options.UseTextOptions = True
        Me.SimpleButton84.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton84.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton84.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton84.Location = New System.Drawing.Point(752, 36)
        Me.SimpleButton84.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton84.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton84.Name = "SimpleButton84"
        Me.SimpleButton84.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton84.TabIndex = 270
        Me.SimpleButton84.Text = "الاكثر ربح"
        '
        'Label44
        '
        Me.Label44.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label44.AutoSize = True
        Me.Label44.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label44.ForeColor = System.Drawing.Color.Maroon
        Me.Label44.Location = New System.Drawing.Point(790, 8)
        Me.Label44.Name = "Label44"
        Me.Label44.Size = New System.Drawing.Size(62, 16)
        Me.Label44.TabIndex = 269
        Me.Label44.Text = "المستخدمين"
        '
        'SimpleButton100
        '
        Me.SimpleButton100.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton100.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton100.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton100.Appearance.Options.UseBackColor = True
        Me.SimpleButton100.Appearance.Options.UseFont = True
        Me.SimpleButton100.Appearance.Options.UseTextOptions = True
        Me.SimpleButton100.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton100.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton100.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton100.Location = New System.Drawing.Point(752, 340)
        Me.SimpleButton100.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton100.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton100.Name = "SimpleButton100"
        Me.SimpleButton100.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton100.TabIndex = 268
        Me.SimpleButton100.Text = "حركة عمل"
        '
        'Label43
        '
        Me.Label43.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label43.AutoSize = True
        Me.Label43.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label43.ForeColor = System.Drawing.Color.Maroon
        Me.Label43.Location = New System.Drawing.Point(815, 167)
        Me.Label43.Name = "Label43"
        Me.Label43.Size = New System.Drawing.Size(24, 16)
        Me.Label43.TabIndex = 267
        Me.Label43.Text = "عام"
        '
        'SimpleButton98
        '
        Me.SimpleButton98.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton98.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton98.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton98.Appearance.Options.UseBackColor = True
        Me.SimpleButton98.Appearance.Options.UseFont = True
        Me.SimpleButton98.Appearance.Options.UseTextOptions = True
        Me.SimpleButton98.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton98.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton98.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton98.Location = New System.Drawing.Point(592, 76)
        Me.SimpleButton98.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton98.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton98.Name = "SimpleButton98"
        Me.SimpleButton98.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton98.TabIndex = 266
        Me.SimpleButton98.Text = "اجمالي المصروفات"
        '
        'SimpleButton99
        '
        Me.SimpleButton99.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton99.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.SimpleButton99.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton99.Appearance.Options.UseBackColor = True
        Me.SimpleButton99.Appearance.Options.UseFont = True
        Me.SimpleButton99.Appearance.Options.UseTextOptions = True
        Me.SimpleButton99.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.SimpleButton99.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton99.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.SimpleButton99.Location = New System.Drawing.Point(592, 36)
        Me.SimpleButton99.LookAndFeel.SkinName = "VS2010"
        Me.SimpleButton99.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton99.Name = "SimpleButton99"
        Me.SimpleButton99.Size = New System.Drawing.Size(143, 31)
        Me.SimpleButton99.TabIndex = 265
        Me.SimpleButton99.Text = "مراجعة المصروفات"
        '
        'Label42
        '
        Me.Label42.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label42.AutoSize = True
        Me.Label42.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label42.ForeColor = System.Drawing.Color.Maroon
        Me.Label42.Location = New System.Drawing.Point(627, 8)
        Me.Label42.Name = "Label42"
        Me.Label42.Size = New System.Drawing.Size(61, 16)
        Me.Label42.TabIndex = 264
        Me.Label42.Text = "المصروفات"
        '
        'CostCenterReportBtn
        '
        Me.CostCenterReportBtn.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CostCenterReportBtn.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.CostCenterReportBtn.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CostCenterReportBtn.Appearance.Options.UseBackColor = True
        Me.CostCenterReportBtn.Appearance.Options.UseFont = True
        Me.CostCenterReportBtn.Appearance.Options.UseTextOptions = True
        Me.CostCenterReportBtn.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.CostCenterReportBtn.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CostCenterReportBtn.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.CostCenterReportBtn.Location = New System.Drawing.Point(592, 115)
        Me.CostCenterReportBtn.LookAndFeel.SkinName = "VS2010"
        Me.CostCenterReportBtn.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CostCenterReportBtn.Name = "CostCenterReportBtn"
        Me.CostCenterReportBtn.Size = New System.Drawing.Size(143, 31)
        Me.CostCenterReportBtn.TabIndex = 286
        Me.CostCenterReportBtn.Text = "تقرير مراكز التكلفة"
        '
        'LabelCostCenter
        '
        Me.LabelCostCenter.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelCostCenter.AutoSize = True
        Me.LabelCostCenter.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCostCenter.ForeColor = System.Drawing.Color.Maroon
        Me.LabelCostCenter.Location = New System.Drawing.Point(627, 130)
        Me.LabelCostCenter.Name = "LabelCostCenter"
        Me.LabelCostCenter.Size = New System.Drawing.Size(75, 16)
        Me.LabelCostCenter.TabIndex = 287
        Me.LabelCostCenter.Text = "مراكز التكلفة"
        '
        'Label47
        '
        Me.Label47.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label47.AutoSize = True
        Me.Label47.Font = New System.Drawing.Font("Microsoft Sans Serif", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(178, Byte))
        Me.Label47.ForeColor = System.Drawing.Color.Gold
        Me.Label47.Image = CType(resources.GetObject("Label47.Image"), System.Drawing.Image)
        Me.Label47.Location = New System.Drawing.Point(478, 0)
        Me.Label47.Name = "Label47"
        Me.Label47.Size = New System.Drawing.Size(71, 29)
        Me.Label47.TabIndex = 1254
        Me.Label47.Text = "التقارير"
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.Image = CType(resources.GetObject("PictureBox1.Image"), System.Drawing.Image)
        Me.PictureBox1.Location = New System.Drawing.Point(-80, -3)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(1240, 46)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox1.TabIndex = 1255
        Me.PictureBox1.TabStop = False
        '
        'report_show
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1011, 494)
        Me.Controls.Add(Me.Label47)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.XtraTabControl2)
        Me.Font = New System.Drawing.Font("Arial", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "report_show"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl2.ResumeLayout(False)
        Me.XtraTabPage3.ResumeLayout(False)
        Me.XtraTabPage3.PerformLayout()
        Me.XtraTabPage4.ResumeLayout(False)
        Me.XtraTabPage4.PerformLayout()
        Me.XtraTabPage5.ResumeLayout(False)
        Me.XtraTabPage5.PerformLayout()
        Me.XtraTabPage6.ResumeLayout(False)
        Me.XtraTabPage6.PerformLayout()
        Me.XtraTabPage7.ResumeLayout(False)
        Me.XtraTabPage7.PerformLayout()
        Me.XtraTabPage8.ResumeLayout(False)
        Me.XtraTabPage8.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents XtraTabControl2 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage3 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents cus_balace As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents XtraTabPage4 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage5 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage6 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage7 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage8 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton6 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton7 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton8 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton9 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton13 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton14 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton15 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton16 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton17 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton18 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton19 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton20 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton21 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton22 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton23 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton24 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton26 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton27 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton28 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton25 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton29 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton30 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton31 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton33 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton34 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton35 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton41 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton40 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton39 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton38 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton37 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton36 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton32 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton42 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton43 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton44 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton45 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton46 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton47 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton48 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton49 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton50 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton51 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton52 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton53 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton54 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton55 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton56 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton73 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton75 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton72 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton67 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton70 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton69 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton64 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton66 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton63 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton57 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton58 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton62 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton59 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton61 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton78 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton77 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton76 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton93 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton94 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label34 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton89 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton90 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton91 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton92 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label32 As System.Windows.Forms.Label
    Friend WithEvents Label33 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton87 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton88 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label31 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton85 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton86 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label30 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton83 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label29 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton79 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton80 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton81 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton82 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label28 As System.Windows.Forms.Label
    Friend WithEvents Label27 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton60 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label35 As System.Windows.Forms.Label
    Friend WithEvents Label36 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton65 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton71 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton74 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton95 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label37 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton96 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton97 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label38 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton68 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents Label39 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton101 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton102 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton103 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton104 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label40 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton105 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton106 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label41 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton12 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton10 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton11 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton100 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label43 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton98 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton99 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label42 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton108 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton107 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton84 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label44 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton109 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton110 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton111 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label45 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton112 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton113 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton114 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label46 As System.Windows.Forms.Label
    Friend WithEvents Label47 As System.Windows.Forms.Label
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents SimpleButton116 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton117 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton119 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton118 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton120 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton115 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton123 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton124 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label48 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton121 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton122 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents SimpleButton125 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CostCenterReportBtn As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelCostCenter As System.Windows.Forms.Label
End Class
