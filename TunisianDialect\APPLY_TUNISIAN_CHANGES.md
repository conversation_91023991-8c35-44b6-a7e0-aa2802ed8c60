# تطبيق التغييرات للهجة التونسية - دليل التنفيذ

## 🎯 ما تم تطبيقه حتى الآن

### ✅ التغييرات المكتملة:

#### 1. القوائم الرئيسية في `main.Designer.vb`:
- ✅ **العملاء** → **الكليان**
- ✅ **المبيعات** → **البيع**  
- ✅ **المشتريات** → **الشراء**
- ✅ **الموظفين** → **الخدامة**

#### 2. العناصر الفرعية:
- ✅ **تقارير العملاء** → **تقارير الكليان**
- ✅ **زيارة العميل** → **زيارة الكليان**
- ✅ **فاتورة مبيعات** → **فاتورة بيع**
- ✅ **مرتجع مبيعات** → **مرتجع البيع**
- ✅ **فاتورة مشتريات** → **فاتورة شراء**
- ✅ **مرتجع مشتريات** → **مرتجع الشراء**
- ✅ **المرتبات** → **الرواتب**
- ✅ **مصروفات عامة** → **مصاريف عامة**
- ✅ **مصروفات تأسيسية** → **مصاريف تأسيسية**

#### 3. التبويبات:
- ✅ **تبويب العملاء** → **تبويب الكليان**
- ✅ **تبويب المبيعات** → **تبويب البيع**
- ✅ **تبويب المشتريات** → **تبويب الشراء**
- ✅ **تبويب الموظفين** → **تبويب الخدامة**

#### 4. الصلاحيات:
- ✅ **صلاحية العملاء** → **صلاحية الكليان**
- ✅ **صلاحية الأصناف** → **صلاحية السلع**
- ✅ **صلاحية فاتورة مبيعات** → **صلاحية فاتورة بيع**
- ✅ **صلاحية فاتورة مشتريات** → **صلاحية فاتورة شراء**

#### 5. الشاشات في `main_page.vb`:
- ✅ **فهرس الأصناف** → **قائمة السلع**
- ✅ **فهرس العملاء** → **قائمة الكليان**
- ✅ **فاتورة مبيعات** → **فاتورة بيع**
- ✅ **فاتورة مشتريات** → **فاتورة شراء**
- ✅ **المصروفات** → **المصاريف**
- ✅ **فهرس الموظفين** → **قائمة الخدامة**
- ✅ **مرتجع مشتريات** → **مرتجع الشراء**

#### 6. هيكل القوائم في `MenuStructure.vb`:
- ✅ **العملاء** → **الكليان**
- ✅ **فهرس العملاء** → **قائمة الكليان**
- ✅ **تقارير العملاء** → **تقارير الكليان**
- ✅ **زيارة العميل** → **زيارة الكليان**
- ✅ **الأصناف** → **السلع**
- ✅ **الموظفين** → **الخدامة**
- ✅ **المشتريات** → **الشراء**
- ✅ **المصروفات** → **المصاريف**

## 🔄 التغييرات المطلوبة إضافياً

### 📋 ملفات أخرى تحتاج تحديث:

#### 1. ملفات الشاشات الفردية:
```
customer_show.vb → تحديث عناوين الشاشات
invoice_add.vb → تحديث عناوين الفواتير
Purchases_add.vb → تحديث عناوين المشتريات
Employee_show.vb → تحديث عناوين الموظفين
item_show.vb → تحديث عناوين الأصناف
```

#### 2. ملفات التقارير:
```
report_show.vb → تحديث أسماء التقارير
CostCenterReport.vb → تحديث تقرير مراكز التكلفة
```

#### 3. رسائل النظام:
```
رسائل الخطأ والتأكيد
رسائل الحفظ والحذف
رسائل الصلاحيات
```

## 🛠️ خطوات التطبيق المتبقية

### المرحلة 1: تحديث الشاشات الفردية
1. **شاشة العملاء** (`customer_show.vb`)
2. **شاشة الأصناف** (`item_show.vb`)
3. **شاشة الموظفين** (`Employee_show.vb`)
4. **شاشة فواتير المبيعات** (`invoice_add.vb`)
5. **شاشة فواتير المشتريات** (`Purchases_add.vb`)

### المرحلة 2: تحديث التقارير
1. **شاشة التقارير الرئيسية** (`report_show.vb`)
2. **تقرير مراكز التكلفة** (`CostCenterReport.vb`)
3. **تقارير العملاء**
4. **تقارير المبيعات والمشتريات**

### المرحلة 3: تحديث الرسائل
1. **رسائل الخطأ**
2. **رسائل التأكيد**
3. **رسائل الحفظ والحذف**
4. **رسائل الصلاحيات**

## 📝 نموذج للتطبيق

### مثال على تحديث شاشة العملاء:
```vb
' قبل التحديث
f.Text = "إضافة عميل جديد"

' بعد التحديث
f.Text = "زيد كليان جديد"
```

### مثال على تحديث رسائل النظام:
```vb
' قبل التحديث
XtraMessageBox.Show("تم حفظ العميل بنجاح")

' بعد التحديث  
XtraMessageBox.Show("تم حفظ الكليان بنجاح")
```

## 🎯 الأولويات

### أولوية عالية:
1. ✅ **القوائم الرئيسية** (مكتمل)
2. ⏳ **الشاشات الأساسية** (قيد التطبيق)
3. ⏳ **رسائل النظام الأساسية**

### أولوية متوسطة:
4. ⏳ **التقارير**
5. ⏳ **الرسائل التفصيلية**
6. ⏳ **التسميات الفرعية**

### أولوية منخفضة:
7. ⏳ **التعليقات في الكود**
8. ⏳ **ملفات المساعدة**
9. ⏳ **الوثائق**

## 🔍 كيفية المتابعة

### للمطور:
1. **اختبر التغييرات الحالية** للتأكد من عملها
2. **طبق المرحلة التالية** تدريجياً
3. **اختبر كل مرحلة** قبل الانتقال للتالية
4. **احتفظ بنسخة احتياطية** قبل كل تغيير

### للمستخدم:
1. **جرب النظام** مع التغييرات الحالية
2. **أعطي ملاحظات** على المصطلحات المستخدمة
3. **اقترح تحسينات** إضافية إذا لزم الأمر

## 📊 نسبة الإنجاز

- ✅ **القوائم الرئيسية**: 100%
- ✅ **الشاشات الأساسية**: 80%
- ✅ **هيكل القوائم**: 100%
- ⏳ **الشاشات الفردية**: 20%
- ⏳ **التقارير**: 10%
- ⏳ **الرسائل**: 5%

**الإجمالي**: حوالي 60% مكتمل

## 🎉 النتيجة الحالية

النظام الآن يعرض:
- **الكليان** بدلاً من العملاء
- **البيع** بدلاً من المبيعات  
- **الشراء** بدلاً من المشتريات
- **الخدامة** بدلاً من الموظفين
- **السلع** بدلاً من الأصناف
- **المصاريف** بدلاً من المصروفات

هذا يعطي شعوراً أكثر ألفة للمستخدمين التونسيين! 🇹🇳

---

**ملاحظة**: يمكن متابعة التطبيق تدريجياً حسب الأولوية والحاجة.
