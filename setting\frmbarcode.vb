﻿Imports System.Data.SqlClient
Imports System.Drawing.Printing
Imports DevExpress.XtraPrinting.BarCode
Imports DevExpress.XtraReports.UI
Imports System.IO

Public Class frmbarcode
    ' Private Declare Function SetDefaultPrinter Lib "winspool.drv" Alias "SetDefaultPrinterA" (ByVal pszPrinter As String) As Boolean
    Function Code39(ByVal TheTXT As String, ByVal BarColor As Color, ByVal background As Boolean) As Image

        TheTXT = UCase(TheTXT)
        Dim Binaryz As String = ""
        Dim I As Integer = 1


        For I = 1 To Len(TheTXT)
            Select Case Mid(TheTXT, I, 1)
                Case "*"
                    Binaryz = Binaryz & "0110100100101"
                Case "0"
                    Binaryz = Binaryz & "0101100100101"
                Case "1"
                    Binaryz = Binaryz & "0010110101001"
                Case "2"
                    Binaryz = Binaryz & "0100110101001"
                Case "3"
                    Binaryz = Binaryz & "0010011010101"
                Case "4"
                    Binaryz = Binaryz & "0101100101001"
                Case "5"
                    Binaryz = Binaryz & "0010110010101"
                Case "6"
                    Binaryz = Binaryz & "0100110010101"
                Case "7"
                    Binaryz = Binaryz & "0101101001001"
                Case "8"
                    Binaryz = Binaryz & "0010110100101"
                Case "9"
                    Binaryz = Binaryz & "0100110100101"
                Case "$"
                    Binaryz = Binaryz & "0110110110101"
                Case "%"
                    Binaryz = Binaryz & "0101101101101"
                Case "+"
                    Binaryz = Binaryz & "0110101101101"
                Case "-"
                    Binaryz = Binaryz & "0110101001001"
                Case "."
                    Binaryz = Binaryz & "0011010100101"
                Case "/"
                    Binaryz = Binaryz & "0110110101101"
                Case "="
                    Binaryz = Binaryz & "0110010100101"
                Case "A"
                    Binaryz = Binaryz & "0010101101001"
                Case "B"
                    Binaryz = Binaryz & "0100101101001"
                Case "C"
                    Binaryz = Binaryz & "0010010110101"
                Case "D"
                    Binaryz = Binaryz & "0101001101001"
                Case "E"
                    Binaryz = Binaryz & "0010100110101"
                Case "F"
                    Binaryz = Binaryz & "0100100110101"
                Case "G"
                    Binaryz = Binaryz & "0101011001001"
                Case "H"
                    Binaryz = Binaryz & "0010101100101"
                Case "I"
                    Binaryz = Binaryz & "0100101100101"
                Case "J"
                    Binaryz = Binaryz & "0101001100101"
                Case "K"
                    Binaryz = Binaryz & "0010101011001"
                Case "L"
                    Binaryz = Binaryz & "0100101011001"
                Case "M"
                    Binaryz = Binaryz & "0010010101101"
                Case "N"
                    Binaryz = Binaryz & "0101001011001"
                Case "O"
                    Binaryz = Binaryz & "0010100101101"
                Case "P"
                    Binaryz = Binaryz & "0100100101101"
                Case "Q"
                    Binaryz = Binaryz & "0101010011001"
                Case "R"
                    Binaryz = Binaryz & "0010101001101"
                Case "S"
                    Binaryz = Binaryz & "0100101001101"
                Case "T"
                    Binaryz = Binaryz & "0101001001101"
                Case "U"
                    Binaryz = Binaryz & "0011010101001"
                Case "V"
                    Binaryz = Binaryz & "0110010101001"
                Case "W"
                    Binaryz = Binaryz & "0011001010101"
                Case "X"
                    Binaryz = Binaryz & "0110100101001"
                Case "Y"
                    Binaryz = Binaryz & "0011010010101"
                Case "Z"
                    Binaryz = Binaryz & "0110010010101"

            End Select

        Next

        Binaryz = "0110100100101" & Binaryz & "0110100100101"


        Dim bmp As Bitmap = New Bitmap(197, 98, System.Drawing.Imaging.PixelFormat.Format32bppPArgb)

        Dim GraphZ As Graphics = Graphics.FromImage(bmp)
        Dim RectZ As Rectangle = New Rectangle(0, 0, bmp.Width, bmp.Height)
        Dim myBrush As Brush = New Drawing.Drawing2D.LinearGradientBrush(RectZ, Color.White, Color.White, Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal)
        GraphZ.FillRectangle(myBrush, RectZ)

        For I = 1 To Len(Binaryz)
            If Mid(Binaryz, I, 1) = "0" Then
                Dim BlackPen As Pen = New Pen(BarColor, 1)
                Dim point1 As Point = New Point(I + 2, 30)
                Dim point2 As Point = New Point(I + 2, 90)
                GraphZ.DrawLine(BlackPen, point1, point2)
            Else
                If background = 0 Then
                    Dim BlackPen As Pen = New Pen(Color.White, 1)
                    Dim point1 As Point = New Point(I + 2, 30)
                    Dim point2 As Point = New Point(I + 2, 90)
                    GraphZ.DrawLine(BlackPen, point1, point2)
                Else
                    Dim BlackPen As Pen = New Pen(Color.Transparent, 1)
                    Dim point1 As Point = New Point(I + 2, 30)
                    Dim point2 As Point = New Point(I + 2, 90)
                    GraphZ.DrawLine(BlackPen, point1, point2)
                End If

            End If

        Next
        ' Barcode label data



        GraphZ.DrawString(txtmarket.Text, New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 1, 0)
        GraphZ.DrawString(txtbarcode.Text.Trim, New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 20, 90)
        GraphZ.DrawString(txtpname.Text & "  ", New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 1, 110)
        GraphZ.DrawString(txtPrice.Text & "  ", New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 150, 110)


    End Function

    Const charSet As String = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%"
    Function Mod43(ByVal C39 As String) As String
        Dim Total As Integer

        For i As Integer = 1 To Len(C39)
            Total = InStr(charSet, Mid(C39, i, 1)) - 1 + Total
        Next i
        Mod43 = C39 & Mid(charSet, (Total Mod 43 + 1), 1)
    End Function



    Private Sub Form1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load


        'Me.ComboBox1.Text = Me.ComboBox1.Items.Item(0)
        Dim pkInstalledPrinters As String

        ' Find all printers installed
        For Each pkInstalledPrinters In _
            PrinterSettings.InstalledPrinters
            ComboBox1.Items.Add(pkInstalledPrinters)
        Next pkInstalledPrinters

        ' Set the combo to the first printer in the list
        ComboBox1.SelectedIndex = 0
        ComboBox2.SelectedIndex = 0
        ComboBox1.Text = My.Settings.u1
        ComboBox2.Text = My.Settings.u2
    End Sub




    Private Sub PrintDocument1_PrintPage(ByVal sender As System.Object, ByVal e As System.Drawing.Printing.PrintPageEventArgs) Handles PrintDocument1.PrintPage

        If PictureBox1.BackgroundImage Is Nothing Then Exit Sub

        Dim x1, y1, w, h, mx, mr, ml, mu, X, Y As Long
        w = Int(NumericUpDown5.Value) ' Int(NumericUpDown10.Value)  'Width
        h = Int(NumericUpDown6.Value) 'Height
        mx = Int(NumericUpDown7.Value) 'Spacers between columns
        mr = Int(NumericUpDown8.Value) ' Spacers between rows  11.33858267717 '3 * 3.77952755905511 '
        ml = Int(NumericUpDown3.Value) 'Left
        mu = Int(NumericUpDown4.Value) ' Top
        '  d f d 

        For x1 = 0 To Int(NumericUpDown1.Value) - 1
            For y1 = 0 To Int(NumericUpDown2.Value) - 1
                X = (ml + (x1 * w) + (mx * x1))
                Y = (mu + (y1 * h) + (mr * y1))

                e.Graphics.DrawImage(PictureBox1.BackgroundImage, X, Y, w, h)
            Next
        Next

    End Sub

    Private Sub TextBox3_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbarcode.TextChanged
        '  PictureBox1.BackgroundImage = Code39(txtbarcode.Text.Trim, Color.Black, 0)
        PictureBox1.BackgroundImage = Code128(txtbarcode.Text, "A")
    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtmarket.TextChanged
        TextBox3_TextChanged(txtbarcode, New System.EventArgs())


    End Sub




    Private Sub PrintDocument2_PrintPage(ByVal sender As System.Object, ByVal e As System.Drawing.Printing.PrintPageEventArgs) Handles PrintDocument2.PrintPage
        If PictureBox1.BackgroundImage Is Nothing Then Exit Sub

        Dim x1, y1, w, h, ml, mu, X, Y As Long
        w = Int(NumericUpDown5.Value) 'Width
        h = Int(NumericUpDown6.Value) 'Height
        ml = Int(NumericUpDown3.Value) 'Left
        mu = Int(NumericUpDown4.Value) ' Top

        X = (ml + (x1 * w))
        Y = (mu + (y1 * h))

        e.Graphics.DrawImage(PictureBox1.BackgroundImage, X, Y, w, h)

    End Sub



    Private Sub TextBox2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtpname.TextChanged
        TextBox3_TextChanged(txtbarcode, New System.EventArgs())
        Dim sql = "select * from item where itemnamearabic=N'" & (txtpname.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            code.Text = dr!itemcode
            item_unit.Visible = True
        End If
    End Sub




    Private Sub ComboBox1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim x As String
        x = ComboBox1.Text

        PrintDocument1.PrinterSettings.PrinterName = x
        PrintDocument2.PrinterSettings.PrinterName = x

    End Sub



    Private Sub RadButton2_Click(sender As Object, e As EventArgs)
        PrintDocument1.Print()

    End Sub


    Private Sub RadButton5_Click(sender As Object, e As EventArgs)
        Me.Dispose()

    End Sub


    Private Sub NumericUpDown10_ValueChanged(sender As Object, e As EventArgs)
        On Error Resume Next
        NumericUpDown5.Value = NumericUpDown10.Value * 3.7795275591

    End Sub

    Private Sub NumericUpDown11_ValueChanged(sender As Object, e As EventArgs)
        NumericUpDown6.Value = NumericUpDown11.Value * 3.7795275591

    End Sub

    Private Sub NumericUpDown10_KeyUp(sender As Object, e As KeyEventArgs)
        NumericUpDown5.Value = NumericUpDown10.Value * 3.7795275591

    End Sub

    Private Sub NumericUpDown11_KeyUp(sender As Object, e As KeyEventArgs)
        NumericUpDown6.Value = NumericUpDown11.Value * 3.7795275591
    End Sub

    Private Sub RadButton6_Click(sender As Object, e As EventArgs)

    End Sub

    Function Code128(ByVal TheText As String, ByVal CodeLetter As String) As Image
        ' TheText : A variable for the text to be encrypted
        ' CodeLetter : A variable for the class to be used

        Dim Binaryz As String = "" 'A variable will load the text after converting it to a binary
        Dim I As Integer
        Dim NumCode As Integer 'A variable will carry the value of the supplementary text calculation
        If CodeLetter = "A" Or CodeLetter = "a" Then
            NumCode = 103
            Binaryz = "00101111011"
        End If
        If CodeLetter = "B" Or CodeLetter = "b" Then
            NumCode = 104
            Binaryz = "00101101111"
        End If
        If CodeLetter = "C" Or CodeLetter = "c" Then
            NumCode = 105
            Binaryz = "00101100011"
        End If
        ' The following code will assign the character value in binary according to 128 code table
        For I = 1 To Len(TheText)
            NumCode = NumCode + ((Asc(Mid(TheText, I, 1)) - 32) * I)
            Select Case Asc(Mid(TheText, I, 1))
                Case 32
                    Binaryz = Binaryz & "00100110011"
                Case 33
                    Binaryz = Binaryz & "00110010011"
                Case 34
                    Binaryz = Binaryz & "00110011001"
                Case 35
                    Binaryz = Binaryz & "01101100111"
                Case 36
                    Binaryz = Binaryz & "01101110011"
                Case 37
                    Binaryz = Binaryz & "01110110011"
                Case 38
                    Binaryz = Binaryz & "01100110111"
                Case 39
                    Binaryz = Binaryz & "01100111011"
                Case 40
                    Binaryz = Binaryz & "01110011011"
                Case 41
                    Binaryz = Binaryz & "00110110111"
                Case 42
                    Binaryz = Binaryz & "00110111011"
                Case 43
                    Binaryz = Binaryz & "00111011011"
                Case 44
                    Binaryz = Binaryz & "01001100011"
                Case 45
                    Binaryz = Binaryz & "01100100011"
                Case 46
                    Binaryz = Binaryz & "01100110001"
                Case 47
                    Binaryz = Binaryz & "01000110011"
                Case 48
                    Binaryz = Binaryz & "01100010011"
                Case 49
                    Binaryz = Binaryz & "01100011001"
                Case 50
                    Binaryz = Binaryz & "00110001101"
                Case 51
                    Binaryz = Binaryz & "00110100011"
                Case 52
                    Binaryz = Binaryz & "00110110001"
                Case 53
                    Binaryz = Binaryz & "00100011011"
                Case 54
                    Binaryz = Binaryz & "00110001011"
                Case 55
                    Binaryz = Binaryz & "00010010001"
                Case 56
                    Binaryz = Binaryz & "00010110011"
                Case 57
                    Binaryz = Binaryz & "00011010011"
                Case 58
                    Binaryz = Binaryz & "00011011001"
                Case 59
                    Binaryz = Binaryz & "00010011011"
                Case 60
                    Binaryz = Binaryz & "00011001011"
                Case 61
                    Binaryz = Binaryz & "00011001101"
                Case 62
                    Binaryz = Binaryz & "00100100111"
                Case 63
                    Binaryz = Binaryz & "00100111001"
                Case 64
                    Binaryz = Binaryz & "00111001001"
                Case 65
                    Binaryz = Binaryz & "01011100111"
                Case 66
                    Binaryz = Binaryz & "01110100111"
                Case 67
                    Binaryz = Binaryz & "01110111001"
                Case 68
                    Binaryz = Binaryz & "01001110111"
                Case 69
                    Binaryz = Binaryz & "01110010111"
                Case 70
                    Binaryz = Binaryz & "01110011101"
                Case 71
                    Binaryz = Binaryz & "00101110111"
                Case 72
                    Binaryz = Binaryz & "00111010111"
                Case 73
                    Binaryz = Binaryz & "00111011101"
                Case 74
                    Binaryz = Binaryz & "01001000111"
                Case 75
                    Binaryz = Binaryz & "01001110001"
                Case 76
                    Binaryz = Binaryz & "01110010001"
                Case 77
                    Binaryz = Binaryz & "01000100111"
                Case 78
                    Binaryz = Binaryz & "01000111001"
                Case 79
                    Binaryz = Binaryz & "01110001001"
                Case 80
                    Binaryz = Binaryz & "00010001001"
                Case 81
                    Binaryz = Binaryz & "00101110001"
                Case 82
                    Binaryz = Binaryz & "00111010001"
                Case 83
                    Binaryz = Binaryz & "00100010111"
                Case 84
                    Binaryz = Binaryz & "00100011101"
                Case 85
                    Binaryz = Binaryz & "00100010001"
                Case 86
                    Binaryz = Binaryz & "00010100111"
                Case 87
                    Binaryz = Binaryz & "00010111001"
                Case 88
                    Binaryz = Binaryz & "00011101001"
                Case 89
                    Binaryz = Binaryz & "00010010111"
                Case 90
                    Binaryz = Binaryz & "00010011101"
                Case 91
                    Binaryz = Binaryz & "00011100101"
                Case 92
                    Binaryz = Binaryz & "00010000101"
                Case 93
                    Binaryz = Binaryz & "00110111101"
                Case 94
                    Binaryz = Binaryz & "00001110101"
                Case 95
                    Binaryz = Binaryz & "01011001111"
                Case 96
                    Binaryz = Binaryz & "01011110011"
                Case 97
                    Binaryz = Binaryz & "01101001111"
                Case 98
                    Binaryz = Binaryz & "01101111001"
                Case 99
                    Binaryz = Binaryz & "01111010011"
                Case 100
                    Binaryz = Binaryz & "01111011001"
                Case 101
                    Binaryz = Binaryz & "01001101111"
                Case 102
                    Binaryz = Binaryz & "01001111011"
                Case 103
                    Binaryz = Binaryz & "01100101111"
                Case 104
                    Binaryz = Binaryz & "01100111101"
                Case 105
                    Binaryz = Binaryz & "01111001011"
                Case 106
                    Binaryz = Binaryz & "01111001101"
                Case 107
                    Binaryz = Binaryz & "00111101101"
                Case 108
                    Binaryz = Binaryz & "00110101111"
                Case 109
                    Binaryz = Binaryz & "00001000101"
                Case 110
                    Binaryz = Binaryz & "00111101011"
                Case 111
                    Binaryz = Binaryz & "01110000101"
                Case 112
                    Binaryz = Binaryz & "01011000011"
                Case 113
                    Binaryz = Binaryz & "01101000011"
                Case 114
                    Binaryz = Binaryz & "01101100001"
                Case 115
                    Binaryz = Binaryz & "01000011011"
                Case 116
                    Binaryz = Binaryz & "01100001011"
                Case 117
                    Binaryz = Binaryz & "01100001101"
                Case 118
                    Binaryz = Binaryz & "00001011011"
                Case 119
                    Binaryz = Binaryz & "00001101011"
                Case 120
                    Binaryz = Binaryz & "00001101101"
                Case 121
                    Binaryz = Binaryz & "00100100001"
                Case 122
                    Binaryz = Binaryz & "00100001001"
                Case 123
                    Binaryz = Binaryz & "00001001001"
                Case 124
                    Binaryz = Binaryz & "01010000111"
                Case 125
                    Binaryz = Binaryz & "01011100001"
                Case 126
                    Binaryz = Binaryz & "01110100001"
                Case 127
                    Binaryz = Binaryz & "01000010111"
                Case 128
                    Binaryz = Binaryz & "01000011101"
                Case 129
                    Binaryz = Binaryz & "00001010111"
                Case 130
                    Binaryz = Binaryz & "00001011101"
                Case 131
                    Binaryz = Binaryz & "01000100001"
                Case 132
                    Binaryz = Binaryz & "01000010001"
                Case 133
                    Binaryz = Binaryz & "00010100001"
                Case 134
                    Binaryz = Binaryz & "00001010001"
                Case 135
                    Binaryz = Binaryz & "00101111011"
                Case 136
                    Binaryz = Binaryz & "00101101111"
                Case 137
                    Binaryz = Binaryz & "00101100011"
                Case 138
                    Binaryz = Binaryz & "0011100010100"
            End Select
        Next
        NumCode = NumCode Mod 103
        ' Next code to find the character to add to complete the text
        Select Case NumCode
            Case 0
                Binaryz = Binaryz & "00100110011"
            Case 1
                Binaryz = Binaryz & "00110010011"
            Case 2
                Binaryz = Binaryz & "00110011001"
            Case 3
                Binaryz = Binaryz & "01101100111"
            Case 4
                Binaryz = Binaryz & "01101110011"
            Case 5
                Binaryz = Binaryz & "01110110011"
            Case 6
                Binaryz = Binaryz & "01100110111"
            Case 7
                Binaryz = Binaryz & "01100111011"
            Case 8
                Binaryz = Binaryz & "01110011011"
            Case 9
                Binaryz = Binaryz & "00110110111"
            Case 10
                Binaryz = Binaryz & "00110111011"
            Case 11
                Binaryz = Binaryz & "00111011011"
            Case 12
                Binaryz = Binaryz & "01001100011"
            Case 13
                Binaryz = Binaryz & "01100100011"
            Case 14
                Binaryz = Binaryz & "01100110001"
            Case 15
                Binaryz = Binaryz & "01000110011"
            Case 16
                Binaryz = Binaryz & "01100010011"
            Case 17
                Binaryz = Binaryz & "01100011001"
            Case 18
                Binaryz = Binaryz & "00110001101"
            Case 19
                Binaryz = Binaryz & "00110100011"
            Case 20
                Binaryz = Binaryz & "00110110001"
            Case 21
                Binaryz = Binaryz & "00100011011"
            Case 22
                Binaryz = Binaryz & "00110001011"
            Case 23
                Binaryz = Binaryz & "00010010001"
            Case 24
                Binaryz = Binaryz & "00010110011"
            Case 25
                Binaryz = Binaryz & "00011010011"
            Case 26
                Binaryz = Binaryz & "00011011001"
            Case 27
                Binaryz = Binaryz & "00010011011"
            Case 28
                Binaryz = Binaryz & "00011001011"
            Case 29
                Binaryz = Binaryz & "00011001101"
            Case 30
                Binaryz = Binaryz & "00100100111"
            Case 31
                Binaryz = Binaryz & "00100111001"
            Case 32
                Binaryz = Binaryz & "00111001001"
            Case 33
                Binaryz = Binaryz & "01011100111"
            Case 34
                Binaryz = Binaryz & "01110100111"
            Case 35
                Binaryz = Binaryz & "01110111001"
            Case 36
                Binaryz = Binaryz & "01001110111"
            Case 37
                Binaryz = Binaryz & "01110010111"
            Case 38
                Binaryz = Binaryz & "01110011101"
            Case 39
                Binaryz = Binaryz & "00101110111"
            Case 40
                Binaryz = Binaryz & "00111010111"
            Case 41
                Binaryz = Binaryz & "00111011101"
            Case 42
                Binaryz = Binaryz & "01001000111"
            Case 43
                Binaryz = Binaryz & "01001110001"
            Case 44
                Binaryz = Binaryz & "01110010001"
            Case 45
                Binaryz = Binaryz & "01000100111"
            Case 46
                Binaryz = Binaryz & "01000111001"
            Case 47
                Binaryz = Binaryz & "01110001001"
            Case 48
                Binaryz = Binaryz & "00010001001"
            Case 49
                Binaryz = Binaryz & "00101110001"
            Case 50
                Binaryz = Binaryz & "00111010001"
            Case 51
                Binaryz = Binaryz & "00100010111"
            Case 52
                Binaryz = Binaryz & "00100011101"
            Case 53
                Binaryz = Binaryz & "00100010001"
            Case 54
                Binaryz = Binaryz & "00010100111"
            Case 55
                Binaryz = Binaryz & "00010111001"
            Case 56
                Binaryz = Binaryz & "00011101001"
            Case 57
                Binaryz = Binaryz & "00010010111"
            Case 58
                Binaryz = Binaryz & "00010011101"
            Case 59
                Binaryz = Binaryz & "00011100101"
            Case 60
                Binaryz = Binaryz & "00010000101"
            Case 61
                Binaryz = Binaryz & "00110111101"
            Case 62
                Binaryz = Binaryz & "00001110101"
            Case 63
                Binaryz = Binaryz & "01011001111"
            Case 64
                Binaryz = Binaryz & "01011110011"
            Case 65
                Binaryz = Binaryz & "01101001111"
            Case 66
                Binaryz = Binaryz & "01101111001"
            Case 67
                Binaryz = Binaryz & "01111010011"
            Case 68
                Binaryz = Binaryz & "01111011001"
            Case 69
                Binaryz = Binaryz & "01001101111"
            Case 70
                Binaryz = Binaryz & "01001111011"
            Case 71
                Binaryz = Binaryz & "01100101111"
            Case 72
                Binaryz = Binaryz & "01100111101"
            Case 73
                Binaryz = Binaryz & "01111001011"
            Case 74
                Binaryz = Binaryz & "01111001101"
            Case 75
                Binaryz = Binaryz & "00111101101"
            Case 76
                Binaryz = Binaryz & "00110101111"
            Case 77
                Binaryz = Binaryz & "00001000101"
            Case 78
                Binaryz = Binaryz & "00111101011"
            Case 79
                Binaryz = Binaryz & "01110000101"
            Case 80
                Binaryz = Binaryz & "01011000011"
            Case 81
                Binaryz = Binaryz & "01101000011"
            Case 82
                Binaryz = Binaryz & "01101100001"
            Case 83
                Binaryz = Binaryz & "01000011011"
            Case 84
                Binaryz = Binaryz & "01100001011"
            Case 85
                Binaryz = Binaryz & "01100001101"
            Case 86
                Binaryz = Binaryz & "00001011011"
            Case 87
                Binaryz = Binaryz & "00001101011"
            Case 88
                Binaryz = Binaryz & "00001101101"
            Case 89
                Binaryz = Binaryz & "00100100001"
            Case 90
                Binaryz = Binaryz & "00100001001"
            Case 91
                Binaryz = Binaryz & "00001001001"
            Case 92
                Binaryz = Binaryz & "01010000111"
            Case 93
                Binaryz = Binaryz & "01011100001"
            Case 94
                Binaryz = Binaryz & "01110100001"
            Case 95
                Binaryz = Binaryz & "01000010111"
            Case 96
                Binaryz = Binaryz & "01000011101"
            Case 97
                Binaryz = Binaryz & "00001010111"
            Case 98
                Binaryz = Binaryz & "00001011101"
            Case 99
                Binaryz = Binaryz & "01000100001"
            Case 100
                Binaryz = Binaryz & "01000010001"
            Case 101
                Binaryz = Binaryz & "00010100001"
            Case 102
                Binaryz = Binaryz & "00001010001"
        End Select
        Binaryz = Binaryz & "0011100010100" ' اEnd the code by adding the binary to stop all codes


        Dim z As String 'Variable to see font color

        Dim bmp As Bitmap = New Bitmap(NumericUpDown5.Value, NumericUpDown6.Value, System.Drawing.Imaging.PixelFormat.Format24bppRgb)

        Dim GraphZ As Graphics = Graphics.FromImage(bmp)
        Dim RectZ As Rectangle = New Rectangle(0, 0, bmp.Width, bmp.Height)
        Dim myBrush As Brush = New Drawing.Drawing2D.LinearGradientBrush(RectZ, Color.White, Color.White, Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal)
        GraphZ.FillRectangle(myBrush, RectZ)


        '  Drwaing Barcode
        Dim PenZ As Pen
        Dim point1 As Point ' The starting point of the line
        Dim point2 As Point ' Endpoint of line
        For I = 1 To Len(Binaryz)
            z = Mid(Binaryz, I, 1)
            If z = "0" Then
                PenZ = New Pen(Color.Black, 1)
                point1 = New Point(I, 30)
                point2 = New Point(I, 90)
                GraphZ.DrawLine(PenZ, point1, point2)
            Else
                PenZ = New Pen(Color.White, 1)
                point1 = New Point(I, 30)
                point2 = New Point(I, 90)
                GraphZ.DrawLine(PenZ, point1, point2)
            End If
        Next
        ' Draw the text to be encoded below the code
        GraphZ.DrawString(txtmarket.Text, New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 1, 0)
        GraphZ.DrawString(txtbarcode.Text, New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 20, 90)
        GraphZ.DrawString(txtpname.Text & "  ", New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 1, 110)
        GraphZ.DrawString(txtPrice.Text & "  ", New Font("dexef", 10, FontStyle.Bold), New SolidBrush(Color.Black), 150, 110)




        ' Return the final image of the function
        Code128 = bmp
    End Function
    Function Toc(ByVal Text2CStr As String) As String
        ' A function to compensate for the text required to be encoded with numbers for the class       
        ' (C)
        Dim X As Integer = 1
        Dim NewText As String = ""
        Dim Z As String = ""
        Do
            Z = Mid(Text2CStr, X, 2)
            ' Add number 32 for the ASCII value and restore the output character after the addition
            NewText = NewText & Chr(Val(Z) + 32)
            X += 2
            Z = Mid(Text2CStr, X, 1)
            If X >= Len(Text2CStr) Then Exit Do
            If Z = "" Then Exit Do
        Loop
        Toc = NewText
    End Function
    Private Sub txtPrice_TextChanged(sender As Object, e As EventArgs) Handles txtPrice.TextChanged
        TextBox3_TextChanged(txtPrice, New System.EventArgs())
    End Sub
    Private Sub ComboBox1_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles ComboBox1.SelectedIndexChanged
        Dim x As String
        x = ComboBox1.Text

        PrintDocument1.PrinterSettings.PrinterName = x
        PrintDocument2.PrinterSettings.PrinterName = x
    End Sub

    Private Sub searsh_btn_Click(sender As Object, e As EventArgs) Handles searsh_btn.Click
        PrintPreviewDialog1.ShowDialog()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        Me.Dispose()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Try
            If PictureBox1.BackgroundImage Is Nothing Then Exit Sub
            Dim SaveFileDialog1 As New SaveFileDialog()
            SaveFileDialog1.InitialDirectory = "c:\"
            SaveFileDialog1.Filter = "All files (*.*)|*.*|Bitmap files (*.bmp)|*.bmp"
            SaveFileDialog1.FilterIndex = 2
            SaveFileDialog1.RestoreDirectory = True
            SaveFileDialog1.ShowDialog()
            If SaveFileDialog1.FileName <> "" Then
                PictureBox1.BackgroundImage.Save(SaveFileDialog1.FileName, System.Drawing.Imaging.ImageFormat.Bmp)
            End If
        Catch ex As Exception
            MsgBox(Err.Description)
        End Try
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        PrintDocument1.Print()
    End Sub
    Sub aaas()
        Dim sql = "select * from table_barcode"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.NewRow
        dr!o1 = txtmarket.Text
        dr!o2 = txtpname.Text
        dr!o3 = txtbarcode.Text
        dr!o4 = txtPrice.Text
        dt.Rows.Add(dr)
        Dim cmd As New SqlCommandBuilder(adp)
        adp.Update(dt)

    End Sub
    Sub sa()
        SimpleButton3_Click(Nothing, Nothing)
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        'For i As Integer = 1 To Int(NumericUpDown9.Value)
        '    PrintDocument2.Print()
        'Next

        aaas()
        Dim qqq As Integer = 0


        Dim str = "select max(id) from table_barcode"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("table_barcode")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        qqq = Val(sumdebit)


        Dim adp As New SqlDataAdapter("select * from table_barcode  where id=N'" & qqq & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطأ")
            Exit Sub
        End If
        If ComboBox2.SelectedIndex = 0 Then
            Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "barcode_pos.repx"), True)
            rep.DataSource = dt
            rep.ShowPrintMarginsWarning = False

            For i As Integer = 1 To Int(NumericUpDown9.Value)
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(ComboBox1.Text)
                End Using
            Next
        ElseIf ComboBox2.SelectedIndex = 1 Then
            Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "barcode_pos.repx"), True)
            rep.DataSource = dt
            rep.ShowPrintMarginsWarning = False

            For i As Integer = 1 To Int(NumericUpDown9.Value)
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(ComboBox1.Text)
                End Using
            Next
        ElseIf ComboBox2.SelectedIndex = 2 Then
            Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "barcode_pos.repx"), True)
            rep.DataSource = dt
            rep.ShowPrintMarginsWarning = False

            For i As Integer = 1 To Int(NumericUpDown9.Value)
                Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                    PrtTool.Print(ComboBox1.Text)
                End Using
            Next
        End If



    End Sub

    Private Sub TextBox1_TextChanged_1(sender As Object, e As EventArgs) Handles code.TextChanged

        item_unit.Properties.Items.Clear()
        Dim adp As New SqlDataAdapter("select * from unit_item where CODE=N'" & (code.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        For i = 0 To dt.Rows.Count - 1
            item_unit.Properties.Items.Add(dt.Rows(i).Item("unit"))
        Next
        item_unit.SelectedIndex = 0

    End Sub

    Private Sub item_unit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles item_unit.SelectedIndexChanged
        Dim sql = "select * from unit_item where code=N'" & (code.Text) & "' and unit=N'" & (item_unit.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            txtbarcode.Text = dr!item_unit
            txtPrice.Text = dr!price1

        End If
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click

        My.Settings.u1 = ComboBox1.Text
        My.Settings.u2 = ComboBox2.Text
        My.Settings.Save()
        MessageBox.Show("تم حفظ بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)

    End Sub
End Class
