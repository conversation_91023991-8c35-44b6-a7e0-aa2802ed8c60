﻿Imports System.Data.SqlClient
Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors.XtraMessageBox
Imports System.Drawing
Imports DevExpress.XtraPrinting
Imports DevExpress.XtraPrinting.Drawing
Imports DevExpress.XtraReports.UI
Imports System.Net

Public Class pos_add2

    Private dtt As DataTable
    Private rng As New Random
    Dim itemsize As Integer = 0
    Dim itemloc As Integer = 0
    Dim check As Integer = 0

    Sub fill_item()

        item_name.Properties.DataSource = Nothing
        item_name.EditValue = ""
        Dim adp As New SqlDataAdapter("select * from item where itemstore=N'" & (store.Text) & "' and item_active = 'true' order by itemnamearabic ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        item_name.Properties.DataSource = dt
        item_name.Properties.DisplayMember = "itemnamearabic"
        item_name.Properties.ValueMember = "itemcode"
    End Sub

    Sub fill_store()
        Try
            store.Properties.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from store where store_active=N'true'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                store.Properties.Items.Add(dt.Rows(i).Item("store_name"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطاء برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub
    Sub fill_price()
        Try
            Dim adp As New SqlDataAdapter("select * from name_price where price_check='true'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                Dealing.Properties.Items.Add(dt.Rows(i).Item("name_price"))
            Next
        Catch ex As Exception
            MsgBox("حدث خطاء برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub
    Function getcodeaccounts(subname) As String
        Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("Cuscode") Else Return ""
    End Function


    Private Sub inserte_Click(sender As Object, e As EventArgs) Handles inserte.Click
        If item_type.Text = "مخزون بتاريخ صلاحية" Then
            Dim sumr As Decimal = 0.0
            For i = 0 To dgv_expire.Rows.Count - 1
                sumr = (sumr) + Val(dgv_expire.Rows(i).Cells(3).Value)
            Next
            If item_count.Text <> Format(sumr, "0.00") Then
                GroupBox3.Visible = True
                If dgv_expire.Rows.Count = 0 Then
                    dgv_expire.DataSource = Nothing
                    dgv_expire.Rows.Clear()
                    Dim adp As New SqlDataAdapter("select * from expire_item  where item_code=N'" & (item_name.EditValue) & "'", sqlconn)
                    Dim ds = New DataSet
                    adp.Fill(ds)
                    Dim dt = ds.Tables(0)
                    For s = 0 To dt.Rows.Count - 1
                        dgv_expire.Rows.Add()
                        dgv_expire.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
                        dgv_expire.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_count")
                        dgv_expire.Rows(s).Cells(2).Value = dt.Rows(s).Item("item_date")
                    Next
                End If
                Exit Sub
            End If
            For i = 0 To dgv_expire.Rows.Count - 1
                If dgv_expire.Rows(i).Cells(3).Value > 0 Then
                    dgv_final_expire.Rows.Add()
                    dgv_final_expire.Rows(dgv_final_expire.Rows.Count - 1).Cells(0).Value = dgv_expire.Rows(i).Cells(0).Value
                    dgv_final_expire.Rows(dgv_final_expire.Rows.Count - 1).Cells(1).Value = dgv_expire.Rows(i).Cells(2).Value.ToString
                    dgv_final_expire.Rows(dgv_final_expire.Rows.Count - 1).Cells(2).Value = dgv_expire.Rows(i).Cells(3).Value
                End If
            Next
        End If
        If item_type.Text = "مخزون بسريل" Then

            dgv_serial.DataSource = Nothing
            dgv_serial.Rows.Clear()
            Dim adp As New SqlDataAdapter("select * from item_srial  where item_code=N'" & (item_name.EditValue) & "'", sqlconn)
            Dim ds = New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                dgv_serial.Rows.Add()
                dgv_serial.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
                dgv_serial.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_serial")
            Next
            item_count.Text = 0
            GroupBox4.Visible = True
            Exit Sub
        End If
        If block_sale.Text = True Then
            MsgBox("هذا الصنف ممنوع من البيع")
            Exit Sub
        End If

        If item_name.Text = "" Then
            MsgBox("أدخل اسم الصنف")
            Exit Sub
        End If
        If item_type.Text <> "مخزون خدمة" Then
            If Val(item_count.Text) > Val(itemcouta.Text) Then
                MsgBox("الكمية اكبر من الكوتة", MsgBoxStyle.Critical, "خطاء")
                Exit Sub
            End If
            If XtraForm1.w1.Text = False Then
                If Val(show_buy.Text) > Val(item_price.Text) Then
                    MsgBox("لايمكن البيع باقل من سعر الشراء", MsgBoxStyle.Critical, "خطاء")
                    Exit Sub
                End If
            End If
            If XtraForm1.w2.Text = False Then
                If Val(item_count.Text) * Val(number_unit.Text) > Val(show_count.Text) Then
                    MsgBox("لايوجد كمية في المخزن", MsgBoxStyle.Critical, "خطاء")
                    Exit Sub
                End If
            End If
        End If

        For i = 0 To GridView2.RowCount - 1
            If GridView2.GetRowCellValue(i, "كود").ToString = item_name.EditValue And GridView2.GetRowCellValue(i, "الوحدة").ToString = item_unit.Text Then
                If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
                    MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
                    Exit Sub
                End If
                If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون بتاريخ صلاحية" Then
                    MsgBox("لايمكن التعديل علي صنف مخزون بتاريخ صلاحية يمكن حذفه فقط")
                    Exit Sub
                End If
                If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون بسريل" Then
                    MsgBox("لايمكن التعديل علي صنف بسريل يمكن حذفه فقط")
                    Exit Sub
                End If
                ' Try
                GridView2.SetRowCellValue(i, "الكمية", GridView2.GetRowCellValue(i, "الكمية") + 1)
                '  Try
                Dim aa As Decimal
                Dim sql1 = "select * from item where itemcode=N'" & GridView2.GetRowCellValue(i, "كود").ToString & "'"
                Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
                Dim ds1 As New DataSet
                adp1.Fill(ds1)
                Dim dt1 = ds1.Tables(0)
                If dt1.Rows.Count > 0 Then
                    Dim dr1 = dt1.Rows(0)

                    aa = dr1!itemcount
                End If

                GridView2.SetRowCellValue(i, "الكمية الحالية", aa - GridView2.GetRowCellValue(i, "الكمية").ToString)

                Dim ss As Integer
                Dim sql = "select * from unit_item where code=N'" & GridView2.GetRowCellValue(i, "كود").ToString & "' and unit=N'" & GridView2.GetRowCellValue(i, "الوحدة").ToString & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count > 0 Then
                    Dim dr55 = dt.Rows(0)
                    ss = dr55!count
                End If
                GridView2.SetRowCellValue(i, "الرصيد", ss * GridView2.GetRowCellValue(i, "الكمية").ToString)

                Dim t As Double = 0.0
                Dim d As Double = 0.0
                Dim s = (Val(GridView2.GetRowCellValue(i, "الخصم").ToString / 100))
                Dim sum = Format(Val(GridView2.GetRowCellValue(i, "السعر").ToString) * Val(s), "0.00")
                GridView2.SetRowCellValue(i, "خصم نقدي", Val(sum))

                GridView2.SetRowCellValue(i, "بعد الخصم", Val(GridView2.GetRowCellValue(i, "السعر").ToString) - Val(GridView2.GetRowCellValue(i, "خصم نقدي").ToString))
                GridView2.SetRowCellValue(i, "الربح", (Val(GridView2.GetRowCellValue(i, "السعر").ToString) - Val(GridView2.GetRowCellValue(i, "خصم نقدي").ToString) - Val(GridView2.GetRowCellValue(i, "سعر الشراء").ToString)) * Val(GridView2.GetRowCellValue(i, "الكمية").ToString))
                Dim x0 = GridView2.GetRowCellValue(i, "السعر").ToString - GridView2.GetRowCellValue(i, "خصم نقدي").ToString
                x0 = x0 * GridView2.GetRowCellValue(i, "الكمية").ToString
                GridView2.SetRowCellValue(i, "الأجمالي", x0)
                plus_invoice()
                'Catch ex As Exception
                'End Try
                'Catch ex As Exception
                '    MsgBox("فشل العملية")
                'End Try
                Exit Sub
            End If

        Next
        Dim dr As DataRow = dtt.NewRow()
        dr(0) = code2.Text
        dr(1) = item_name.Text
        dr(2) = item_unit.Text
        dr(3) = item_count.Text
        dr(4) = item_price.Text
        dr(5) = disc_rate.Text
        Dim x = Val(item_price.Text) - Val(item_descound.Text)
        x = x * Val(item_count.Text)
        dr(6) = Format(x, "0.00")
        If item_type.Text = "مخزون تجميع" Then
            dr(7) = 0
        Else
            dr(7) = (Val(item_price.Text) - Val(item_descound.Text) - Val(show_buy.Text)) * Val(item_count.Text)
        End If
        dr(8) = item_catorgey.Text
        dr(9) = item_company.Text
        dr(10) = tax_add.Text
        dr(11) = item_tax_add.Text
        If item_type.Text = "مخزون تجميع" Then
            dr(12) = 0
        Else
            dr(12) = Val(show_count.Text) - Val(item_count.Text)
        End If
        dr(13) = item_group.Text
        If item_type.Text = "مخزون تجميع" Then
            dr(14) = 0
        Else
            dr(14) = Val(item_count.Text) * Val(number_unit.Text)
        End If
        dr(15) = item_type.Text
        dr(16) = Val(show_buy.Text)
        dr(17) = Val(item_price.Text) - Val(item_descound.Text)
        dr(18) = Val(item_descound.Text)
        dtt.Rows.Add(dr)
        GridControl1.DataSource = dtt
        plus_invoice()
        If item_type.Text = "مخزون تجميع" Then
            For h = 0 To dgv_total.Rows.Count - 1
                item_name.Text = dgv_total.Rows(h).Cells(1).Value
                item_count.Text = dgv_total.Rows(h).Cells(2).Value * GridView2.GetRowCellValue(GridView2.DataRowCount - 1, "الكمية")
                inserte_total()
            Next
            item_name.Text = GridView2.GetRowCellValue(GridView2.DataRowCount - 1, "أسم الصنف")
            item_name.Select()
        End If
        Dim value As Integer = GridView2.RowCount
        GridView2.TopRowIndex = value
        GridView2.FocusedRowHandle = value
    End Sub
    Private Sub inserte_total()
        If block_sale.Text = True Then
            MsgBox("هذا الصنف ممنوع من البيع")
            Exit Sub
        End If

        If item_name.Text = "" Then
            MsgBox("أدخل اسم الصنف")
            Exit Sub
        End If

        If Val(item_count.Text) > Val(itemcouta.Text) Then
            MsgBox("الكمية اكبر من الكوتة", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        If XtraForm1.w1.Text = False Then
            If Val(show_buy.Text) > Val(item_price.Text) Then
                MsgBox("لايمكن البيع باقل من سعر الشراء", MsgBoxStyle.Critical, "خطاء")
                Exit Sub
            End If
        End If
        If XtraForm1.w2.Text = False Then
            If Val(item_count.Text) * Val(number_unit.Text) > Val(show_count.Text) Then
                MsgBox("لايوجد كمية في المخزن", MsgBoxStyle.Critical, "خطاء")
                Exit Sub
            End If
        End If
        dgv_10.Rows.Add()
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(0).Value = code2.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(1).Value = item_name.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(2).Value = item_unit.Text
        Dim x = Val(item_price.Text) - Val(item_descound.Text)
        x = x * Val(item_count.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(6).Value = Format(x, "0.00")
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(3).Value = item_count.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(14).Value = show_count.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(4).Value = item_price.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(5).Value = disc_rate.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(8).Value = (Val(item_price.Text) - Val(item_descound.Text) - Val(show_buy.Text)) * Val(item_count.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(9).Value = item_catorgey.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(10).Value = item_company.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(12).Value = tax_add.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(13).Value = item_tax_add.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(15).Value = item_group.Text
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(14).Value = Val(show_count.Text) - Val(item_count.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(16).Value = Val(item_count.Text) * Val(number_unit.Text)
        dgv_10.Rows(dgv_10.Rows.Count - 1).Cells(17).Value = GridView2.GetRowCellValue(GridView2.DataRowCount - 1, "كود").ToString
    End Sub
    Sub plus_invoice()

        Try

            GridView2.UpdateTotalSummary()
            invoice_pound.Text = GridView2.Columns("الأجمالي").SummaryItem.SummaryValue

            If Val(descound_Rate.Text) > 0 Then
                desc_value()
            End If
            after_total.Text = Val(invoice_pound.Text) - Val(invoice_descound.Text)
            If Val(tax_Rate.Text) > 0 Then
                tax_value()
            End If
            total_invoice.Text = Val(after_total.Text) + Val(invoice_tax.Text)

            invoice_count.Text = GridView2.Columns("الكمية").SummaryItem.SummaryValue

            earn_invoice.Text = GridView2.Columns("الربح").SummaryItem.SummaryValue
            Dim sumt As Decimal = 0.0

            For i = 0 To dgv_10.Rows.Count - 1
                sumt = (sumt) + Val(dgv_10.Rows(i).Cells(8).Value)
            Next


            earn_invoice.Text = (Val(earn_invoice.Text) + sumt) + (Val(descound_Values.Text) + Val(tax_Values.Text))

            pay_money_EditValueChanged(Nothing, Nothing)


        Catch ex As Exception

        End Try

    End Sub
    Private Sub total_damage_TextChanged(sender As Object, e As EventArgs) Handles total_invoice.TextChanged
        Try
            show_total.Text = total_invoice.Text
        Catch ex As Exception
            MsgBox("حدث خطاء برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub
    Private Sub item_name_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            item_count.Focus()
        End If
    End Sub
    Private Sub reason_damage_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            item_name.Focus()
            plus_invoice()
        End If
    End Sub
    Private Sub dgv_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs)
        If e.ColumnIndex = 7 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.clear1
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub
    Sub delete_iem()
        Try
            For i2 = 0 To 1000
                If dgv_10.Rows(i2).Cells(17).Value = GridView2.GetFocusedRowCellValue("كود") Then
                    dgv_10.Rows.Remove(dgv_10.Rows(i2))
                End If
            Next
        Catch ex As Exception

        End Try

    End Sub
    Sub delete_exp()
        Try
            For i2 = 0 To 1000
                If dgv_final_expire.Rows(i2).Cells(0).Value = GridView2.GetFocusedRowCellValue("كود") Then
                    dgv_final_expire.Rows.Remove(dgv_final_expire.Rows(i2))
                End If
            Next
        Catch ex As Exception

        End Try

    End Sub
    Sub delete_serial()
        Try
            For i2 = 0 To 1000
                If dgv_serial_no.Rows(i2).Cells(0).Value = GridView2.GetFocusedRowCellValue("كود") Then
                    dgv_serial_no.Rows.Remove(dgv_serial_no.Rows(i2))
                End If
            Next
        Catch ex As Exception

        End Try

    End Sub
    Sub msg_new()
        new_btn_Click(Nothing, Nothing)
    End Sub

    Private Sub Accounts_name_SelectedIndexChanged(sender As Object, e As EventArgs)

        Accounts_code.Text = getcodeaccounts(Accounts_name.Text)
    End Sub

    Sub balace_cus()
        Dim sql = "select * from customer where Cuscode=N'" & (Accounts_code.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            Accounts_balace.Text = dr!Accounts_balace
        End If
    End Sub
    Sub date_sale()
        Dim str = "select max(invoice_number) from invoice_add where Accounts_code=N'" & Accounts_code.Text & "'"
        Dim cmd As New SqlCommand(str, sqlconn)
        Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
        Dim dta As New DataTable("invoice_add")
        da.Fill(dta)
        Dim sumdebit As Decimal = 0.0
        If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
        Label22.Text = Val(sumdebit)
        Dim sql = "select * from invoice_add where invoice_number=N'" & (Label22.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)

    End Sub
    Private Sub item_descound_KeyDown(sender As Object, e As KeyEventArgs) Handles item_descound.KeyDown
        If e.KeyCode = Keys.Enter Then
            If e.KeyCode = Keys.Enter Then
                inserte_Click(Nothing, Nothing)
                item_name.Focus()
                plus_invoice()
            End If
        End If
    End Sub
    Private Sub monetary_CheckedChanged(sender As Object, e As EventArgs) Handles monetary.CheckedChanged
        If monetary.Checked = True Then
            colour_invoice.BackColor = Color.Gainsboro
            monetary.ForeColor = Color.Black
            Yup.ForeColor = Color.Black
            monetary.BackColor = Color.Gainsboro
            Yup.BackColor = Color.Gainsboro
        End If
        plus_invoice()
    End Sub
    Private Sub Yup_CheckedChanged(sender As Object, e As EventArgs) Handles Yup.CheckedChanged
        If Yup.Checked = True Then
            colour_invoice.BackColor = Color.RoyalBlue
            monetary.ForeColor = Color.White
            Yup.ForeColor = Color.White
            monetary.BackColor = Color.RoyalBlue
            Yup.BackColor = Color.RoyalBlue
        End If
        plus_invoice()
        If Yup.Checked = True Then
            pay_money.Text = 0
        End If
    End Sub
    Private Sub Label45_Click(sender As Object, e As EventArgs)
        If XtraForm1.m54.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تعديل صنف "
        f.MdiParent = XtraForm1
        f.show_data(code.Text)
        f.Show()
    End Sub
    Private Sub Label46_Click(sender As Object, e As EventArgs)
        fill_item()
        code.Text = item_name.EditValue
    End Sub
    Private Sub item_barcode_TextChanged(sender As Object, e As EventArgs)
        Try
            Dim sql = "select * from item_add where code=N'" & (code.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                code2.Text = dr!code
            End If
            inserte_Click(Nothing, Nothing)
            item_barcode.Focus()

        Catch ex As Exception
            MsgBox("حدث خطاء برجاء اعد تشغيل البرنامج وإعادة المحاولة")
        End Try
    End Sub

    Private Sub store_SelectedIndexChanged(sender As Object, e As EventArgs)
        fill_item()
    End Sub

    Function getcode(subname) As String

        Dim sql = "select * from item where itemstore=N'" & (store.Text) & "' and itemnamearabic=N'" & (item_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt As New DataTable
        dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then Return dt.Rows(0).Item("itemcode") Else Return ""
    End Function

    Private Sub invoice_add_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_price()
        new_btn_Click(Nothing, Nothing)
        item_barcode.Visible = False
        set_fatore()
        login_user()
        column_devexpress()
        tax_Rate.Text = XtraForm1.tax_invo.Text
        column_visible()


        SplitContainerControl1.SplitterPosition = My.Settings.f_size
    End Sub

    Private Sub item_tax()
        Dim sql = "select * from tax where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        tax_Rate.Text = dr!tax_invo
    End Sub

    Sub column_visible()
        Dim sql = "select * from column_visible where id=N'" & (1) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            GridColumn2.Visible = dr!GridColumn2
            GridColumn7.Visible = dr!GridColumn7
            GridColumn8.Visible = dr!GridColumn8
            GridColumn9.Visible = dr!GridColumn9
            GridColumn10.Visible = dr!GridColumn10
            disc_rate.Visible = dr!GridColumn10
            Label17.Visible = dr!GridColumn10
            inserte.Visible = dr!GridColumn10
            Label5.Visible = dr!GridColumn10
            item_total.Visible = dr!GridColumn10
            GridColumn13.Visible = dr!GridColumn13
            GridColumn12.Visible = dr!GridColumn12
            GridColumn5.Visible = dr!GridColumn5
            GridColumn22.Visible = dr!GridColumn22
            GridColumn23.Visible = dr!GridColumn23
            GridColumn1.Visible = dr!GridColumn1
            GridColumn14.Visible = dr!GridColumn14
            GridColumn11.Visible = dr!GridColumn11
            GridColumn15.Visible = dr!GridColumn15
            GridColumn16.Visible = dr!GridColumn16
            GridColumn17.Visible = dr!GridColumn17
            GridColumn18.Visible = dr!GridColumn18
            GridColumn19.Visible = dr!GridColumn19
            GridColumn20.Visible = dr!GridColumn20
            GridColumn3.Visible = dr!GridColumn3
            GridColumn4.Visible = dr!GridColumn4
            GridColumn6.Visible = dr!GridColumn6
            GridColumn21.Visible = dr!GridColumn21
        End If
    End Sub

    Sub column_devexpress()
        dtt = New DataTable
        dtt.Columns.Add("كود")
        dtt.Columns.Add("أسم الصنف")
        dtt.Columns.Add("الوحدة")
        dtt.Columns.Add("الكمية")
        dtt.Columns.Add("السعر")
        dtt.Columns.Add("الخصم")
        dtt.Columns.Add("الأجمالي")
        dtt.Columns.Add("الربح")
        dtt.Columns.Add("التصنيف")
        dtt.Columns.Add("الشركة")
        dtt.Columns.Add("ضريبة الصنف")
        dtt.Columns.Add("مصنعية التركيب")
        dtt.Columns.Add("الكمية الحالية")
        dtt.Columns.Add("المجموعة")
        dtt.Columns.Add("الرصيد")
        dtt.Columns.Add("نوع الصنف")
        dtt.Columns.Add("سعر الشراء")
        dtt.Columns.Add("بعد الخصم")
        dtt.Columns.Add("خصم نقدي")
        GridControl1.DataSource = dtt
    End Sub

    Sub login_user()
        Dim sql = "select * from users where user_name=N'" & (XtraForm1.user_name.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        Dim dr = dt.Rows(0)
        '========= بيانات اساسية============
        dr!user_time = Now.ToString("hh:mm")
        dr!user_seen = Now
        dr!user_page = "فاتورة مبيعات"
        Dim cmd As New SqlCommandBuilder(adp)
        adp.Update(dt)
    End Sub
    Sub set_fatore()
        If XtraForm1.w3.Text = False Then
            item_price.Enabled = False
        End If
        If XtraForm1.w4.Text = False Then
            Dealing.Enabled = False
        End If
        If XtraForm1.w5.Text = False Then
            descound_Rate.Enabled = False
            descound_Values.Enabled = False
        End If
        If XtraForm1.w6.Text = False Then
            tax_Rate.Enabled = False
            tax_Values.Enabled = False
        End If
        If XtraForm1.w7.Text = False Then
            Yup.Enabled = False
        End If
        If XtraForm1.w8.Text = False Then
            XtraTabPage7.PageVisible = False
        End If
        If XtraForm1.w9.Text = False Then
            XtraTabPage8.PageVisible = False
        End If
        If XtraForm1.w16.Text = False Then
            item_descound.Enabled = False
        End If
    End Sub

    Private Sub code_TextChanged(sender As Object, e As EventArgs) Handles code.TextChanged
        item_total2.Text = 0
        item_total.Text = 0
        count_text.Text = ""
        item_count.Text = 1
        item_type.Text = ""
        show_count.Text = ""

        Dim sql = "select * from item where itemcode=N'" & (code.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            code2.Text = dr!itemcode
            item_name.Text = dr!itemnamearabic
            show_count.Text = dr!itemcount
            item_notes.Text = dr!item_notes
            item_type.Text = dr!item_type
            If dr!item_type <> "مخزون خدمة" Then
                item_count.Text = 1
            Else
                item_count.Text = 0
            End If
            Try
                curency_item.Text = dr!curency2
            Catch ex As Exception

            End Try

            item_count.Text = 1
            fill_unit()
            fill_unit_item()
            item_catorgey.Text = dr!Itemtacategory
            item_company.Text = dr!itemcompany
            item_group.Text = dr!item_group
            item_tax_add.Text = dr!item_tax_add
            tax_add.Text = dr!tax_add
            block_sale.Text = dr!block_sale
            itemcouta.Text = dr!itemcouta

        End If

        If tax_add.Text > 0 Then
            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(tax_add.Text) / 100)
            Dim sum = Format(Val(show_buy.Text) * Val(s), "0.00")
            item_price.Text = Val(sum) + Val(show_buy.Text)
        End If


        item_unit_SelectedIndexChanged(Nothing, Nothing)

    End Sub
    Sub fill_unit()
        Try
            item_unit.Properties.Items.Clear()
            Dim adp As New SqlDataAdapter("select * from unit_item where CODE=N'" & (code.Text) & "'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For i = 0 To dt.Rows.Count - 1
                item_unit.Properties.Items.Add(dt.Rows(i).Item("unit"))
            Next
            item_unit.SelectedIndex = 0
        Catch ex As Exception

        End Try

    End Sub
    Sub fill_unit_item()
        Dim adp As New SqlDataAdapter("select * from unit_item where code=N'" & (code.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        DataGridView1.AutoGenerateColumns = False
        DataGridView1.DataSource = dt
        Try

            If DataGridView1.Rows.Count = 3 Then
                threeunit()
            End If
            If DataGridView1.Rows.Count = 2 Then
                twounit()
            End If
            If DataGridView1.Rows.Count = 1 Then
                oneunit()
            End If

        Catch ex As Exception

        End Try
        For i = 0 To DataGridView1.Rows.Count - 1
            If DataGridView1.Rows(i).Cells(2).Value <> 0 Then
                count_text.Text = count_text.Text & " و " & DataGridView1.Rows(i).Cells(2).Value & " " & DataGridView1.Rows(i).Cells(0).Value
            End If
        Next
    End Sub
    Private Sub threeunit()
        Try


            Dim unit1, unit2, unit3, qty1, qty2, qty3, qtyo2, qtyo3, fin1, fin2 As Double
            Dim qty = Val(show_count.Text)
            unit1 = Val(DataGridView1.Rows(0).Cells(1).Value)
            unit2 = Val(DataGridView1.Rows(1).Cells(1).Value)
            unit3 = Val(DataGridView1.Rows(2).Cells(1).Value)

            qty3 = Format(qty / unit3, "0000.")
            If qty3 * unit3 > qty Then
                qty3 = qty3 - 1
            End If

            qtyo3 = qty3 * unit3
            fin1 = qty - qtyo3



            qty2 = Format(fin1 / unit2, "00.")
            If qty2 * unit2 > fin1 Then
                qty2 = qty2 - 1
            End If

            qtyo2 = qty2 * unit2
            fin2 = fin1 - qtyo2
            qty1 = fin2 / unit1




            DataGridView1.Rows(0).Cells(2).Value = qty1

            DataGridView1.Rows(1).Cells(2).Value = qty2

            DataGridView1.Rows(2).Cells(2).Value = qty3
        Catch ex As Exception

        End Try
    End Sub
    Private Sub twounit()
        Try


            Dim unit2, unit3, qty2, qty3, qtyo3, fin1 As Double
            Dim qty = Val(show_count.Text)

            unit2 = Val(DataGridView1.Rows(0).Cells(1).Value)
            unit3 = Val(DataGridView1.Rows(1).Cells(1).Value)

            qty3 = Format(qty / unit3, "0000.")
            If qty3 * unit3 > qty Then
                qty3 = qty3 - 1
            End If

            qtyo3 = qty3 * unit3
            fin1 = qty - qtyo3
            qty2 = fin1 / unit2




            DataGridView1.Rows(0).Cells(2).Value = qty2

            DataGridView1.Rows(1).Cells(2).Value = qty3


        Catch ex As Exception

        End Try
    End Sub
    Private Sub oneunit()
        Try


            Dim unit1, qty1 As Double
            Dim qty = Val(show_count.Text)
            unit1 = Val(DataGridView1.Rows(0).Cells(1).Value)



            qty1 = qty / unit1


            DataGridView1.Rows(0).Cells(2).Value = qty1


        Catch ex As Exception

        End Try

    End Sub
    Sub fill_catorhey()
        Dim adp As New SqlDataAdapter("select * from Category", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_catarogy.AutoGenerateColumns = False
        dgv_catarogy.DataSource = dt

    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs)
        fill_store()
        fill_item()
        fill_tru()
        invoice_note.Text = ""
        invoice_number.Text = getlastcode("invoice_add", "invoice_number") + 1
        invoice_date.EditValue = Now.Date
        item_name.Text = ""
        new_balace.Text = 0
        item_count.Text = 0
        GroupBox3.Visible = False
        GroupBox4.Visible = False
        item_price.Text = 0
        Accounts_balace.Text = 0
        Accounts_code.Text = 0
        GroupBox6.Visible = False
        item_total2.Text = 0
        item_total.Text = 0
        GroupBox9.Visible = False
        fill_catorhey()
        show_count.Text = ""
        GridControl1.DataSource = Nothing
        show_buy.Text = ""
        Try
            dtt.Clear()
        Catch ex As Exception

        End Try
        GroupBox5.Visible = False
        disc_rate.Text = 0
        earn_invoice.Text = 0
        Dealing.SelectedIndex = 2
        item_notes.Text = ""
        cuscity.Text = ""
        item_tax_add.Text = 0
        tax_add.Text = 0
        Label19.Visible = False
        cusGovernorate.Text = ""
        CusCreditlimit.Text = 0
        pay_money.Text = 0
        tax_Rate.Text = XtraForm1.tax_invo.Text
        cus_price_private.Text = 0
        Cuscusgroup.Text = ""
        tax_Values.Text = 0


        Accounts_name.Text = "نقدي " + Now.Date
        invoice_tax.Text = 0
        tax_Values.Text = 0


        total_invoice.Text = 0

        total_invoice.Text = 0
        descound_Rate.Text = 0
        item_descound.Text = 0
        descound_Values.Text = 0
        after_total.Text = 0
        show_total.Text = 0

        invoice_descound.Text = 0
        invoice_count.Text = 0
        invoice_pound.Text = 0
        monetary.Checked = True
        dgv_10.DataSource = Nothing
        dgv_10.Rows.Clear()
        dgv_serial_no.DataSource = Nothing
        dgv_serial_no.Rows.Clear()
        dgv_final_expire.DataSource = Nothing
        dgv_final_expire.Rows.Clear()
        print_btn.Enabled = True
        store.Text = XtraForm1.store.Text
        If XtraForm1.m74.Checked = False Then
            show_buy.Visible = False
        End If
        If XtraForm1.m75.Checked = False Then
            show_count.Visible = False
        End If
        show_buy.Text = ""
        ComboBoxEdit1.SelectedIndex = 0
        ComboBoxEdit2.SelectedIndex = 0
        new_balace.Text = 0
    End Sub
    Private Sub SimpleButton9_Click(sender As Object, e As EventArgs)
        Me.Dispose()
    End Sub


    Private Sub print_btn_Click()
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_a5.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub invoice_add_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.F1 Then
            new_btn_Click(Nothing, Nothing)
        End If
        If e.KeyCode = Keys.F2 Then
            If save_btn.Enabled = True Then
                save_btn_Click(Nothing, Nothing)
            End If

        End If

        If e.KeyCode = Keys.F5 Then
            If print_btn.Enabled = True Then
                Button6_Click(Nothing, Nothing)
            End If

        End If
        If e.KeyCode = Keys.F6 Then
            Accounts_name.Focus()
        End If

        If e.KeyCode = Keys.F7 Then
            item_name.Focus()
        End If
        If e.KeyCode = Keys.F8 Then
            pay_money.Focus()
        End If

        If e.KeyCode = Keys.F12 Then
            Dim adp As New SqlDataAdapter("select (itemcode),(itemnamearabic),(itemcount),(itemprice1) from item where itemstore=N'" & (store.Text) & "'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            GridControl2.DataSource = dt
            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If

        If e.KeyCode = Keys.Escape Then
            If TextEdit1.Text <> Nothing Then
                TextEdit1.Text = ""
                TextEdit1.Focus()
                Exit Sub
            End If
            If item_name.Text <> Nothing Then
                item_name.Text = ""
                item_name.Focus()
                Exit Sub
            End If
            If MsgBox("هل انت متأكد من الخروج؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
                Exit Sub
            End If
            Me.Dispose()
        End If
    End Sub

    Private Sub pay_money_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Double.TryParse((pay_money.Text & e.KeyChar), Nothing) Then
            e.Handled = True
        End If
    End Sub

    Private Sub PictureBox2_Click(sender As Object, e As EventArgs)
        Process.Start("https://www.facebook.com/Beta-store-361440301309862/?ref=aymt_homepage_panel&eid=ARDbnMA3vmMHk2XGLGVvVV8J1evZLRO5o7Tvq5jTv8YqAAkiBzn4VxmaIZi4mobvru532j8T3ry184b9")
    End Sub

    Private Sub شكل1ToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem.Click
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_a4_2.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub شكل2ToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem.Click
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_a4.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub شكل1ToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem1.Click
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_a5.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub شكل2ToolStripMenuItem1_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem1.Click
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_a5_2.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub شكل1ToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles شكل1ToolStripMenuItem2.Click
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_pos.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub

    Private Sub شكل2ToolStripMenuItem2_Click(sender As Object, e As EventArgs) Handles شكل2ToolStripMenuItem2.Click
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim adp As New SqlDataAdapter("select * from invoice_print where invoice_number=N'" & (printcode.Text) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
            MsgBox("لايوجد بيانات لطباعتها", MsgBoxStyle.Critical, "خطاء")
            Exit Sub
        End If
        Dim rep As XtraReport = XtraReport.FromFile(Path.Combine(Application.StartupPath + "\print", "invoice_pos_2.vb.vb.repx"), True)
        rep.DataSource = dt
        If XtraForm1.RadioButton1.Checked = True Then
            Dim frm As New preview
            preview.DocumentViewer1.DocumentSource = rep
            preview.Show()
        Else
            Using PrtTool As New DevExpress.XtraReports.UI.ReportPrintTool(rep)
                PrtTool.Print(XtraForm1.printer_name.Text)
            End Using
        End If
    End Sub
    Private Sub TextBox24_TextChanged_1(sender As Object, e As EventArgs) Handles lastpay.TextChanged
        If GridView2.RowCount = 0 Then
            MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
            Exit Sub
        End If

        save_btn_Click(Nothing, Nothing)
        Dim sql = "select * from invoice_list where id=N'" & (lastpay.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            item_price.Text = dr!item_price
            disc_rate.Text = dr!item_descound
        End If
    End Sub
    Private Sub item_barcode_KeyDown(sender As Object, e As KeyEventArgs) Handles item_barcode.KeyDown
        If e.KeyCode = Keys.Enter Then

            Dim sql = "select * from unit_item where item_unit=N'" & (item_barcode.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                code.Text = dr!code
            Else
                MsgBox("لايوجد باركود لهذا الرقم")
                item_barcode.Text = ""
                item_barcode.Focus()
                Exit Sub
            End If

            Try
                Dim sql7 = "select * from unit_item where item_unit=N'" & (item_barcode.Text) & "'"
                Dim adp7 As New SqlDataAdapter(sql7, sqlconn)
                Dim ds7 As New DataSet
                adp7.Fill(ds7)
                Dim dt7 = ds7.Tables(0)
                If dt7.Rows.Count > 0 Then
                    Dim dr7 = dt7.Rows(0)
                    item_unit.Text = dr7!unit
                End If
            Catch ex As Exception

            End Try


            inserte_Click(Nothing, Nothing)
            item_barcode.Text = ""
            item_barcode.Focus()
        End If
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs)
        code.Text = getcode(item_name.Text)
    End Sub
    Private Sub Accounts_name_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from customer where cusname=N'" & (Accounts_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                If MsgBox("هذا العميل غير موجود هل تريد أضافتة ؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Ok Then
                    Dim f As customer_add = New customer_add()
                    f.Text = "تسجيل عميل"
                    f.new_cus()
                    f.MdiParent = XtraForm1
                    f.Cusname.Text = Accounts_name.Text
                    f.Cusname.Focus()
                    f.Show()
                End If


            End If
            item_name.Focus()
        End If
    End Sub

    Private Sub TextEdit1_KeyDown(sender As Object, e As KeyEventArgs) Handles item_count.KeyDown
        If item_type.Text = "مخزون بتاريخ صلاحية" Then
            GroupBox3.Visible = True

            dgv_expire.DataSource = Nothing
            dgv_expire.Rows.Clear()
            Dim adp As New SqlDataAdapter("select * from expire_item  where item_code=N'" & (item_name.EditValue) & "' and item_count > 0", sqlconn)
            Dim ds = New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                dgv_expire.Rows.Add()
                dgv_expire.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
                dgv_expire.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_count")
                dgv_expire.Rows(s).Cells(2).Value = dt.Rows(s).Item("item_date")
            Next


            Exit Sub
        End If

        If e.KeyCode = Keys.Enter Then
            item_price.Focus()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_name.Text.Length
        End If
        If e.KeyCode = Keys.ControlKey Then
            item_unit.Select()
            item_unit.SelectionStart = 0
            item_unit.SelectionLength = item_name.Text.Length
        End If
    End Sub

    Private Sub TextEdit1_KeyDown_1(sender As Object, e As KeyEventArgs) Handles item_price.KeyDown
        If e.KeyCode = Keys.Enter Then
            If disc_rate.Visible = False Then
                item_total2.Text = Val(item_price.Text) * Val(item_count.Text)
                item_total2.Select()
                item_total2.SelectionStart = 0
                item_total2.SelectionLength = item_total2.Text.Length

            Else

                disc_rate.Select()
                disc_rate.SelectionStart = 0
                disc_rate.SelectionLength = disc_rate.Text.Length
            End If
        End If
        If e.KeyCode = Keys.Left Then
            If disc_rate.Visible = False Then
                item_total2.Text = Val(item_price.Text) * Val(item_count.Text)
                item_total2.Select()
                item_total2.SelectionStart = 0
                item_total2.SelectionLength = item_total2.Text.Length

            Else

                disc_rate.Select()
                disc_rate.SelectionStart = 0
                disc_rate.SelectionLength = disc_rate.Text.Length
            End If
        End If
        If e.KeyCode = Keys.Right Then
            item_count.Select()
            item_count.SelectionStart = 0
            item_count.SelectionLength = item_count.Text.Length
        End If
    End Sub

    Private Sub item_name_KeyDown_1(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from item where itemnamearabic=N'" & (item_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                If MsgBox("هذا الصنف غير موجود هل تريد أضافتة ؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Ok Then
                    Dim f As item_add = New item_add()
                    f.Text = "تسجيل صنف"
                    f.MdiParent = XtraForm1
                    f.new_item()
                    f.Show()
                    f.new_unit()
                    f.itemunit()
                    f.itemnamearabic.Text = item_name.Text
                    f.itemnamearabic.Focus()
                End If


            End If
            code.Text = item_name.EditValue

            item_unit.Focus()
            item_unit.SelectionStart = 0
            item_unit.SelectionLength = item_name.Text.Length
        End If
        If e.KeyCode = Keys.ControlKey Then
            item_name.SelectionStart = 0
            item_name.SelectionLength = item_name.Text.Length
        End If

    End Sub
    Private Sub Button1_Click_1(sender As Object, e As EventArgs) Handles Button1.Click
        inserte_Click(Nothing, Nothing)
    End Sub

    Private Sub item_type_TextChanged(sender As Object, e As EventArgs) Handles item_type.TextChanged
        If item_type.Text = "مخزون تجميع" Then
            dgv_total.DataSource = Nothing
            dgv_total.Rows.Clear()
            Dim adp As New SqlDataAdapter("select * from item_collection  where item_first=N'" & (code.Text) & "'", sqlconn)
            Dim ds = New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            For s = 0 To dt.Rows.Count - 1
                dgv_total.Rows.Add()
                dgv_total.Rows(s).Cells(0).Value = dt.Rows(s).Item("item_code")
                dgv_total.Rows(s).Cells(1).Value = dt.Rows(s).Item("item_name")
                dgv_total.Rows(s).Cells(2).Value = dt.Rows(s).Item("item_count")
            Next
        End If
    End Sub

    Private Sub Button2_Click_1(sender As Object, e As EventArgs) Handles new_btn.Click
        new_btn_Click(Nothing, Nothing)
    End Sub
    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        If XtraForm1.w17.Text = True Then
            GroupBox9.Visible = True
            qq3.Text = Val(total_invoice.Text)
            qq1.Text = Val(show_total.Text)
            qq1.Focus()
        Else
            print_btn_Click()
        End If
    End Sub

    Private Sub CalcEdit1_KeyDown(sender As Object, e As KeyEventArgs) Handles descound_Rate.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(descound_Rate.Text) / 100)
            Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
            descound_Values.Text = Format(Val(sum), "0.00")
            invoice_descound.Text = Format(Val(descound_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub
    Sub desc_value()
        Dim t As Double = 0.0
        Dim d As Double = 0.0
        Dim s = (Val(descound_Rate.Text) / 100)
        Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
        descound_Values.Text = Format(Val(sum), "0.00")
        invoice_descound.Text = Format(Val(descound_Values.Text), "0.00")
    End Sub

    Private Sub CalcEdit2_KeyDown(sender As Object, e As KeyEventArgs) Handles descound_Values.KeyDown
        If e.KeyCode = Keys.Enter Then

            invoice_descound.Text = Val(descound_Values.Text)
            plus_invoice()
        End If
    End Sub

    Private Sub CalcEdit3_KeyDown(sender As Object, e As KeyEventArgs) Handles tax_Rate.KeyDown

        If e.KeyCode = Keys.Enter Then

            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(tax_Rate.Text) / 100)
            Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.00")
            tax_Values.Text = Format(Val(sum), "0.00")
            invoice_tax.Text = Format(Val(tax_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub
    Sub tax_value()
        Dim t As Double = 0.0
        Dim d As Double = 0.0
        Dim s = (Val(tax_Rate.Text) / 100)
        Dim sum = Format(Val(invoice_pound.Text) * Val(s), "0.0")
        tax_Values.Text = Format(Val(sum), "0.0")
        invoice_tax.Text = Format(Val(tax_Values.Text), "0.0")
    End Sub

    Private Sub CalcEdit4_KeyDown(sender As Object, e As KeyEventArgs) Handles tax_Values.KeyDown
        If e.KeyCode = Keys.Enter Then
            invoice_tax.Text = Format(Val(tax_Values.Text), "0.00")
            plus_invoice()
        End If
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        If XtraForm1.w17.Text = True Then
            GroupBox9.Visible = True
            qq3.Text = Val(show_total.Text)
            treasury_name.Rows(0).Cells(1).Value = Val(show_total.Text)
            treasury_name_CellEndEdit(Nothing, Nothing)
            treasury_name.ClearSelection()
            treasury_name.Rows(0).Cells(1).Selected = True
            treasury_name.Select()
        Else
            save_qq(Nothing, Nothing)
        End If
    End Sub

    Private Sub pay_money_EditValueChanged(sender As Object, e As EventArgs) Handles pay_money.EditValueChanged
        new_balace.Text = (Val(Accounts_balace.Text) + Val(total_invoice.Text)) - Val(pay_money.Text)
        money_plus.Text = (Val(Accounts_balace.Text) + Val(total_invoice.Text))
        TextBox8.Text = Val(pay_money.Text)
    End Sub
    Sub get_pos()
        Dim sql = "select * from invoice_add where Accounts_name=N'" & ("نقدي " + Now.Date) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            invoice_number.Text = dr!invoice_number
        End If

    End Sub

    Private Sub save_qq(sender As Object, e As EventArgs)

        Try
            If GridView2.RowCount = 0 Then
                MsgBox("يجب ادخال صنف علي الاقل", MsgBoxStyle.Exclamation, "تعليمات")
                Exit Sub
            End If
            invoice_number.Text = getlastcode("invoice_add", "invoice_number") + 1
            printcode.Text = getlastcode("invoice_add", "invoice_number") + 1
            plus_invoice()
            pay_money_EditValueChanged(Nothing, Nothing)
            Dim sql = "select * from invoice_add where Accounts_name=N'" & "نقدي " + Now.Date & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                '========= بيانات اساسية============
                dr!store = store.Text
                dr!invoice_note = invoice_note.Text
                dr!invoice_descound = Val(dr!invoice_descound) + Val(invoice_descound.Text)
                dr!invoice_tax = Val(dr!invoice_tax) + Val(invoice_tax.Text)
                dr!invoice_count = Val(dr!invoice_count) + Val(invoice_count.Text)
                dr!total_invoice = Val(dr!total_invoice) + Val(total_invoice.Text)
                dr!earn_invoice = Val(dr!earn_invoice) + Val(earn_invoice.Text)
                dr!pay_money = Val(dr!pay_money) + Val(pay_money.Text)
                dr!user_invoice = XtraForm1.user_name.Text
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                get_pos()
            Else
                Dim dr = dt.NewRow
                dr!invoice_number = invoice_number.Text
                dr!store = store.Text
                dr!invoice_date = invoice_date.EditValue
                dr!Accounts_name = "نقدي " + Now.Date
                dr!Accounts_code = Accounts_code.Text
                dr!invoice_note = invoice_note.Text
                dr!invoice_descound = invoice_descound.Text
                dr!invoice_tax = invoice_tax.Text
                dr!invoice_count = invoice_count.Text
                dr!total_invoice = total_invoice.Text
                dr!delegate_name = ""
                dr!earn_invoice = earn_invoice.Text
                dr!Cuscusgroup = Cuscusgroup.Text
                dr!cusGovernorate = cusGovernorate.Text
                dr!cuscity = cuscity.Text
                dr!pay_money = pay_money.Text
                dr!code_print = 1
                dr!user_invoice = XtraForm1.user_name.Text
                dr!total_string = amount_string.Text
                dr!invoice_pound = invoice_pound.Text
                dr!pay = pay_money.Text
                dr!new_balace = new_balace.Text
                dr!Accounts_adress = ""
                dr!Accounts_phone1 = ""
                dr!past_balace = Val(Accounts_balace.Text)
                dr!money_plus = money_plus.Text
                dr!Date_pay = Now.Date
                dr!type_money = "نقدي"
                dt.Rows.Add(dr)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            End If
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            '    '============حفظ السحل================
            adp = New SqlDataAdapter("select * from invoice_list", sqlconn)
            ds = New DataSet
            adp.Fill(ds)
            dt = ds.Tables(0)
            For i = 0 To GridView2.RowCount - 1
                Dim Dr = dt.NewRow
                Dr!invoice_number = invoice_number.Text
                Dr!store = store.Text
                Dr!Accounts_code = 0
                Dr!Accounts_name = "نقدي " + Now.Date
                Dr!item_code = GridView2.GetRowCellValue(i, "كود").ToString
                Dr!item_name = GridView2.GetRowCellValue(i, "أسم الصنف").ToString
                Dr!item_unit = GridView2.GetRowCellValue(i, "الوحدة").ToString
                Dr!item_count = GridView2.GetRowCellValue(i, "الكمية").ToString
                Dr!item_price = GridView2.GetRowCellValue(i, "السعر").ToString
                Dr!item_descound = GridView2.GetRowCellValue(i, "الخصم").ToString
                Dr!item_total = GridView2.GetRowCellValue(i, "الأجمالي").ToString
                Dr!item_date = invoice_date.EditValue
                Dr!item_earn = GridView2.GetRowCellValue(i, "الربح").ToString
                Dr!item_catorgey = GridView2.GetRowCellValue(i, "التصنيف").ToString
                Dr!item_company = GridView2.GetRowCellValue(i, "الشركة").ToString
                Dr!tax_add = GridView2.GetRowCellValue(i, "ضريبة الصنف").ToString
                Dr!item_tax_add = GridView2.GetRowCellValue(i, "مصنعية التركيب").ToString
                Dr!new_count = GridView2.GetRowCellValue(i, "الكمية الحالية").ToString
                Dr!item_group = GridView2.GetRowCellValue(i, "المجموعة").ToString
                Dr!item_balace = GridView2.GetRowCellValue(i, "الرصيد").ToString
                Dr!item_type = GridView2.GetRowCellValue(i, "نوع الصنف").ToString
                Dr!buy_item = GridView2.GetRowCellValue(i, "سعر الشراء").ToString
                Dr!after_desc = GridView2.GetRowCellValue(i, "بعد الخصم").ToString
                Dr!desc_money = GridView2.GetRowCellValue(i, "خصم نقدي").ToString
                dt.Rows.Add(Dr)
                Dim cmdbuilder As New SqlCommandBuilder(adp)
                adp.Update(dt)
                set_itemtrans(GridView2.GetRowCellValue(i, "أسم الصنف").ToString, invoice_date.EditValue, invoice_number.Text, "فاتورة مبيعات", 0, GridView2.GetRowCellValue(i, "الكمية").ToString, GridView2.GetRowCellValue(i, "الرصيد").ToString)

            Next
            '    '======================================== حفظ الحركات
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            '    '============حفظ السحل================
            If dgv_10.Rows.Count > 0 Then
                adp = New SqlDataAdapter("select * from invoice_collection", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                For g = 0 To dgv_10.Rows.Count - 1
                    Dim Dr = dt.NewRow
                    Dr!invoice_number = invoice_number.Text
                    Dr!store = store.Text
                    Dr!Accounts_code = Accounts_code.Text
                    Dr!Accounts_name = "نقدي " + Now.Date
                    Dr!item_code = dgv_10.Rows(g).Cells(0).Value
                    Dr!item_name = dgv_10.Rows(g).Cells(1).Value
                    Dr!item_unit = dgv_10.Rows(g).Cells(2).Value
                    Dr!item_count = dgv_10.Rows(g).Cells(3).Value
                    Dr!item_price = dgv_10.Rows(g).Cells(4).Value
                    Dr!item_descound = dgv_10.Rows(g).Cells(5).Value
                    Dr!item_total = Val(dgv_10.Rows(g).Cells(5).Value.ToString)
                    Dr!item_date = invoice_date.EditValue
                    Dr!item_earn = Convert.ToDecimal(dgv_10.Rows(g).Cells(8).Value)
                    Dr!item_catorgey = dgv_10.Rows(g).Cells(9).Value
                    Dr!item_company = dgv_10.Rows(g).Cells(10).Value
                    Dr!tax_add = dgv_10.Rows(g).Cells(12).Value
                    Dr!item_tax_add = dgv_10.Rows(g).Cells(13).Value
                    Dr!new_count = dgv_10.Rows(g).Cells(14).Value
                    Dr!item_group = dgv_10.Rows(g).Cells(15).Value
                    Dr!item_balace = dgv_10.Rows(g).Cells(16).Value
                    Dr!code_first = dgv_10.Rows(g).Cells(17).Value
                    dt.Rows.Add(Dr)
                    Dim cmdbuilder As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                    set_itemtrans(dgv_10.Rows(g).Cells(1).Value, invoice_date.EditValue, invoice_number.Text, "فاتورة مبيعات", 0, dgv_10.Rows(g).Cells(3).Value, dgv_10.Rows(g).Cells(14).Value)

                Next
            End If

            '    '======================================== حفظ الحركات
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            If dgv_serial_no.Rows.Count > 0 Then
                adp = New SqlDataAdapter("select * from invo_serial_no", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                For g = 0 To dgv_serial_no.Rows.Count - 1
                    Dim Dr = dt.NewRow
                    Dr!invoice_number = invoice_number.Text
                    Dr!item_code = dgv_serial_no.Rows(g).Cells(0).Value
                    Dr!serial_no = dgv_serial_no.Rows(g).Cells(1).Value
                    dt.Rows.Add(Dr)
                    Dim cmdbuilder As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                Next

            End If

            '    '======================================== حفظ الحركات
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            If dgv_final_expire.Rows.Count > 0 Then
                adp = New SqlDataAdapter("select * from invo_item_expire", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                For h = 0 To dgv_final_expire.Rows.Count - 1
                    Dim Dr = dt.NewRow
                    Dr!invoice_number = invoice_number.Text
                    Dr!item_code = dgv_final_expire.Rows(h).Cells(0).Value
                    Dr!item_expire = dgv_final_expire.Rows(h).Cells(1).Value.ToString
                    Dr!count = Val(dgv_final_expire.Rows(h).Cells(2).Value.ToString)
                    dt.Rows.Add(Dr)
                    Dim cmdbuilder As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                Next
            End If

            '    '======================================== حفظ الحركات
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            For a = 0 To dgv_serial_no.Rows.Count - 1
                sql = "select * from item_srial where item_serial=N'" & dgv_serial_no.Rows(a).Cells(1).Value & "'"
                adp = New SqlDataAdapter(sql, sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                If dt.Rows.Count = 0 Then
                Else
                    Dim dr = dt.Rows(0)
                    dr.Delete()
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()
            For o = 0 To dgv_final_expire.Rows.Count - 1
                adp = New SqlDataAdapter("select * from expire_item where item_code=N'" & dgv_final_expire.Rows(o).Cells(0).Value & "' and item_date=N'" & dgv_final_expire.Rows(o).Cells(1).Value.ToString & "'", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                Dim dr = dt.Rows(0)
                dr!item_count = Val(dr!item_count) - Val(dgv_final_expire.Rows(o).Cells(2).Value)
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            Next

            For a = 0 To GridView2.RowCount - 1
                If GridView2.GetRowCellValue(a, "الرصيد").ToString <> "نوع الصنف" Then
                    adp = New SqlDataAdapter("select * from item where itemcode=N'" & (GridView2.GetRowCellValue(a, "كود").ToString) & "'", sqlconn)
                    ds = New DataSet
                    adp.Fill(ds)
                    dt = ds.Tables(0)
                    Dim dr = dt.Rows(0)
                    dr!itemcount = Val(dr!itemcount) - GridView2.GetRowCellValue(a, "الرصيد").ToString
                    dr!Profits = Val(dr!Profits) + GridView2.GetRowCellValue(a, "الربح").ToString
                    dr!total_price = Val(dr!total_price) - (GridView2.GetRowCellValue(a, "الأجمالي").ToString - GridView2.GetRowCellValue(a, "الربح").ToString)
                    dr!date_sale = invoice_date.EditValue
                    Dim cmd As New SqlCommandBuilder(adp)
                    adp.Update(dt)
                End If
            Next
            adp.Dispose()
            ds.Dispose()
            dt.Dispose()

            For o = 0 To dgv_10.Rows.Count - 1
                adp = New SqlDataAdapter("select * from item where itemcode=N'" & (dgv_10.Rows(o).Cells(0).Value) & "'", sqlconn)
                ds = New DataSet
                adp.Fill(ds)
                dt = ds.Tables(0)
                Dim dr = dt.Rows(0)
                dr!itemcount = Val(dr!itemcount) - Val(dgv_10.Rows(o).Cells(16).Value)
                dr!Profits = Val(dr!Profits) + Val(dgv_10.Rows(o).Cells(8).Value)
                dr!total_price = Val(dr!total_price) - (Val(dgv_10.Rows(o).Cells(6).Value) - Val(dgv_10.Rows(o).Cells(8).Value))
                dr!date_sale = invoice_date.EditValue
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
            Next
            For i = 0 To treasury_name.Rows.Count - 1
                If Val(treasury_name.Rows(i).Cells(1).Value) > 0 Then
                    If monetary.Checked = True Then
                        treasury_pay(treasury_name.Rows(i).Cells(0).Value, "فاتورة مبيعات", invoice_date.EditValue, Now.ToString("hh:mm:ss:tt"), "فاتورة مبيعات الي " & Accounts_name.Text, XtraForm1.user_name.Text, Val(treasury_name.Rows(i).Cells(1).Value), 0, 0, invoice_number.Text)
                    ElseIf Yup.Checked = True Then
                        treasury_pay(treasury_name.Rows(i).Cells(0).Value, "فاتورة مبيعات", invoice_date.EditValue, Now.ToString("hh:mm:ss:tt"), "فاتورة مبيعات الي " & Accounts_name.Text, XtraForm1.user_name.Text, Val(treasury_name.Rows(i).Cells(1).Value), 0, 0, invoice_number.Text)
                    End If
                End If
            Next
            new_btn_Click(Nothing, Nothing)
            My.Computer.Audio.Play(My.Resources.suc, AudioPlayMode.Background)

        Catch ex As Exception
            MsgBox("فشل حفظ الفاتورة اعد المحاولة", MsgBoxStyle.Exclamation, "تعليمات")
        End Try

    End Sub
    Sub fill_tru()
        treasury_name.DataSource = Nothing
        Dim adp As New SqlDataAdapter("select (treasury_name) from treasury_name ", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        treasury_name.AutoGenerateColumns = False
        treasury_name.DataSource = dt
    End Sub

    Private Sub show_total_TextChanged(sender As Object, e As EventArgs) Handles show_total.TextChanged
        If show_total.Text = 0 Then
            amount_string.Visible = False
        Else
            amount_string.Visible = True
        End If
        amount_string.Text = NoToTxt(show_total.Text, XtraForm1.curn1.Text, XtraForm1.curn2.Text) & " " & "فقط لاغير"

    End Sub
    Private Sub CalcEdit1_KeyDown_1(sender As Object, e As KeyEventArgs) Handles disc_rate.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_total2.Text = (Val(item_price.Text) - Val(item_descound.Text)) * Val(item_count.Text)
            item_total.Select()
            item_total.SelectionStart = 0
            item_total.SelectionLength = item_total.Text.Length

        End If
        If e.KeyCode = Keys.Left Then
            item_total2.Text = (Val(item_price.Text) - Val(item_descound.Text)) * Val(item_count.Text)
            item_total.Select()
            item_total.SelectionStart = 0
            item_total.SelectionLength = item_total.Text.Length
        End If
        If e.KeyCode = Keys.Right Then
            item_price.Select()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_price.Text.Length
        End If
    End Sub

    Private Sub disc_rate_EditValueChanged(sender As Object, e As EventArgs) Handles disc_rate.EditValueChanged
        Dim t As Double = 0.0
        Dim d As Double = 0.0
        Dim s = (Val(disc_rate.Text) / 100)
        Dim sum = Format(Val(item_price.Text) * Val(s), "0.00")
        item_descound.Text = Val(sum)
    End Sub
    Sub clac()
        Try
            Dim aa As Integer
            Dim sql1 = "select * from item where itemcode=N'" & (GridView2.GetFocusedRowCellValue("كود")) & "'"
            Dim adp1 As New SqlDataAdapter(sql1, sqlconn)
            Dim ds1 As New DataSet
            adp1.Fill(ds1)
            Dim dt1 = ds1.Tables(0)
            If dt1.Rows.Count > 0 Then
                Dim dr1 = dt1.Rows(0)

                aa = dr1!itemcount
            End If

            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الكمية الحالية", aa - GridView2.GetFocusedRowCellValue("الكمية"))

            Dim ss As Integer
            Dim sql = "select * from unit_item where code=N'" & GridView2.GetFocusedRowCellValue("كود") & "' and unit=N'" & GridView2.GetFocusedRowCellValue("الوحدة") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                ss = dr!count
            End If
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الرصيد", ss * GridView2.GetFocusedRowCellValue("الكمية"))

            Dim t As Double = 0.0
            Dim d As Double = 0.0
            Dim s = (Val(GridView2.GetFocusedRowCellValue("الخصم")) / 100)
            Dim sum = Format(Val(GridView2.GetFocusedRowCellValue("السعر")) * Val(s), "0.00")
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "خصم نقدي", Val(sum))

            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "بعد الخصم", Val(GridView2.GetFocusedRowCellValue("السعر")) - Val(GridView2.GetFocusedRowCellValue("خصم نقدي")))
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الربح", (Val(GridView2.GetFocusedRowCellValue("السعر")) - Val(GridView2.GetFocusedRowCellValue("خصم نقدي")) - Val(GridView2.GetFocusedRowCellValue("سعر الشراء"))) * Val(GridView2.GetFocusedRowCellValue("الكمية")))
            Dim x = GridView2.GetFocusedRowCellValue("السعر") - GridView2.GetFocusedRowCellValue("خصم نقدي")
            x = x * GridView2.GetFocusedRowCellValue("الكمية")
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الأجمالي", x)
            plus_invoice()
        Catch ex As Exception
            MsgBox("فشل التعديل")
        End Try

    End Sub
    Private Sub RepositoryItemButtonEdit2_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit2.Click
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون بتاريخ صلاحية" Then
            MsgBox("لايمكن التعديل علي صنف مخزون بتاريخ صلاحية يمكن حذفه فقط")
            Exit Sub
        End If
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون بسريل" Then
            MsgBox("لايمكن التعديل علي صنف بسريل يمكن حذفه فقط")
            Exit Sub
        End If
        clac()
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs)
        If XtraForm1.m39.Checked = False Then
            XtraMessageBox.Show("لايوجد لك صلاحية", "خطاء", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As item_add = New item_add()
        f.Text = "تسجيل صنف"
        f.MdiParent = XtraForm1
        f.new_item()
        f.Show()
    End Sub

    Private Sub RepositoryItemButtonEdit3_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit3.Click
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        Try
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الكمية", GridView2.GetFocusedRowCellValue("الكمية") + 1)
            clac()
        Catch ex As Exception
            MsgBox("فشل العملية")
        End Try

    End Sub

    Private Sub RepositoryItemButtonEdit4_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit4.Click
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        Try
            GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الكمية", GridView2.GetFocusedRowCellValue("الكمية") - 1)
            clac()
        Catch ex As Exception
            MsgBox("فشل العملية")
        End Try
    End Sub


    Private Sub new_balace_TextChanged(sender As Object, e As EventArgs) Handles new_balace.TextChanged
        new_balace.Text = Math.Round(Val(new_balace.Text), 2)
    End Sub

    Private Sub TextBox8_TextChanged(sender As Object, e As EventArgs) Handles TextBox8.TextChanged
        TextBox8.Text = Math.Round(Val(TextBox8.Text), 2)
    End Sub
    Private Sub Button8_Click(sender As Object, e As EventArgs) Handles Button8.Click
        GridView2.ShowPrintPreview()
    End Sub
    Private Sub Button10_Click(sender As Object, e As EventArgs) Handles Button10.Click
        TextBox23.ForeColor = Color.White
        TextBox21.ForeColor = Color.White

        TextBox23.BackColor = Color.DimGray
        TextBox21.BackColor = Color.DimGray

        count_text.ForeColor = Color.White
        show_buy.ForeColor = Color.White

        item_notes.ForeColor = Color.White
        count_text.BackColor = Color.DimGray
        show_buy.BackColor = Color.DimGray

        item_notes.BackColor = Color.DimGray
        DataGridView1.BackColor = Color.DimGray

        LabelControl5.ForeColor = Color.White
        LabelControl3.ForeColor = Color.White
        LabelControl4.ForeColor = Color.White
        Accounts_balace.ForeColor = Color.White
        Label19.ForeColor = Color.White

        Label47.ForeColor = Color.White
        Label13.ForeColor = Color.White
        Label14.ForeColor = Color.White
        Label17.ForeColor = Color.White
        Label24.ForeColor = Color.White
        Label23.ForeColor = Color.White
        Label25.ForeColor = Color.White
        Label9.ForeColor = Color.White
        Label16.ForeColor = Color.White
        Label3.ForeColor = Color.White
        Label1.ForeColor = Color.White
        TextBox3.ForeColor = Color.White
        LabelControl1.ForeColor = Color.White
        LabelControl2.ForeColor = Color.White
        earn_invoice.ForeColor = Color.White
        show_total.ForeColor = Color.White
        amount_string.ForeColor = Color.White
        amount_string.BackColor = Color.DimGray
        invoice_descound.BackColor = Color.DimGray
        invoice_tax.BackColor = Color.DimGray
        TextBox8.BackColor = Color.DimGray
        new_balace.BackColor = Color.DimGray
        earn_invoice.BackColor = Color.DimGray
        show_total.BackColor = Color.DimGray
        Me.BackColor = Color.DimGray
        GridView2.OptionsView.EnableAppearanceEvenRow = False
        DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle("DevExpress Dark Style")
        XtraTabControl2.LookAndFeel.SkinName = ("DevExpress Dark Style")
    End Sub

    Private Sub store_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles store.SelectedIndexChanged
        fill_item()
    End Sub

    Private Sub Button11_Click(sender As Object, e As EventArgs) Handles Button11.Click
        GridView2.ShowCustomization()
    End Sub

    Private Sub item_unit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles item_unit.SelectedIndexChanged
        show_buy.Text = ""
        item_price.Text = 0
        disc_rate.Text = 0
        Dim sql = "select * from unit_item where code=N'" & (code.Text) & "' and unit=N'" & (item_unit.Text) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count > 0 Then
            Dim dr = dt.Rows(0)
            number_unit.Text = dr!count

            Dim tp As Double = 0.0
            Dim dp As Double = 0.0
            Dim sp = (Val(dr!disc_buy) / 100)
            Dim sump = Format(Val(dr!price_buy) * Val(sp), "0.00")
            Dim sui = Val(sump)

            show_buy.Text = dr!price_buy - sui

            If Dealing.SelectedIndex = 2 Then
                item_price.Text = dr!price1
            End If
            If Dealing.SelectedIndex = 3 Then
                item_price.Text = dr!price2
            End If
            If Dealing.SelectedIndex = 4 Then
                item_price.Text = dr!price3
            End If
            If Dealing.SelectedIndex = 5 Then
                item_price.Text = dr!price4
            End If
            If Dealing.SelectedIndex = 6 Then
                item_price.Text = dr!price5
            End If
            If Dealing.SelectedIndex = 1 Then
                Dim t As Double = 0.0
                Dim d As Double = 0.0
                Dim s = (Val(cus_price_private.Text) / 100)
                Dim sum = Format(Val(show_buy.Text) * Val(s), "0.00")
                item_price.Text = Val(sum) + Val(show_buy.Text)
            End If
            If Dealing.SelectedIndex = 0 Then
                Dim str = "select max(id) from invoice_list where item_name=N'" & item_name.Text & "' and Accounts_name=N'" & "نقدي " + Now.Date & "' and item_unit=N'" & item_unit.Text & "'"
                Dim cmd As New SqlCommand(str, sqlconn)
                Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
                Dim dta As New DataTable("invoice_list")
                da.Fill(dta)
                Dim sumdebit As Decimal = 0.0
                If IsDBNull(dta.Rows(0).Item(0)) = False Then sumdebit = dta.Rows(0).Item(0)
                lastpay.Text = Val(sumdebit)

            End If
            Select Case Dealing.SelectedIndex
                Case 2
                    disc_rate.Text = dr!dicoundprice1
                Case 3
                    disc_rate.Text = dr!dicoundprice2
                Case 4
                    disc_rate.Text = dr!dicoundprice3
                Case 5
                    disc_rate.Text = dr!dicoundprice4
                Case 6
                    disc_rate.Text = dr!dicoundprice5
                Case 1
                    item_descound.Text = 0
                Case 0
                    item_descound.Text = 0
            End Select
        End If
    End Sub

    Private Sub ComboBoxEdit3_KeyDown(sender As Object, e As KeyEventArgs) Handles item_unit.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_count.Select()
            item_count.SelectionStart = 0
            item_count.SelectionLength = item_name.Text.Length
        End If
        If e.KeyCode = Keys.ControlKey Then
            item_name.Select()
            item_name.SelectionStart = 0
            item_name.SelectionLength = item_name.Text.Length
        End If
    End Sub

    Private Sub ComboBoxEdit3_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxEdit1.SelectedIndexChanged
        If ComboBoxEdit1.SelectedIndex = 0 Then
            descound_Values.Visible = False
            descound_Rate.Visible = True
        ElseIf ComboBoxEdit1.SelectedIndex = 1 Then
            descound_Values.Visible = True
            descound_Rate.Visible = False
        End If
    End Sub

    Private Sub ComboBoxEdit4_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxEdit2.SelectedIndexChanged
        If ComboBoxEdit1.SelectedIndex = 0 Then
            tax_Values.Visible = False
            tax_Rate.Visible = True
        ElseIf ComboBoxEdit1.SelectedIndex = 1 Then
            tax_Values.Visible = True
            tax_Rate.Visible = False
        End If
    End Sub

    Private Sub ComboBoxEdit3_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles Dealing.SelectedIndexChanged
        code_TextChanged(Nothing, Nothing)
        item_unit_SelectedIndexChanged(Nothing, Nothing)
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs)
        price_item.code.Text = GridView2.GetFocusedRowCellValue("كود")
        price_item.item_name.Text = GridView2.GetFocusedRowCellValue("أسم الصنف")
        price_item.Show()
    End Sub


    Private Sub print_btn_MouseDown(sender As Object, e As MouseEventArgs) Handles print_btn.MouseDown
        If (e.Button = MouseButtons.Right) Then
            ContextMenuStrip1.Show(Me, e.Location)
        End If
    End Sub

    Private Sub RepositoryItemButtonEdit5_Click(sender As Object, e As EventArgs) Handles RepositoryItemButtonEdit5.Click
        If MsgBox("هل انت متأكد بحذف الصنف المحدد؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Cancel Then
            Exit Sub
        End If
        Try
            For h = 0 To dgv_10.Rows.Count - 1
                delete_iem()
            Next
            For a = 0 To dgv_final_expire.Rows.Count - 1
                delete_exp()
            Next
            For f = 0 To dgv_serial_no.Rows.Count - 1
                delete_serial()
            Next

            GridView2.DeleteRow(GridView2.FocusedRowHandle)
            plus_invoice()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        If XtraForm1.w17.Text = True Then
            GroupBox9.Visible = True
            qq3.Text = Val(show_total.Text)
            treasury_name.Rows(0).Cells(1).Value = Val(show_total.Text)
            treasury_name_CellEndEdit(Nothing, Nothing)
            treasury_name.ClearSelection()
            treasury_name.Rows(0).Cells(1).Selected = True
            treasury_name.Select()
        Else
            save_qq(Nothing, Nothing)
        End If
    End Sub

    Private Sub Button7_Click(sender As Object, e As EventArgs)
        System.Diagnostics.Process.Start("CALC")
    End Sub
    Private Sub Button5_Click_1(sender As Object, e As EventArgs)
        order_item.Show()
        order_item.item_name.Text = item_name.Text
        order_item.code.Text = code.Text
    End Sub

    Private Sub GridView2_KeyUp(sender As Object, e As KeyEventArgs) Handles GridView2.KeyUp
        If GridView2.GetFocusedRowCellValue("نوع الصنف") = "مخزون تجميع" Then
            MsgBox("لايمكن التعديل علي صنف مجمع يمكن حذفه فقط")
            Exit Sub
        End If
        clac()
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        GroupBox3.Visible = False
    End Sub

    Private Sub dgv_expire_CellEndEdit(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_expire.CellEndEdit
        If Val(dgv_expire.CurrentRow.Cells(3).Value.ToString) > Val(dgv_expire.CurrentRow.Cells(1).Value.ToString) Then
            MsgBox("هذه الكمية اكبر من الكمية الحالية لهذه الصلاحية")
            dgv_expire.CurrentRow.Cells(3).Value = Val(dgv_expire.CurrentRow.Cells(1).Value.ToString)
        End If
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click

        Dim sumr As Decimal = 0.0
        For i = 0 To dgv_expire.Rows.Count - 1
            sumr = (sumr) + Val(dgv_expire.Rows(i).Cells(3).Value)
        Next
        item_count.Text = Format(sumr, "0.00")
        GroupBox3.Visible = False
        item_price.Focus()
        item_price.SelectionStart = 0
        item_price.SelectionLength = item_name.Text.Length
    End Sub

    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        If block_sale.Text = True Then
            MsgBox("هذا الصنف ممنوع من البيع")
            Exit Sub
        End If

        If item_name.Text = "" Then
            MsgBox("أدخل اسم الصنف")
            Exit Sub
        End If

        Dim dr As DataRow = dtt.NewRow()
        dr(0) = code2.Text
        dr(1) = item_name.Text
        dr(2) = item_unit.Text
        dr(3) = item_count.Text
        dr(4) = item_price.Text
        dr(5) = disc_rate.Text
        Dim x = Val(item_price.Text) - Val(item_descound.Text)
        x = x * Val(item_count.Text)
        dr(6) = Format(x, "0.00")
        dr(7) = (Val(item_price.Text) - Val(item_descound.Text) - Val(show_buy.Text)) * Val(item_count.Text)
        dr(8) = item_catorgey.Text
        dr(9) = item_company.Text
        dr(10) = tax_add.Text
        dr(11) = item_tax_add.Text
        dr(12) = Val(show_count.Text) - Val(item_count.Text)
        dr(13) = item_group.Text
        dr(14) = Val(item_count.Text) * Val(number_unit.Text)
        dr(15) = item_type.Text
        dr(16) = Val(show_buy.Text)
        dr(17) = Val(item_price.Text) - Val(item_descound.Text)
        dr(18) = Val(item_descound.Text)
        dtt.Rows.Add(dr)
        GridControl1.DataSource = dtt
        plus_invoice()
        Dim value As Integer = GridView2.RowCount
        GridView2.TopRowIndex = value
        GridView2.FocusedRowHandle = value
        GroupBox4.Visible = False
    End Sub

    Private Sub dgv_serial_CellPainting(sender As Object, e As DataGridViewCellPaintingEventArgs) Handles dgv_serial.CellPainting
        If e.ColumnIndex = 2 AndAlso e.RowIndex >= 0 Then
            e.Paint(e.CellBounds, DataGridViewPaintParts.All)
            Dim img As Image = My.Resources.check
            e.Graphics.DrawImage(img, e.CellBounds.Left + 20, e.CellBounds.Top + 7, 10, 10)
            e.Handled = True
        End If
    End Sub

    Private Sub dgv_serial_Click(sender As Object, e As EventArgs) Handles dgv_serial.Click

    End Sub

    Private Sub dgv_serial_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_serial.CellClick
        Try
            If e.ColumnIndex = 2 Then
                dgv_serial_no.Rows.Add()
                dgv_serial_no.Rows(dgv_serial_no.Rows.Count - 1).Cells(0).Value = item_name.EditValue
                dgv_serial_no.Rows(dgv_serial_no.Rows.Count - 1).Cells(1).Value = dgv_serial.CurrentRow.Cells(1).Value.ToString
                dgv_serial.Rows.Remove(dgv_serial.CurrentRow)
                item_count.Text = Val(item_count.Text) + 1
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub serial_search_EditValueChanged(sender As Object, e As EventArgs) Handles serial_search.EditValueChanged
        Dim adp As New SqlDataAdapter("select * from item_srial where item_code=N'" & (item_name.EditValue) & "' and item_serial like N'" & serial_search.Text & "%" & "'" & "", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_serial.AutoGenerateColumns = False
        dgv_serial.DataSource = dt
    End Sub

    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        Try
            For i = 0 To dgv_serial_no.Rows.Count - 1
                If Val(dgv_serial_no.Rows(i).Cells(0).Value.ToString) = item_name.EditValue Then
                    dgv_serial_no.Rows.Remove(dgv_serial_no.Rows(i))
                End If
            Next
            GroupBox4.Visible = False
        Catch ex As Exception

        End Try

    End Sub

    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        GroupBox5.Visible = False
    End Sub

    Private Sub Button6_Click_1(sender As Object, e As EventArgs)
        GroupBox5.Visible = True
        cobon_code.Focus()
    End Sub


    Private Sub Button2_Click(sender As Object, e As EventArgs)
        Dim f As frmbarcode = New frmbarcode()
        f.Text = "طباعة باركود"
        f.MdiParent = XtraForm1
        f.Show()
        f.txtpname.Text = item_name.Text
        f.txtPrice.Text = item_count.Text
        Dim sql = "select * from barcode where itemcode=N'" & (GridView2.GetFocusedRowCellValue("itemcode")) & "'"
        Dim adp As New SqlDataAdapter(sql, sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        If dt.Rows.Count = 0 Then
        Else
            Dim dr = dt.Rows(0)
            f.txtbarcode.Text = dr!barcode
        End If
    End Sub

    Private Sub cobon_code_KeyDown(sender As Object, e As KeyEventArgs) Handles cobon_code.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from cobon where code=N'" & (cobon_code.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count > 0 Then
                Dim dr = dt.Rows(0)
                start_date.Text = dr!start_date
                end_date.Text = dr!end_date
                amount_rate.Text = dr!amount_rate
            End If
        End If
    End Sub
    Private Sub LookUpEdit1_EditValueChanged_1(sender As Object, e As EventArgs) Handles item_name.EditValueChanged
        code.Text = item_name.EditValue
    End Sub

    Private Sub LookUpEdit1_KeyDown(sender As Object, e As KeyEventArgs) Handles item_name.KeyDown
        If e.KeyCode = Keys.Enter Then
            Dim sql = "select * from item where itemnamearabic=N'" & (item_name.Text) & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                If MsgBox("هذا الصنف غير موجود هل تريد أضافتة ؟", MsgBoxStyle.OkCancel, "تأكيد") = MsgBoxResult.Ok Then
                    Dim f As item_add = New item_add()
                    f.Text = "تسجيل صنف"
                    f.MdiParent = XtraForm1
                    f.new_item()
                    f.Show()
                    f.new_unit()
                    f.itemunit()
                    f.itemnamearabic.Text = item_name.Text
                    f.itemnamearabic.Focus()
                End If

                code.Text = item_name.EditValue
                check = 2

            End If
            item_unit.Focus()
            item_unit.SelectionStart = 0
            item_unit.SelectionLength = item_name.Text.Length
        End If

        If e.KeyCode = Keys.Alt Then
            item_name.SelectionStart = 0
            item_name.SelectionLength = item_name.Text.Length
        End If
    End Sub
    Private Sub save_btn_Click(sender As Object, e As EventArgs) Handles save_btn.Click
        If XtraForm1.w17.Text = True Then
            GroupBox9.Visible = True
            qq3.Text = Val(show_total.Text)
            treasury_name.Rows(0).Cells(1).Value = Val(show_total.Text)
            treasury_name_CellEndEdit(Nothing, Nothing)
            treasury_name.ClearSelection()
            treasury_name.Rows(0).Cells(1).Selected = True
            treasury_name.Select()
        Else
            save_qq(Nothing, Nothing)
        End If
    End Sub

    Private Sub TextEdit1_EditValueChanged(sender As Object, e As EventArgs) Handles TextEdit1.EditValueChanged
        If TextEdit1.Text = "" Then
            GridControl2.DataSource = Nothing
            Exit Sub
        End If
        Dim zStr As String

        zStr = Replace$(Trim$(TextEdit1.Text), " ", "")

        If Len(Trim$(zStr)) > 1 Then

            Dim adp As New SqlDataAdapter("select (itemcode),(itemnamearabic),(itemcount),(itemprice1) from item where itemstore=N'" & (store.Text) & "' and itemnamearabic like N'%" & (TextEdit1.Text) & "%'", sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            GridControl2.DataSource = dt
        End If
    End Sub

    Private Sub TextEdit1_KeyDown_2(sender As Object, e As KeyEventArgs) Handles TextEdit1.KeyDown
        If e.KeyCode = Keys.Enter Then

            If GridView1.RowCount = 0 Then
                Dim sss As Integer = 0
                Dim sql = "select code from unit_item where item_unit=N'" & (TextEdit1.Text) & "'"
                Dim adp As New SqlDataAdapter(sql, sqlconn)
                Dim ds As New DataSet
                adp.Fill(ds)
                Dim dt = ds.Tables(0)
                If dt.Rows.Count > 0 Then
                    Dim dr = dt.Rows(0)
                    code.Text = dr!code
                Else
                    If XtraForm1.balance_check.Text = True Then
                        sss = 1
                        Dim sql4 = "select code from unit_item where item_unit=N'" & Mid(TextEdit1.Text, 3, XtraForm1.balance_w1.Text) & "'"
                        Dim adp4 As New SqlDataAdapter(sql4, sqlconn)
                        Dim ds4 As New DataSet
                        adp4.Fill(ds4)
                        Dim dt4 = ds4.Tables(0)
                        If dt4.Rows.Count > 0 Then
                            Dim dr4 = dt4.Rows(0)
                            code.Text = dr4!code

                        Else
                            MsgBox("لايوجد باركود لهذا الرقم")
                            item_barcode.Text = ""
                            item_barcode.Focus()

                            Exit Sub
                        End If
                    Else
                        MsgBox("لايوجد باركود لهذا الرقم")
                        item_barcode.Text = ""
                        item_barcode.Focus()

                        Exit Sub
                    End If


                End If
                Try
                    Dim sql7 = "select unit from unit_item where item_unit=N'" & (TextEdit1.Text) & "'"
                    Dim adp7 As New SqlDataAdapter(sql7, sqlconn)
                    Dim ds7 As New DataSet
                    adp7.Fill(ds7)
                    Dim dt7 = ds7.Tables(0)
                    If dt7.Rows.Count > 0 Then
                        Dim dr7 = dt7.Rows(0)
                        item_unit.Text = dr7!unit
                    End If
                Catch ex As Exception
                End Try
                If sss = 1 Then
                    Dim aa As Decimal = Mid(TextEdit1.Text, XtraForm1.balance_w1.Text + 3, XtraForm1.balance_w2.Text)
                    item_count.Text = aa / 1000
                End If

                inserte_Click(Nothing, Nothing)
                TextEdit1.Text = ""
                TextEdit1.Focus()
            Else
                GridControl2.DefaultView.Focus()
            End If
        End If

        If e.KeyCode = Keys.Down Then
            GridControl2.DefaultView.Focus()
        End If
    End Sub

    Private Sub GridView1_KeyDown(sender As Object, e As KeyEventArgs) Handles GridView1.KeyDown
        If e.KeyCode = Keys.Enter Then
            item_name.Text = GridView1.GetFocusedRowCellValue("itemnamearabic")
            check = 1
            item_unit.Focus()
        End If
    End Sub

    Private Sub DataGridView2_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_catarogy.CellClick
        Dim adp As New SqlDataAdapter("select * from item where itemstore=N'" & (store.Text) & "' and item_active = 'true' and Itemtacategory=N'" & (dgv_catarogy.CurrentRow.Cells(0).Value) & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv_touch.DataSource = dt
    End Sub

    Private Sub CardView2_Click(sender As Object, e As EventArgs) Handles CardView2.Click
        Try
            item_name.Text = CardView2.GetFocusedRowCellValue("itemnamearabic").ToString
            inserte_Click(Nothing, Nothing)

        Catch ex As Exception

        End Try
    End Sub
    Private Sub qq1_EditValueChanged(sender As Object, e As EventArgs)
        pay_money.Text = Val(qq1.Text)
        qq5.Text = Val(qq3.Text) - Val(qq1.Text)

    End Sub
    Private Sub qq1_KeyDown(sender As Object, e As KeyEventArgs)
        If e.Control = True And e.KeyCode = Keys.Enter Then

            save_qq(Nothing, Nothing)
            Exit Sub
        End If
        If e.KeyCode = Keys.Enter Then
            print_btn_Click()
        End If
    End Sub

    '===================== وحدة الصنف
    Private Sub GridView2_DoubleClick(sender As Object, e As EventArgs) Handles GridView2.DoubleClick
        Label6.Text = GridView2.GetFocusedRowCellValue("أسم الصنف")
        Dim adp As New SqlDataAdapter("select (unit),(count),(price1) from unit_item where code=N'" & GridView2.GetFocusedRowCellValue("كود") & "'", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        GridControl3.DataSource = dt
        GroupBox6.Visible = True
    End Sub
    Private Sub GridView3_DoubleClick(sender As Object, e As EventArgs) Handles GridView3.DoubleClick
        GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "الوحدة", GridView3.GetFocusedRowCellValue("unit"))
        GridView2.SetRowCellValue(GridView2.FocusedRowHandle, "السعر", GridView3.GetFocusedRowCellValue("price1"))
        clac()
        GroupBox6.Visible = False
    End Sub

    Private Sub SimpleButton10_Click(sender As Object, e As EventArgs) Handles SimpleButton10.Click
        GroupBox6.Visible = False
    End Sub

    Private Sub item_total2_EditValueChanged(sender As Object, e As EventArgs) Handles item_total2.EditValueChanged
        item_count.Text = Val(item_total2.Text) / Val(item_price.Text)
        item_count.Text = Math.Round(Val(item_count.Text), 2)
    End Sub

    Private Sub item_total_EditValueChanged(sender As Object, e As EventArgs) Handles item_total.EditValueChanged
        item_count.Text = Val(item_total.Text) / Val(item_price.Text)
        item_count.Text = Math.Round(Val(item_count.Text), 2)
    End Sub

    Private Sub item_total_KeyDown(sender As Object, e As KeyEventArgs) Handles item_total.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()

        End If


        If e.KeyCode = Keys.Left Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If

        If e.KeyCode = Keys.Right Then
            disc_rate.Select()
            disc_rate.SelectionStart = 0
            disc_rate.SelectionLength = disc_rate.Text.Length
        End If
    End Sub

    Private Sub item_total2_KeyDown(sender As Object, e As KeyEventArgs) Handles item_total2.KeyDown
        If e.KeyCode = Keys.Enter Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()
            TextEdit1.Text = ""
            TextEdit1.Focus()

        End If


        If e.KeyCode = Keys.Left Then
            inserte_Click(Nothing, Nothing)
            plus_invoice()

            TextEdit1.Text = ""
            TextEdit1.Focus()
        End If

        If e.KeyCode = Keys.Right Then
            item_price.Select()
            item_price.SelectionStart = 0
            item_price.SelectionLength = item_price.Text.Length
        End If
    End Sub
    Private Sub treasury_name_CurrentCellChanged(sender As Object, e As EventArgs) Handles treasury_name.CurrentCellChanged
        Dim sumt As Decimal = 0.0
        For i = 0 To treasury_name.Rows.Count - 1
            sumt = (sumt) + Val(treasury_name.Rows(i).Cells(1).Value)
        Next
        qq1.Text = Format(sumt, "0.00")

    End Sub

    Private Sub treasury_name_CellBeginEdit(sender As Object, e As DataGridViewCellCancelEventArgs) Handles treasury_name.CellBeginEdit
        Dim sumt As Decimal = 0.0
        For i = 0 To treasury_name.Rows.Count - 1
            sumt = (sumt) + Val(treasury_name.Rows(i).Cells(1).Value)
        Next
        qq1.Text = Format(sumt, "0.00")

    End Sub

    Private Sub treasury_name_CellEndEdit(sender As Object, e As DataGridViewCellEventArgs) Handles treasury_name.CellEndEdit
        Dim sumt As Decimal = 0.0
        For i = 0 To treasury_name.Rows.Count - 1
            sumt = (sumt) + Val(treasury_name.Rows(i).Cells(1).Value)
        Next
        qq1.Text = Format(sumt, "0.00")

    End Sub

    Private Sub SimpleButton42_Click(sender As Object, e As EventArgs) Handles SimpleButton42.Click
        save_qq(Nothing, Nothing)
    End Sub

    Private Sub SimpleButton43_Click(sender As Object, e As EventArgs) Handles SimpleButton43.Click
        print_btn_Click()
    End Sub

    Private Sub SimpleButton41_Click(sender As Object, e As EventArgs) Handles SimpleButton41.Click
        GroupBox9.Visible = False

    End Sub

    Private Sub qq1_EditValueChanged_1(sender As Object, e As EventArgs) Handles qq1.EditValueChanged
        qq5.Text = Val(qq3.Text) - Val(qq1.Text)
        pay_money.Text = Val(qq1.Text)

    End Sub
End Class