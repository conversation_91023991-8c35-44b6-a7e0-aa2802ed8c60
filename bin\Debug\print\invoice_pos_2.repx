﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="17.2.6.0" Ref="1" ControlType="spaxet.invoice_pos_2, spaxet store, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null" Name="invoice_pos_2" RollPaper="true" Margins="0, 0, 0, 0" PaperKind="Custom" PageWidth="251" PageHeight="1100" Version="17.2" DataMember="invoice_print" DataSource="#Ref-0">
  <Bands>
    <Item1 Ref="2" ControlType="DetailBand" Name="Detail" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item2 Ref="3" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item3 Ref="4" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" TextAlignment="TopLeft" Padding="0,0,0,0,100" />
    <Item4 Ref="5" ControlType="DetailReportBand" Name="DetailReport" Level="0">
      <Bands>
        <Item1 Ref="6" ControlType="DetailBand" Name="Detail1" HeightF="20.83333">
          <Controls>
            <Item1 Ref="7" ControlType="XRTable" Name="XrTable1" TextAlignment="MiddleCenter" SizeF="243.6874,17.0573" LocationFloat="0.6563034, 0" Font="Droid Arabic Kufi, 7pt" ForeColor="Black" BackColor="White" BorderColor="White">
              <Rows>
                <Item1 Ref="8" ControlType="XRTableRow" Name="XrTableRow1" Weight="1">
                  <Cells>
                    <Item1 Ref="9" ControlType="XRTableCell" Name="XrTableCell1" Weight="0.34842684818111214" TextFormatString="{0:#,#}" Text="الاجمالي" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="10" Expression="[item_total]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="11" UsePadding="false" />
                    </Item1>
                    <Item2 Ref="12" ControlType="XRTableCell" Name="XrTableCell2" Weight="0.30313313203298747" TextFormatString="{0:#.00}" Text="السعر" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="13" Expression="[item_price]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                    </Item2>
                    <Item3 Ref="14" ControlType="XRTableCell" Name="XrTableCell3" Weight="0.28168509471873737" Text="الكمية" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="15" Expression="[item_count]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="16" UsePadding="false" />
                    </Item3>
                    <Item4 Ref="17" ControlType="XRTableCell" Name="XrTableCell4" Weight="0.91442565892173233" Text="اسم الصنف" TextAlignment="MiddleRight" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100" BorderColor="White">
                      <ExpressionBindings>
                        <Item1 Ref="18" Expression="[item_name]" PropertyName="Text" EventName="BeforePrint" />
                      </ExpressionBindings>
                      <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
                    </Item4>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="20" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item4>
    <Item5 Ref="21" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="123.0416">
      <Controls>
        <Item1 Ref="22" ControlType="XRLabel" Name="XrLabel23" Text="XrLabel23" TextAlignment="TopCenter" SizeF="237,29.58336" LocationFloat="0, 93.45829" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="23" Expression="[sec_s1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="24" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="25" ControlType="XRLabel" Name="XrLabel24" Text="XrLabel24" TextAlignment="TopCenter" SizeF="234,31.33333" LocationFloat="0, 62.12497" Font="Droid Arabic Kufi, 11pt, style=Bold" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="26" Expression="[sec_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="27" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="28" ControlType="XRPictureBox" Name="XrPictureBox3" Sizing="StretchImage" SizeF="116.2499,62.12497" LocationFloat="56.12505, 0">
          <ExpressionBindings>
            <Item1 Ref="29" Expression="[sec_pic]" PropertyName="Image" EventName="BeforePrint" />
          </ExpressionBindings>
        </Item3>
      </Controls>
    </Item5>
    <Item6 Ref="30" ControlType="PageHeaderBand" Name="PageHeader" HeightF="156.8119">
      <Controls>
        <Item1 Ref="31" ControlType="XRTable" Name="XrTable4" TextAlignment="MiddleCenter" SizeF="243.6874,17.0573" LocationFloat="0, 139.7546" Font="Droid Arabic Kufi, 7pt" ForeColor="White" BackColor="DarkGreen" BorderColor="White">
          <Rows>
            <Item1 Ref="32" ControlType="XRTableRow" Name="XrTableRow5" Weight="1">
              <Cells>
                <Item1 Ref="33" ControlType="XRTableCell" Name="XrTableCell17" Weight="0.34842684818111214" Text="الاجمالي" Padding="2,2,0,0,100">
                  <StylePriority Ref="34" UsePadding="false" />
                </Item1>
                <Item2 Ref="35" ControlType="XRTableCell" Name="XrTableCell33" Weight="0.30313313203298747" Text="السعر" Padding="2,2,0,0,100" />
                <Item3 Ref="36" ControlType="XRTableCell" Name="XrTableCell34" Weight="0.28168509471873737" Text="الكمية" Padding="2,2,0,0,100">
                  <StylePriority Ref="37" UsePadding="false" />
                </Item3>
                <Item4 Ref="38" ControlType="XRTableCell" Name="XrTableCell36" Weight="0.91442565892173233" Text="اسم الصنف" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100" BorderColor="White">
                  <StylePriority Ref="39" UseFont="false" />
                </Item4>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="40" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="41" ControlType="XRLabel" Name="XrLabel3" TextFormatString="{0:yyyy-MM-dd}" Text="XrLabel3" TextAlignment="MiddleCenter" SizeF="91.18738,23.41665" LocationFloat="149.724, 7.916677" Font="Droid Arabic Kufi, 9pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="42" Expression="[invoice_date]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="43" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="44" ControlType="XRLabel" Name="XrLabel16" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="174.2292,23.95831" LocationFloat="0, 109.4583" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="45" Expression="[Accounts_phone1]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="46" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="47" ControlType="XRLabel" Name="XrLabel17" RightToLeft="Yes" Text="هاتف" TextAlignment="TopCenter" SizeF="70.32291,23.95832" LocationFloat="174.2292, 109.4583" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="49" ControlType="XRLabel" Name="XrLabel14" RightToLeft="Yes" Text="العنوان :" TextAlignment="TopCenter" SizeF="70.32294,23.95833" LocationFloat="174.2292, 85.49993" Font="Droid Arabic Kufi, 9pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="51" ControlType="XRLabel" Name="XrLabel15" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="174.2292,23.95833" LocationFloat="0, 85.49996" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="52" Expression="[Accounts_adress]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="53" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="54" ControlType="XRLabel" Name="XrLabel13" RightToLeft="Yes" Text="اسم العميل :" TextAlignment="TopCenter" SizeF="70.32288,22.91664" LocationFloat="174.2292, 62.58332" Font="Droid Arabic Kufi, 8pt" ForeColor="Black" BackColor="WhiteSmoke" Padding="2,2,0,0,100" BorderColor="Silver" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="55" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="56" ControlType="XRLabel" Name="XrLabel12" Text="XrLabel5" TextAlignment="MiddleRight" SizeF="174.2292,22.91663" LocationFloat="0, 62.58332" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="57" Expression="[Accounts_name]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="58" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="59" ControlType="XRLabel" Name="XrLabel5" Text="XrLabel5" TextAlignment="MiddleCenter" SizeF="64.16669,31.24999" LocationFloat="85.55724, 31.33332" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <ExpressionBindings>
            <Item1 Ref="60" Expression="[invoice_number]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="61" UseFont="false" UseForeColor="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="62" ControlType="XRLabel" Name="XrLabel19" Text="فاتورة بيع" TextAlignment="MiddleCenter" SizeF="64.16669,23.41665" LocationFloat="85.55724, 7.916671" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="All">
          <StylePriority Ref="63" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="64" ControlType="XRLabel" Name="XrLabel11" Text="XrLabel11" TextAlignment="MiddleCenter" SizeF="84.82989,23.41665" LocationFloat="0, 7.916674" Font="Droid Arabic Kufi, 8pt" Padding="2,2,0,0,100" BorderColor="Gainsboro" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="65" Expression="[store]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="66" UseFont="false" UseBorderColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
      </Controls>
    </Item6>
    <Item7 Ref="67" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="73.83975">
      <Controls>
        <Item1 Ref="68" ControlType="XRLabel" Name="XrLabel9" Text="XrLabel9" TextAlignment="MiddleRight" SizeF="195.6257,14.45314" LocationFloat="46.49047, 0" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="69" Expression="[invoice_note]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="70" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="71" ControlType="XRLabel" Name="XrLabel6" Text="XrLabel6" TextAlignment="MiddleRight" SizeF="195.6255,14.45311" LocationFloat="46.49047, 14.45319" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="72" Expression="[total_string]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="73" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="74" ControlType="XRLabel" Name="XrLabel21" Text=" : أرقام الهواتف  " TextAlignment="MiddleRight" SizeF="84.58365,13.03909" LocationFloat="157.5323, 45.0122" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="76" ControlType="XRLabel" Name="XrLabel20" Text=" : العنوان " TextAlignment="MiddleRight" SizeF="84.58365,13.03905" LocationFloat="157.5323, 31.97319" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="77" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="78" ControlType="XRLabel" Name="XrLabel2" RightToLeft="Yes" Text="XrLabel2" TextAlignment="MiddleLeft" SizeF="156.3242,13.03906" LocationFloat="1.208153, 31.9732" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="79" Expression="[sec_address]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="81" ControlType="XRLabel" Name="XrLabel1" RightToLeft="Yes" Text="XrLabel1" TextAlignment="MiddleLeft" SizeF="156.3242,13.03909" LocationFloat="1.208153, 45.0122" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="82" Expression="[sec_phone]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="83" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="84" ControlType="XRLabel" Name="XrLabel25" RightToLeft="Yes" Text="XrLabel25" TextAlignment="TopLeft" SizeF="240.4755,15.78847" LocationFloat="1.640508, 58.05128" Font="Droid Arabic Kufi, 7pt" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="85" Expression="[sec_s2]" PropertyName="Text" EventName="BeforePrint" />
          </ExpressionBindings>
          <StylePriority Ref="86" UseFont="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item7>
    <Item8 Ref="87" ControlType="PageFooterBand" Name="PageFooter" HeightF="22.91667">
      <Controls>
        <Item1 Ref="88" ControlType="XRLabel" Name="XrLabel22" RightToLeft="Yes" Text=" بيتا سوفت للبرمجيات (01028521771)" TextAlignment="MiddleCenter" SizeF="192.6042,15.58078" LocationFloat="25.15623, 0" Font="Times New Roman, 7pt" Padding="2,2,0,0,100">
          <StylePriority Ref="89" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item8>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v17.2" Name="SqlDataSource1" Base64="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" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>