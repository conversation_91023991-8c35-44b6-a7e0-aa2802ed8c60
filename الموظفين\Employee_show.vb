﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors.XtraMessageBox

Public Class Employee_show
    Private ctr As Integer = 0
    Sub fill_dgv()
        Dim adp As New SqlDataAdapter("select * from employees", sqlconn)
        Dim ds As New DataSet
        adp.Fill(ds)
        Dim dt = ds.Tables(0)
        dgv.DataSource = dt
    End Sub
    Private Sub customer_show_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        fill_dgv()

    End Sub

    Private Sub new_btn_Click(sender As Object, e As EventArgs) Handles new_btn.Click
        Dim f As Employee_add = New Employee_add()
        f.Text = "تسجيل موظف"
        f.new_cus()
        f.MdiParent = XtraForm1
        f.Show()
    End Sub

    Private Sub delete_btn_Click(sender As Object, e As EventArgs) Handles delete_btn.Click

        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد موظف لحذفه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If XtraMessageBox.Show(" هل انت متاكد من حذفة ؟", "تاكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) <> DialogResult.No Then

            Dim sql = "select * from employees where emp_code=N'" & GridView2.GetFocusedRowCellValue("emp_code") & "'"
            Dim adp As New SqlDataAdapter(sql, sqlconn)
            Dim ds As New DataSet
            adp.Fill(ds)
            Dim dt = ds.Tables(0)
            If dt.Rows.Count = 0 Then
                MsgBox("فشل في جلب البيانات")
            Else
                Dim dr = dt.Rows(0)
                dr.Delete()
                Dim cmd As New SqlCommandBuilder(adp)
                adp.Update(dt)
                XtraMessageBox.Show("تم حذف الموظف بنجاح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                fill_dgv()
            End If
        End If
    End Sub
    Private Sub edit_btn_Click(sender As Object, e As EventArgs) Handles edit_btn.Click
       
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد موظف للتعديل عليه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Employee_add = New Employee_add()
        f.Text = "تسجيل موظف"
        f.new_cus()
        f.MdiParent = XtraForm1
        f.show_data(GridView2.GetFocusedRowCellValue("emp_code"))
        f.Show()
    End Sub
    Private Sub print_btn_Click(sender As Object, e As EventArgs) Handles print_btn.Click
        dgv.ShowPrintPreview()
    End Sub

    Private Sub exit_btn_Click(sender As Object, e As EventArgs) Handles exit_btn.Click
        Me.Dispose()
    End Sub
 

    Private Sub catch_btn_Click(sender As Object, e As EventArgs)
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد موظف لاستلام النقدية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As cashing_customer = New cashing_customer()
        f.Text = "سند قبض"
        f.MdiParent = XtraForm1
        f.Show()
        f.Accounts_name.Text = GridView2.GetFocusedRowCellValue("cusname")
        f.Amount.Focus()
    End Sub

    Private Sub report_btn_Click(sender As Object, e As EventArgs)
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد موظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As customer_Detailed = New customer_Detailed()
        f.Text = "كشف تفصيلي"
        f.MdiParent = XtraForm1
        f.Show()
        f.customername.Text = GridView2.GetFocusedRowCellValue("cusname")
        f.search()
    End Sub

    Private Sub statistic_Click(sender As Object, e As EventArgs)
        If GridView2.RowCount = Nothing Then
            XtraMessageBox.Show("لايوجد موظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        Dim f As Statistic_customer = New Statistic_customer()
        f.Text = "احصائية موظف"
        f.MdiParent = XtraForm1
        f.Show()
        f.customername.Text = GridView2.GetFocusedRowCellValue("cusname")
        f.search()
    End Sub
    Private Sub خروجToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles w10.Click
        Me.Dispose()
    End Sub
End Class